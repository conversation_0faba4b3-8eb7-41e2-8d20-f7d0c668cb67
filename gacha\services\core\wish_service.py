from __future__ import annotations
import asyncpg
import asyncio
import json
from typing import Dict, List, Optional, Any, Tuple, TYPE_CHECKING
import random
from gacha.app_config import config_service
if TYPE_CHECKING:
    from gacha.services.common.validation_service import ValidationService
import logging
import heapq
import math
from utils.logger import logger
from gacha.constants import RarityLevel
from database.postgresql.async_manager import AsyncPgManager
from gacha.repositories.collection.user_collection_repository import UserCollectionRepository
from gacha.repositories.collection.user_wish_repository import UserWishRepository
from gacha.repositories.card.master_card_repository import MasterCardRepository
from gacha.services.core.user_service import UserService
from gacha.services.core.economy_service import EconomyService
from gacha.exceptions import UserNotFoundError, GachaSystemError, CardNotInWishListError # 導入 UserNotFoundError 和其他可能需要的異常
from gacha.models.models import Card
from gacha.constants import MarketStatsEventType
from gacha.utils.redis_publisher import RedisEventPublisher
from gacha.exceptions import (
    UserNotFoundError, 
    CardNotFoundError, 
    CardAlreadyInWishListError,
    WishListFullError,
    CardNotInWishListError,
    GachaSystemError
)

class WishService:
    """許願服務 (Asyncpg 版本)，處理用戶許願卡片相關邏輯"""

    def __init__(self, pool: asyncpg.Pool, user_service: UserService, user_wish_repo: UserWishRepository, collection_repo: UserCollectionRepository, card_repo: MasterCardRepository, economy_service: EconomyService, validation_service: ValidationService, redis_publisher: RedisEventPublisher):
        """初始化許願服務 (Asyncpg 版本)"""
        if pool is None:
            err_msg = '許願服務 初始化失敗：必須提供 asyncpg 連接池。'
            logger.error(err_msg)
            raise ValueError(err_msg)
        if user_service is None or user_wish_repo is None or collection_repo is None or card_repo is None or economy_service is None or redis_publisher is None or validation_service is None:
            err_msg = '許願服務 初始化失敗：必須提供所有依賴的服務、Repository、RedisEventPublisher 和 ValidationService 實例。'
            logger.error(err_msg)
            raise ValueError(err_msg)
            
        self.pool = pool
        self.user_service = user_service
        self.user_wish_repo = user_wish_repo
        self.collection_repo = collection_repo
        self.card_repo = card_repo
        self.economy_service = economy_service
        self.validation_service = validation_service
        self.redis_publisher = redis_publisher
        self.MAX_WISH_SLOTS = config_service.get_wish_max_slots(defaultValue=10)
        self.MAX_WISH_POWER_LEVEL = config_service.get_wish_max_power_level(defaultValue=10)
        self.default_wish_slots = config_service.get_default_wish_slots(defaultValue=1)
        self.default_wish_power_level = config_service.get_default_wish_power_level(defaultValue=1)
        self.default_wish_multiplier = config_service.get_default_wish_multiplier(defaultValue=3.0)

    async def add_wish(self, user_id: int, card_id: int) -> Dict[str, Any]:
        """(Async) 將卡片添加到用戶的許願列表"""
        try:
            user = await self.validation_service.ensure_user_exists(user_id, create_if_missing=False)
            card = await self.validation_service.ensure_card_exists(card_id)
            inserted_id_val = None
            slot_index_val = -1
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    wishes = await self.user_wish_repo.get_user_wishes(user_id, connection=conn)
                    if any((wish['card_id'] == card_id for wish in wishes)):
                        raise CardAlreadyInWishListError(card_id=card_id)
                    
                    user_for_slots = await self.user_service.get_user(user_id, connection=conn)
                    if not user_for_slots:
                        logger.error('[WishService] add_wish: User %s not found via user_service.get_user within transaction after initial validation.', user_id)
                        raise RuntimeError('無法在事務中獲取用戶信息以檢查許願槽位')
                    
                    wish_slots = user_for_slots.wish_slots
                    if len(wishes) >= wish_slots:
                        raise WishListFullError(current_slots=wish_slots)
                    
                    used_slots = set((wish['slot_index'] for wish in wishes))
                    current_slot_index = 0
                    while current_slot_index in used_slots:
                        current_slot_index += 1
                    slot_index_val = current_slot_index
                    inserted_id_val = await self.user_wish_repo.add_user_wish(user_id, card_id, slot_index_val, connection=conn)
                    if inserted_id_val is None:
                        raise RuntimeError('添加許願失敗，倉庫未能成功插入記錄')
            
            if inserted_id_val:
                payload = [(card_id, 1)]
                await self.redis_publisher.publish(event_type=MarketStatsEventType.WISHLIST_COUNT_UPDATE, payload=payload, user_id_for_log=user_id)
            
            return {
                'slot_index': slot_index_val
            }
        except (UserNotFoundError, CardNotFoundError, CardAlreadyInWishListError, WishListFullError):
            # 直接向上傳播已知異常
            raise
        except Exception as e:
            logger.error('[轉蛋系統] 添加許願失敗: %s', str(e), exc_info=True)
            raise GachaSystemError(f'系統錯誤: {str(e)}')

    async def remove_wish(self, user_id: int, card_id: int) -> Dict[str, Any]:
        """(Async) 從用戶的許願列表中移除卡片"""
        delete_status = None
        try:
            await self.validation_service.ensure_user_exists(user_id, create_if_missing=False)
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    rows_affected = await self.user_wish_repo.remove_user_wish(user_id, card_id, connection=conn)
                    delete_status = f'DELETE {rows_affected}'
                    if rows_affected == 0:
                        raise CardNotInWishListError(card_id=card_id)
                    elif rows_affected != 1:
                        raise RuntimeError(f'移除許願時發生意外，影響行數: {rows_affected}')
            
            if delete_status == 'DELETE 1':
                payload = [(card_id, -1)]
                await self.redis_publisher.publish(event_type=MarketStatsEventType.WISHLIST_COUNT_UPDATE, payload=payload, user_id_for_log=user_id)
            
            return {}
        except (UserNotFoundError, CardNotInWishListError):
            # 直接向上傳播已知異常
            raise
        except Exception as e:
            logger.error('[轉蛋系統] 移除許願失敗: %s', str(e), exc_info=True)
            raise GachaSystemError(f'系統錯誤: {str(e)}')

    async def get_wish_list(self, user_id: int) -> Dict[str, Any]:
        """(Async) 獲取用戶的許願列表及相關信息"""
        try:
            user = await self.user_service.get_user(user_id, create_if_missing=True)
            if not user:
                wish_slots = self.default_wish_slots
                wish_power_level = self.default_wish_power_level
                nickname = None
            else:
                wish_slots = user.wish_slots if user.wish_slots is not None else self.default_wish_slots
                wish_power_level = user.wish_power_level if user.wish_power_level is not None else self.default_wish_power_level
                nickname = user.nickname
            
            wishes_details_list = await self.user_wish_repo.get_user_wishes_with_details(user_id)
            wish_list = []
            if wishes_details_list:
                for wish_detail in wishes_details_list:
                    card_info_raw = wish_detail.get('card_info')
                    if not card_info_raw:
                        logger.warning('Wish item for user %s missing card_info: %s', user_id, wish_detail)
                        continue
                    raw_rarity = card_info_raw.get('rarity')
                    try:
                        rarity_enum = RarityLevel(int(raw_rarity)) if raw_rarity is not None else RarityLevel.COMMON
                    except ValueError:
                        logger.warning("Invalid rarity value '%s' for card_id %s. Defaulting to COMMON.", raw_rarity, card_info_raw.get('card_id'))
                        rarity_enum = RarityLevel.COMMON
                    card_info_formatted = {'card_id': card_info_raw.get('card_id'), 'name': card_info_raw.get('name'), 'series': card_info_raw.get('series'), 'rarity': rarity_enum, 'image_url': card_info_raw.get('image_url'), 'pool_type': card_info_raw.get('pool_type')}
                    wish_item = {'card_id': wish_detail.get('card_id'), 'slot_index': wish_detail.get('slot_index'), 'card_info': card_info_formatted}
                    wish_list.append(wish_item)

            used_slots_count = len(wish_list)
            
            return {
                'wishes': wish_list,
                'wish_slots': wish_slots,
                'wish_power_level': wish_power_level,
                'used_slots': used_slots_count,
                'user_nickname': nickname
            }
        except Exception as e:
            logger.error('[轉蛋系統] 獲取許願列表失敗: %s', str(e), exc_info=True)
            # 確保這裡也導入了 GachaSystemError
            raise GachaSystemError(f'獲取許願列表失敗: {str(e)}')

    def calculate_next_slot_cost(self, current_slots: int) -> int:
        """計算下一個許願槽位的成本"""
        if current_slots >= self.MAX_WISH_SLOTS:
            return -1
        slot_costs = config_service.get_wish_slot_costs()
        return slot_costs.get(current_slots, -1)

    def calculate_next_power_cost(self, current_level: int) -> int:
        """計算下一個許願力度等級的成本"""
        if current_level >= self.MAX_WISH_POWER_LEVEL:
            return -1
        power_costs = config_service.get_wish_power_costs()
        return power_costs.get(current_level, -1)

    async def expand_slot(self, user_id: int) -> Dict[str, Any]:
        """(Async) 為用戶擴充許願槽位"""
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    await self.validation_service.ensure_user_exists(user_id, create_if_missing=False, connection=conn)
                    user = await self.user_service.user_repo.get_user_for_update(user_id, connection=conn)
                    if not user:
                        logger.error('[WishService] expand_slot: User %s not found with get_user_for_update after ensure_user_exists. UNEXPECTED.', user_id)
                        raise RuntimeError('獲取用戶以鎖定記錄時發生意外錯誤')
                    
                    current_slots = user.wish_slots
                    if current_slots >= self.MAX_WISH_SLOTS:
                        raise ValueError(f'已達到許願槽位上限（{self.MAX_WISH_SLOTS}）')
                    
                    cost = self.calculate_next_slot_cost(current_slots)
                    if cost == -1:
                        raise ValueError(f'已達到許願槽位上限（{self.MAX_WISH_SLOTS}）')
                    
                    # 直接使用 award_oil 返回的整數值（新餘額）
                    new_balance = await self.economy_service.award_oil(user_id, -cost, '擴充許願槽位', connection=conn)
                    
                    new_slots = current_slots + 1
                    # 直接呼叫 update_wish_slots，它會在失敗時拋出異常
                    await self.user_service.user_repo.update_wish_slots(user_id, new_slots, connection=conn)
            
            return {
                'new_slots': new_slots,
                'cost': cost,
                'new_balance': new_balance
            }
        except UserNotFoundError:
            # 直接向上傳播已知異常
            raise
        except ValueError as ve:
            logger.warning('[轉蛋系統] 擴充許願槽位失敗: %s', str(ve))
            raise GachaSystemError(str(ve))
        except Exception as e:
            logger.error('[轉蛋系統] 擴充許願槽位失敗: %s', str(e), exc_info=True)
            raise GachaSystemError(f'系統錯誤: {str(e)}')

    async def power_up(self, user_id: int) -> Dict[str, Any]:
        """(Async) 為用戶提升許願力度等級"""
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    await self.validation_service.ensure_user_exists(user_id, create_if_missing=False, connection=conn)
                    user = await self.user_service.user_repo.get_user_for_update(user_id, connection=conn)
                    if not user:
                        logger.error('[WishService] power_up: User %s not found with get_user_for_update after ensure_user_exists. UNEXPECTED.', user_id)
                        raise RuntimeError('獲取用戶以鎖定記錄時發生意外錯誤')
                    
                    current_level = user.wish_power_level
                    if current_level >= self.MAX_WISH_POWER_LEVEL:
                        raise ValueError(f'已達到許願力度上限（{self.MAX_WISH_POWER_LEVEL}）')
                    
                    cost = self.calculate_next_power_cost(current_level)
                    if cost == -1:
                        raise ValueError(f'已達到許願力度上限（{self.MAX_WISH_POWER_LEVEL}）')
                    
                    # 直接使用 award_oil 返回的整數值（新餘額）
                    new_balance = await self.economy_service.award_oil(user_id, -cost, '提升許願力度', connection=conn)
                    
                    new_level = current_level + 1
                    # 直接呼叫 update_wish_power_level，它會在失敗時拋出異常
                    await self.user_service.user_repo.update_wish_power_level(user_id, new_level, connection=conn)
            
            return {
                'new_level': new_level,
                'cost': cost,
                'new_balance': new_balance
            }
        except UserNotFoundError:
            # 直接向上傳播已知異常
            raise
        except ValueError as ve:
            logger.warning('[轉蛋系統] 提升許願力度失敗: %s', str(ve))
            raise GachaSystemError(str(ve))
        except Exception as e:
            logger.error('[轉蛋系統] 提升許願力度失敗: %s', str(e), exc_info=True)
            raise GachaSystemError(f'系統錯誤: {str(e)}')

    def get_wish_chance_multiplier(self, power_level: int) -> float:
        """根據用戶的許願力度等級，計算抽中許願卡片的概率提升倍數。

        倍數是預先定義好的，與每個力度等級相對應。
        如果 power_level 超出定義範圍，會被限制在 [1, MAX_WISH_POWER_LEVEL] 之間。

        Args:
            power_level (int): 用戶的許願力度等級。

        Returns:
            float: 對應的概率提升倍數。默認為 3.0 (等級 1)。
        """
        power_level = max(1, min(power_level, self.MAX_WISH_POWER_LEVEL))
        multipliers = config_service.get_wish_power_multipliers()
        return multipliers.get(power_level, self.default_wish_multiplier)

    def select_with_weights(self, weighted_items: List[Tuple[Any, float]]) -> Optional[Any]:
        """從帶權重的項目列表中隨機選擇一個項目。

        使用標準的輪盤賭選擇算法（按權重比例分配概率）。

        Args:
            weighted_items (List[Tuple[Any, float]]): 包含 (項目, 權重) 元組的列表。
                                                      權重必須是非負數。

        Returns:
            Optional[Any]: 隨機選中的項目。如果列表為空或所有權重總和為 0 或負數，
                           則返回 None。
        """
        if not weighted_items:
            return None
        positive_weighted_items = [(item, weight) for item, weight in weighted_items if weight > 0]
        if not positive_weighted_items:
            logger.warning('[許願服務] select_with_weights: 沒有帶有正權重的項目。原始項目數量: %s。', len(weighted_items))
            return None
        total_weight = sum((weight for _, weight in positive_weighted_items))
        if total_weight <= 0:
            logger.error('[許願服務] select_with_weights: 正權重總和為 %s，此為非預期情況。返回 None。', total_weight)
            return None
        r = random.uniform(0, total_weight)
        current_weight_sum = 0
        for item, weight in positive_weighted_items:
            current_weight_sum += weight
            if r < current_weight_sum:
                return item
        logger.warning('[許願服務] select_with_weights: 循環意外結束 (r=%s, total_weight=%s)。返回最後一個帶有正權重的項目。', r, total_weight)
        return positive_weighted_items[-1][0]

    def select_multiple_with_weights(self, weighted_items: List[Tuple[Any, float]], k: int) -> List[Any]:
        """
        使用 A* 加權隨機抽樣算法 (不放回) 從帶權重的列表中選擇 k 個唯一的項目。

        此算法為每個項目分配一個基於其權重和隨機數的 "key"，然後選擇 key 值最大的 k 個項目。
        這確保了選擇概率與權重成正比，並且適用於不放回抽樣。
        參考: "Weighted Random Sampling with a Reservoir" by Efraimidis and Spirakis (2006).

        Args:
            weighted_items (List[Tuple[Any, float]]): 包含 (項目, 權重) 元組的列表。
                                                      權重必須是正數。權重為 0 或負數的項目將被忽略。
            k (int): 需要選擇的項目數量。

        Returns:
            List[Any]: 包含 k 個唯一選中項目的列表。如果可用項目數少於 k，則返回所有可用項目。
                       如果輸入列表為空或 k <= 0，則返回空列表。
        """
        if not weighted_items:
            logger.debug('[許願服務] select_multiple_with_weights: 輸入的 weighted_items 為空。')
            return []
        if k <= 0:
            logger.debug('[許願服務] select_multiple_with_weights: k <= 0，返回空列表。')
            return []
        keyed_items = []
        positive_weight_items_count = 0
        for item, weight in weighted_items:
            if weight <= 0:
                logger.debug('[許願服務] select_multiple_with_weights: 忽略權重非正的項目，權重值: %s。', weight)
                continue
            positive_weight_items_count += 1
            u = random.uniform(1e-12, 1.0)
            log_key = 1.0 / weight * math.log(u)
            keyed_items.append((log_key, item))
        n = positive_weight_items_count
        if k >= n:
            logger.debug('[許願服務] select_multiple_with_weights: k (%s) >= 帶正權重的可用項目數 (%s)。返回所有 %s 個可用項目並打亂順序。', k, n, n)
            items = [item for log_key, item in keyed_items]
            random.shuffle(items)
            return items
        if n == 0:
            logger.warning('[許願服務] select_multiple_with_weights: 未找到帶有正權重的項目。')
            return []
        top_k_keyed = heapq.nlargest(k, keyed_items, key=lambda x: x[0])
        selected_items = [item for log_key, item in top_k_keyed]
        logger.debug('[許願服務] select_multiple_with_weights: 從 %s 個帶正權重的可用項目中選擇了 %s 個項目 (k=%s)。', n, len(selected_items), k)
        return selected_items

    def apply_wish_weights(self, items: List[Any], wished_item_ids: List[int], wish_power_level: int=1, id_extractor=lambda x: x['card_id']) -> List[Tuple[Any, float]]:
        """將許願權重應用於一個項目列表，增加許願列表中項目的相對權重。

        此方法遍歷輸入的項目列表，對於 ID 存在於 `wished_item_ids` 中的項目，
        其權重將乘以由 `get_wish_chance_multiplier` 根據 `wish_power_level` 計算出的倍數。
        其他項目的權重保持為 1.0。

        Args:
            items (List[Any]): 需要應用權重的原始項目列表。列表中的每個元素
                               應能通過 `id_extractor` 提取出其唯一標識符。
            wished_item_ids (List[int]): 用戶許願的項目 ID 列表。
            wish_power_level (int, optional): 用戶的許願強度等級。默認為 1。
            id_extractor (callable, optional): 一個函數，用於從 `items` 列表的每個元素中
                                                提取其唯一 ID。默認為提取字典中的 'card_id'。

        Returns:
            List[Tuple[Any, float]]: 一個新的列表，包含 (原始項目, 計算出的權重) 元組。
        """
        weight_multiplier = self.get_wish_chance_multiplier(wish_power_level)
        logger.debug('[許願服務] 應用許願權重，倍數: %s (許願強度: %s)', weight_multiplier, wish_power_level)
        weighted_results = []
        wished_items_set = set(wished_item_ids)
        for item in items:
            try:
                item_id = id_extractor(item)
                weight = weight_multiplier if item_id in wished_items_set else 1.0
                weighted_results.append((item, weight))
            except Exception as e:
                logger.error('[許願服務] 在 apply_wish_weights 中提取 ID 或處理項目時出錯: %s。錯誤: %s', item, e, exc_info=False)
                weighted_results.append((item, 1.0))
        logger.debug('[許願服務] 完成權重應用。原始項目數: %s，許願 ID 數: %s，結果加權項目數: %s', len(items), len(wished_item_ids), len(weighted_results))
        boosted_count = sum((1 for _, w in weighted_results if w == weight_multiplier))
        logger.debug('[許願服務] 獲得權重提升的項目數量: %s', boosted_count)
        return weighted_results

    async def get_wishes_status_batch(self, user_id: int, card_ids: List[int]) -> Dict[int, bool]:
        """(Async) 批量獲取多張卡片的許願狀態"""
        if not card_ids:
            return {}
        try:
            user = await self.user_service.get_user(user_id, create_if_missing=False)
            if not user:
                logger.warning('[WishService] User %s not found for get_wishes_status_batch.', user_id)
                return {card_id: False for card_id in card_ids}
            wished_card_ids_set = await self.user_wish_repo.get_wished_card_ids_for_user(user_id, card_ids)
            return {card_id: card_id in wished_card_ids_set for card_id in card_ids}
        except Exception as e:
            logger.error('[轉蛋系統] 批量獲取許願狀態失敗: %s', str(e), exc_info=True)
            return {card_id: False for card_id in card_ids}