-- 添加 user_status 字段到 user_profiles 表
-- 先检查表是否存在，如果不存在则创建

-- 检查表是否存在
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'user_profiles') THEN
        -- 创建 user_profiles 表
        CREATE TABLE public.user_profiles (
            id SERIAL PRIMARY KEY,
            user_id BIGINT NOT NULL UNIQUE,
            showcased_card_collection_id INTEGER,
            sub_card_1_collection_id INTEGER,
            sub_card_2_collection_id INTEGER, 
            sub_card_3_collection_id INTEGER,
            sub_card_4_collection_id INTEGER,
            background_image_url TEXT,
            cached_profile_image_url TEXT,
            like_count INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT fk_user_id FOREIGN KEY (user_id) REFERENCES gacha_users(user_id) ON DELETE CASCADE
        );
        
        -- 添加索引
        CREATE INDEX idx_user_profiles_user_id ON public.user_profiles(user_id);
        
        RAISE NOTICE 'Created user_profiles table';
    ELSE
        RAISE NOTICE 'user_profiles table already exists';
    END IF;
END $$;

-- 检查字段是否存在，如果不存在则添加 user_status 字段
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_profiles' 
        AND column_name = 'user_status'
    ) THEN
        ALTER TABLE public.user_profiles ADD COLUMN user_status VARCHAR(150);
        RAISE NOTICE 'Added user_status column to user_profiles table';
    ELSE
        RAISE NOTICE 'user_status column already exists in user_profiles table';
    END IF;
END $$; 