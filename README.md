# DICKPK Discord 機器人專案

## 專案概述

這是一個功能豐富的 Discord 抽卡遊戲機器人專案，提供了多種互動功能和遊戲系統。主要特色包括：

- 多樣化的抽卡機制與卡牌收集系統
- 完善的卡牌管理功能（圖鑑、強化、最愛標記等）
- 模擬經濟系統和卡牌市場交易
- 獨立且動態的虛擬資產/股票市場模擬系統
- 增加用戶互動性的多種小遊戲
- 社群功能，如排行榜等

## 技術架構

### 主要技術棧

- **核心語言與框架**: Python (基於 Discord.py)
- **資料庫**: PostgreSQL (用於持久化存儲)
- **緩存/消息隊列**: Redis (用於市場系統的生命週期計數器、新聞影響隊列等)
- **圖表生成**: Matplotlib (用於生成股票價格趨勢圖)
- **AI輔助**: 內部或外部 AI服務 (用於生成內容)

### 專案架構

```
DICKPK/
├── ai_assistant/           # AI 助理模組，提供 AI 輔助功能
│   ├── outfit_rater/       # 穿搭評分功能
│   ├── prompts/            # AI提示模板
│   └── qa_system/          # 問答系統
├── catch/                  # 資料捕獲模組
├── cogs/                   # Discord.py 的指令模組
├── database/               # 資料庫相關模組
│   ├── gacha_waifu/        # 抽卡資料
│   │   └── card_data/      # 卡牌基礎資料存儲
│   ├── postgresql/         # PostgreSQL 資料庫管理
│   └── redis/              # Redis 緩存管理
├── docs/                   # 文檔
├── fonts/                  # 字體文件
├── gacha/                  # 主要的抽卡系統模組
│   ├── cogs/               # 抽卡相關指令
│   ├── models/             # 數據模型
│   ├── repositories/       # 數據訪問層
│   │   ├── card/           # 卡牌資料訪問
│   │   ├── collection/     # 收藏資料訪問
│   │   └── user/           # 用戶資料訪問
│   ├── scripts/            # 相關腳本
│   ├── services/           # 核心業務邏輯
│   │   ├── core/           # 核心服務，包含抽卡、卡池管理等
│   │   ├── games/          # 遊戲相關服務
│   │   ├── market/         # 市場和交易服務
│   │   └── ui/             # UI 介面服務
│   ├── utils/              # 工具函數
│   └── views/              # 用戶界面呈現層
├── image_processing/       # 圖像處理模組
│   ├── gif_modifier/       # GIF 修改工具
│   ├── gifs/               # GIF 資源
│   └── image_processing/   # 圖像處理核心
├── logs/                   # 日誌文件
├── memory-bank/            # 內存相關
├── scripts/                # 各種腳本工具
├── tests/                  # 測試文件
├── utils/                  # 通用工具函數
├── .env                    # 環境變數配置（敏感數據如Discord令牌、資料庫連接資訊）
├── .env.example            # 環境變數範例
├── bot.py                  # 主入口文件
├── command_registry.py     # 命令註冊
├── config.yaml             # 主要配置文件（應用程式配置、資料庫連接池設定、AI設定等）
├── gacha_settings.yaml     # 抽卡系統配置（卡池、抽卡機率、卡牌價格等）
├── requirements.txt        # 依賴管理
├── schema.sql              # 資料庫結構定義
└── ui_settings.yaml        # UI 配置（如按鈕顏色、嵌入設定等）
```

## 主要模組詳解

### 1. 核心應用程式 (Core Application)
- **主要文件**: `bot.py`, `config.yaml`, `requirements.txt`
- **職責**: 應用程式入口點，處理與 Discord API 的交互，命令路由和分派

### 2. 抽卡系統模組 (Gacha System)
- **主要目錄**: `gacha/`
- **職責**: 實現抽卡核心玩法，包括卡池管理、許願系統、卡牌圖鑑、用戶收藏管理等
- **主要組件**:
  - `commands/`: 用戶命令接口
  - `services/`: 核心業務邏輯
  - `repositories/`: 數據訪問層
  - `models/`: 數據模型
  - `views/`: 用戶界面呈現

### 3. 圖像處理模組 (Image Processing)
- **主要目錄**: `image_processing/`
- **職責**: 提供圖像生成、修改和處理功能，特別是 GIF 動圖處理

### 4. AI 助理模組 (AI Assistant)
- **主要目錄**: `ai_assistant/`
- **職責**: 提供 AI 輔助功能，如生成公司名稱、股票代碼和描述

### 5. 資料庫模組 (Database)
- **主要目錄**: `database/`, `schema.sql`
- **職責**: 定義和管理資料庫結構，提供資料庫連接

### 6. 腳本與工具 (Scripts & Utilities)
- **主要目錄**: `scripts/`, `utils/`
- **職責**: 提供一次性腳本和通用工具函數

### 7. 虛擬資產/股票市場模組
- **主要目錄**: 主要位於 `gacha/services/market/` 下
- **職責**: 實現模擬股票交易市場，包括股票生命週期管理、價格更新、交易請求處理等

## 配置文件詳解

本專案使用多個配置文件來管理不同方面的設定：

### 1. `.env` 和 `.env.example`
- **位置**: 專案根目錄
- **用途**: 包含敏感配置資訊，如：
  - Discord Bot 令牌 (`DISCORD_TOKEN`)
  - 資料庫連接資訊 (`PG_HOST`, `PG_USER`, `PG_PASSWORD` 等)
  - Redis 連接資訊
  - API 金鑰和其他敏感資訊
- **注意**: `.env` 文件不應提交到版本控制系統，而應使用 `.env.example` 作為模板

### 2. `config.yaml`
- **位置**: 專案根目錄
- **用途**: 主要應用程式配置，包含：
  - 日誌級別設定
  - 資料庫連接池設定
  - AI助理設定
  - 股票市場整合設定
  - 新聞生成設定
  - 交易費率設定

### 3. `gacha_settings.yaml`
- **位置**: 專案根目錄
- **用途**: 抽卡系統核心配置，包含：
  - 卡池定義和配置
  - 抽卡機率設定
  - 卡牌稀有度分佈
  - 卡牌基礎價格設定
  - 每種卡池的抽卡消耗設定

### 4. `ui_settings.yaml`
- **位置**: 專案根目錄
- **用途**: 用戶界面相關設定，包含：
  - Discord嵌入訊息顏色設定
  - 按鈕樣式配置
  - 分頁設定
  - 各類UI組件的外觀配置

## 卡池設定詳解

卡池設定的主要位置：

### 1. `gacha_settings.yaml`
- 包含所有卡池的基本定義，如卡池名稱、抽卡消耗、稀有度分佈等

### 2. `database/gacha_waifu/card_data/`
- 存儲卡牌的基礎資料，如卡牌ID、名稱、圖片、稀有度、系列等
- 這些資料最終會被載入到資料庫中的 `gacha_master_cards` 表

### 3. `gacha/services/core/gacha_service.py`
- 實現卡池抽取邏輯，包括稀有度判定、保底機制等

### 4. 資料庫 (`schema.sql`)
- `gacha_master_cards` 表存儲所有卡牌資料
- `gacha_pools` 表(如果存在)可能定義了卡池配置
- 卡牌與卡池的關聯可能通過中間表或標籤實現

## 數據模型

主要數據實體包括：

### 抽卡系統相關
- **gacha_users**: 用戶信息、貨幣、許願槽等
- **gacha_master_cards**: 卡牌元數據，包括名稱、稀有度、價格等
- **gacha_user_collections**: 用戶擁有的卡牌實例
- **gacha_user_wishes**: 用戶設定的目標卡牌

### 股票市場相關
- **virtual_assets**: 可交易的虛擬資產（股票）定義
- **asset_price_history**: 股票歷史價格記錄
- **player_portfolios**: 玩家持有的股票組合
- **market_transactions**: 股票交易記錄
- **market_news**: 市場新聞事件

## 環境設置

1. 複製 `.env.example` 到 `.env` 並填入必要的配置:
   ```bash
   cp .env.example .env
   ```

2. 安裝依賴:
   ```bash
   pip install -r requirements.txt
   ```

3. 設置 PostgreSQL 資料庫並運行 `schema.sql` 創建數據結構

4. 運行機器人:
   ```bash
   python bot.py
   ```

## 主要功能

1. **抽卡系統**
   - 多種卡池抽取
   - 卡牌收集與管理
   - 許願系統
   - 卡牌強化與星級提升

2. **虛擬資產市場**
   - 股票交易（買入/賣出）
   - 動態價格波動
   - 市場新聞影響
   - 股票生命週期管理（活躍、ST警告、退市）

3. **小遊戲系統**
   - 黑傑克
   - 骰子遊戲
   - 踩地雷等

4. **社群功能**
   - 用戶排行榜
   - 互動指令
   - 自訂卡牌展示

## 開發指南

本專案採用分層架構設計：
- **命令層**: 處理用戶輸入
- **服務層**: 封裝核心業務邏輯
- **資料存取層**: 處理數據庫操作
- **模型層**: 定義數據結構
- **視圖層**: 處理用戶界面呈現

開發新功能時，建議遵循此分層結構，確保代碼的可維護性和可擴展性。

## 常用開發工作流程

1. **新增卡牌**:
   - 在 `database/gacha_waifu/card_data/` 中添加新卡牌資料
   - 運行資料導入腳本 (如 `scripts/import_cards.py`)
   - 或直接更新資料庫的 `gacha_master_cards` 表

2. **修改卡池設定**:
   - 編輯 `gacha_settings.yaml` 文件中的卡池配置
   - 重啟機器人或使用重載命令以套用新設定

3. **調整抽卡機率**:
   - 更新 `gacha_settings.yaml` 中的機率設定
   - 可能需要修改 `gacha/services/core/draw_engine_service.py` 中的相關邏輯 