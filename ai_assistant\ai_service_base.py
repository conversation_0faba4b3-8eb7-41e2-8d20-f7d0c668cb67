"""
基礎AI服務模組 - 提供通用AI服務處理邏輯
"""

import base64
import logging
import os
from typing import Dict, Any, Optional, List, Union
import aiohttp
import asyncio
import json
import re # 新增導入 re 模組
import uuid

# 引入重試庫
from tenacity import retry, wait_random_exponential, stop_after_attempt, retry_if_exception_type

# 相對導入
from . import config

# 設置日誌
logger = logging.getLogger("AI_Service_Base")

class AIServiceBase:
    """AI服務基礎類，提供通用API調用和圖像處理邏輯"""
    
    def __init__(self, initial_api_type: str = "primary"): # 允許外部指定初始API類型，主要用於測試
        """
        初始化AI服務基礎類
        
        參數:
            initial_api_type (str): 初始使用的API類型 ("primary" 或 "backup")
        """
        self.current_api_type: Optional[str] = None
        self.api_url: Optional[str] = None
        self.api_key: Optional[str] = None
        self.model_name: Optional[str] = None
        self.chat_endpoint: Optional[str] = None
        self.headers: Optional[Dict[str, str]] = None
        
        try:
            self._setup_api_config(initial_api_type)
        except Exception as e:
            logger.error(f"AIServiceBase 初始化期間設置 API 配置 ({initial_api_type}) 失敗: {str(e)}", exc_info=True)
            # 如果初始設置失敗，嘗試設置備用 (如果初始是主) 或主 (如果初始是備用)
            fallback_type = "backup" if initial_api_type == "primary" else "primary"
            logger.warning(f"嘗試回退到 {fallback_type} API 進行初始化...")
            try:
                self._setup_api_config(fallback_type)
            except Exception as final_e:
                logger.critical(f"AIServiceBase 初始化完全失敗，主備 API 配置均無法加載: {final_e}", exc_info=True)
                raise RuntimeError(f"AI服務初始化失敗，無法加載任何API配置: {final_e}") from final_e

    def _setup_api_config(self, api_type: str) -> None:
        """
        設置當前使用的API配置 (URL, Key, Model, Endpoint, Headers)
        
        參數:
            api_type (str): "primary" 或 "backup"
        
        拋出:
            ValueError: 如果 api_type 無效或配置缺失
        """
        logger.info(f"正在為 AIServiceBase 設置 API 配置: {api_type}")
        if api_type == "primary":
            self.api_url = config.PRIMARY_API_URL
            self.api_key = config.PRIMARY_API_KEY
            self.model_name = config.PRIMARY_DEFAULT_MODEL
        elif api_type == "backup":
            self.api_url = config.BACKUP_API_URL
            self.api_key = config.BACKUP_API_KEY
            self.model_name = config.BACKUP_DEFAULT_MODEL
        else:
            logger.error(f"無效的 api_type: {api_type}")
            raise ValueError(f"無效的 api_type: {api_type}. 必須是 'primary' 或 'backup'.")

        if not self.api_url or not self.api_key or not self.model_name:
            logger.error(f"{api_type} API 的配置不完整。URL: {self.api_url}, Key配置: {bool(self.api_key)}, Model: {self.model_name}")
            raise ValueError(f"{api_type} API 的配置不完整。請檢查 ai_assistant/config.py 或相關環境變數。")

        self.chat_endpoint = f"{self.api_url}/v1/chat/completions"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        self.current_api_type = api_type
        logger.info(f"AIServiceBase 已切換到 {self.current_api_type} API。URL: {self.api_url}, Model: {self.model_name}, Key配置: {bool(self.api_key)}")

    @retry(
        wait=wait_random_exponential(multiplier=1, max=30), # 調整重試等待時間和最大值
        stop=stop_after_attempt(3), # 每個 API 端點嘗試3次
        reraise=True,
        retry=retry_if_exception_type((aiohttp.ClientError, asyncio.TimeoutError, Exception)), # 更廣泛的重試條件
        before_sleep=lambda retry_state: logger.warning(
            f"調用 {retry_state.args[0].current_api_type if retry_state.args else 'Unknown API'} API 遇到錯誤 "
            f"(嘗試 {retry_state.attempt_number}), "
            f"異常: {retry_state.outcome.exception()}. "
            f"將在 {retry_state.next_action.sleep:.2f} 秒後重試..."
        )
    )
    async def _call_api_single_attempt(
        self,
        messages: List[Dict[str, Any]],
        request_id: Optional[str] = None,
        temperature: float = 1.0,
        max_tokens: int = 8192,
        stream: bool = False
    ) -> Dict[str, Any]:
        """
        單次嘗試調用當前配置的 AI API，帶有 Tenacity 重試邏輯。
        此方法供 call_api 內部使用。
        """
        if not self.chat_endpoint or not self.headers or not self.model_name:
            logger.error(f"[{self.current_api_type}] API 配置不完整，無法調用。Endpoint: {self.chat_endpoint}, Headers Set: {bool(self.headers)}, Model: {self.model_name}")
            raise RuntimeError(f"[{self.current_api_type}] API 配置不完整，無法調用。")

        logger.info(f"正在使用 {self.current_api_type} API ({self.api_url}) 發起請求。Request ID: {request_id}")
        
        payload = {
            "model": self.model_name,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "presence_penalty": 0,
            "frequency_penalty": 0,
            "top_p": 0.98,
            "stream": stream
        }
        
        # 發送請求
        # 增加超時設置
        timeout = aiohttp.ClientTimeout(total=60) # 總超時60秒
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(self.chat_endpoint, headers=self.headers, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(
                        f"[{self.current_api_type}] API請求失敗。狀態碼: {response.status}, "
                        f"URL: {self.chat_endpoint}, 錯誤: {error_text}"
                    )
                    # 根據狀態碼判斷是否應該重試，例如 4xx 通常不應重試特定請求
                    if 400 <= response.status < 500:
                        # 對於客戶端錯誤，例如401, 403, 429 (如果API明確指出不要重試), 應直接拋出，不再由Tenacity重試
                        # Tenacity的 retry_if_exception_type 已經比較通用，但這裡可以更細緻
                        # 不過，為了簡化，我們讓Tenacity處理大部分重試，這裡只記錄
                        pass
                    # 對於服務器錯誤 (5xx) 或網絡問題，Tenacity會處理重試
                    raise Exception(f"[{self.current_api_type}] API請求失敗 (狀態碼 {response.status}): {error_text}")
                
                api_response = await response.json()
                logger.info(f"[{self.current_api_type}] API請求成功。Request ID: {request_id}")
                await self._save_api_response_to_json(api_response, messages, request_id, self.current_api_type)
                return api_response
    
    async def call_api(
        self,
        messages: List[Dict[str, Any]],
        request_id: Optional[str] = None,
        temperature: float = 1.0,
        max_tokens: int = 8192,
        stream: bool = False
    ) -> Dict[str, Any]:
        """
        調用AI API，實現主/備API故障轉移。
        
        參數與 _call_api_single_attempt 相同。
        
        返回:
            Dict[str, Any]: API響應
            
        拋出:
            Exception: 如果主備API均調用失敗
        """
        timer_suffix = f"_{request_id}" if request_id else ""
        # api_timer_name = f"api_request{timer_suffix}" # 此計時器可能需要重新考慮或移除，因為現在有多個嘗試點

        try:
            self._setup_api_config("primary")
            logger.info(f"嘗試使用主 API ({self.api_url})。Request ID: {request_id}")
            return await self._call_api_single_attempt(
                messages=messages,
                request_id=request_id,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=stream
            )
        except Exception as primary_error:
            logger.error(
                f"主 API ({config.PRIMARY_API_URL}) 調用失敗 (Request ID: {request_id})。錯誤: {primary_error}",
                exc_info=True # 記錄詳細的異常信息
            )
            logger.warning(f"主 API 失敗，嘗試切換到備用 API。Request ID: {request_id}")

            try:
                self._setup_api_config("backup")
                logger.info(f"嘗試使用備用 API ({self.api_url})。Request ID: {request_id}")
                return await self._call_api_single_attempt(
                    messages=messages,
                    request_id=request_id,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    stream=stream
                )
            except Exception as backup_error:
                logger.critical(
                    f"備用 API ({config.BACKUP_API_URL}) 調用也失敗 (Request ID: {request_id})。錯誤: {backup_error}",
                    exc_info=True # 記錄詳細的異常信息
                )
                # 兩個 API 都失敗了，拋出最後一個遇到的錯誤 (備用API的錯誤)
                # 或者可以考慮拋出一個自定義的聚合錯誤
                raise Exception(f"主備API均調用失敗。主API錯誤: {primary_error}; 備用API錯誤: {backup_error}") from backup_error
    
    async def _save_api_response_to_json(
        self, 
        api_response: Dict[str, Any], 
        messages: List[Dict[str, Any]], 
        request_id: Optional[str] = None,
        api_type_used: Optional[str] = "unknown" # 新增參數
    ) -> None:
        """
        保存API響應到JSON文件
        
        參數:
            api_response (Dict[str, Any]): API響應數據
            messages (List[Dict[str, Any]]): 發送的消息列表
            request_id (Optional[str]): 請求ID
            api_type_used (Optional[str]): 使用的API類型
        """
        import json
        import os
        import time
        from datetime import datetime
        
        # 確定調用來源（QA還是Outfit）
        source = "unknown"
        # 查看system prompt中的關鍵詞來判斷來源
        if messages and len(messages) > 0 and "content" in messages[0]:
            system_prompt = messages[0]["content"]
            if "穿搭" in system_prompt or "outfit" in system_prompt.lower():
                source = "outfit"
            elif "問答" in system_prompt or "qa" in system_prompt.lower():
                source = "qa"
        
        # 創建日誌目錄
        log_dir = os.path.join("logs", "api_responses")
        os.makedirs(log_dir, exist_ok=True)
        
        # 創建日期子目錄
        date_str = datetime.now().strftime("%Y-%m-%d")
        date_dir = os.path.join(log_dir, date_str)
        os.makedirs(date_dir, exist_ok=True)
        
        # 創建源碼子目錄
        source_dir = os.path.join(date_dir, source)
        os.makedirs(source_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = int(time.time())
        req_id = request_id or "no_id"
        filename = f"{timestamp}_{req_id}_{source}.json"
        filepath = os.path.join(source_dir, filename)
        
        # 準備寫入數據
        data_to_save = {
            "timestamp": timestamp,
            "formatted_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "request_id": req_id,
            "source": source,
            "api_type_used": api_type_used, # 新增字段
            "api_config": { # 新增字段，記錄當時使用的API配置
                "url": self.api_url,
                "model": self.model_name,
                "key_configured": bool(self.api_key) # 不直接記錄key本身
            },
            "request": {
                "messages": messages
            },
            "response": api_response
        }
        
        # 寫入JSON文件
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存API響應到 {filepath}")
        except Exception as e:
            logger.error(f"保存API響應到JSON時出錯: {str(e)}")
    
    @staticmethod
    def simple_base64_encode(image_data: bytes) -> str:
        """
        簡單的base64編碼
        
        參數:
            image_data (bytes): 圖像的二進制數據
            
        返回:
            str: base64編碼後的圖像
        """
        return base64.b64encode(image_data).decode('utf-8')
    
    @staticmethod
    def resize_image(image_data: bytes, max_size: int = None, max_dimensions: tuple = None) -> bytes:
        """
        調整圖像大小
        
        參數:
            image_data (bytes): 圖像的二進制數據
            max_size (int, optional): 最大文件大小（字節）
            max_dimensions (tuple, optional): 最大尺寸 (width, height)
            
        返回:
            bytes: 調整大小後的圖像數據
        """
        from io import BytesIO
        from PIL import Image
        
        # 如果未提供限制，使用配置中的默認值
        if max_size is None:
            max_size = config.MAX_IMAGE_SIZE
        if max_dimensions is None:
            max_dimensions = config.MAX_IMAGE_DIMENSIONS
        
        # 讀取圖像
        with BytesIO(image_data) as input_buffer:
            with Image.open(input_buffer) as img:
                # 如果提供了最大尺寸限制，檢查是否需要調整尺寸
                if max_dimensions:
                    width, height = img.size
                    max_width, max_height = max_dimensions
                    
                    # 計算縮放比例
                    if width > max_width or height > max_height:
                        scale = min(max_width / width, max_height / height)
                        new_width = int(width * scale)
                        new_height = int(height * scale)
                        
                        # 調整圖像尺寸
                        img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # 處理RGBA模式圖像（帶透明度）
                if img.mode == 'RGBA':
                    # 創建白色背景
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    # 將原圖合成到白色背景上
                    background.paste(img, mask=img.split()[3])  # 使用alpha通道作為蒙版
                    img = background
                elif img.mode != 'RGB':
                    # 將其他模式轉換為RGB
                    img = img.convert('RGB')
                
                # 保存調整後的圖像，逐步降低質量直到滿足大小限制
                quality = 95
                output_buffer = BytesIO()
                
                while quality >= 30:  # 最低質量限制
                    output_buffer = BytesIO()
                    img.save(output_buffer, format='JPEG', quality=quality, optimize=True)
                    
                    # 檢查大小是否滿足要求
                    if output_buffer.tell() <= max_size:
                        break
                    
                    # 降低質量繼續嘗試
                    quality -= 5
                
                return output_buffer.getvalue()
    
    async def process_with_image(
        self,
        image_data: bytes,
        prompt: str,
        system_prompt: str,
        request_id: Optional[str] = None,
        nonce: Optional[str] = None # 接收 nonce
    ) -> Dict[str, Any]:
        """
        處理包含圖像的請求
        
        參數:
            image_data (bytes): 圖像數據
            prompt (str): 提示詞
            system_prompt (str): 系統提示詞
            request_id (Optional[str]): 請求ID
            nonce (Optional[str]): 接收 nonce
            
        返回:
            Dict[str, Any]: API響應
        """
        image_process_timer_name = f"image_process{f'_{request_id}' if request_id else ''}"
        
        try:
            # 調整圖像大小以符合API要求
            resized_image_bytes = self.resize_image(image_data)
            
            # 生成一個隨機的 nonce (UUID)
            # 注意：nonce應由調用方生成並傳遞，以確保在重試時nonce不變
            # 如果未提供nonce，則生成一個新的
            current_nonce = nonce or str(uuid.uuid4())
            
            # 將 nonce 添加到用戶提示中，作為一種避免緩存的手段
            # 確保 nonce 不會意外匹配到圖片中的文字，添加特殊標記
            # 例如，可以將 nonce 包裹在 XML 標籤或特殊字符序列中
            # <request_nonce_boundary>{current_nonce}</request_nonce_boundary>
            # 或者更簡單的方式，直接附加，AI模型通常能識別這種元數據
            # 這裡我們選擇直接附加到用戶原始提示的末尾，並加以說明
            
            # 檢查 prompt 類型，確保它是字符串
            if not isinstance(prompt, str):
                logger.warning(f"process_with_image 接收到的 prompt 不是字符串類型，而是 {type(prompt)}。將嘗試轉換。")
                try:
                    prompt = str(prompt)
                except Exception as e:
                    logger.error(f"無法將 prompt 轉換為字符串: {e}", exc_info=True)
                    raise TypeError("Prompt must be a string or convertible to a string.") from e

            prompt_with_nonce = f"{prompt}\\n\\n[Request Nonce: {current_nonce}]"
            logger.info(f"圖像處理請求 {request_id} 使用 Nonce: {current_nonce}")
            
            messages = [
                {"role": "system", "content": system_prompt},
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt_with_nonce}, # 使用帶有 nonce 的提示
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64.b64encode(resized_image_bytes).decode('utf-8')}"
                            }
                        }
                    ]
                }
            ]
            
            # 調用API
            api_response = await self.call_api(
                messages=messages,
                request_id=request_id
            )
            
            return api_response
            
        except Exception as e:
            logger.error(f"處理圖像請求時出錯: {str(e)}")
            raise
    
    async def process_text(
        self,
        prompt: str,
        system_prompt: str,
        history: Optional[List[Dict[str, str]]] = None,
        request_id: Optional[str] = None,
        nonce: Optional[str] = None # 接收 nonce
    ) -> Dict[str, Any]:
        """
        處理文本請求
        
        參數:
            prompt (str): 提示文本
            system_prompt (str): 系統提示
            history (Optional[List[Dict[str, str]]]): 對話歷史
            request_id (Optional[str]): 請求標識符
            nonce (Optional[str]): 接收 nonce
            
        返回:
            Dict[str, Any]: 處理結果
        """
        try:
            # 構建消息
            messages = [{"role": "system", "content": system_prompt}]
            
            # 添加歷史消息（如果有）
            if history:
                messages.extend(history)
            
            # 生成一個隨機的 nonce (UUID)
            # 注意：nonce應由調用方生成並傳遞，以確保在重試時nonce不變
            # 如果未提供nonce，則生成一個新的
            current_nonce = nonce or str(uuid.uuid4())

            # 將 nonce 添加到用戶提示中
            # 檢查 prompt 類型
            if not isinstance(prompt, str):
                logger.warning(f"process_text 接收到的 prompt 不是字符串類型，而是 {type(prompt)}。將嘗試轉換。")
                try:
                    prompt = str(prompt)
                except Exception as e:
                    logger.error(f"無法將 prompt 轉換為字符串: {e}", exc_info=True)
                    raise TypeError("Prompt must be a string or convertible to a string.") from e
            
            prompt_with_nonce = f"{prompt}\\n\\n[Request Nonce: {current_nonce}]"
            logger.info(f"文本處理請求 {request_id} 使用 Nonce: {current_nonce}")

            # 更新消息列表中的用戶消息
            user_message = {"role": "user", "content": prompt_with_nonce}
            
            messages.append(user_message)
            
            # 調用API
            api_response = await self.call_api(messages, request_id)
            
            return api_response
        except Exception as e:
            logger.error(f"處理文本時出錯: {str(e)}")
            raise
    
    @staticmethod
    def extract_response_text(api_response: Dict[str, Any]) -> str:
        """
        從API響應中提取 <ai_payload> XML標籤內的文本。
        
        參數:
            api_response (Dict[str, Any]): API響應數據
            
        返回:
            str: 提取到的 <ai_payload> XML標籤內的文本，如果找不到則返回錯誤訊息，
                 或在發生其他錯誤時返回錯誤訊息。
        """
        try:
            # 檢查響應格式
            if not api_response or "choices" not in api_response or not api_response["choices"]:
                logger.error(f"無效的API響應格式: {api_response}")
                return "無法獲取回答，API響應格式無效。"
            
            # 提取回答文本
            message = api_response["choices"][0]["message"]
            
            if "content" in message:
                raw_text = message["content"]
                
                # 嘗試提取 <ai_payload> 標籤
                payload_match = re.search(r"<ai_payload>(.*?)</ai_payload>", raw_text, re.DOTALL)
                if payload_match:
                    extracted_text = payload_match.group(1).strip()
                    logger.info(f"成功提取 <ai_payload> 內容: {extracted_text[:100]}...")
                    return extracted_text
                
                # 如果沒找到標準標籤，嘗試更寬鬆的匹配（可能的標籤錯誤）
                loose_match = re.search(r"<ai[_\-\s]*payload[^>]*>(.*?)</ai[_\-\s]*payload[^>]*>", raw_text, re.DOTALL | re.IGNORECASE)
                if loose_match:
                    extracted_text = loose_match.group(1).strip()
                    logger.warning(f"使用寬鬆匹配提取到近似的 ai_payload 內容: {extracted_text[:100]}...")
                    return extracted_text
                
                # 如果依然找不到任何標籤，可能是AI沒有正確使用標籤，直接返回處理後的原始回應
                logger.warning(f"在AI回應中未找到任何 <ai_payload> 標籤，將返回處理後的原始內容")
                
                # 移除可能的思考過程標記
                cleaned_text = re.sub(r'\*思考[:：].*?\*', '', raw_text, flags=re.DOTALL)
                cleaned_text = re.sub(r'\*[Tt]hinking[:：].*?\*', '', cleaned_text, flags=re.DOTALL)
                cleaned_text = re.sub(r'步驟\s*\d+\s*[:：].*?(?=步驟\s*\d+\s*[:：]|$)', '', cleaned_text, flags=re.DOTALL)
                
                # 移除指令標記和其他非內容元素
                cleaned_text = re.sub(r'^.*?(我將|我會|我應該|I will|I should|I need to).*?(?=\n|$)', '', cleaned_text, flags=re.MULTILINE)
                
                # 去除多餘空行並整理
                cleaned_text = re.sub(r'\n{3,}', '\n\n', cleaned_text)
                cleaned_text = cleaned_text.strip()
                
                if cleaned_text:
                    return cleaned_text
                
                # 如果找不到任何有用內容，返回錯誤訊息和原始內容片段以便調試
                logger.error(f"在AI回應中未找到可用內容。原始回應片段: {raw_text[:200]}...")
                return f"AI回應未遵循格式要求。AI嘗試說: {raw_text[:200]}..."
            else:
                logger.error(f"API響應中沒有content字段: {message}")
                return "無法獲取回答，API響應格式異常。"
        except Exception as e:
            logger.error(f"提取回答文本時出錯: {str(e)}")
            return "提取回答文本時發生錯誤。"