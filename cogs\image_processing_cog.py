"""
圖像處理相關Discord命令
"""
import os
import discord
from discord import app_commands
from discord.ext import commands
from typing import Optional, Literal

import io # 新增導入
from PIL import Image # 新增導入 Pillow Image
from image_processing.gif_modifier.avatar_gif_overlay import overlay_avatar_on_gif, fetch_avatar # 從 avatar_gif_overlay 引入 fetch_avatar
from image_processing.gif_modifier.punch_overlay import overlay_punch_gif
from image_processing.gif_modifier.kick_trash_overlay import generate_kick_trash_frames # 新增導入
from image_processing.gif_modifier.config import list_available_gifs, get_gif_config

# GIF消息模板
GIF_MESSAGES = {
    "frog.gif": "{user_mention} 想吃迪奧了484",
    "uncle_abe.gif": "{user_mention} 吃阿北薯條啦",
    "punching.gif": "{user_mention} 正在使用渾身解數打拳!",
    "kick_trash.gif": "{user_mention} 被踢進了垃圾桶！🗑️" # 新增消息模板
    # 可以在這裡添加更多GIF的消息模板
}

# 默認消息模板
DEFAULT_GIF_MESSAGE = "{user_mention} 的{gif_type}GIF"

# GIF尺寸設定 - 針對不同GIF指定特殊輸出尺寸
GIF_SIZES = {
    "frog.gif": (150, 150),
    "uncle_abe.gif": (200, 122),
    "punching.gif": (200, 200)
}

# 默認GIF尺寸
DEFAULT_GIF_SIZE = (150, 150)

# 特殊處理GIF列表 - 用於指定需要特殊處理函數的GIF
SPECIAL_GIF_HANDLERS = {
    "punching.gif": overlay_punch_gif,
    "kick_trash.gif": None # 佔位符，稍後會賦值為 overlay_kick_trash_gif
    # 其他需要特殊處理的GIF可以在這裡添加
}

async def overlay_kick_trash_gif(gif_path_identifier: str, user: discord.User, output_path: Optional[str] = None, fixed_size: Optional[tuple] = None):
    """
    處理"踢飛頭像進垃圾桶"GIF的疊加邏輯。
    gif_path_identifier 實際上是 "kick_trash.gif" 這個鍵。
    fixed_size 在此函式中可能不會直接使用，因為尺寸由config控制。
    """
    config = get_gif_config(gif_path_identifier) # 使用標識符獲取配置
    
    avatar_img = await fetch_avatar(user)
    if avatar_img is None:
        raise ValueError("無法獲取用戶頭像")
    
    avatar_img = avatar_img.convert("RGBA") # 確保是RGBA格式

    # 生成所有動畫影格
    # 注意：generate_kick_trash_frames 是同步函式，如果它內部有IO密集型操作且未用非同步，
    # 在async函式中直接調用可能會阻塞事件循環。但Pillow操作主要是CPU密集型。
    # 如果 generate_kick_trash_frames 變得非常耗時，可以考慮用 run_in_executor。
    # 目前我們假設它是可接受的。
    processed_frames = generate_kick_trash_frames(avatar_img, config)

    if not processed_frames:
        raise ValueError("未能為 kick_trash 生成任何影格")

    # 創建輸出路徑
    final_output_path = output_path
    if final_output_path is None:
        output_dir = os.path.join("image_processing", "temp")
        os.makedirs(output_dir, exist_ok=True)
        # 使用 user.id 和時間戳避免衝突可能更好
        final_output_path = os.path.join(output_dir, f"kick_trash_{user.id}_{int(discord.utils.utcnow().timestamp())}.gif")

    # 手動處理顏色量化和透明度以提高品質
    converted_paletted_frames = []
    # transparent_color_fill = (0, 0, 0) # 黑色背景填充，用於處理 alpha
    # 選擇一個不太可能與圖像主要顏色衝突的顏色作為透明背景填充
    # 例如一個非常特殊的綠色或藍色，如果圖像中沒有這種顏色
    # 但為了通用性，黑色 (0,0,0) 或白色 (255,255,255) 常用。
    # 如果垃圾桶本身是深色的，用白色背景可能更好區分，反之亦然。
    # 考慮到垃圾桶是綠色的，用一個與綠色差異大的顏色，例如洋紅色 (255,0,255)
    # 或者，堅持使用黑色，並依賴量化和透明索引。
    # 為了與 avatar_gif_overlay.py 保持一致性（假設其使用黑色），我們也用黑色。
    transparent_color_fill = (0, 0, 0)
    transparency_index = 255 # 我們將指定調色板索引255為透明

    for frame_rgba in processed_frames:
        if frame_rgba.mode != 'RGBA':
            frame_rgba = frame_rgba.convert('RGBA')

        # 1. 創建一個RGB背景，原始RGBA影格的透明部分將顯示此背景色
        background = Image.new("RGB", frame_rgba.size, transparent_color_fill)
        # 2. 將原始RGBA影格粘貼到背景上，使用其自身的alpha通道作為遮罩
        background.paste(frame_rgba, mask=frame_rgba.split()[3])

        # 3. 將合成後的RGB圖像量化為P模式（帶調色板）
        #    量化到255色，以便索引255可以用作透明色。
        try:
            # Pillow 9.1.0+ 推薦使用 Image.Quantize 枚舉
            paletted_frame = background.quantize(colors=255, method=Image.Quantize.MEDIANCUT, dither=Image.Dither.FLOYDSTEINBERG)
        except AttributeError: # 兼容舊版 Pillow
            paletted_frame = background.quantize(colors=255, method=2, dither=1) # method=2 is MEDIANCUT, dither=1 is FLOYDSTEINBERG

        # 4. 創建一個遮罩，標記出原始RGBA影格中需要設為透明的區域
        #    (alpha值小於某個閾值，例如128，視為完全透明)
        alpha_channel = frame_rgba.split()[3]
        # Image.eval: 對每個像素應用lambda函數。
        # 如果alpha < 128，返回1 (標記為透明)，否則返回0。
        # 這個mask用於 paletted_frame.paste()，將透明區域的調色板索引改為 transparency_index
        transparent_mask = Image.eval(alpha_channel, lambda pixel_alpha: 255 if pixel_alpha < 128 else 0)
        
        # 5. 將調色板圖像中，對應於原始透明區域的像素，其調色板索引設置為 transparency_index (255)
        #    注意：paste的第二個參數是box或mask。如果mask的模式是'1'或'L'，則只有mask中非零的像素區域會被第一個參數填充。
        #    這裡我們用 transparency_index (一個整數) 填充。
        paletted_frame.paste(transparency_index, mask=transparent_mask)
        
        # 6. 告知Pillow在保存時使用哪個調色板索引作為透明色
        paletted_frame.info['transparency'] = transparency_index
        paletted_frame.info['duration'] = config.get("frame_duration", 50) # 確保每幀都有時長信息
        
        converted_paletted_frames.append(paletted_frame)

    # 保存 GIF
    output_buffer = io.BytesIO()
    if not converted_paletted_frames: # 再次檢查以防萬一
        raise ValueError("converted_paletted_frames 為空，無法保存 GIF")

    converted_paletted_frames[0].save(
        output_buffer,
        format="GIF",
        save_all=True,
        append_images=converted_paletted_frames[1:],
        # duration 已經在 paletted_frame.info 中設定，save_all=True 時會使用
        # duration=config.get("frame_duration", 50),
        loop=0, # 0 表示無限循環
        disposal=2, # 處理透明GIF的推薦方式
        transparency=transparency_index, # 明確指定透明索引
        optimize=False
    )
    
    with open(final_output_path, 'wb') as f:
        f.write(output_buffer.getvalue())
    
    return final_output_path

# 在類定義外部賦值，因為 overlay_kick_trash_gif 是 async def
SPECIAL_GIF_HANDLERS["kick_trash.gif"] = overlay_kick_trash_gif

class ImageCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self._gif_base_path = os.path.join("image_processing", "gifs")
    
    def get_available_gifs(self):
        """取得可用的GIF選項列表"""
        # 從配置系統獲取GIF列表
        gif_names = list_available_gifs()
        
        # 定義 GIF 名稱的顯示文字映射
        gif_display_names = {
            "kick_trash.gif": "踢進垃圾桶",
            "frog.gif": "青蛙",
            "uncle_abe.gif": "阿北",
            "punching.gif": "打拳"
            # 可以在這裡添加更多自定義顯示名稱
        }
        
        # 轉換為Choice對象
        return [
            app_commands.Choice(
                name=gif_display_names.get(name, name.split('.')[0]), 
                value=name
            ) 
            for name in gif_names
        ]
    
    @app_commands.command(name="gif", description="將用戶頭像疊加到GIF上")
    @app_commands.allowed_installs(guilds=True, users=True)
    @app_commands.allowed_contexts(guilds=True, dms=True, private_channels=True)
    @app_commands.describe(
        gif_type="要使用的GIF類型",
        user="要處理的用戶頭像 (默認為自己)"
    )
    async def gif_overlay(
        self, 
        interaction: discord.Interaction, 
        gif_type: str,
        user: Optional[discord.User] = None
    ):
        """
        將用戶頭像疊加到指定的GIF上
        
        參數:
            interaction: Discord交互對象
            gif_type: 要使用的GIF類型
            user: 要處理的用戶，默認為命令使用者
        """
        # 延遲響應，因為處理可能需要時間
        await interaction.response.defer(thinking=True)
        
        # 確認要處理的用戶
        target_user = user if user else interaction.user
        
        try:
            # 首先檢查是否有特殊處理函數
            if gif_type in SPECIAL_GIF_HANDLERS:
                processor_func = SPECIAL_GIF_HANDLERS[gif_type]
                # 對於特殊處理的GIF，gif_path 可能只是一個標識符，或者由處理函數內部決定
                # output_size 也由特殊處理函數內部從config獲取和處理

                if processor_func == overlay_kick_trash_gif:
                    # overlay_kick_trash_gif 的簽名是 (gif_path_identifier, user, output_path=None, fixed_size=None)
                    # 它不需要從這裡傳入 output_path 或 fixed_size，這些由其內部邏輯或config決定
                    result_path = await processor_func(gif_type, target_user)
                elif processor_func == overlay_punch_gif:
                    # overlay_punch_gif 的簽名是 (gif_path, user, output_path=None, fixed_size=(0, 0))
                    # 它需要實際的 gif_path，fixed_size 也是可選的，但通常由 GIF_SIZES 提供
                    gif_path = os.path.join(self._gif_base_path, gif_type)
                    if not os.path.exists(gif_path):
                        available_gifs = [choice.name for choice in self.get_available_gifs()]
                        await interaction.followup.send(
                            f"GIF文件 '{gif_type}' 不存在。可用類型: {', '.join(available_gifs)}",
                            ephemeral=True
                        )
                        return
                    output_size = GIF_SIZES.get(gif_type, DEFAULT_GIF_SIZE)
                    result_path = await processor_func(gif_path, target_user, fixed_size=output_size)
                else:
                    # 其他未明確處理的特殊處理函數，假設它們與 overlay_punch_gif 類似
                    # 或者需要更通用的處理方式
                    # 為了安全，如果添加了新的特殊處理器，應在此處明確其調用方式
                    gif_path = os.path.join(self._gif_base_path, gif_type)
                    if not os.path.exists(gif_path):
                        available_gifs = [choice.name for choice in self.get_available_gifs()]
                        await interaction.followup.send(
                            f"GIF文件 '{gif_type}' 不存在。可用類型: {', '.join(available_gifs)}",
                            ephemeral=True
                        )
                        return
                    output_size = GIF_SIZES.get(gif_type, DEFAULT_GIF_SIZE)
                    result_path = await processor_func(gif_path, target_user, fixed_size=output_size)
            else:
                # 如果不是特殊處理的GIF，則執行標準流程 (默認為 overlay_avatar_on_gif)
                gif_path = os.path.join(self._gif_base_path, gif_type)
                if not os.path.exists(gif_path):
                    available_gifs = [choice.name for choice in self.get_available_gifs()]
                    await interaction.followup.send(
                        f"GIF文件不存在。可用類型: {', '.join(available_gifs)}",
                        ephemeral=True
                    )
                    return
                
                output_size = GIF_SIZES.get(gif_type, DEFAULT_GIF_SIZE)
                result_path = await overlay_avatar_on_gif(gif_path, target_user, fixed_size=output_size)
            
            # 獲取對應的訊息模板
            message_template = GIF_MESSAGES.get(gif_type, DEFAULT_GIF_MESSAGE)
            
            # 替換模板中的變量
            message = message_template.format(
                user_mention=target_user.mention,
                gif_type=gif_type.split('.')[0]
            )
            
            # 發送結果
            await interaction.followup.send(
                message, 
                file=discord.File(result_path)
            )
            
            # 可選：處理完成後刪除臨時文件
            os.remove(result_path)
            
        except Exception as e:
            await interaction.followup.send(
                f"處理GIF時發生錯誤: {str(e)}", 
                ephemeral=True
            )
    
    @gif_overlay.autocomplete('gif_type')
    async def gif_autocomplete(self, interaction: discord.Interaction, current: str):
        """提供GIF類型的自動完成功能"""
        choices = self.get_available_gifs()
        return [
            choice for choice in choices 
            if current.lower() in choice.name.lower()
        ]

async def setup(bot):
    """註冊圖像處理相關命令"""
    await bot.add_cog(ImageCommands(bot)) 