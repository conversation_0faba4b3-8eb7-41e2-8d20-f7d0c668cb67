**I. 系統架構方案**

採用分層架構，融合你現有的 Cog/Service/View/Repository 模式：

1.  **表現層 (Presentation Layer)**
    *   **組件：** Discord Cogs, Embed Builders, Message Formatters.
    *   **職責：** 處理用戶交互 (Discord命令 `/fight_floor`, `/equip_skill` 等)，調用應用服務，格式化數據並展示給用戶 (戰鬥日誌、卡牌信息、PVE進度等)。

2.  **應用服務層 (Application Service Layer)**
    *   **組件：**
        *   `BattleCoordinatorService`: 負責接收Cog請求，協調戰鬥準備 (加載數據)、啟動戰鬥引擎、處理戰鬥結果 (更新進度、發放獎勵)。(**註：** 初期 1v1 實現時，加載數據的邏輯會簡化為處理單個玩家卡牌和單個敵人。)
        *   `PlayerCardManagementService`: 處理卡牌RPG等級提升、技能裝備/卸下、升星等操作。
        *   `PlayerSkillManagementService`: 處理通用技能的獲取和升級。
        *   (現有的) `EconomyService`, `UserProgressService`, `InventoryService` (如果技能卡是物品) 等。
    *   **職責：** 編排業務流程，協調領域服務和基礎設施服務，處理用例。

3.  **領域層 (Domain Layer - 戰鬥與卡牌系統的核心)**
    *   **組件 (詳見後續配置文件和核心運行邏輯)：**
        *   **Domain Models:**
            *   `Battle`: 戰鬥實例，管理戰鬥流程和狀態。(**註：** 雖然內部可能包含隊伍列表屬性以備擴展，初期 1v1 實現時，這些列表將只包含單個玩家和單個敵人。)
            *   `Combatant`: 戰鬥單位 (玩家卡牌或怪物)，封裝其屬性、狀態和戰鬥行為。
            *   `SkillInstance`: 代表一個被裝備或可用的技能實例 (主動、被動、天賦)，包含ID和等級。
            *   `EffectDefinition`: (隱含在JSON中) 描述單個效果的數據結構。
            *   `StatusEffectInstance`: 戰鬥中單位身上的Buff/Debuff實例。
            *   `BattleLogEntry`: 戰鬥日誌條目。
        *   **Domain Services / Handlers (如果邏輯複雜到需要單獨抽離)：**
            *   `DamageHandler` (核心): 包含 `calculate_final_damage` 和 `_apply_modifier` 等方法，根據JSON配置處理複雜的傷害計算和修正。內部使用字典分發 `modifier_type` 到具體的處理邏輯。
            *   `EffectApplier` (協調者): 負責遍歷技能定義中的所有效果，並調用相應的處理器（例如，調用 `DamageHandler` 處理傷害效果，調用 `StatusEffectHandler` 處理狀態施加效果等）。
            *   `StatusEffectHandler`: 負責處理狀態效果的施加、移除、持續時間和效果觸發。
            *   `TargetSelector`: 根據技能定義和戰場情況選擇目標。(**註：** 初期 1v1 實現時，目標選擇邏輯極大簡化，通常直接選擇唯一的對手。)
            *   `AttributeCalculator`: 負責根據基礎值、等級、星級、被動、Buff等計算 `Combatant` 的最終戰鬥屬性。
        *   **Battle Engine (概念上)：** 由 `Battle` 對象和上述服務/處理器協同工作，驅動整個戰鬥流程。

4.  **基礎設施層 (Infrastructure Layer)**
    *   **組件：**
        *   **Repositories:** `PlayerCollectionRepository`, `UserProgressRepository`, `GlobalSkillRepository`, etc. (與數據庫交互)。
        *   **ConfigLoader:** 服務器啟動時加載所有 `*.json` 配置文件到內存中的Python數據結構 (通常是字典)。
        *   (可能需要的) `DiscordApiService` (如果需要更底層的Discord交互)。

---

**V. 建議的專案文件結構**

為了更好地組織RPG系統的代碼和資源，建議在現有專案結構中引入以下佈局：

```
DICKPK/
├── gacha/ (現有的)
│   ├── cogs/
│   ├── models/
│   ├── repositories/
│   ├── services/
│   ├── utils/
│   └── views/
├── rpg_system/
│   ├── battle_system/      # 領域層 - 戰鬥與卡牌系統的核心
│   │   ├── models/         # Battle, Combatant, SkillInstance 等領域模型
│   │   ├── handlers/       # DamageHandler, EffectApplier, StatusEffectHandler, TargetSelector, PassiveTriggerHandler
│   │   └── services/       # (如果某些戰鬥邏輯被抽象為服務) AttributeCalculator
│   │
│   ├── config/             # 存放所有 JSON 配置文件
│   │   ├── pydantic_models/ # 對應每個 JSON 的 Pydantic 驗證模型 (.py 文件)
│   │   ├── data/           # 實際的 *.json 文件 (cards.json, active_skills.json 等)
│   │   └── loader.py       # ConfigLoader 實現
│   │
│   ├── services/           # 應用服務層
│   │   ├── battle_coordinator_service.py
│   │   ├── player_card_management_service.py
│   │   ├── player_skill_management_service.py
│   │   └── ...             # 其他RPG應用服務
│   │
│   ├── repositories/       # 基礎設施層 - 數據庫交互
│   │   ├── player_collection_rpg_repository.py
│   │   ├── global_skill_repository.py
│   │   ├── user_progress_rpg_repository.py
│   │   └── ...
│   │
│   ├── cogs/                 # 表現層 - Discord Cogs
│   │   ├── battle_cog.py
│   │   ├── skill_management_cog.py
│   │   └── ...
│   │
│   ├── views/                # 表現層 - Embed Builders, Message Formatters
│   │   ├── embeds/
│   │   │   ├── battle_embeds.py
│   │   │   └── ...
│   │   └── formatters/
│   │
│   ├── formula_engine/       # 公式求值引擎 (如果獨立實現)
│   │   └── evaluator.py
│   │
│   ├── docs/                 # RPG 系統的設計文檔 (本系列文檔應移至此處)
│   │   ├── RPG_00_Intro_Core_Philosophy.md
│   │   ├── ...
│   │   └── RPG_11_Battle_Presentation.md
│   │
│   └── utils/                # RPG 系統特有的工具函數
│
├── cogs/ (現有的頂層cogs)
├── database/ (現有的)
│   └── postgresql/
│       └── migrations/     # RPG 相關的數據庫遷移腳本
├── docs/ (現有的頂層docs, RPG專用文檔建議移至 rpg_system/docs)
├── scripts/ (現有的)
│   └── generation/         # 用於生成 RPG JSON 配置的腳本
│
├── bot.py
└── ... 其他現有文件和目錄
```

**結構說明:**

*   **`rpg_system/`**: RPG系統的根目錄。
    *   **`battle_system/`**: 包含核心戰鬥邏輯的領域模型和處理器。
        *   `models/`: 戰鬥相關的數據結構和類 (e.g., `Combatant`, `Battle`)。
        *   `handlers/`: 核心邏輯處理單元 (e.g., `DamageHandler`, `EffectApplier`, `PassiveTriggerHandler`)。
        *   `services/`: 如 `AttributeCalculator` 等輔助戰鬥計算的服務。
    *   **`config/`**: 管理所有RPG配置文件。
        *   `pydantic_models/`: 存放用於驗證JSON配置的Pydantic模型。
        *   `data/`: 實際的 `.json` 配置文件。
        *   `loader.py`: `ConfigLoader` 實現。
    *   **`services/`**: 應用層服務，如 `BattleCoordinatorService`。
    *   **`repositories/`**: 數據庫交互接口的實現。
    *   **`cogs/`**: RPG相關的Discord指令。
    *   **`views/`**: Embed構建器和消息格式化工具。
    *   **`formula_engine/`**: 安全的公式求值器。
    *   **`docs/`**: 本系列RPG設計文檔的建議存放位置。
    *   **`utils/`**: RPG系統專用的輔助函數。
*   **`scripts/generation/`**: 在項目頂層的 `scripts` 目錄下，用於存放生成RPG JSON配置的輔助腳本。
*   **`database/postgresql/migrations/`**: 存放RPG系統相關的數據庫遷移腳本。
*   **商店系統集成**: RPG系統中涉及的商店功能（例如購買技能或RPG物品）應優先考慮重用或擴展現有 `gacha` 模塊中已經實現的商店系統組件，避免重複建設。相關的服務調用或UI集成需在此基礎上進行。

這樣的結構有助於保持代碼的組織性和可維護性，特別是當RPG系統功能逐漸豐富時。 