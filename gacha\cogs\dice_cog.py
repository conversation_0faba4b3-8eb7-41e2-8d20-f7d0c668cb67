"""
Gacha系統骰子大小遊戲 COG
處理骰子大小遊戲的Discord命令和交互
使用純異常模式進行錯誤處理
"""
import discord
from discord import app_commands
from discord.ext import commands
from typing import Optional, TYPE_CHECKING, Union
from discord.ui import View, Button
from utils.logger import logger
from gacha.services.games.dice_game_service import DiceGameService
from gacha.services.core.economy_service import EconomyService
from gacha.services.ui.game_display_service import GameDisplayService
from gacha.views.games.dice_game_view import DiceGameView
from gacha.views.embeds.games.dice_game_embed_builder import DiceGameEmbedBuilder
from gacha.views.collection.collection_view.interaction_manager import InteractionManager
from gacha.utils import interaction_utils
from gacha.exceptions import InsufficientFundsError

if TYPE_CHECKING:
    from discord.ext.commands import Bot as BotType


class DiceCog(commands.Cog):
    """處理骰子大小遊戲命令的COG - 使用純異常模式"""

    def __init__(self, bot: 'BotType', dice_game_service: DiceGameService, economy_service: EconomyService, game_display_service: GameDisplayService):
        self.bot = bot
        self._dice_game_service = dice_game_service
        self._economy_service = economy_service
        self._game_display_service = game_display_service

    async def _show_choice_interface(self, interaction: discord.Interaction, bet: int = 10):
        """顯示選擇界面，讓玩家選擇大或小"""
        try:
            # 使用純異常模式獲取餘額
            balance_info = await self._economy_service.get_balance(interaction.user.id)
            current_balance = balance_info.get('balance', 0)
            
            view = DiceGameView(
                user=interaction.user, 
                dice_game_service=self._dice_game_service, 
                economy_service=self._economy_service, 
                game_display_service=self._game_display_service, 
                original_bet=bet
            )
            await view.init_buttons()
            
            builder = DiceGameEmbedBuilder(user=interaction.user, bet=bet)
            embed = builder.build_game_embed()
            embed.add_field(
                name='💳 當前餘額', 
                value=f"<:oil1:1368446294594424872> 餘額:`{current_balance}`", 
                inline=True
            )
            
            message = await interaction_utils.safe_send_message(interaction, embed=embed, view=view)
            if message:
                view.message = message
            else:
                logger.error('DiceCog: Failed to get message object after sending choice interface for interaction %s', interaction.id)
                
        except Exception as e:
            logger.error('DiceCog: Failed to send/edit choice interface message: %s', e, exc_info=True)
            try:
                await interaction_utils.safe_send_message(interaction, '顯示遊戲選擇畫面時發生錯誤。', ephemeral=True)
            except Exception as followup_err:
                logger.warning('DiceCog: Failed to send fallback error message after sending choice interface: %s', followup_err)

    @app_commands.command(name='dice', description='開始一局骰子大小遊戲')
    @app_commands.describe(bet='下注金額（最低10油幣）', choice='選擇大或小（不選擇將顯示選擇界面）')
    @app_commands.choices(choice=[
        app_commands.Choice(name='大 (4-6)', value='big'), 
        app_commands.Choice(name='小 (1-3)', value='small')
    ])
    async def dice(self, interaction: discord.Interaction, bet: app_commands.Range[int, 10] = 10, choice: Optional[str] = None):
        """處理骰子大小遊戲命令 - 使用純異常模式"""
        from gacha.utils.cog_error_handler import handle_game_error
        
        try:
            # 在開始前先檢查餘額是否足夠
            if not interaction.response.is_done():
                await interaction.response.defer(thinking=True, ephemeral=False)
                
            balance_info = await self._economy_service.get_balance(interaction.user.id)
            current_balance = balance_info.get('balance', 0)
            
            # 檢查餘額是否足夠支付下注金額
            if current_balance < bet:
                raise InsufficientFundsError(required_amount=bet, current_balance=current_balance)
                
            if choice is None:
                # 顯示選擇界面
                await self._show_choice_interface(interaction, bet)
                return
                
            game_id = str(interaction.id)
            
            # 直接調用服務，異常會自動向上傳播
            await self._dice_game_service.start_dice_game(interaction, interaction.user.id, bet, choice, game_id)
            
        except Exception as e:
            # 使用統一的錯誤處理
            await handle_game_error(interaction, e, '處理 Dice 命令時')


async def setup(bot: commands.Bot):
    """將COG添加到Bot中"""
    dice_game_service = getattr(bot, 'dice_game_service', None)
    economy_service = getattr(bot, 'economy_service', None)
    game_display_service = getattr(bot, 'game_display_service', None)
    
    if not all([dice_game_service, economy_service, game_display_service]):
        logger.error('Failed to load DiceCog: Required services not found on bot instance.')
        return
        
    await bot.add_cog(DiceCog(bot, dice_game_service, economy_service, game_display_service))
    logger.info('DiceCog 已成功加載並註冊。') 