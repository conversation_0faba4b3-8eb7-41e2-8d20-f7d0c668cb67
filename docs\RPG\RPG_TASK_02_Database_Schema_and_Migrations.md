# RPG 任務 02：數據庫結構與遷移

本文檔詳細列出了 RPG 系統實現的第二個任務集：數據庫表結構的定義、確認以及相應遷移腳本的編寫。

**參考設計文檔：**
*   `RPG_03_Database_Schema.md`
*   現有的 `schema.sql` (用於確認是否有衝突或可複用的部分)

## 1. 確認並定義數據庫表結構

根據 `RPG_03_Database_Schema.md` 中的定義，確認或新增以下表的結構。請特別注意字段名、數據類型、主鍵、外鍵、默認值和約束。

- [ ] **`gacha_master_cards` 表** (此表應已存在，確認 RPG 需要的字段是否齊備)
    - [ ] `card_id` (主鍵, 整數)
    - [ ] `name` (字符串)
    - [ ] `rarity` (整數, 1-7) - **確認此字段已存在且符合RPG系統的1-7稀有度定義**
    - [ ] `pool_type` (字符串) - **確認此字段已存在**
    - [ ] **移除規則檢查**：確認 `rpg_card_config_key` 字段 **不存在**於此表。

- [ ] **`gacha_user_learned_global_skills` 表** (新表)
    - [ ] `user_id` (主鍵部分, 外鍵, 指向用戶表 - 確認用戶表名及主鍵)
    - [ ] `skill_id` (主鍵部分, 字符串)
    - [ ] `skill_type` (主鍵部分, 枚舉/字符串: "ACTIVE", "PASSIVE")
    - [ ] `skill_level` (整數, 默認 1, NOT NULL)
    - [ ] `skill_xp` (整數, 默認 0, NOT NULL)
    - [ ] `unlocked_at` (時間戳, 默認 `CURRENT_TIMESTAMP`, NOT NULL)

- [ ] **`gacha_user_collections` 表** (此表應已存在，確認並新增RPG相關字段)
    - [ ] `id` (主鍵)
    - [ ] `user_id` (外鍵)
    - [ ] `card_id` (外鍵, 指向 `gacha_master_cards.card_id`)
    - [ ] `quantity` (整數)
    - [ ] `is_favorite` (布爾值, 默認 `false`, NOT NULL) - **確認此字段已存在**
    - [ ] **RPG 新增/確認字段:**
        - [ ] `rpg_level` (整數, 默認 1, NOT NULL)
        - [ ] `rpg_xp` (整數, 默認 0, NOT NULL)
        - [ ] `star_level` (整數, 默認 0, 範圍 0-35, NOT NULL) - **確認此字段已存在且範圍符合設計**
        - [ ] `equipped_active_skill_ids` (JSON數組 of 字符串, e.g., `["skill_X_id", null]`, 可為NULL或空數組 `[]`)
            *   考慮默認值：可能是 `NULL` 或根據卡牌配置預設的 `default_active_skill_slot_X_id` 初始化 (初始化邏輯可能在應用層)。
        - [ ] `equipped_common_passives` (JSONB, e.g., `{"slot_0": "passive_A_id", "slot_1": null}`. 可為NULL或空對象 `{}`)
            *   考慮默認值：可能是 `NULL` 或根據卡牌配置的 `default_passives_on_acquire` 初始化 (初始化邏輯可能在應用層)。
    - [ ] **移除規則檢查**：確認 `next_rpg_level_xp_required` 字段 **不存在**於此表。

- [ ] **`rpg_user_progress` 表** (新表)
    - [ ] `user_id` (主鍵, 外鍵, 指向用戶表)
    - [ ] `current_floor_unlocked` (整數, 默認 1, NOT NULL)
    - [ ] `current_floor_wins` (整數, 默認 0, NOT NULL)
    - [ ] `max_floor_cleared` (整數, 默認 0, NOT NULL)
    - [ ] `current_team_formation` (JSONB, e.g., `["collection_id_1", "collection_id_2"]` - 存儲 `gacha_user_collections.id` 列表，可為NULL或空數組 `[]`)

## 2. 編寫數據庫遷移腳本

在 `database/postgresql/migrations/` 目錄下創建新的遷移腳本 (例如，使用 Alembic 或類似工具，或者手動編寫 SQL 腳本)。

- [ ] **腳本命名規範：** 遵循項目現有的遷移腳本命名規範 (例如 `YYYYMMDD_HHMMSS_add_rpg_tables.sql` 或 Alembic 的版本號)。
- [ ] **遷移腳本內容 (`UP` / `FORWARD`):**
    - [ ] 創建 `gacha_user_learned_global_skills` 表。
        - [ ] 定義所有字段、類型、約束 (主鍵, 外鍵, NOT NULL, DEFAULT)。
        - [ ] 考慮為 (`user_id`, `skill_id`, `skill_type`) 創建複合主鍵。
        - [ ] 考慮為 `user_id` 和 `skill_id` 分別創建索引 (如果適用)。
    - [ ] 修改 `gacha_user_collections` 表。
        - [ ] 新增 `rpg_level` 字段。
        - [ ] 新增 `rpg_xp` 字段。
        - [ ] 確認/新增 `star_level` 字段並設置其約束/默認值。
        - [ ] 新增 `equipped_active_skill_ids` 字段 (JSON/JSONB)。
        - [ ] 新增 `equipped_common_passives` 字段 (JSON/JSONB)。
        - [ ] **移除** `next_rpg_level_xp_required` 字段 (如果存在)。
        - [ ] **確認** `is_favorite` 字段存在。
    - [ ] 創建 `rpg_user_progress` 表。
        - [ ] 定義所有字段、類型、約束 (主鍵, 外鍵, NOT NULL, DEFAULT)。
        - [ ] `user_id` 作為主鍵和外鍵。
    - [ ] 檢查 `gacha_master_cards` 表。
        - [ ] **移除** `rpg_card_config_key` 字段 (如果存在)。
        - [ ] **確認** `rarity` 和 `pool_type` 字段存在。
- [ ] **回滾腳本內容 (`DOWN` / `BACKWARD`) (如果適用):**
    - [ ] 刪除 `rpg_user_progress` 表。
    - [ ] 從 `gacha_user_collections` 表中移除RPG相關字段 (如果它們是本次遷移加入的)。
    - [ ] 刪除 `gacha_user_learned_global_skills` 表。
    - [ ] (如果適用) 恢復 `gacha_master_cards` 和 `gacha_user_collections` 中被移除的字段。

## 3. 執行與測試遷移

- [ ] 在開發環境中執行遷移腳本。
- [ ] 驗證表結構是否按預期創建/修改。
    - [ ] 使用SQL客戶端檢查表定義。
    - [ ] 檢查字段類型、默認值、約束是否正確。
- [ ] (如果編寫了回滾腳本) 測試回滾腳本是否能正確還原數據庫狀態。

## 4. 更新 `schema.sql` (如果項目使用)

- [ ] 如果項目中維護一個總的 `schema.sql` 文件來表示最新的數據庫結構，請在遷移成功後更新此文件以包含新的RPG相關表和字段。

---
完成所有上述任務後，請勾選並更新 `RPG_IMPLEMENTATION_PLAN.md` 中的相應條目。 