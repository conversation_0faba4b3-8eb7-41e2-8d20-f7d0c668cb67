# RPG系統事件系統詳解

本文檔詳細描述Gacha RPG系統中的事件系統，特別是 `PassiveTriggerHandler` 使用的 `event_type` 及其關聯的 `event_data`。

## 1. 核心概念

事件系統是被動技能觸發的核心機制。當戰鬥中發生特定動作或狀態變化時，系統會生成一個事件，`PassiveTriggerHandler` 會捕獲這些事件，並檢查是否有被動技能的觸發條件與該事件匹配。

*   **`event_type` (事件類型):** 一個字符串枚舉值，唯一標識了發生的事件種類。
*   **`event_data` (事件數據):** 一個字典，包含了與該事件相關的上下文信息。不同 `event_type` 附帶的 `event_data` 結構可能不同。
*   **事件源 (Event Source/Emitter):** 觸發事件的邏輯點，例如戰鬥流程中的傷害計算完畢後、回合開始時等。
*   **事件監聽器 (Event Listener):** `PassiveTriggerHandler` 是主要的事件監聽器，它根據事件類型和數據來觸發被動技能。

## 2. 標準事件類型與數據結構

以下是建議的標準事件類型及其關聯的 `event_data` 字段。在設計時，應確保 `event_data` 包含足夠的信息供被動技能的條件判斷和效果目標選擇。

**命名約定：**
*   `event_type` 使用大寫字母和下劃線。
*   `event_data` 中的鍵名使用小寫字母和下劃線。
*   `combatant_xxx` 通常指 `Combatant` 對象的實例ID或引用。

--- 

**A. 戰鬥流程事件 (Battle Flow Events)**

1.  **`ON_BATTLE_START`**
    *   **觸發時機：** 戰鬥實例創建並初始化完成，所有戰鬥單位就位，準備開始第一回合之前。
    *   **`event_data`:**
        *   `player_team`: (list of `combatant_id`) 玩家隊伍所有單位ID。
        *   `monster_team`: (list of `combatant_id`) 怪物隊伍所有單位ID。
        *   `all_combatants`: (list of `combatant_id`) 戰場上所有單位ID。
    *   **用途：** 觸發"戰鬥開始時"生效的被動，如光環、入場屬性提升等。

2.  **`ON_BATTLE_END`**
    *   **觸發時機：** 戰鬥結束條件達成（一方獲勝或平局）之後，但在獎勵結算之前。
    *   **`event_data`:**
        *   `winning_team_side`: ("PLAYER" / "MONSTER" / "NONE")
        *   `losing_team_side`: ("PLAYER" / "MONSTER" / "NONE")
        *   `surviving_player_combatants`: (list of `combatant_id`)
        *   `surviving_monster_combatants`: (list of `combatant_id`)
    *   **用途：** 觸發"戰鬥結束時"的被動，如結算獎勵加成、特殊狀態移除等。

--- 

**B. 回合流程事件 (Turn Flow Events)**

1.  **`ON_TURN_START`**
    *   **觸發時機：** 新回合開始，確定了當前行動單位之後，但在該單位執行任何動作（包括狀態效果tick）之前。
    *   **`event_data`:**
        *   `current_turn_number`: (int) 當前回合數。
        *   `acting_combatant_id`: (string) 當前回合行動的單位ID。
        *   `all_combatants_on_field`: (list of `combatant_id`) 當時場上所有存活單位ID。
    *   **用途：** 觸發"回合開始時"生效的被動，如每回合回藍、施加特定Buff/Debuff、改變自身屬性等。

2.  **`ON_TURN_END`**
    *   **觸發時機：** 當前回合行動單位所有動作執行完畢（包括其回合結束階段的狀態效果tick），即將輪到下一個單位行動或下一回合之前。
    *   **`event_data`:**
        *   `current_turn_number`: (int) 當前回合數。
        *   `completed_action_combatant_id`: (string) 剛結束行動的單位ID。
        *   `all_combatants_on_field`: (list of `combatant_id`)。
    *   **用途：** 觸發"回合結束時"生效的被動。

--- 

**C. 行動與技能事件 (Action & Skill Events)**

1.  **`ON_PRIMARY_ATTACK_EXECUTED`**
    *   **觸發時機：** 施法者執行了"普攻"技能之後，效果已結算。
    *   **`event_data`:**
        *   `caster_id`: (string) 施法者ID。
        *   `skill_id`: (string) 使用的普攻技能ID。
        *   `targets`: (list of `target_resolution`) 每個目標的處理結果。
            *   `target_resolution` 結構: `{"target_id": string, "was_hit": bool, "was_crit": bool, "damage_dealt": float, "is_fatal": bool}`
    *   **用途：** 觸發"普攻後"的被動，如追擊、吸血、附加額外效果等。

2.  **`ON_ACTIVE_SKILL_EXECUTED`**
    *   **觸發時機：** 施法者執行了"主動技能"（非普攻）之後，效果已結算。
    *   **`event_data`:**
        *   `caster_id`: (string) 施法者ID。
        *   `skill_id`: (string) 使用的主動技能ID。
        *   `mp_cost`: (int) 消耗的MP。
        *   `targets`: (list of `target_resolution`) 同上。
        *   `skill_tags`: (list of string, 可選) 該技能擁有的標籤。
    *   **用途：** 觸發"施放技能後"的被動，如連擊、根據技能標籤觸發額外效果等。

3.  **`ON_SKILL_TARGETED` (更細粒度, 可選)**
    *   **觸發時機：** 技能選擇了目標之後，但在效果結算之前。
    *   **`event_data`:**
        *   `caster_id`: (string)
        *   `skill_id`: (string)
        *   `intended_targets`: (list of `combatant_id`)
    *   **用途：** 觸發"成為技能目標時"的被動，如援護、改變目標、施加反制效果等。

--- 

**D. 傷害與治療事件 (Damage & Healing Events)**

1.  **`ON_DAMAGE_DEALT` (Source Perspective)**
    *   **觸發時機：** 某單位對一個或多個目標成功造成傷害後 (傷害計算完畢，HP已扣減)。
    *   **`event_data`:**
        *   `source_attacker_id`: (string) 造成傷害的單位ID。
        *   `damage_source_skill_id`: (string, 可選) 造成此傷害的技能ID（如果是技能傷害）。
        *   `damage_source_status_effect_id`: (string, 可選) 造成此傷害的狀態效果ID（如中毒）。
        *   `target_id`: (string) 承受該次傷害的單個目標ID。
        *   `damage_amount`: (float) 對該目標造成的最終傷害量。
        *   `damage_type`: ("PHYSICAL" / "MAGICAL" / "TRUE_DAMAGE")
        *   `is_crit`: (bool)
        *   `is_fatal_to_target`: (bool) 此次傷害是否導致目標死亡。
        *   `is_primary_attack_damage`: (bool)
        *   `is_skill_damage`: (bool)
    *   **用途：** 觸發"造成傷害時/後"的被動，如吸血、濺射、根據傷害量觸發效果、擊殺回覆等。
    *   **注意：** 如果一個技能對多個目標造成傷害，會為每個目標觸發一次此事件。

2.  **`ON_DAMAGE_TAKEN` (Target Perspective)**
    *   **觸發時機：** 某單位承受了一次傷害後 (傷害計算完畢，HP已扣減)。
    *   **`event_data`:**
        *   `target_id`: (string) 承受傷害的單位ID。
        *   `source_attacker_id`: (string, 可選) 造成傷害的單位ID。
        *   `damage_source_skill_id`: (string, 可選) 。
        *   `damage_source_status_effect_id`: (string, 可選)。
        *   `damage_amount`: (float) 承受的最終傷害量。
        *   `damage_type`: ("PHYSICAL" / "MAGICAL" / "TRUE_DAMAGE")
        *   `is_crit`: (bool)
        *   `was_fatal`: (bool) 此次傷害是否致命。
    *   **用途：** 觸發"受到傷害時/後"的被動，如反傷、減傷、根據受傷類型觸發效果、瀕死反擊等。

3.  **`ON_HEAL_DEALT` (Source Perspective)**
    *   **觸發時機：** 某單位對一個或多個目標成功施加治療後。
    *   **`event_data`:**
        *   `source_healer_id`: (string)
        *   `heal_source_skill_id`: (string, 可選)
        *   `target_id`: (string)
        *   `heal_amount`: (float)
        *   `is_crit_heal`: (bool, 可選)
    *   **用途：** 觸發"造成治療時/後"的被動。

4.  **`ON_HEAL_RECEIVED` (Target Perspective)**
    *   **觸發時機：** 某單位接受了一次治療後。
    *   **`event_data`:**
        *   `target_id`: (string)
        *   `source_healer_id`: (string, 可選)
        *   `heal_source_skill_id`: (string, 可選)
        *   `heal_amount`: (float)
        *   `is_crit_heal`: (bool, 可選)
    *   **用途：** 觸發"受到治療時/後"的被動，如治療增效、根據治療量觸發效果。

5.  **`ON_DODGE` / `ON_MISS` (可選, 更細粒度)**
    *   **觸發時機：** 攻擊被閃避或未命中時。
    *   **`event_data`:** `attacker_id`, `target_id`, `skill_id`
    *   **用途：** 觸發閃避/未命中相關的被動。

--- 

**E. 狀態效果事件 (Status Effect Events)**

1.  **`ON_STATUS_EFFECT_APPLIED`**
    *   **觸發時機：** 一個狀態效果 (Buff/Debuff) 成功施加到目標身上之後。
    *   **`event_data`:**
        *   `target_id`: (string) 被施加狀態的單位ID。
        *   `source_caster_id`: (string, 可選) 施加該狀態的單位ID。
        *   `status_effect_id`: (string) 被施加的狀態ID。
        *   `is_buff`: (bool)
        *   `duration_turns`: (int)
        *   `stack_count`: (int)
    *   **用途：** 觸發"當自身/友方/敵方被施加某狀態時"的被動。

2.  **`ON_STATUS_EFFECT_REMOVED` / `ON_STATUS_EFFECT_EXPIRED` / `ON_STATUS_EFFECT_DISPELLED`**
    *   **觸發時機：** 狀態效果因持續時間結束、被驅散或其他原因移除時。
    *   **`event_data`:** `target_id`, `status_effect_id`, `is_buff`, `removal_reason` ("EXPIRED", "DISPELLED", "CLEANSED")
    *   **用途：** 觸發狀態移除相關的被動。

3.  **`ON_STATUS_EFFECT_RESISTED` (可選)**
    *   **觸發時機：** 嘗試施加狀態效果但被目標抵抗時。
    *   **`event_data`:** `target_id`, `source_caster_id`, `status_effect_id`, `is_buff`
    *   **用途：** 觸發抵抗相關的被動。

4.  **`ON_STATUS_EFFECT_TICK`**
    *   **觸發時機：** 持續型狀態效果（如中毒、再生）觸發其周期性效果時。
    *   **`event_data`:**
        *   `target_id`: (string) 擁有該狀態的單位ID。
        *   `status_effect_id`: (string)
        *   `tick_damage`: (float, 可選) 若是傷害型tick。
        *   `tick_heal`: (float, 可選) 若是治療型tick。
        *   `effects_applied_this_tick`: (list of effect_definition, 可選)
    *   **用途：** 觸發"當某狀態效果造成傷害/治療時"的被動。

--- 

**F. 生存狀態事件 (Survival Status Events)**

1.  **`ON_ALLY_DEATH`**
    *   **觸發時機：** 某戰鬥單位的友方單位死亡時（對每個存活友方觸發）。
    *   **`event_data`:**
        *   `observer_id`: (string) 觀察者（即擁有該被動的友方存活單位）ID。
        *   `deceased_ally_id`: (string) 死亡的友方單位ID。
        *   `killer_id`: (string, 可選) 造成致命一擊的單位ID。
    *   **用途：** 觸發"友方死亡時"的被動，如復仇、屬性提升、治療全隊等。

2.  **`ON_ENEMY_DEATH`**
    *   **觸發時機：** 某戰鬥單位的敵方單位死亡時（對每個存活敵方觸發，即玩家角色）。
    *   **`event_data`:**
        *   `observer_id`: (string) 觀察者（即擁有該被動的單位）ID。
        *   `deceased_enemy_id`: (string) 死亡的敵方單位ID。
        *   `killer_id`: (string, 可選) 造成致命一擊的單位ID。
    *   **用途：** 觸發"敵方死亡時"的被動，如回覆資源、獲得Buff、對其他敵人造成傷害等。

3.  **`ON_COMBATANT_DEATH` (General)**
    *   **觸發時機：** 任何戰鬥單位死亡時。
    *   **`event_data`:**
        *   `deceased_combatant_id`: (string)
        *   `killer_id`: (string, 可選)
        *   `deceased_is_ally_to_observer`: (bool, 需要上下文判斷)
    *   **用途：** 通用死亡事件。

4.  **`ON_HP_THRESHOLD_REACHED`**
    *   **觸發時機：** 戰鬥單位的HP首次降到或升到某個特定百分比閾值時。
    *   **`event_data`:**
        *   `combatant_id`: (string) HP達到閾值的單位ID。
        *   `hp_percent`: (float) 當前HP百分比。
        *   `threshold_crossed`: (float) 觸發的閾值。
        *   `direction`: ("BELOW" / "ABOVE") 是降到閾值以下還是升到閾值以上。
    *   **用途：** 觸發"血量低於X%時"或"血量高於Y%時"的被動，如瀕死爆發、保持健康狀態獲得增益等。

## 3. 事件觸發流程與擴展性

*   **觸發點：** 戰鬥引擎的核心邏輯中，在相應的動作執行完畢或狀態改變後，應調用一個事件分發函數，傳入 `event_type` 和構造好的 `event_data`。
*   **`PassiveTriggerHandler` 的響應：** `PassiveTriggerHandler.check_and_apply_passives` 方法接收這些事件，並遍歷相關戰鬥單位身上的被動技能，檢查其 `trigger_condition.type` 是否與 `event_type` 匹配，並利用 `event_data` 進行後續的條件判斷和效果應用。
*   **擴展性：**
    *   **新增事件類型：** 只需在此文檔中定義新的 `event_type` 及其 `event_data` 結構，並在戰鬥引擎的適當時機觸發它。`PassiveTriggerHandler` 的核心匹配邏輯無需大改，JSON配置中即可使用新的 `event_type`。
    *   **修改事件數據：** 如果需要為現有事件類型增加更多上下文信息，只需擴展其 `event_data` 結構。

## 4. 設計考量

*   **事件粒度：** 需要在"提供足夠信息支持複雜被動"和"避免事件過於頻繁導致性能問題"之間找到平衡。例如，`ON_DAMAGE_DEALT` 比 `ON_ATTACK_HIT` 提供了更具體的傷害結果信息。
*   **數據準確性：** 確保 `event_data` 中的信息在事件觸發時是準確和完整的。
*   **順序依賴：** 如果多個被動可能由同一個事件觸發，需要明確它們的執行順序（參考 `II. 核心邏輯與戰鬥系統的魯棒性 - 1. 效果與被動的觸發順序與優先級` 的討論）。

通過標準化事件類型和數據，可以極大地增強被動技能設計的靈活性和系統的可維護性。 