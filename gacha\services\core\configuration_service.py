from __future__ import annotations
import typing
from typing import Dict, List, Any, Optional, Union, TypeVar, Generic, Callable

if typing.TYPE_CHECKING:
    from gacha.app_config import (
        AppSettings,
        GachaCoreSettings,
        UISettings,
        PoolConfigurationDetail,
        PoolRarityConfigItem,
        LeaderboardConfigDetail,
        PoolConfigItem,
        UIButtonEmojis,
        GachaStockIntegrationConfig,
        StockMarketConfig,
        PriceUpdateServiceConfig,
        SupplyDemandConfig,
    )

T = TypeVar("T")


class ConfigurationService:
    """配置服務 - 提供對應用程式配置的訪問，並支援快取機制"""

    def __init__(self, settings: AppSettings):
        self._settings = settings
        # 初始化快取字典，儲存常用配置
        self._cache = {}
        self._init_cache()

    def _init_cache(self):
        """初始化常用配置的快取"""
        # 快取核心配置
        self._cache.update(
            {
                "pool_costs": self._settings.gacha_core_settings.pool_costs,
                "rarity_sort_values": self._settings.gacha_core_settings.rarity_sort_values,
                "pool_type_names": self._settings.gacha_core_settings.pool_type_names,
                "pool_type_prefixes": self._settings.gacha_core_settings.pool_type_prefixes,
                "max_star_level": self._settings.gacha_core_settings.max_star_level,
                "stars_per_tier": self._settings.gacha_core_settings.stars_per_tier,
                "mixed_pool_draw_config": self._settings.gacha_core_settings.mixed_pool_draw_config,
                "all_pool_rarity_configs": self._settings.gacha_core_settings.all_pool_rarity_configs,
                "leaderboard_items_per_page": self._settings.gacha_core_settings.leaderboard_items_per_page,
                # 快取許願系統相關配置
                "wish_max_slots": self._settings.gacha_core_settings.wish_max_slots,
                "wish_max_power_level": self._settings.gacha_core_settings.wish_max_power_level,
                "default_wish_slots": self._settings.gacha_core_settings.default_wish_slots,
                "default_wish_power_level": self._settings.gacha_core_settings.default_wish_power_level,
                "default_wish_multiplier": self._settings.gacha_core_settings.default_wish_multiplier,
                "wish_slot_costs": self._settings.gacha_core_settings.wish_slot_costs,
                "wish_power_costs": self._settings.gacha_core_settings.wish_power_costs,
                "wish_power_multipliers": self._settings.gacha_core_settings.wish_power_multipliers,
            }
        )

        # 快取UI配置
        self._cache.update(
            {
                "default_rarity_emojis": self._settings.ui_settings.default_rarity_emojis,
                "rarity_emojis": self._settings.ui_settings.rarity_emojis,
                "star_emojis": self._settings.ui_settings.star_emojis,
                "ui_button_emojis": self._settings.ui_settings.ui_button_emojis,
                "oil_emoji": self._settings.ui_settings.oil_emoji,
                "pool_type_emojis": self._settings.ui_settings.pool_type_emojis,
                "rarity_colors_int": self._settings.ui_settings.rarity_colors_int,
                "rarity_images": self._settings.ui_settings.rarity_images,
                "default_encyclopedia_rarity_emojis": self._settings.ui_settings.default_encyclopedia_rarity_emojis,
                "encyclopedia_rarity_emojis": self._settings.ui_settings.encyclopedia_rarity_emojis,
                "rarity_display_codes": self._settings.ui_settings.rarity_display_codes,
                "user_friendly_display_names": self._settings.ui_settings.user_friendly_display_names,
                "completion_indicator_emojis": self._settings.ui_settings.completion_indicator_emojis,
                "character_archetype_readable_names": self._settings.ui_settings.character_archetype_readable_names,
                "news_type_readable_names": self._settings.ui_settings.news_type_readable_names,
                "news_type_colors_int": self._settings.ui_settings.news_type_colors_int,
            })

    def get_config(self, path: str,
                   default_value: Optional[T] = None) -> Union[Any, T]:
        """
        通用配置訪問方法，支援點分隔路徑

        Args:
            path: 點分隔的配置路徑，例如 "gacha_core_settings.pool_costs"
            default_value: 如果配置不存在時的預設值

        Returns:
            配置值或預設值
        """
        # 先檢查快取
        if path in self._cache:
            return self._cache[path]

        # 路徑解析
        parts = path.split(".")
        current = self._settings

        try:
            for part in parts:
                if hasattr(current, part):
                    current = getattr(current, part)
                elif isinstance(current, dict) and part in current:
                    current = current[part]
                else:
                    return default_value

            # 對於常用配置，添加到快取
            if len(parts) <= 3:  # 只快取路徑較短的配置
                self._cache[path] = current

            return current
        except (AttributeError, KeyError, TypeError):
            return default_value

    @property
    def gacha_core(self) -> GachaCoreSettings:
        """提供對 gacha_core_settings 的直接訪問"""
        return self._settings.gacha_core_settings

    @property
    def ui(self) -> UISettings:
        """提供對 ui_settings 的直接訪問"""
        return self._settings.ui_settings

    # === 保留少量最常用的配置訪問器，使用快取 ===

    def get_pool_costs(self) -> Dict[str, int]:
        """獲取卡池抽卡成本"""
        return self._cache["pool_costs"]

    def get_pool_type_names(self) -> Dict[str, str]:
        """獲取卡池類型名稱映射"""
        return self._cache["pool_type_names"]

    def get_max_star_level(self) -> int:
        """獲取最大星級"""
        return self._cache["max_star_level"]

    def get_all_pool_rarity_configs(
            self) -> Dict[str, List[PoolRarityConfigItem]]:
        """獲取所有卡池的稀有度配置"""
        return self._cache["all_pool_rarity_configs"]

    def get_mixed_pool_draw_config(self) -> List[PoolConfigItem]:
        """獲取混合卡池抽卡配置"""
        return self._cache["mixed_pool_draw_config"]

    def get_oil_emoji(self) -> str:
        """獲取油幣表情符號"""
        return self._cache["oil_emoji"]

    def get_ui_button_emojis(self) -> UIButtonEmojis:
        """獲取UI按鈕表情符號"""
        return self._cache["ui_button_emojis"]

    # === 許願系統相關的配置訪問方法 ===

    def get_wish_max_slots(self, defaultValue: int = 10) -> int:
        """獲取用戶可擁有的最大許願槽位數量"""
        return self._cache.get("wish_max_slots", defaultValue)

    def get_wish_max_power_level(self, defaultValue: int = 10) -> int:
        """獲取用戶可達到的最大許願力度等級"""
        return self._cache.get("wish_max_power_level", defaultValue)

    def get_default_wish_slots(self, defaultValue: int = 1) -> int:
        """獲取新用戶的默認許願槽位數量"""
        return self._cache.get("default_wish_slots", defaultValue)

    def get_default_wish_power_level(self, defaultValue: int = 1) -> int:
        """獲取新用戶的默認許願力度等級"""
        return self._cache.get("default_wish_power_level", defaultValue)

    def get_default_wish_multiplier(self, defaultValue: float = 3.0) -> float:
        """獲取默認的許願概率倍數（通常對應力度等級1）"""
        return self._cache.get("default_wish_multiplier", defaultValue)

    def get_wish_slot_costs(self) -> Dict[int, int]:
        """獲取升級許願槽位的成本表（當前槽位數->升級成本）"""
        return self._cache.get("wish_slot_costs", {})

    def get_wish_power_costs(self) -> Dict[int, int]:
        """獲取升級許願力度的成本表（當前等級->升級成本）"""
        return self._cache.get("wish_power_costs", {})

    def get_wish_power_multipliers(self) -> Dict[int, float]:
        """獲取許願力度等級與概率倍數的對應表"""
        return self._cache.get("wish_power_multipliers", {})


    # Redis相關配置
    def get_market_stats_updates_stream_name(self) -> str:
        """獲取市場統計更新流名稱"""
        return self._settings.MARKET_STATS_UPDATES_STREAM_NAME

    def get_redis_event_batch_size(self) -> int:
        """獲取Redis事件批次大小"""
        return self._settings.REDIS_EVENT_BATCH_SIZE

    def get_redis_stream_maxlen(self) -> int:
        """獲取Redis流最大長度"""
        return self._settings.REDIS_STREAM_MAXLEN

    # 資源相關配置
    def get_gacha_stock_integration_config(
            self) -> GachaStockIntegrationConfig:
        """獲取Gacha股票整合配置"""
        return self._settings.gacha_stock_integration

    def get_stock_market_config(self) -> StockMarketConfig:
        """獲取股票市場配置"""
        return self._settings.gacha_stock_integration.stock_market

    def get_price_update_service_config(self) -> PriceUpdateServiceConfig:
        """獲取價格更新服務配置"""
        return self._settings.price_update_service

    def get_supply_demand_config(self) -> SupplyDemandConfig:
        """獲取供需配置"""
        return self._settings.gacha_stock_integration.supply_demand

    def get_rarity_colors_int(self) -> Dict[str, Dict[int, int]]:
        """獲取稀有度顏色配置（顏色值為整數）"""
        return self._cache.get("rarity_colors_int", self.ui.rarity_colors_int)

    def get_rarity_images_url(self) -> Dict[str, Dict[int, str]]:
        """獲取稀有度圖片URL配置"""
        return self._cache.get("rarity_images", self.ui.rarity_images)

    def get_pool_type_emojis(self) -> Dict[str, str]:
        """獲取卡池類型表情符號配置"""
        return self._cache.get("pool_type_emojis", self.ui.pool_type_emojis)

    def get_default_rarity_emojis(self) -> Dict[int, str]:
        """獲取默認稀有度表情符號配置"""
        return self._cache.get(
            "default_rarity_emojis",
            self.ui.default_rarity_emojis)

    def get_star_emojis(self) -> Dict[int, str]:
        """獲取星級表情符號配置"""
        return self._cache.get("star_emojis", self.ui.star_emojis)

    def get_pool_specific_rarity_emojis(self) -> Dict[str, Dict[int, str]]:
        """獲取特定卡池的稀有度表情符號配置"""
        return self._cache.get("rarity_emojis", self.ui.rarity_emojis)

    def get_default_encyclopedia_rarity_emojis(self) -> Dict[int, str]:
        """獲取默認圖鑑稀有度表情符號配置"""
        return self._cache.get(
            "default_encyclopedia_rarity_emojis",
            self.ui.default_encyclopedia_rarity_emojis,
        )

    def get_pool_specific_encyclopedia_rarity_emojis(
            self) -> Dict[str, Dict[int, str]]:
        """獲取特定卡池的圖鑑稀有度表情符號配置"""
        return self._cache.get(
            "encyclopedia_rarity_emojis", self.ui.encyclopedia_rarity_emojis
        )

    def get_rarity_display_codes(self) -> Dict[int, str]:
        """獲取稀有度顯示代碼配置"""
        return self._cache.get(
            "rarity_display_codes",
            self.ui.rarity_display_codes)

    def get_user_friendly_rarity_display_names(self) -> Dict[str, str]:
        """獲取用戶友好的稀有度顯示名稱配置"""
        return self._cache.get(
            "user_friendly_display_names", self.ui.user_friendly_display_names
        )

    def get_completion_indicator_emojis(self) -> Dict[str, str]:
        """獲取完成度指示器表情符號配置"""
        return self._cache.get(
            "completion_indicator_emojis", self.ui.completion_indicator_emojis
        )

    def get_character_archetype_readable_names(self) -> Dict[str, str]:
        """獲取角色原型可讀名稱配置"""
        return self._cache.get(
            "character_archetype_readable_names",
            self.ui.character_archetype_readable_names,
        )

    def get_news_type_readable_names(self) -> Dict[str, str]:
        """獲取新聞類型可讀名稱配置"""
        return self._cache.get(
            "news_type_readable_names",
            self.ui.news_type_readable_names,
        )

    def get_news_type_colors_int(self) -> Dict[str, int]:
        """獲取新聞類型顏色整數值"""
        return self._cache.get("news_type_colors_int", {})

    def get_leaderboard_config(self) -> Dict[str, 'LeaderboardConfigDetail']:
        """獲取排行榜配置"""
        return self.get_config("gacha_core_settings.leaderboard_config", {})


# Example instantiation (this would typically be centralized or injected)
# from gacha.app_config import settings as global_app_settings
# config_service_instance = ConfigurationService(global_app_settings)
