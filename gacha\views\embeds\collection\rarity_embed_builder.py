# -*- coding: utf-8 -*-
"""
Gacha系統稀有度收藏統計Embed構建器
"""

from typing import Any, Dict

import discord

from gacha.models.models import Card  # Card 模型可能仍需用於類型提示
from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder  # 導入基類
from gacha.views import utils as view_utils  # 導入視圖工具函數

# from gacha import config as gacha_config # Removed
from gacha.app_config import config_service  # <--- Import config_service


class RarityEmbedBuilder(BaseEmbedBuilder):  # 繼承基類
    """構建稀有度收藏統計嵌入消息的類"""

    def __init__(
        self,
        user: discord.User,
        cards_summary: Dict[str, Any],
        collection_stats: Dict[str, Any],
        rarities_total: Dict[str, Dict[int, int]],
    ):  # rarities_total uses int key
        """初始化稀有度收藏統計Embed構建器

        參數:
            user: Discord用戶對象
            cards_summary: 用戶卡片摘要數據
            collection_stats: 收藏統計數據
            rarities_total: 每個卡池和稀有度的總卡片數量
        """
        rarity_data = {
            "user": user,
            "cards_summary": cards_summary,
            "collection_stats": collection_stats,
            "rarities_total": rarities_total,
        }
        # RarityEmbedBuilder 目前不直接處理 interaction
        super().__init__(data=rarity_data)  # 調用基類初始化並傳遞 data
        self.user = user
        self.cards_summary = cards_summary
        self.collection_stats = collection_stats
        self.rarities_total = rarities_total

    def build_embed(self) -> discord.Embed:
        """獲取稀有度收藏統計的嵌入式消息

        返回:
            discord.Embed: 格式化的嵌入式消息
        """
        # 使用視圖工具函數進行稀有度統計，僅計算不同卡片 (返回 Dict[str, Dict[int, int]])
        pool_rarity_counts: Dict[str, Dict[int, int]] = (
            view_utils.get_pool_rarity_stats(self.cards_summary, unique_only=True)
        )

        # 使用基類初始化Embed
        embed = self._create_base_embed(  # MODIFIED: _initialize_embed to _create_base_embed
            title="稀有度收藏統計",
            description=f"你總共收集了 **{self.collection_stats['unique_cards']}** 種不同的卡片，共 **{self.collection_stats['total_quantity']}** 張",
            color=self.DEFAULT_EMBED_COLOR,  # MODIFIED: 使用基類默認藍色
        )

        # 添加用戶信息
        embed.set_author(
            name=self.user.display_name, icon_url=self.user.display_avatar.url
        )

        # 從配置獲取所有卡池類型名稱
        pool_type_names = config_service.get_pool_type_names()

        # 僅處理有數據的卡池
        available_pools = list(pool_rarity_counts.keys())

        # 根據 pool_type_names 的順序對可用卡池進行排序
        # 這樣可以保持一致的顯示順序，並且 main 卡池會被放在前面（如果它在 pool_type_names 中排在前面）
        sorted_pools = sorted(
            available_pools,
            key=lambda x: (
                list(pool_type_names.keys()).index(x) if x in pool_type_names else 999
            ),
        )

        # 處理所有卡池的稀有度統計，按照排序後的順序
        for pool_type_name in sorted_pools:
            rarity_counts = pool_rarity_counts[pool_type_name]
            if not rarity_counts:
                continue

            # 按數字稀有度降序排序 (大數字在前，即高稀有度在前)
            sorted_rarities = sorted(
                rarity_counts.keys(), reverse=True  # Keys are now int
            )

            # 生成稀有度統計文本
            rarity_groups = []
            current_group = []

            for i, rarity in enumerate(sorted_rarities):
                # 獲取用戶擁有的該稀有度卡片數量
                collected_count = rarity_counts[rarity]
                # 獲取該稀有度卡片總數量 (使用數字 rarity)
                total_count = self.rarities_total.get(pool_type_name, {}).get(
                    rarity, 0
                )  # rarity is int

                emoji = view_utils.get_rarity_display_code(
                    view_utils.RarityLevel(rarity), pool_type_name
                )  # Pass RarityLevel enum and pool_type
                # 以X/Y的形式顯示收集進度
                current_group.append(
                    f"{emoji} `{collected_count}/{total_count}`")

                # 每5個稀有度或處理完所有稀有度後，將當前組添加到結果中
                if (i + 1) % 5 == 0 or i == len(sorted_rarities) - 1:
                    rarity_groups.append(" | ".join(current_group))
                    current_group = []

            if rarity_groups:
                # 獲取卡池顯示名稱 - 從 pool_type_names 獲取
                pool_name = pool_type_names.get(pool_type_name, pool_type_name)

                # 添加回覆符號到每一行
                formatted_lines = []
                for i, group in enumerate(rarity_groups):
                    if i == 0 and len(rarity_groups) > 1:
                        # 第一行使用回覆延續符號（僅當有多行時）
                        reply_emoji = "<:ReplyCont:1357534065841930290>"
                    else:
                        # 單行或其他行使用回覆結尾符號
                        reply_emoji = "<:Reply:1357534074830590143>"

                    formatted_lines.append(f"{reply_emoji}{group}")

                embed.add_field(
                    name=f"{pool_name}",
                    value="\n".join(formatted_lines),
                    inline=False)

        return embed
