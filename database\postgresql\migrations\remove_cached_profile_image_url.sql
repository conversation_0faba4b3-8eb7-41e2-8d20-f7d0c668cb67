-- 从 user_profiles 表中移除 cached_profile_image_url 列
-- 此迁移脚本是个人资料缓存机制重构的一部分，将缓存从 PostgreSQL 迁移到 Redis

-- 创建一个临时表来保存移除前的数据（以备回滚）
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_profiles' 
        AND column_name = 'cached_profile_image_url'
    ) THEN
        -- 创建临时备份表
        CREATE TABLE IF NOT EXISTS temp_profile_cache_backup (
            user_id BIGINT NOT NULL PRIMARY KEY,
            cached_profile_image_url TEXT,
            backup_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 备份现有的缓存数据
        INSERT INTO temp_profile_cache_backup (user_id, cached_profile_image_url)
        SELECT user_id, cached_profile_image_url
        FROM user_profiles
        WHERE cached_profile_image_url IS NOT NULL;
        
        -- 移除列
        ALTER TABLE public.user_profiles DROP COLUMN cached_profile_image_url;
        
        RAISE NOTICE '已从 user_profiles 表中移除 cached_profile_image_url 列';
    ELSE
        RAISE NOTICE 'cached_profile_image_url 列不存在，无需移除';
    END IF;
END $$;

-- 回滚说明 (如需回滚，请执行以下SQL):
/*
-- 回滚步骤:
-- 1. 添加回列
ALTER TABLE public.user_profiles ADD COLUMN cached_profile_image_url TEXT;

-- 2. 恢复数据
UPDATE public.user_profiles p
SET cached_profile_image_url = b.cached_profile_image_url
FROM temp_profile_cache_backup b
WHERE p.user_id = b.user_id;

-- 3. 可选：删除临时备份表
-- DROP TABLE temp_profile_cache_backup;
*/ 