"""
測試GIF訊息格式
"""

# Discord 訊息模擬器測試

# 可用的GIF列表
AVAILABLE_GIFS = {
    "青蛙": {
        "path": "gifs/青蛙.gif",
        "description": "青蛙GIF效果",
        "message": "{user_mention} 想吃迪奧了484"
    },
    "阿北": {
        "path": "gifs/阿北.gif",
        "description": "阿北GIF效果",
        "message": "{user_mention} 吃阿北薯條啦"
    }
}

# 測試用的資料
def test_message_templates():
    """測試GIF訊息模板"""
    print("GIF類型及對應的訊息:")
    
    # 模擬用戶
    test_user_mention = "@測試用戶"
    
    # 測試每種GIF類型的訊息
    for gif_type, data in AVAILABLE_GIFS.items():
        # 獲取模板，如果沒有則使用默認模板
        template = data.get("message", "{user_mention} 的{gif_type}GIF:")
        
        # 替換模板中的變量
        message = template.format(
            user_mention=test_user_mention,
            gif_type=gif_type
        )
        
        # 打印結果
        print(f"- {gif_type}: {message}")
        
    # 測試默認訊息格式
    default_template = "{user_mention} 的{gif_type}GIF:"
    
    # 測試添加新的GIF類型
    new_type = "新類型"
    default_message = default_template.format(
        user_mention=test_user_mention,
        gif_type=new_type
    )
    print(f"- 新GIF類型 (使用默認訊息): {default_message}")

if __name__ == "__main__":
    test_message_templates() 