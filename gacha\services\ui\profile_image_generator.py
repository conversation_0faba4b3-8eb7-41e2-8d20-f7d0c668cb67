"""
Profile 圖片生成器 - 使用 HTML 模板和 Selenium 生成用戶檔案圖片
"""
import os
import io
import json
import asyncio
import base64
import aiohttp
from typing import Dict, Any, Optional
from PIL import Image
from playwright.async_api import Page, Locator # Import Playwright types
from urllib.parse import urljoin # Import urljoin

# Import the unified PlaywrightManager
# Ensure 'utils' directory is accessible
import sys
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')) # Adjust path if needed
if project_root not in sys.path:
    sys.path.insert(0, project_root)
from utils.playwright_manager import PlaywrightManager
from utils.logger import logger # Keep using the existing logger setup
from gacha.models.profile_models import ProfileData # ProfileImageGenerationData seems unused


class ProfileImageGenerator:
    """Profile 圖片生成器 (使用 Playwright)"""

    def __init__(self, playwright_manager: PlaywrightManager):
        """
        初始化 Profile 圖片生成器
        Args:
            playwright_manager: An instance of PlaywrightManager.
        """
        self.playwright_manager = playwright_manager
        # 獲取HTML模板路徑
        self.template_path = os.path.join(
            os.path.dirname(__file__), "templates", "profile_template.html"
        )
        # 獲取JS注入腳本路徑並讀取內容
        self.injector_script_path = os.path.join(
            os.path.dirname(__file__), "profile_injector.js" # 相對於當前文件
        )
        try:
            with open(self.injector_script_path, 'r', encoding='utf-8') as f:
                self.injector_script = f.read()
            logger.info(f"Profile圖片生成器初始化 (Playwright)，模板路徑: {self.template_path}, JS注入腳本已載入.")
        except FileNotFoundError:
            logger.error(f"JS注入腳本 {self.injector_script_path} 未找到! Profile圖片生成可能失敗。")
            self.injector_script = "" # Fallback to prevent crashes, though functionality will be broken

    async def _get_image_as_base64(self, url: str) -> Optional[str]:
        """Fetches an image from a URL and returns it as a base64 data URI. (Unchanged)"""
        if not url or not url.startswith(('http://', 'https://')):
            logger.warning(f"Invalid or empty URL provided for base64 conversion: {url}")
            return None
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        image_data = await response.read()
                        content_type = response.headers.get("Content-Type", "image/png")
                        if not content_type.startswith("image/"):
                            logger.warning(f"URL {url} did not return an image. Content-Type: {content_type}")
                            return None
                        base64_encoded_data = base64.b64encode(image_data).decode('utf-8')
                        result_uri = f"data:{content_type};base64,{base64_encoded_data}"
                        return result_uri
                    else:
                        logger.error(f"Failed to fetch image from {url} for base64. Status: {response.status}")
                        return None
        except aiohttp.ClientError as e:
            logger.error(f"aiohttp.ClientError when fetching {url} for base64: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching {url} for base64: {e}", exc_info=True)
            return None

    # Removed initialize_browser, close_browser, and _escape_js_string methods
    # as browser management is handled by PlaywrightManager and injection uses locators/evaluate.

    def _generate_stars_html(self, star_level: int) -> str:
        """生成星級HTML (Unchanged)
        
        實現每5星循環顯示星星數量，並在每組使用不同顏色：
        - 第1組5星(1-5星)：金色，顯示1-5顆星
        - 第2組5星(6-10星)：橙色，顯示1-5顆星
        - 第3組5星(11-15星)：紅橙色，顯示1-5顆星
        - 第4組5星(16-20星)：品紅色，顯示1-5顆星
        - 第5組5星(21-25星)：紫色，顯示1-5顆星
        - 第6組5星(26-30星)：藍色，顯示1-5顆星
        - 第7組5星(31-35星)：綠色，顯示1-5顆星
        
        例如：3星顯示3顆金色星，7星顯示2顆橙色星，12星顯示2顆紅橙色星
        """
        if star_level <= 0:
            return ""
        
        # 定義每組5星的顏色類別
        star_colors = [
            "star-normal",     # 1-5星: 金色
            "star-special",    # 6-10星: 橙色
            "star-rare",       # 11-15星: 紅橙色
            "star-epic",       # 16-20星: 品紅色
            "star-legendary",  # 21-25星: 紫色
            "star-mythic",     # 26-30星: 藍色
            "star-ultimate",   # 31-35星: 綠色
        ]
        
        # 計算當前所在組和在該組內的位置
        group_index = (star_level - 1) // 5
        position_in_group = (star_level - 1) % 5 + 1  # 在組內的位置 (1-5)
        
        # 獲取顏色類別，避免索引超出範圍
        color_class = star_colors[min(group_index, len(star_colors) - 1)]
        
        # 生成對應數量的星星
        stars_html = ''.join([f'<span class="{color_class}">★</span>' for _ in range(position_in_group)])
        
        return stars_html

    async def generate_profile_image(
        self,
        profile_data: ProfileData
    ) -> bytes:
        """使用 Playwright 生成檔案圖片"""
        page: Page | None = None
        try:
            logger.info(f"開始生成用戶 {profile_data.user_id} 的檔案圖片 (Playwright)")

            image_urls_to_fetch_map: Dict[str, str] = {}
            if profile_data.avatar_url and profile_data.avatar_url.startswith('http'):
                image_urls_to_fetch_map['avatar_url'] = profile_data.avatar_url
            if profile_data.background_image_url and profile_data.background_image_url.startswith('http'):
                image_urls_to_fetch_map['background_image_url'] = profile_data.background_image_url

            if profile_data.main_card:
                card = profile_data.main_card
                if not (card.local_image_path and os.path.exists(card.local_image_path) and os.path.getsize(card.local_image_path) > 0):
                    if card.image_url and card.image_url.startswith('http'):
                        image_urls_to_fetch_map[f'main_card_image_url_{card.card_id}'] = card.image_url
            
            for slot, card in profile_data.sub_cards.items():
                if not (card.local_image_path and os.path.exists(card.local_image_path) and os.path.getsize(card.local_image_path) > 0):
                    if card.image_url and card.image_url.startswith('http'):
                         image_urls_to_fetch_map[f'sub_card_image_url_{slot}_{card.card_id}'] = card.image_url
            
            if image_urls_to_fetch_map:
                logger.info(f"檢測到 {len(image_urls_to_fetch_map)} 個網絡圖片需要預加载為 Base64。")

            base64_encoded_images: Dict[str, Optional[str]] = {}
            if image_urls_to_fetch_map:
                fetch_tasks = [self._get_image_as_base64(url) for url in image_urls_to_fetch_map.values()]
                results = await asyncio.gather(*fetch_tasks, return_exceptions=True)
                
                for i, key in enumerate(image_urls_to_fetch_map.keys()):
                    result = results[i]
                    if isinstance(result, Exception):
                        logger.warning(f"Base64 encoding task for image key '{key}' failed: {result}")
                        base64_encoded_images[key] = None
                    elif result is None:
                        logger.warning(f"Base64 encoding for image key '{key}' ({image_urls_to_fetch_map[key]}) returned None.")
                        base64_encoded_images[key] = None
                    else:
                        base64_encoded_images[key] = result
            
            if 'avatar_url' in image_urls_to_fetch_map:
                fetched_b64 = base64_encoded_images.get('avatar_url')
                if fetched_b64:
                    profile_data.avatar_url = fetched_b64
            
            if 'background_image_url' in image_urls_to_fetch_map:
                fetched_b64 = base64_encoded_images.get('background_image_url')
                if fetched_b64:
                    profile_data.background_image_url = fetched_b64

            if profile_data.main_card:
                card = profile_data.main_card
                key = f'main_card_image_url_{card.card_id}'
                if key in image_urls_to_fetch_map:
                    fetched_b64 = base64_encoded_images.get(key)
                    if fetched_b64:
                        card.image_url = fetched_b64
                        card.local_image_path = None 
            
            for slot, card in profile_data.sub_cards.items():
                key = f'sub_card_image_url_{slot}_{card.card_id}'
                if key in image_urls_to_fetch_map:
                    fetched_b64 = base64_encoded_images.get(key)
                    if fetched_b64:
                        card.image_url = fetched_b64
                        card.local_image_path = None

            page = await self.playwright_manager.acquire_page()

            # 設置視口大小以匹配模板固定尺寸 (1080x720px)
            await page.set_viewport_size({"width": 1080, "height": 720})

            # 載入HTML模板
            abs_template_path = os.path.abspath(self.template_path)
            template_file_uri = urljoin('file:', abs_template_path.replace('\\', '/'))
            if not template_file_uri.startswith('file:///'):
                template_file_uri = template_file_uri.replace('file:/','file:///', 1)
            logger.debug(f"Loading template from URI: {template_file_uri}")
            await page.goto(template_file_uri, wait_until='domcontentloaded')

            # 注入用戶資料
            await self._inject_profile_data(page, profile_data)

            # 等待網絡空閒，確保所有資源（尤其是背景圖）已加載
            # 增加超時時間，因為背景圖和卡片圖可能較大
            try:
                await page.wait_for_load_state('networkidle', timeout=15000) # 15秒超時
                logger.info("Network idle state reached after injecting data.")
            except Exception as wait_error:
                # 如果超時，記錄警告但繼續嘗試截圖，可能部分資源未加載完成
                logger.warning(f"Network idle wait timed out or failed: {wait_error}. Proceeding with screenshot anyway.")

            # 截圖整個頁面
            screenshot_bytes = await page.screenshot(type="png", full_page=False) # full_page=False because viewport is set

            # 使用 PIL 處理圖片
            img = Image.open(io.BytesIO(screenshot_bytes))

            # (可選) 驗證尺寸，如果 viewport 未完全控制輸出
            # if img.size != (1536, 864):
            #     logger.warning(f"Screenshot size {img.size} differs from viewport. Resizing to 1536x864.")
            #     img = img.resize((1536, 864), Image.LANCZOS)

            # 轉換為 WebP bytes
            img_bytes_io = io.BytesIO()
            # WebP 支持的選項: quality (0-100, default 80 for lossy), lossless (True/False)
            # method (0-6, 控制編碼速度和壓縮比的平衡，6最慢但壓縮最好)
            # alpha_quality (0-100, 透明通道質量)
            # 品質設為 100，method 保持為 6 以獲得最佳壓縮
            img.save(img_bytes_io, format='WEBP', quality=100, method=6)
            result_bytes = img_bytes_io.getvalue()

            logger.info(f"成功生成檔案圖片 (Playwright, WebP, Q:100)，大小: {len(result_bytes)} bytes")
            return result_bytes

        except Exception as e:
            logger.error(f"生成檔案圖片失敗 (Playwright): {e}", exc_info=True)
            raise
        finally:
            if page:
                await self.playwright_manager.release_page(page)

    async def _inject_profile_data(self, page: Page, profile_data: ProfileData):
        """使用 Playwright 注入檔案資料到HTML模板 (已优化 - 直接使用圖片URL 或 Base64)"""
        try:
            def format_number(number):
                if number >= 1000:
                    return f"{number / 1000:.1f}k".replace('.0k', 'k')
                return str(number)

            payload = {
                "texts": {},
                "attributes": {},
                "inner_htmls": {},
                "styles": {},
                "display_toggles": {}
            }

            # 基本信息
            if profile_data.nickname:
                payload["texts"]["#username"] = profile_data.nickname
                payload["texts"]["#username-initial"] = profile_data.nickname[0].upper() # JS會處理是否顯示
            else:
                payload["texts"]["#username"] = "Unknown User"
                payload["texts"]["#username-initial"] = "U"

            # 頭像處理 (修改為設置 background-image)
            if profile_data.avatar_url and profile_data.avatar_url.startswith(('http://', 'https://', 'data:image')):
                payload["styles"]["#user-avatar"] = {
                    "background-image": f"url('{profile_data.avatar_url}')"
                }
                payload["display_toggles"]["#username-initial"] = "none" # 有頭像則隱藏首字母
            else: 
                payload["display_toggles"]["#username-initial"] = "block" # 無頭像則顯示首字母

            payload["texts"][".ml-6 p.text-gray-400"] = f"ID: #{profile_data.user_id}"
            payload["texts"]["#user-status"] = getattr(profile_data, 'user_status', '「保持熱愛，奔赴下一個SSR！」')
            payload["texts"]["#oil-balance"] = format_number(profile_data.oil_balance)
            payload["texts"]["#oil-ticket-balance"] = format_number(profile_data.oil_ticket_balance)
            payload["texts"]["#total-draws"] = str(profile_data.total_draws)
            payload["texts"]["#like-count"] = format_number(profile_data.like_count)
            payload["texts"]["#completion-rate"] = f"{profile_data.collection_completion_rate}%"
            payload["texts"]["#total-owned"] = format_number(profile_data.total_owned_cards)

            # 稀有度统计 - 使用格式化数值
            for rarity_id, code in enumerate(['C', 'R', 'SR', 'SSR', 'UR', 'LR', 'EX'], 1):
                count = profile_data.rarity_counts.get(rarity_id, 0)
                payload["texts"][f'#rarity-{code.lower()}-count'] = format_number(count)

            # 主卡片
            if profile_data.main_card:
                payload["display_toggles"]["#main-card-section"] = "flex"
                card = profile_data.main_card
                
                image_source_set = False
                if card.local_image_path and os.path.exists(card.local_image_path) and os.path.getsize(card.local_image_path) > 0:
                    normalized_path = card.local_image_path.replace('\\', '/').replace(' ', '%20')
                    file_uri = f"file:///{normalized_path}"
                    payload["attributes"]["#main-card-img"] = {"src": file_uri, "alt": card.name or ""}
                    image_source_set = True
                elif card.image_url: 
                    payload["attributes"]["#main-card-img"] = {"src": card.image_url, "alt": card.name or ""}
                    image_source_set = True
                
                if not image_source_set:
                    payload["attributes"]["#main-card-img"] = {"alt": '主卡片圖片無效或缺失', "src": ""}
                    logger.warning(f"Main card '{card.name}' 缺少有效圖片源 (本地路徑或URL/Base64)")
                
                payload["texts"]["#main-card-name"] = card.name or ""
                payload["texts"]["#main-card-series"] = card.series or ""
                payload["inner_htmls"]["#main-card-stars"] = self._generate_stars_html(card.star_level)
            else:
                payload["display_toggles"]["#main-card-section"] = "none"
                payload["attributes"]["#main-card-img"] = {"alt": '', "src": ""}
                payload["texts"]["#main-card-name"] = ""
                payload["texts"]["#main-card-series"] = ""
                payload["inner_htmls"]["#main-card-stars"] = ""

            # 副卡片
            active_sub_cards_count = 0
            for slot in range(1, 5):
                card_data = profile_data.sub_cards.get(slot)
                card_section_selector = f"#sub-card-container-{slot}"
                card_img_selector = f"#sub-card-img-{slot}"
                card_name_selector = f"#sub-card-name-{slot}"
                # card_series_selector = f"#sub-card-series-{slot}" # Not used in template for subcards
                card_stars_selector = f"#sub-card-stars-{slot}"

                if card_data:
                    active_sub_cards_count += 1
                    payload["display_toggles"][card_section_selector] = "block" 
                    
                    sub_image_source_set = False
                    if card_data.local_image_path and os.path.exists(card_data.local_image_path) and os.path.getsize(card_data.local_image_path) > 0:
                        normalized_path = card_data.local_image_path.replace('\\', '/').replace(' ', '%20')
                        file_uri = f"file:///{normalized_path}"
                        payload["attributes"][card_img_selector] = {"src": file_uri, "alt": card_data.name or ""}
                        sub_image_source_set = True
                    elif card_data.image_url: 
                        payload["attributes"][card_img_selector] = {"src": card_data.image_url, "alt": card_data.name or ""}
                        sub_image_source_set = True

                    if not sub_image_source_set:
                        payload["attributes"][card_img_selector] = {"alt": f'副卡片 {slot} 圖片無效或缺失', "src": ""}
                        logger.warning(f"Sub card slot {slot} ('{card_data.name}') 缺少有效圖片源")

                    payload["texts"][card_name_selector] = card_data.name or ""
                    # Series name for sub-cards is not in the provided HTML structure for sub-cards, so no injection for it.
                    if card_data.star_level > 0:
                        payload["inner_htmls"][card_stars_selector] = self._generate_stars_html(card_data.star_level)
                    else:
                        payload["inner_htmls"][card_stars_selector] = ""
                else: 
                    payload["display_toggles"][card_section_selector] = "none"
                    payload["attributes"][card_img_selector] = {"alt": '', "src": ""}
                    payload["texts"][card_name_selector] = ""
                    payload["inner_htmls"][card_stars_selector] = ""

            if active_sub_cards_count == 0:
                payload["display_toggles"]["#sub-cards-section"] = "none"
            else:
                payload["display_toggles"]["#sub-cards-section"] = "flex" 
                if active_sub_cards_count < 4:
                    payload["styles"]["#sub-cards-row"] = {"justifyContent": "flex-start"}
                else: 
                    payload["styles"]["#sub-cards-row"] = {"justifyContent": "space-between"}

            # 背景圖片 (修改選擇器為 #profile-background)
            if profile_data.background_image_url:
                if profile_data.background_image_url.startswith(('http://', 'https://', 'data:image')):
                    payload["styles"]["#profile-background"] = { 
                        "background-image": f"url('{profile_data.background_image_url}')"
                    }
                else:
                    logger.warning(f"Background: Invalid background_image_url (not http/https/data): {profile_data.background_image_url[:100]}. Default DIV background may apply.")

            # 3. 執行注入腳本
            if not self.injector_script:
                logger.error("JS注入腳本未載入，無法注入檔案資料。")
                raise RuntimeError("JS injector script not loaded for ProfileImageGenerator")

            await page.add_script_tag(content=self.injector_script)
            await page.evaluate("data => injectProfileData(data)", payload)

            logger.info("檔案資料注入完成 (Playwright via add_script_tag and evaluate call - 使用直接URL)")

        except Exception as e:
            logger.error(f"注入檔案資料失敗 (Playwright): {e}", exc_info=True)
            raise