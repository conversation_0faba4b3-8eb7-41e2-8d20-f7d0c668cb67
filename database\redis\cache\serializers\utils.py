"""
序列化工具模塊 - 緩存相關的通用工具函數
"""

import logging
from typing import Any, Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


def generate_cache_key(prefix: str, func_name: str, args: tuple, kwargs: dict) -> str:
    """生成緩存鍵（優化版）

    Args:
        prefix: 緩存鍵前綴
        func_name: 函數名稱
        args: 位置參數
        kwargs: 關鍵字參數

    Returns:
        緩存鍵字符串
    """
    # 高性能緩存鍵生成
    parts = []

    # 添加前綴和函數名
    if prefix:
        parts.append(prefix)
    parts.append(func_name)

    # 處理位置參數（最多處理前3個非字典/列表參數）
    arg_count = 0
    for arg in args:
        if not isinstance(arg, (dict, list, set)):
            parts.append(str(arg))
            arg_count += 1
            if arg_count >= 3:
                break

    # 處理關鍵字參數（最多處理3個）
    if kwargs:
        kw_items = sorted(kwargs.items())
        kw_count = 0
        for k, v in kw_items:
            if not isinstance(v, (dict, list, set)):
                parts.append(f"{k}_{v}")
                kw_count += 1
                if kw_count >= 3:
                    break

    # 連接所有部分
    return "_".join(parts)
