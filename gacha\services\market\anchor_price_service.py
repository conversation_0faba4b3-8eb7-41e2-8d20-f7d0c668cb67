import asyncpg
from utils.logger import logger
from decimal import Decimal, ROUND_HALF_UP
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone, timedelta
from gacha.app_config import config_service

class AnchorPriceService:

    def __init__(self, pool: asyncpg.Pool):
        if pool is None:
            raise ValueError('AnchorPriceService requires an asyncpg connection pool.')
        self.pool = pool
        self._anchor_price_task_failure_count = 0

    async def get_effective_anchor_price(self, conn: asyncpg.Connection, asset_id: int, initial_anchor_price_db: Optional[Decimal], current_anchor_price_db: Optional[Decimal], anchor_price_updated_at_db: Optional[datetime]) -> Decimal:
        """
        Retrieves or calculates the effective anchor price for an asset,
        implementing an enhanced fallback mechanism.
        Priority:
        1. Current day's `current_anchor_price`.
        2. Recent N-day average closing price from `asset_price_history`.
        3. Stale `current_anchor_price` if not too old.
        4. `initial_anchor_price` as the final fallback.
        Logs warnings for each step of the fallback.
        """
        stock_market_cfg = config_service.get_stock_market_config()
        fallback_days_history = stock_market_cfg.anchor_price_fallback_days_history
        max_stale_days = stock_market_cfg.anchor_price_max_stale_days
        min_trading_days_for_avg = stock_market_cfg.anchor_price_min_trading_days_for_avg
        if initial_anchor_price_db is None:
            logger.warning("Asset ID %s: initial_anchor_price is NULL in DB. Using Decimal('0.01') as a minimal fallback.", asset_id)
            initial_anchor_price_decimal = Decimal('0.01')
        else:
            initial_anchor_price_decimal = Decimal(str(initial_anchor_price_db))
            if initial_anchor_price_decimal <= Decimal('0'):
                logger.warning("Asset ID %s: initial_anchor_price_db is %s. Using Decimal('0.01') to avoid issues.", asset_id, initial_anchor_price_decimal)
                initial_anchor_price_decimal = Decimal('0.01')
        today_utc_date = datetime.now(timezone.utc).date()
        if current_anchor_price_db is not None and anchor_price_updated_at_db is not None:
            anchor_price_updated_at_db_aware = anchor_price_updated_at_db.replace(tzinfo=timezone.utc) if anchor_price_updated_at_db.tzinfo is None else anchor_price_updated_at_db
            if anchor_price_updated_at_db_aware.date() == today_utc_date:
                effective_price = Decimal(str(current_anchor_price_db))
                if effective_price > Decimal('0'):
                    logger.info('資產ID %s：使用當日預先計算的錨定價格：%s', asset_id, effective_price)
                    return effective_price
                else:
                    logger.warning("Asset ID %s: Current day's pre-calculated anchor price is %s. Proceeding to fallback.", asset_id, effective_price)
        logger.info('Asset ID %s: Attempting to calculate %s-day average closing price.', asset_id, fallback_days_history)
        query_closing_prices = f"\n        WITH DailyClosingPrices AS (\n            SELECT\n                DATE(timestamp AT TIME ZONE 'UTC') AS trade_date,\n                (array_agg(price ORDER BY timestamp DESC))[1] AS closing_price\n            FROM asset_price_history\n            WHERE asset_id = $1\n            GROUP BY DATE(timestamp AT TIME ZONE 'UTC')\n            ORDER BY trade_date DESC\n            LIMIT {fallback_days_history}\n        )\n        SELECT closing_price\n        FROM DailyClosingPrices;\n        "
        try:
            closing_prices_records = await conn.fetch(query_closing_prices, asset_id)
            closing_prices = [Decimal(str(record['closing_price'])) for record in closing_prices_records if record['closing_price'] is not None and Decimal(str(record['closing_price'])) > 0]
            if len(closing_prices) >= min_trading_days_for_avg:
                calculated_anchor_price = sum(closing_prices) / Decimal(len(closing_prices))
                calculated_anchor_price_rounded = calculated_anchor_price.quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
                if calculated_anchor_price_rounded > Decimal('0'):
                    logger.info('Asset ID %s: Using calculated %s-day average closing price: %s', asset_id, len(closing_prices), calculated_anchor_price_rounded)
                    return calculated_anchor_price_rounded
                else:
                    logger.warning('Asset ID %s: Calculated %s-day average closing price is %s. Proceeding to fallback.', asset_id, len(closing_prices), calculated_anchor_price_rounded)
            else:
                logger.warning('Asset ID %s: Insufficient data for %s-day average (%s valid days, need %s). Proceeding to fallback.', asset_id, fallback_days_history, len(closing_prices), min_trading_days_for_avg)
        except Exception as e:
            logger.error('Asset ID %s: Error during %s-day average calculation: %s. Proceeding to fallback.', asset_id, fallback_days_history, e, exc_info=True)
        if current_anchor_price_db is not None and anchor_price_updated_at_db is not None:
            anchor_price_updated_at_db_aware = anchor_price_updated_at_db.replace(tzinfo=timezone.utc) if anchor_price_updated_at_db.tzinfo is None else anchor_price_updated_at_db
            days_since_last_update = (today_utc_date - anchor_price_updated_at_db_aware.date()).days
            if days_since_last_update <= max_stale_days:
                effective_price = Decimal(str(current_anchor_price_db))
                if effective_price > Decimal('0'):
                    logger.warning('Asset ID %s: Using stale pre-calculated anchor price (updated %s days ago): %s', asset_id, days_since_last_update, effective_price)
                    return effective_price
                else:
                    logger.warning('Asset ID %s: Stale pre-calculated anchor price is %s. Proceeding to final fallback.', asset_id, effective_price)
            else:
                logger.warning('Asset ID %s: Stale pre-calculated anchor price is too old (%s days, max %s). Proceeding to final fallback.', asset_id, days_since_last_update, max_stale_days)
        else:
            logger.warning('Asset ID %s: No pre-calculated anchor price available for stale check. Proceeding to final fallback.', asset_id)
        logger.error('Asset ID %s: All fallback mechanisms exhausted. Using initial_anchor_price: %s. This indicates potential issues with daily anchor price calculation or sparse trading data.', asset_id, initial_anchor_price_decimal)
        return initial_anchor_price_decimal

    async def calculate_daily_anchor_prices(self):
        logger.info('Starting daily anchor price calculation.')
        task_successful = False
        try:
            async with self.pool.acquire() as conn:
                assets_to_process = await conn.fetch('SELECT asset_id, initial_anchor_price FROM virtual_assets')
                if not assets_to_process:
                    logger.info('No virtual assets found to calculate anchor prices.')
                    self._anchor_price_task_failure_count = 0
                    task_successful = True
                    return
                updates_to_perform = []
                current_time = datetime.now(timezone.utc)
                stock_market_cfg = config_service.get_stock_market_config()
                calc_days_history = getattr(stock_market_cfg, 'anchor_price_fallback_days_history', 3)
                for asset in assets_to_process:
                    asset_id = asset['asset_id']
                    initial_anchor_price_db = asset['initial_anchor_price']
                    if initial_anchor_price_db is None:
                        logger.warning('Asset ID %s has NULL initial_anchor_price. Skipping anchor price calculation for this asset.', asset_id)
                        continue
                    initial_anchor_price_decimal = Decimal(str(initial_anchor_price_db))
                    if initial_anchor_price_decimal <= Decimal('0'):
                        logger.warning("Asset ID %s: initial_anchor_price_db is %s. Using Decimal('0.01') as a base for calculation if no history.", asset_id, initial_anchor_price_decimal)
                        initial_anchor_price_decimal = Decimal('0.01')
                    query_closing_prices = f"\n                    WITH DailyClosingPrices AS (\n                        SELECT\n                            DATE(timestamp AT TIME ZONE 'UTC') AS trade_date,\n                            (array_agg(price ORDER BY timestamp DESC))[1] AS closing_price\n                        FROM asset_price_history\n                        WHERE asset_id = $1\n                        GROUP BY DATE(timestamp AT TIME ZONE 'UTC')\n                        ORDER BY trade_date DESC\n                        LIMIT {calc_days_history}\n                    )\n                    SELECT closing_price\n                    FROM DailyClosingPrices;\n                    "
                    closing_prices_records = await conn.fetch(query_closing_prices, asset_id)
                    closing_prices = [Decimal(str(record['closing_price'])) for record in closing_prices_records if record['closing_price'] is not None and Decimal(str(record['closing_price'])) > 0]
                    calculated_anchor_price = None
                    if closing_prices:
                        calculated_anchor_price = sum(closing_prices) / Decimal(len(closing_prices))
                        logger.debug('Asset ID %s: Calculated anchor price from %s day(s) (up to %s requested): %s', asset_id, len(closing_prices), calc_days_history, calculated_anchor_price)
                    else:
                        calculated_anchor_price = initial_anchor_price_decimal
                        logger.warning('Asset ID %s: No valid trading history for the last %s days. Using initial_anchor_price: %s', asset_id, calc_days_history, calculated_anchor_price)
                    if calculated_anchor_price is not None and calculated_anchor_price > Decimal('0'):
                        current_anchor_price_rounded = calculated_anchor_price.quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
                        updates_to_perform.append((current_anchor_price_rounded, current_time, asset_id))
                    elif calculated_anchor_price is not None and calculated_anchor_price <= Decimal('0'):
                        logger.error('Asset ID %s: Calculated anchor price was zero or negative (%s). Using initial anchor price %s instead for update, but this is unusual.', asset_id, calculated_anchor_price, initial_anchor_price_decimal)
                        updates_to_perform.append((initial_anchor_price_decimal.quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP), current_time, asset_id))
                    else:
                        logger.error('Asset ID %s: Could not determine a valid anchor price. Initial anchor was %s. Skipping update for this asset.', asset_id, initial_anchor_price_db)
                if updates_to_perform:
                    update_query = '\n                    UPDATE virtual_assets\n                    SET current_anchor_price = $1,\n                        anchor_price_updated_at = $2\n                    WHERE asset_id = $3;\n                    '
                    await conn.executemany(update_query, updates_to_perform)
                    logger.info('Successfully updated anchor prices for %s assets.', len(updates_to_perform))
                else:
                    logger.info('No anchor prices needed updating in this cycle (either no assets or no valid calculations).')
                self._anchor_price_task_failure_count = 0
                task_successful = True
        except Exception as e:
            self._anchor_price_task_failure_count += 1
            logger.error('Error in calculate_daily_anchor_prices (failure #%s): %s', self._anchor_price_task_failure_count, e, exc_info=True)
        if task_successful:
            logger.info('Finished daily anchor price calculation successfully.')
        else:
            logger.error('Finished daily anchor price calculation with errors.')