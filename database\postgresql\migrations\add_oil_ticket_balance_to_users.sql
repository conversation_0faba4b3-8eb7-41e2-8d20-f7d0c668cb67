-- Migration: Add oil_ticket_balance to gacha_users and remove oil_spent_remainder
-- UP: Apply the migration
ALTER TABLE gacha_users ADD COLUMN oil_ticket_balance NUMERIC(18, 2) NOT NULL DEFAULT 0.00;
ALTER TABLE gacha_users DROP COLUMN oil_spent_remainder;
CREATE INDEX idx_gacha_users_oil_ticket_balance ON gacha_users (oil_ticket_balance);

-- DOWN: Revert the migration
DROP INDEX idx_gacha_users_oil_ticket_balance;
ALTER TABLE gacha_users ADD COLUMN oil_spent_remainder INTEGER NOT NULL DEFAULT 0; -- Assuming original type was INTEGER
ALTER TABLE gacha_users DROP COLUMN oil_ticket_balance;