"""
提供可重用的加入最愛功能組件

這個模組提供了一個可重用的加入最愛功能組件，可以被不同的視圖類使用，
避免代碼重複並保持統一的行為和外觀。
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Tuple
import discord
from utils.logger import logger
from gacha.utils import interaction_utils
from gacha.repositories.collection.user_collection_repository import UserCollectionRepository
from gacha.exceptions import CardNotFoundError, UserNotFoundError, DatabaseOperationError, GachaError

class FavoriteStateUpdatable(ABC):
    """定義可更新最愛狀態的介面

    所有需要支援最愛功能的視圖都應實現此介面。

    實現此介面的視圖類必須：
    1. 提供 update_favorite_state 方法來更新內部狀態
    2. 確保 get_current_page_embed 方法使用更新後的狀態

    優點：
    - 統一介面降低心智負擔
    - 消除特例處理，提高代碼可讀性
    - 視圖類自行負責狀態更新邏輯，無需外部了解其內部結構
    """

    @abstractmethod
    def update_favorite_state(self, card_id: int, is_favorite: bool) -> bool:
        """更新視圖中指定卡片的最愛狀態

        參數:
            card_id: 卡片ID
            is_favorite: 新的最愛狀態

        返回:
            bool: 是否成功更新
        """
        pass

class FavoriteComponent:
    """加入最愛功能組件

    提供加入/取消最愛功能的統一實現，可以被不同的視圖類使用。

    使用方式：
    1. 讓視圖類實現 FavoriteStateUpdatable 介面
    2. 使用 setup_favorite_button 方法創建並添加按鈕到視圖
    3. 實現 get_current_page_embed 方法來返回嵌入

    設計原則：
    - 統一介面：所有視圖通過 FavoriteStateUpdatable 介面處理狀態更新
    - 關注點分離：按鈕狀態、視圖內部狀態和 UI 更新分開處理
    - 可擴展性：新增視圖類只需實現介面，不需修改此組件
    """
    FAVORITE_EMOJI = '<a:sr:1357714854244515936>'
    UNFAVORITE_EMOJI = '<a:sw:1365447243863429273>'
    ALREADY_FAVORITE_EMOJI = '<a:pu:1365482490478989353>'

    @staticmethod
    def setup_favorite_button(view: discord.ui.View, user_id: int, card_id: int, row: int=0, custom_id: str='add_to_favorite_button') -> discord.ui.Button:
        """創建並返回加入最愛按鈕"""
        button = discord.ui.Button(label='加入最愛', style=discord.ButtonStyle.success, emoji=FavoriteComponent.UNFAVORITE_EMOJI, row=row, custom_id=custom_id)

        async def favorite_callback(interaction: discord.Interaction):
            await FavoriteComponent.handle_favorite_toggle(interaction=interaction, view=view, user_id=user_id, card_id=card_id, button=button)
        button.callback = favorite_callback
        return button

    @staticmethod
    def create_initial_favorite_button(view: discord.ui.View, user_id: int, card_id: int, is_favorite: bool, row: int, custom_id: str) -> discord.ui.Button:
        """
        創建並返回一個初始狀態配置好的最愛按鈕。

        參數:
            view: 按鈕所在的 discord.ui.View 實例。
            user_id: 卡片擁有者的用戶 ID。
            card_id: 卡片的 ID。
            is_favorite: 卡片的初始最愛狀態。
            row: 按鈕所在的行。
            custom_id: 按鈕的自訂 ID。

        返回:
            discord.ui.Button: 配置好的最愛按鈕。
        """
        button = discord.ui.Button(label='', style=discord.ButtonStyle.secondary, emoji=None, row=row, custom_id=custom_id)
        FavoriteComponent.update_button_state(button, is_favorite)

        async def favorite_callback(interaction: discord.Interaction):
            await FavoriteComponent.handle_favorite_toggle(interaction=interaction, view=view, user_id=user_id, card_id=card_id, button=button)
        button.callback = favorite_callback
        return button

    @staticmethod
    def update_button_state(button: Optional[discord.ui.Button], is_favorite: bool) -> None:
        """更新加入最愛按鈕狀態

        參數:
            button: 要更新的按鈕，如果為None則不進行操作
            is_favorite: 是否為最愛狀態
        """
        if button is None:
            return
        if is_favorite:
            button.style = discord.ButtonStyle.danger
            button.label = '取消最愛'
            button.emoji = FavoriteComponent.FAVORITE_EMOJI
        else:
            button.style = discord.ButtonStyle.success
            button.label = '加入最愛'
            button.emoji = FavoriteComponent.UNFAVORITE_EMOJI

    @staticmethod
    async def check_favorite_status(interaction: discord.Interaction, user_id: int, card_id: int, button: Optional[discord.ui.Button]=None) -> bool:
        """檢查卡片是否已被收藏

        參數:
            interaction: Discord 交互對象 (用於訪問服務)
            user_id: 用戶ID
            card_id: 卡片ID
            button: 可選的按鈕對象，用於更新狀態

        返回:
            bool: 卡片是否被標記為最愛
        """
        try:
            if not card_id or not user_id:
                return False
            from gacha.services.core.favorite_service import FavoriteService
            from database.postgresql.async_manager import AsyncPgManager
            if not hasattr(interaction.client, 'favorite_service'):
                logger.error('FavoriteService not found on bot instance (interaction.client).')
                return False
            if not hasattr(interaction.client, 'user_collection_repo'):
                logger.error('UserCollectionRepository not found on bot instance (interaction.client).')
                return False
            collection_repo: 'UserCollectionRepository' = interaction.client.user_collection_repo
            user_card = await collection_repo.get_user_card(user_id, card_id)
            is_favorite = user_card.is_favorite if user_card else False
            FavoriteComponent.update_button_state(button, is_favorite)
            return is_favorite
        except Exception as e:
            logger.error('檢查最愛狀態失敗: %s', e, exc_info=True)
            return False

    @staticmethod
    async def handle_favorite_toggle(interaction: discord.Interaction, view: discord.ui.View, user_id: int, card_id: int, button: discord.ui.Button) -> bool:
        """
        處理加入/取消最愛的點擊事件
        
        Args:
            interaction: Discord 交互對象
            view: 視圖對象
            user_id: 用戶ID
            card_id: 卡片ID
            button: 按鈕對象
            
        Returns:
            bool: 操作是否成功
        """
        # --- 重要：忽略傳入的 user_id 和 card_id，從 view 實例獲取當前狀態 ---
        if not hasattr(view, 'user') or not hasattr(view, 'current_card'):
            logger.error("[FavoriteComponent] View object is missing 'user' or 'current_card' attribute.")
            await interaction_utils.safe_send_message(interaction, '處理請求時發生錯誤：視圖狀態不完整。', ephemeral=True)
            return False

        owner_user_id = view.user.id
        current_user_card = view.current_card # 假設 view.current_card 返回 UserCard 或 None

        if current_user_card is None or not hasattr(current_user_card, 'card') or current_user_card.card is None:
            logger.warning("[FavoriteComponent] No current card available in the view for favorite toggle.")
            # 嘗試從 interaction message view 獲取？(不建議，但作為備用)
            # 或者直接提示錯誤
            await interaction_utils.safe_send_message(interaction, '當前沒有可操作的卡片。請嘗試刷新或重新打開視圖。', ephemeral=True)
            return False

        current_card_id = current_user_card.card.card_id
        # --- 使用 owner_user_id 和 current_card_id 進行後續操作 ---

        # 權限檢查使用從 view 獲取的 owner_user_id
        if not await interaction_utils.check_user_permission(interaction, owner_user_id, '只有卡片擁有者才能使用此按鈕'):
            return False

        await interaction_utils.safe_defer(interaction, ephemeral=True)

        favorite_service = None
        if hasattr(view, 'services') and view.services and hasattr(view.services, 'favorite'):
            favorite_service = view.services.favorite
        
        if not favorite_service:
            # 作為備選，嘗試從 client 上獲取，但優先使用 view.services
            if hasattr(interaction.client, 'favorite_service'):
                favorite_service = interaction.client.favorite_service
                logger.warning("[FavoriteComponent] Used favorite_service from interaction.client as fallback.")
            else:
                logger.error('[FavoriteComponent] FavoriteService not found on view.services or interaction.client.')
                await interaction_utils.safe_send_message(interaction, '處理最愛狀態時發生內部錯誤，服務未配置，請稍後再試。', ephemeral=True)
                return False
            
        try:
            # 使用從 view 獲取的 owner_user_id 和 current_card_id
            is_favorite = await favorite_service.toggle_favorite_card(owner_user_id, current_card_id)

            # 更新按鈕和視圖狀態 (使用 current_card_id)
            FavoriteComponent.update_button_state(button, is_favorite)
            FavoriteComponent._update_view_card_status(view, current_card_id, is_favorite)

            # 更新UI
            await FavoriteComponent._update_ui(interaction, view)
            
            return True
        except (CardNotFoundError, UserNotFoundError, DatabaseOperationError) as e:
            logger.warning(f"[FavoriteComponent] Operation failed with business error: {type(e).__name__}: {e}")
            await interaction_utils.safe_send_message(interaction, str(e), ephemeral=True)
            return False
        except GachaError as e: # 捕獲其他 Gacha 相關的業務異常
            logger.warning(f"[FavoriteComponent] Operation failed with GachaError: {type(e).__name__}: {e}")
            await interaction_utils.safe_send_message(interaction, f"操作失敗: {e}", ephemeral=True)
            return False
        except Exception as e:
            logger.error(f"[FavoriteComponent] Failed to toggle favorite state due to unexpected error: {type(e).__name__}: {e}", exc_info=True)
            await interaction_utils.safe_send_message(interaction, "更新最愛狀態時發生未知錯誤，請稍後再試。", ephemeral=True)
            return False

    @staticmethod
    def _update_view_card_status(view, card_id: int, is_favorite: bool) -> None:
        """更新視圖中卡片的最愛狀態"""
        if isinstance(view, FavoriteStateUpdatable):
            success = view.update_favorite_state(card_id, is_favorite)
        else:
            logger.warning('視圖類型%s未實現FavoriteStateUpdatable介面', type(view).__name__)

    @staticmethod
    async def _update_ui(interaction: discord.Interaction, view) -> None:
        """更新用戶界面"""
        try:
            embed_to_send = None
            if view.__class__.__name__ == 'MultiDrawView' and hasattr(view, 'get_combined_embed'):
                get_combined_embed_method = getattr(view, 'get_combined_embed')
                if callable(get_combined_embed_method):
                    embed_to_send = await get_combined_embed_method(interaction=interaction)
            if embed_to_send is None and hasattr(view, 'get_current_page_embed'):
                get_current_page_embed_method = getattr(view, 'get_current_page_embed')
                if callable(get_current_page_embed_method):
                    try:
                        embed_to_send = await get_current_page_embed_method(interaction=interaction)
                    except TypeError:
                        embed_to_send = await get_current_page_embed_method()
            if embed_to_send:
                await interaction_utils.safe_edit_message(interaction, embed=embed_to_send, view=view)
            else:
                logger.warning('View %s did not provide an embed for UI update in FavoriteComponent._update_ui. Updating view components only.', type(view).__name__)
                await interaction_utils.safe_edit_message(interaction, view=view)
        except Exception as e:
            logger.error('更新界面失敗: %s', e, exc_info=True)