import discord
from decimal import Decimal
from typing import Optional, Dict, Any, List
from utils.logger import logger

class StockDetailEmbedBuilder:

    def __init__(self, stock_data: Optional[Dict[str, Any]], asset_symbol_or_id: Any, aggregated_volume_7d: Optional[Dict[str, int]], recent_transactions_5: Optional[List[Dict[str, Any]]], chart_image_bytes: Optional[bytes], user_oil_balance: Optional[Decimal]=None, user_stock_quantity: Optional[int]=None):
        self.stock_data = stock_data
        self.asset_symbol_or_id = asset_symbol_or_id
        self.aggregated_volume_7d = aggregated_volume_7d
        self.recent_transactions_5 = recent_transactions_5
        self.chart_image_bytes = chart_image_bytes
        self.user_oil_balance = user_oil_balance
        self.user_stock_quantity = user_stock_quantity

    def build(self) -> discord.Embed:
        if not self.stock_data:
            return discord.Embed(title='錯誤', description=f'找不到股票 {self.asset_symbol_or_id} 的詳細信息。', color=discord.Color.red())
        s = self.stock_data
        price = Decimal(s['current_price'])
        embed_color = discord.Color.teal()
        trend_emoji_price = '💰'
        volatility_emoji = '📊'
        volume_emoji = '📈'
        transactions_emoji = '⏱️'
        chart_emoji = '📉'
        user_info_emoji = '👤'
        embed = discord.Embed(title=f"{s['asset_symbol']} - {s['asset_name']}", description=s['description'] or '暫無公司介紹。', color=embed_color)
        embed.add_field(name=f'{trend_emoji_price} 當前價格', value=f'**{price:.2f}** 油幣', inline=True)
        embed.add_field(name=f'{volatility_emoji} 基礎波動率', value=f"{s['base_volatility']:.3f}", inline=True)
        embed.add_field(name=f'{volatility_emoji} 波動放大因子', value=f"{s['volatility_factor']:.2f}", inline=True)
        total_shares_val = s.get('total_shares')
        if total_shares_val is not None:
            try:
                total_shares = int(total_shares_val)
                current_market_cap = price * total_shares
                size_category = 'N/A'
                if total_shares < 15000:
                    size_category = '微型股 (<1.5萬股)'
                elif 15000 <= total_shares < 40000:
                    size_category = '小型股 (1.5萬-4萬股)'
                elif 40000 <= total_shares < 70000:
                    size_category = '中型股 (4萬-7萬股)'
                elif 70000 <= total_shares <= 100000:
                    size_category = '大型股 (7萬-10萬股)'
                elif total_shares > 100000:
                    size_category = '超大型股 (>10萬股)'
                embed.add_field(name='📊 總股本', value=f'{total_shares:,} 股', inline=True)
                embed.add_field(name='🏦 當前市值', value=f'{current_market_cap:,.0f} 油幣', inline=True)
                embed.add_field(name='🔖 規模分類', value=size_category, inline=True)
                st_trigger_min_market_cap = Decimal(150000)
                st_critical_price_market_cap_text = 'N/A'
                if total_shares > 0:
                    st_critical_price_market_cap = st_trigger_min_market_cap / total_shares
                    st_critical_price_market_cap_text = f'**{st_critical_price_market_cap:.2f}** 油幣 (市值 < {st_trigger_min_market_cap:,.0f})'
                else:
                    st_critical_price_market_cap_text = '無法計算 (總股本為0)'
                st_critical_price_low = Decimal('1.5')
                st_risk_value = f'📉 市值型ST臨界股價: {st_critical_price_market_cap_text}\n📉 股價型ST: 持續低於或等於 **{st_critical_price_low:.2f}** 油幣'
                embed.add_field(name='⚠️ ST風險提示', value=st_risk_value, inline=False)
            except (ValueError, TypeError) as e:
                logger.warning('處理 total_shares (%s) 時出錯: %s. 股票: %s', total_shares_val, e, s.get('asset_symbol'))
                embed.add_field(name='📊 總股本', value='數據錯誤', inline=True)
                embed.add_field(name='🏦 當前市值', value='數據錯誤', inline=True)
                embed.add_field(name='🔖 規模分類', value='數據錯誤', inline=True)
                embed.add_field(name='⚠️ ST風險提示', value='數據錯誤，無法計算', inline=False)
        else:
            embed.add_field(name='📊 總股本', value='未知', inline=True)
            embed.add_field(name='🏦 當前市值', value='未知', inline=True)
            embed.add_field(name='🔖 規模分類', value='未知', inline=True)
            embed.add_field(name='⚠️ ST風險提示', value='總股本未知，無法計算', inline=False)
        if self.user_oil_balance is not None or self.user_stock_quantity is not None:
            if self.user_oil_balance is not None:
                embed.add_field(name=f'{user_info_emoji} 您的油幣餘額', value=f'{self.user_oil_balance:,.0f} 油幣', inline=True)
            if self.user_stock_quantity is not None:
                embed.add_field(name=f"{user_info_emoji} 您持有 {s['asset_symbol']}", value=f'{self.user_stock_quantity:,} 股', inline=True)
            if self.user_oil_balance is not None and self.user_stock_quantity is not None and (self.user_oil_balance is not None or self.user_stock_quantity is not None):
                user_info_fields_count = (1 if self.user_oil_balance is not None else 0) + (1 if self.user_stock_quantity is not None else 0)
                if user_info_fields_count == 2:
                    embed.add_field(name='\u200b', value='\u200b', inline=True)
                elif user_info_fields_count == 1 and len(embed.fields) % 3 == 1:
                    embed.add_field(name='\u200b', value='\u200b', inline=True)
                    embed.add_field(name='\u200b', value='\u200b', inline=True)
        if self.aggregated_volume_7d:
            buy_vol = self.aggregated_volume_7d.get('total_buy_volume', 0)
            sell_vol = self.aggregated_volume_7d.get('total_sell_volume', 0)
            volume_details = f'🟢 買入: {buy_vol:,} 股\n🔴 賣出: {sell_vol:,} 股'
            embed.add_field(name=f'{volume_emoji} 買賣量 (近7日)', value=volume_details, inline=False)
        else:
            embed.add_field(name=f'{volume_emoji} 買賣量 (近7日)', value='數據加載中或不可用...', inline=False)
        if self.recent_transactions_5:
            transactions_str_list = []
            for t in self.recent_transactions_5:
                direction_emoji = '🟢' if t['transaction_type'] == 'BUY' else '🔴'
                direction_text = '買入' if t['transaction_type'] == 'BUY' else '賣出'
                ts_dt = t.get('timestamp')
                ts_formatted = discord.utils.format_dt(ts_dt, style='R') if ts_dt else '未知時間'
                transactions_str_list.append(f"{direction_emoji} `{t.get('user_nickname', '未知用戶')}` {direction_text} `{t.get('quantity', 'N/A')}`股 @ `{Decimal(t.get('price_per_unit', 0)):.2f}` ({ts_formatted})")
            embed.add_field(name=f'{transactions_emoji} 近期成交 (最近5筆)', value='\n'.join(transactions_str_list) if transactions_str_list else '暫無近期交易記錄。', inline=False)
        else:
            embed.add_field(name=f'{transactions_emoji} 近期成交 (最近5筆)', value='數據加載中或不可用...', inline=False)
        if self.chart_image_bytes:
            embed.set_image(url='attachment://price_chart_7d.png')
        else:
            embed.add_field(name=f'{chart_emoji} 價格趨勢 (近7日)', value='圖表生成失敗或無數據。', inline=False)
        last_updated_dt = s.get('last_updated')
        last_updated_formatted = discord.utils.format_dt(last_updated_dt, style='R') if last_updated_dt else '未知'
        embed.set_footer(text=f'數據更新於: {last_updated_formatted} | Roo Bot 技術支持')
        return embed