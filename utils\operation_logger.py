"""
操作日誌記錄器模組
管理用戶操作消息的發送和更新
"""
import discord
import time
import asyncio
from utils.logger import logger

class OperationLogger:
    """操作日誌記錄器，管理用戶操作消息"""
    _user_messages = {}
    _pending_interactions = set()
    _temp_message_view_types = {'ContinueEnhanceButtonView', 'ConfirmationView'}
    _clear_on_command = {'sw', 'mw'}

    @classmethod
    async def log_operation(cls, interaction, content=None, embed=None, view=None, ephemeral=True):
        """記錄一條操作消息

        如果用戶已有消息，則編輯該消息
        如果用戶沒有消息，則發送新消息

        Args:
            interaction: Discord互動對象
            content: 文本內容(可選)
            embed: 嵌入消息(可選)
            view: 交互視圖(可選)
            ephemeral: 是否僅對用戶可見
        """
        start_time = time.time()
        user_id = int(interaction.user.id)
        interaction_id = str(interaction.id)
        if hasattr(interaction, 'command') and interaction.command:
            command_name = interaction.command.name
            if command_name in cls._clear_on_command:
                await cls.clear_operation_message(user_id)
        if interaction_id in cls._pending_interactions:
            return
        cls._pending_interactions.add(interaction_id)
        try:
            if not content and (not embed):
                content = '操作已完成'
            view_name = view.__class__.__name__ if view and hasattr(view, '__class__') else ''
            is_temp_message = view is None or view_name in cls._temp_message_view_types
            has_existing_message = user_id in cls._user_messages
            if is_temp_message and has_existing_message:
                edit_success = await cls._try_edit_existing_message(user_id, content, embed, view, view_name)
                elapsed_time = (time.time() - start_time) * 1000
                if elapsed_time > 100:
                    logger.debug('編輯臨時訊息耗時: %sms', elapsed_time)
                if edit_success:
                    return
            actual_view = view if view is not None else discord.ui.View()
            if interaction.response.is_done():
                message = await interaction.followup.send(content=content, embed=embed, view=actual_view, ephemeral=ephemeral)
                if is_temp_message and message:
                    cls._user_messages[user_id] = message
            else:
                await interaction.response.send_message(content=content, embed=embed, view=actual_view, ephemeral=ephemeral)
                if is_temp_message:
                    await asyncio.sleep(0.05)
                    try:
                        if hasattr(interaction, 'original_response'):
                            cls._user_messages[user_id] = await interaction.original_response()
                    except Exception as e:
                        logger.debug('獲取原始訊息時出錯: %s', str(e))
                        pass
        except Exception as e:
            logger.error('記錄操作日誌時出錯: %s', str(e))
        finally:
            cls._pending_interactions.discard(interaction_id)
            elapsed_time = (time.time() - start_time) * 1000
            if elapsed_time > 200:
                logger.debug('記錄操作完成，總耗時: %sms', elapsed_time)

    @classmethod
    async def _try_edit_existing_message(cls, user_id, content, embed, view, view_name):
        """嘗試編輯現有訊息，抽取為單獨方法減少主方法複雜度"""
        try:
            message = cls._user_messages[user_id]
            if view_name in cls._temp_message_view_types:
                await message.edit(content=content, embed=embed, view=view)
            else:
                await message.edit(content=content, embed=embed)
            return True
        except discord.NotFound:
            cls._user_messages.pop(user_id, None)
        except Exception as e:
            logger.error('編輯操作日誌消息時出錯: %s', str(e))
            cls._user_messages.pop(user_id, None)
        return False

    @classmethod
    async def clear_operation_message(cls, user_id):
        """清除用戶操作消息

        Args:
            user_id: 用戶ID
        """
        message = cls._user_messages.pop(user_id, None)
        if message:
            try:
                await message.delete()
            except Exception as e:
                logger.debug('清除操作日誌消息時出錯: %s', str(e))