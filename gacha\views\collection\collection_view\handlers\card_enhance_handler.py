from typing import TYPE_CHECKING, Any, Dict
import discord
from gacha.models.models import Card
from gacha.views.collection.collection_view.enhance_confirm_view import EnhanceConfirmView
from utils.logger import logger
from gacha.exceptions import CardNotFoundError, InsufficientBalanceError, GachaRuntimeError
from .base_handler import BaseHandler
if TYPE_CHECKING:
    from gacha.views.collection.collection_view.card_view import CollectionView
    from gacha.services.core.star_enhancement_service import StarEnhancementService
    from gacha.services.core.economy_service import EconomyService

class CardEnhanceHandler(BaseHandler):
    """卡片升星處理器

    採用統一的服務訪問模式，所有服務都通過 view 統一訪問，
    保持系統架構的一致性和可維護性。
    """

    def __init__(self, view: 'CollectionView'):
        super().__init__(view)

    @property
    def star_enhancement_service(self) -> 'StarEnhancementService':
        """統一的星級提升服務訪問接口"""
        return self.view.services.star_enhancement

    @property
    def economy_service(self) -> 'EconomyService':
        """統一的經濟服務訪問接口"""
        return self.view.services.economy

    async def enhance_option_callback(self, interaction: discord.Interaction):
        """升星按鈕回調函數"""
        if not await self.view.interaction_check(interaction):
            return
        if not self.view.current_card:
            await interaction.response.send_message('沒有選定的卡片。', ephemeral=True)
            return
        if not await self.check_cards_available(interaction):
            return
        await self._check_and_show_enhancement_confirmation(interaction)

    async def _check_and_show_enhancement_confirmation(self, interaction: discord.Interaction):
        """檢查升星可行性並顯示確認界面"""
        card_obj = self.current_card
        if not card_obj:
            logger.warning('升星處理器中當前卡片為空')
            await self._send_error_message(interaction, '錯誤：找不到當前卡片。')
            return
            
        card_id = card_obj.card.card_id
        
        try:
            # 檢查升星可能性 - 如果不可能會拋出異常
            enhancement_info = await self.star_enhancement_service.check_enhancement_possibility(self.view.user_id, card_id)
            
            # 如果沒有拋出異常，則顯示確認界面
            await self._show_enhancement_confirmation(interaction, card_obj, enhancement_info)
            
        except CardNotFoundError:
            await self._send_error_message(interaction, '找不到指定卡片')
        except InsufficientBalanceError as e:
            await self._send_error_message(interaction, f'油幣不足：{str(e)}')
        except GachaRuntimeError as e:
            await self._send_error_message(interaction, str(e))
        except Exception as e:
            logger.error('檢查升星可能性時發生未知錯誤: %s', str(e), exc_info=True)
            await self._send_error_message(interaction, f'檢查升星可能性時發生錯誤: {str(e)}')

    async def _show_enhancement_confirmation(self, interaction: discord.Interaction, card_obj: Any, enhancement_info: Dict[str, Any]):
        """顯示升星確認界面"""
        costs = enhancement_info.get('costs', {})
        success_rate = enhancement_info.get('success_rate', 0)
        current_stars = enhancement_info.get('current_star_level', 0)
        self.view.original_interaction = interaction
        initial_confirm_embed = self._create_enhancement_confirmation_embed(card_obj.card.name, current_stars, costs, success_rate)
        enhance_confirm_view = EnhanceConfirmView(original_interaction=interaction, user_id=self.view.user_id, card_id=card_obj.card.card_id, collection_view=self.view, initial_enhancement_info=enhancement_info)
        await interaction.response.send_message(embed=initial_confirm_embed, view=enhance_confirm_view, ephemeral=True)

    def _create_enhancement_confirmation_embed(self, card_name: str, current_stars: int, costs: Dict[str, int], success_rate: float) -> discord.Embed:
        """創建升星確認嵌入"""
        embed = discord.Embed(title=f'升星確認 {current_stars}★ → {current_stars + 1}★', description=f'你確定要嘗試將 **{card_name}** 升星嗎？', color=discord.Color.blue())
        embed.add_field(name='所需油幣', value=f"{costs.get('oil', 0)} <:oil1:1368446294594424872>", inline=True)
        embed.add_field(name='所需重複卡', value=f"{costs.get('duplicates', 0)} 張", inline=True)
        embed.add_field(name='成功率', value=f'{success_rate:.1f}%', inline=True)
        embed.set_footer(text='點擊確認消耗資源嘗試升星')
        return embed