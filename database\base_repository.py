"""
數據庫存儲庫基礎類 (Asyncpg 版本)
提供所有存儲庫通用的非同步數據庫操作
"""

import json
import time
# import re # No longer needed for _convert_placeholders
import asyncpg
from datetime import datetime
from typing import Any, Dict, Generic, List, Optional, Tuple, TypeVar, Union, Sequence
from utils.logger import logger # Use logger from utils

from database.postgresql.async_manager import AsyncPgManager # Corrected import path

T = TypeVar("T")

class BaseRepository(Generic[T]):
    """存儲庫基礎類，實現通用的非同步數據庫操作和批量處理功能"""

    def __init__(self, pool: asyncpg.Pool):
        """初始化非同步數據庫存儲庫

        參數:
            pool: asyncpg 連接池實例
        """
        if pool is None:
             # 嘗試從管理器獲取
             try:
                 self.pool = AsyncPgManager.get_pool()
             except RuntimeError as e:
                 logger.error("無法初始化 BaseRepository：asyncpg 連接池未提供也無法從管理器獲取。") # Use utils logger
                 raise e
        else:
            self.pool = pool
        logger.debug(f"BaseRepository initialized with pool: {self.pool}") # Use utils logger

    async def _execute(
        self, query: str, params: Optional[Sequence] = None, connection: Optional[asyncpg.Connection] = None
    ) -> str:
        """執行 INSERT, UPDATE, DELETE 等不返回多行的查詢。
           如果提供了 connection，則在該連接上執行，否則從連接池獲取。

        參數:
            query: SQL 查詢 (應使用 $1, $2, ... 佔位符)
            params: 查詢參數
            connection: (可選) 要使用的 asyncpg 連接

        返回:
            str: asyncpg 返回的狀態信息 (例如 "INSERT 0 1")
            
        Raises:
            DatabaseIntegrityError: 當資料庫完整性約束被違反時
            DatabaseOperationError: 當其他資料庫操作錯誤發生時
        """
        params = params or []
        conn_to_use = connection # 優先使用傳入的連接

        try:
            if conn_to_use:
                # logger.debug(f"Executing query on provided connection: {query} with params: {params}")
                status = await conn_to_use.execute(query, *params)
                # logger.debug(f"Query executed on provided connection. Status: {status}")
                return status
            else:
                # logger.debug(f"Acquiring connection from pool to execute query: {query} with params: {params}")
                async with self.pool.acquire() as pool_conn:
                    status = await pool_conn.execute(query, *params)
                    # logger.debug(f"Query executed on acquired connection. Status: {status}")
                    return status
        except asyncpg.UniqueViolationError as e:
            logger.error(f"唯一約束違反: {str(e)} - 查詢: {query} - 參數: {params}", exc_info=True)
            from gacha.exceptions import DatabaseIntegrityError
            raise DatabaseIntegrityError(f"唯一約束違反: {e}")
        except asyncpg.ForeignKeyViolationError as e:
            logger.error(f"外鍵約束違反: {str(e)} - 查詢: {query} - 參數: {params}", exc_info=True)
            from gacha.exceptions import DatabaseIntegrityError
            raise DatabaseIntegrityError(f"外鍵約束違反: {e}")
        except asyncpg.PostgresError as e:
            logger.error(f"執行查詢失敗: {str(e)} - 查詢: {query} - 參數: {params}", exc_info=True)
            from gacha.exceptions import DatabaseOperationError
            raise DatabaseOperationError(f"資料庫操作失敗: {e}", original_exception=e)

    async def _fetch(
        self, query: str, params: Optional[Sequence] = None, connection: Optional[asyncpg.Connection] = None
    ) -> List[asyncpg.Record]:
        """執行 SELECT 查詢並返回所有行。
           如果提供了 connection，則在該連接上執行，否則從連接池獲取。

        參數:
            query: SQL 查詢 (應使用 $1, $2, ... 佔位符)
            params: 查詢參數
            connection: (可選) 要使用的 asyncpg 連接

        返回:
            List[asyncpg.Record]: 查詢結果列表，每行是一個 Record 對象 (類似字典)
            
        Raises:
            DatabaseOperationError: 當資料庫操作錯誤發生時
        """
        params = params or []
        conn_to_use = connection

        try:
            if conn_to_use:
                # logger.debug(f"Fetching query on provided connection: {query} with params: {params}")
                results = await conn_to_use.fetch(query, *params)
                # logger.debug(f"Query fetched {len(results)} records on provided connection.")
                return results
            else:
                # logger.debug(f"Acquiring connection from pool to fetch query: {query} with params: {params}")
                async with self.pool.acquire() as pool_conn:
                    results = await pool_conn.fetch(query, *params)
                    # logger.debug(f"Query fetched {len(results)} records on acquired connection.")
                    return results
        except asyncpg.PostgresError as e:
            logger.error(f"獲取查詢失敗: {str(e)} - 查詢: {query} - 參數: {params}", exc_info=True)
            from gacha.exceptions import DatabaseOperationError
            raise DatabaseOperationError(f"資料庫查詢失敗: {e}", original_exception=e)

    async def _fetchrow(
        self, query: str, params: Optional[Sequence] = None, connection: Optional[asyncpg.Connection] = None
    ) -> Optional[asyncpg.Record]:
        """執行 SELECT 查詢並返回第一行。
           如果提供了 connection，則在該連接上執行，否則從連接池獲取。

        參數:
            query: SQL 查詢 (應使用 $1, $2, ... 佔位符)
            params: 查詢參數
            connection: (可選) 要使用的 asyncpg 連接

        返回:
            Optional[asyncpg.Record]: 查詢結果的第一行，如果沒有結果則返回 None
            
        Raises:
            DatabaseOperationError: 當資料庫操作錯誤發生時
        """
        params = params or []
        conn_to_use = connection

        try:
            if conn_to_use:
                # logger.debug(f"Fetching row on provided connection: {query} with params: {params}")
                result = await conn_to_use.fetchrow(query, *params)
                # logger.debug(f"Query fetched row on provided connection: {'Found' if result else 'Not Found'}")
                return result
            else:
                # logger.debug(f"Acquiring connection from pool to fetch row: {query} with params: {params}")
                async with self.pool.acquire() as pool_conn:
                    result = await pool_conn.fetchrow(query, *params)
                    # logger.debug(f"Query fetched row on acquired connection: {'Found' if result else 'Not Found'}")
                    return result
        except asyncpg.PostgresError as e:
            logger.error(f"獲取單行查詢失敗: {str(e)} - 查詢: {query} - 參數: {params}", exc_info=True)
            from gacha.exceptions import DatabaseOperationError
            raise DatabaseOperationError(f"資料庫查詢失敗: {e}", original_exception=e)

    async def _fetchval(
        self, query: str, params: Optional[Sequence] = None, column: int = 0, connection: Optional[asyncpg.Connection] = None
    ) -> Any:
        """執行 SELECT 查詢並返回第一行的指定列的值。
           如果提供了 connection，則在該連接上執行，否則從連接池獲取。

        參數:
            query: SQL 查詢 (應使用 $1, $2, ... 佔位符)
            params: 查詢參數
            column: 要返回的列索引 (默認為 0)
            connection: (可選) 要使用的 asyncpg 連接

        返回:
            Any: 查詢結果第一行指定列的值，如果沒有結果則返回 None
            
        Raises:
            DatabaseOperationError: 當資料庫操作錯誤發生時
        """
        params = params or []
        conn_to_use = connection

        try:
            if conn_to_use:
                # logger.debug(f"Fetching value on provided connection: {query} with params: {params}")
                value = await conn_to_use.fetchval(query, *params, column=column)
                # logger.debug(f"Query fetched value on provided connection: {value}")
                return value
            else:
                # logger.debug(f"Acquiring connection from pool to fetch value: {query} with params: {params}")
                async with self.pool.acquire() as pool_conn:
                    value = await pool_conn.fetchval(query, *params, column=column)
                    # logger.debug(f"Query fetched value on acquired connection: {value}")
                    return value
        except asyncpg.PostgresError as e:
            logger.error(f"獲取值查詢失敗: {str(e)} - 查詢: {query} - 參數: {params}", exc_info=True)
            from gacha.exceptions import DatabaseOperationError
            raise DatabaseOperationError(f"資料庫查詢失敗: {e}", original_exception=e)

    # --- 重構後的批量操作 ---

    async def execute_batch(
        self,
        query: str,
        params_list: List[tuple],
        batch_size: int = 100
    ) -> None:
        """批量執行SQL語句 (INSERT, UPDATE, DELETE)，自動分批處理。
           查詢應使用 $1, $2, ... 佔位符。

        注意：此方法不返回結果，主要用於批量執行。
             如果需要處理返回結果 (如 RETURNING)，請使用其他方法或自定義邏輯。
        """
        if not params_list:
            return

        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction(): # 在事務中執行批量操作
                    for i in range(0, len(params_list), batch_size):
                        batch = params_list[i : i + batch_size]
                        await conn.executemany(query, batch)
                        logger.debug(f"Executed batch of {len(batch)} statements.") # Use utils logger
        except Exception as e:
            logger.error(f"批量執行失敗: {str(e)} - 查詢: {query}", exc_info=True) # Use utils logger
            raise

    async def bulk_insert(
        self, table_name: str, columns: List[str], data: List[Tuple], return_ids: bool = False, id_column: str = 'id'
    ) -> Optional[List[Any]]:
        """非同步批量插入數據

        參數:
            table_name: 表名
            columns: 列名
            data: 數據列表，每個元素是一個包含對應列值的元組
            return_ids: 是否返回插入數據的ID列表 (需要表中有名為 id_column 的主鍵)
            id_column: 用於 RETURNING 的 ID 列名，默認為 'id'

        返回:
            Optional[List[Any]]: 如果 return_ids 為 True，返回插入數據的ID列表，否則返回 None
        """
        if not data:
            return [] if return_ids else None

        column_str = ", ".join(columns)
        placeholders = ", ".join([f"${i+1}" for i in range(len(columns))])
        query = f"INSERT INTO {table_name} ({column_str}) VALUES ({placeholders})"

        if return_ids:
            query += f" RETURNING {id_column}"

        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    if return_ids:
                        inserted_ids = []
                        stmt = await conn.prepare(query)
                        for row_data in data:
                            inserted_id = await stmt.fetchval(*row_data)
                            if inserted_id is not None:
                                inserted_ids.append(inserted_id)
                        return inserted_ids
                    else:
                        await conn.copy_records_to_table(
                            table_name,
                            records=data,
                            columns=columns
                        )
                        return None
        except Exception as e:
            logger.error(f"批量插入失敗: {str(e)} - 表: {table_name}", exc_info=True) # Use utils logger
            raise

    async def bulk_update(
        self, table: str, id_column: str, columns: List[str], values_list: List[Tuple]
    ) -> int:
        """非同步批量更新數據 (使用逐條執行模擬)

        參數:
            table: 表名
            id_column: ID列名
            columns: 要更新的列
            values_list: 值列表，每個元組包含要更新的值，最後一個值為ID

        返回:
            int: 成功更新的總行數
        """
        if not values_list:
            return 0

        set_clause = ", ".join([f"{col} = ${i+1}" for i, col in enumerate(columns)])
        id_placeholder = f"${len(columns) + 1}"
        query = f"UPDATE {table} SET {set_clause} WHERE {id_column} = {id_placeholder}"

        total_updated_rows = 0
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    stmt = await conn.prepare(query)
                    for values in values_list:
                        status = await stmt.fetchval(*values)
                        if status and status.startswith("UPDATE"):
                            try:
                                count = int(status.split()[-1])
                                total_updated_rows += count
                            except (ValueError, IndexError):
                                logger.warning(f"無法解析 UPDATE 狀態: {status}") # Use utils logger
                                total_updated_rows += 1
            return total_updated_rows
        except Exception as e:
            logger.error(f"批量更新失敗: {str(e)} - 表: {table}", exc_info=True) # Use utils logger
            raise

    async def bulk_delete(self, table: str, id_column: str, ids: List[Any]) -> int:
        """非同步批量刪除數據

        參數:
            table: 表名
            id_column: ID列名
            ids: ID列表

        返回:
            int: 刪除的行數 (基於 asyncpg execute 返回的狀態)
        """
        if not ids:
            return 0

        query = f"DELETE FROM {table} WHERE {id_column} = ANY($1::integer[])"

        try:
            status = await self._execute(query, [ids])
            if status and status.startswith("DELETE"):
                try:
                    return int(status.split()[-1])
                except (ValueError, IndexError):
                    logger.warning(f"無法解析 DELETE 狀態: {status}") # Use utils logger
                    return 0
            return 0
        except Exception as e:
            if "cannot cast type" in str(e).lower():
                 logger.warning(f"嘗試不指定類型進行批量刪除: {table}.{id_column}") # Use utils logger
                 query_fallback = f"DELETE FROM {table} WHERE {id_column} = ANY($1)"
                 try:
                     status = await self._execute(query_fallback, [ids])
                     if status and status.startswith("DELETE"):
                         try:
                             return int(status.split()[-1])
                         except (ValueError, IndexError):
                             logger.warning(f"無法解析 DELETE 狀態 (fallback): {status}") # Use utils logger
                             return 0
                     return 0
                 except Exception as fallback_e:
                     logger.error(f"批量刪除失敗 (fallback): {str(fallback_e)} - 表: {table}", exc_info=True) # Use utils logger
                     raise fallback_e
            else:
                logger.error(f"批量刪除失敗: {str(e)} - 表: {table}", exc_info=True) # Use utils logger
                raise

    async def batch_upsert(
        self, table: str, records: List[Dict[str, Any]], key_field: str
    ) -> Tuple[int, int]:
        """非同步批量插入或更新（upsert）數據

        參數:
            table: 表名
            records: 記錄字典列表
            key_field: 鍵字段 (用於 ON CONFLICT)

        返回:
            Tuple[int, int]: (插入的行數, 更新的行數) - 注意：asyncpg execute 不直接返回此信息，此實現返回 (成功執行的語句數, 0)
        """
        if not records:
            return (0, 0)

        first_record = records[0]
        all_fields = list(first_record.keys())
        if key_field not in all_fields:
            raise ValueError(f"鍵字段 '{key_field}' 不在記錄的字段列表中")
        if not all(set(record.keys()) == set(all_fields) for record in records):
             raise ValueError("所有記錄必須有相同的字段")

        update_fields = [f for f in all_fields if f != key_field]
        if not update_fields:
            raise ValueError("需要至少一個非鍵字段進行更新")

        field_list = ", ".join(all_fields)
        placeholders = ", ".join([f"${i+1}" for i in range(len(all_fields))])
        update_clause = ", ".join([f"{field} = EXCLUDED.{field}" for field in update_fields])

        query = f"""
            INSERT INTO {table} ({field_list})
            VALUES ({placeholders})
            ON CONFLICT ({key_field})
            DO UPDATE SET {update_clause}
        """

        params_list = [tuple(record.get(field) for field in all_fields) for record in records]

        executed_count = 0
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    await conn.executemany(query, params_list)
                    executed_count = len(params_list)
            return (executed_count, 0)
        except Exception as e:
            logger.error(f"批量 Upsert 失敗: {str(e)} - 表: {table}", exc_info=True) # Use utils logger
            raise

    # --- 重構後的輔助方法 ---

    async def get_by_field(
        self, table: str, field_name: str, field_value: Any
    ) -> Optional[Dict]:
        """通過指定欄位獲取單條記錄 (非同步)"""
        query = f"SELECT * FROM {table} WHERE {field_name} = $1"
        result = await self._fetchrow(query, (field_value,))
        return dict(result) if result else None

    async def get_list_by_field(
        self,
        table: str,
        field_name: str,
        field_value: Any,
        order_by: Optional[str] = None,
        order: str = "ASC",
        limit: Optional[int] = None,
        offset: Optional[int] = None,
    ) -> List[Dict]:
        """通過指定欄位獲取多條記錄 (非同步)"""
        query = f"SELECT * FROM {table} WHERE {field_name} = $1"
        params = [field_value]

        if order_by:
            query += f" ORDER BY {order_by} {order}"

        param_index = 1
        if limit is not None:
            param_index += 1
            query += f" LIMIT $${param_index}"
            params.append(limit)
            if offset is not None:
                param_index += 1
                query += f" OFFSET $${param_index}"
                params.append(offset)

        results = await self._fetch(query, params)
        return [dict(row) for row in results]

    async def update_by_field(
        self, table: str, field_name: str, field_value: Any, data: Dict[str, Any]
    ) -> int:
        """通過指定欄位更新記錄 (非同步)"""
        if not data:
            return 0

        set_items = []
        values = []
        param_index = 1
        for k, v in data.items():
            set_items.append(f"{k} = $${param_index}")
            values.append(v)
            param_index += 1

        set_clause = ", ".join(set_items)
        where_placeholder = f"${param_index}"
        values.append(field_value)

        query = f"UPDATE {table} SET {set_clause} WHERE {field_name} = $${param_index}"
        status = await self._execute(query, values)
        if status and status.startswith("UPDATE"):
            try:
                return int(status.split()[-1])
            except (ValueError, IndexError):
                logger.warning(f"無法解析 UPDATE 狀態: {status}") # Use utils logger
                return 0
        return 0

    async def insert_record(
        self, table: str, data: Dict[str, Any], return_id: bool = True, id_column: str = 'id'
    ) -> Union[int, bool, None]:
        """插入新記錄 (非同步)"""
        if not data:
            return False

        keys = list(data.keys())
        values_placeholders = ", ".join([f"${i+1}" for i in range(len(keys))])
        keys_str = ", ".join(keys)
        values = list(data.values())

        query = f"INSERT INTO {table} ({keys_str}) VALUES ({values_placeholders})"

        if return_id:
            query += f" RETURNING {id_column}"
            inserted_id = await self._fetchval(query, values)
            return inserted_id
        else:
            status = await self._execute(query, values)
            return status == "INSERT 0 1"

    async def delete_by_field(self, table: str, field_name: str, field_value: Any) -> int:
        """通過指定欄位刪除記錄 (非同步)"""
        query = f"DELETE FROM {table} WHERE {field_name} = $1"
        status = await self._execute(query, (field_value,))
        if status and status.startswith("DELETE"):
            try:
                return int(status.split()[-1])
            except (ValueError, IndexError):
                logger.warning(f"無法解析 DELETE 狀態: {status}") # Use utils logger
                return 0
        return 0

    async def join_query(
        self,
        tables: List[str],
        join_conditions: List[str],
        fields: List[str] = None,
        where_clause: str = None,
        params: tuple = None,
        order_by: Optional[str] = None,
        order: str = "ASC",
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        fetch_all: bool = True
    ) -> Union[Dict, List[Dict], None]:
        """動態構建並執行 JOIN 查詢。

        參數:
            tables: 要 JOIN 的表名列表 (例如 ["users", "orders"])
            join_conditions: JOIN 條件列表 (例如 ["users.id = orders.user_id"])
            fields: 要選擇的欄位列表 (例如 ["users.name", "orders.item"])，默認為 "*"
            where_clause: WHERE 子句 (例如 "users.status = $1 AND orders.amount > $2")，使用 $N 佔位符
            params: 對應 where_clause 中佔位符的參數元組
            order_by: 排序欄位 (例如 "users.name")
            order: 排序順序 ("ASC" 或 "DESC")
            limit: 返回記錄數限制
            offset: 返回記錄數偏移
            fetch_all: True 返回所有匹配行 (List[Dict])，False 返回第一行 (Dict)

        返回:
            Union[Dict, List[Dict], None]: 查詢結果
        """
        if not tables:
            raise ValueError("至少需要一個表來執行查詢")

        select_fields = ", ".join(fields) if fields else "*"
        query = f"SELECT {select_fields} FROM {tables[0]}"

        if len(tables) > 1:
            if not join_conditions or len(join_conditions) != len(tables) - 1:
                raise ValueError("JOIN 表的數量與 JOIN 條件的數量不匹配")
            for i in range(len(tables) - 1):
                query += f" JOIN {tables[i+1]} ON {join_conditions[i]}"

        if where_clause:
            query += f" WHERE {where_clause}"

        if order_by:
            query += f" ORDER BY {order_by} {order.upper()}"

        if limit is not None:
            param_idx_start = (len(params) if params else 0) + 1
            
            query += f" LIMIT $${param_idx_start}"
            current_params = list(params) if params else []
            current_params.append(limit)
            params = tuple(current_params)

            if offset is not None:
                query += f" OFFSET $${param_idx_start + 1}"
                current_params.append(offset)
                params = tuple(current_params)
        
        if fetch_all:
            rows = await self._fetch(query, params)
            return [dict(row) for row in rows] if rows else []
        else:
            row = await self._fetchrow(query, params)
            return dict(row) if row else None

    async def bulk_delete_by_criteria(
        self,
        table: str,
        criteria: Dict[str, Any],
        operator: str = "AND"
    ) -> int:
        """按複雜條件批量刪除記錄 (非同步)"""
        if not criteria:
            return 0

        conditions = []
        params = []
        param_index = 1

        for field, value in criteria.items():
            placeholder = f"${param_index}"
            param_index += 1

            if field.endswith("_lt"):
                real_field = field[:-3]
                conditions.append(f"{real_field} < {placeholder}")
                params.append(value)
            elif field.endswith("_lte"):
                real_field = field[:-4]
                conditions.append(f"{real_field} <= {placeholder}")
                params.append(value)
            elif field.endswith("_gt"):
                real_field = field[:-3]
                conditions.append(f"{real_field} > {placeholder}")
                params.append(value)
            elif field.endswith("_gte"):
                real_field = field[:-4]
                conditions.append(f"{real_field} >= {placeholder}")
                params.append(value)
            elif field.endswith("_in"):
                real_field = field[:-3]
                conditions.append(f"{real_field} = ANY({placeholder})")
                params.append(value if isinstance(value, (list, tuple)) else [value])
            elif field.endswith("_like"):
                real_field = field[:-5]
                conditions.append(f"{real_field} LIKE {placeholder}")
                params.append(value)
            elif field.endswith("_is_null"):
                real_field = field[:-8]
                if value:
                    conditions.append(f"{real_field} IS NULL")
                else:
                    conditions.append(f"{real_field} IS NOT NULL")
                param_index -= 1
            else:
                conditions.append(f"{field} = {placeholder}")
                params.append(value)

        where_clause = f" {operator} ".join(conditions)
        query = f"DELETE FROM {table} WHERE {where_clause}"

        status = await self._execute(query, params)
        if status and status.startswith("DELETE"):
            try:
                return int(status.split()[-1])
            except (ValueError, IndexError):
                logger.warning(f"無法解析 DELETE 狀態: {status}") # Use utils logger
                return 0
        return 0
