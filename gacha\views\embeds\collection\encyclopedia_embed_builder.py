# -*- coding: utf-8 -*-
"""
Gacha系統全圖鑑Embed構建器
"""

from datetime import datetime
from typing import Any, Dict, Optional
from decimal import Decimal

import discord

from gacha.models.models import Card
from gacha.constants import RarityLevel  # 導入 RarityLevel

# from gacha.pool_config import PoolConfig # --- REMOVED
from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder
from gacha.views.utils import format_oil  # 雖然會被取代，但暫時保留以防萬一
from gacha.views import utils as view_utils

# from gacha import config as gacha_config # --- REMOVED (Unused)
# from gacha import constants as gacha_constants # --- REMOVED
from gacha.app_config import config_service  # <--- CHANGED


class EncyclopediaEmbedBuilder(BaseEmbedBuilder):
    """構建全圖鑑嵌入消息的類"""

    def __init__(
        self,
        card_data: Optional[Dict[str, Any]],
        page: int,
        total_pages: int,
        total_cards: int,
        owner_count: int = 0,
        dynamic_price_details: Optional[Dict[str, Any]] = None,
    ):
        """初始化全圖鑑Embed構建器

        參數:
            card_data: 卡片數據，如果沒有卡片則為 None
            page: 當前頁碼
            total_pages: 總頁數
            total_cards: 總卡片數
            owner_count: 該卡片的擁有者數量
            dynamic_price_details: 動態計算的價格詳情
        """
        encyclopedia_data = {
            "card_data": card_data,
            "page": page,
            "total_pages": total_pages,
            "total_cards": total_cards,
            "owner_count": owner_count,
            "dynamic_price_details": dynamic_price_details,
        }
        # EncyclopediaEmbedBuilder 目前不直接處理 interaction
        super().__init__(data=encyclopedia_data)
        self.card_data = card_data
        self.page = page
        self.total_pages = total_pages
        self.total_cards = total_cards
        self.owner_count = owner_count
        self.dynamic_price_details = dynamic_price_details

    def build_embed(self) -> discord.Embed:
        """構建全圖鑑嵌入消息

        返回:
            discord.Embed: 全圖鑑嵌入消息
        """
        page_info = (
            f"頁面: {self.page}/{self.total_pages} | 總卡片數: {self.total_cards}"
        )

        if not self.card_data:
            embed = self._create_base_embed(  # MODIFIED: _initialize_embed to _create_base_embed
                title="全圖鑑",
                description="沒有找到卡片。",
                color=discord.Color.light_grey(),
            )
            embed.set_footer(text=page_info)
            return embed

        card_data = self.card_data

        card_id = card_data.get("card_id")
        card_name = card_data.get("name", "未知卡片")
        card_series = card_data.get("series", "未知系列")
        card_rarity_val = card_data.get("rarity")
        # 將 int 轉換為 RarityLevel Enum
        card_rarity_enum: Optional[RarityLevel] = (RarityLevel(
            int(card_rarity_val)) if card_rarity_val is not None else None)
        card_pool = card_data.get("pool_type", "main")
        card_image = card_data.get("image_url")

        rarity_color = (
            view_utils.get_rarity_color(card_rarity_enum, card_pool)
            if card_rarity_enum is not None
            else discord.Color.light_grey()
        )

        sell_price_display_text = "價格計算中"  # Default text
        price_label = "市場價"

        # Use current_market_sell_price from card_data
        # The key in card_data should be 'current_market_sell_price' after repo
        # changes
        stored_price_from_data = card_data.get("current_market_sell_price")

        if stored_price_from_data is not None:
            stored_price = stored_price_from_data
            if not isinstance(stored_price, Decimal):
                try:
                    stored_price = Decimal(str(stored_price))
                except BaseException:
                    stored_price = None  # Fallback if conversion fails

            if stored_price is not None:
                sell_price_display_text = (
                    f"`{int(stored_price)}` {config_service.get_oil_emoji()}"
                )
            # Trend symbol is not directly available with stored price.
        else:
            # If current_market_sell_price is None in DB, it will be None here.
            # sell_price_display_text remains "價格計算中"
            # Optionally, could show base price as a further fallback if desired,
            # but sticking to "價格計算中" for missing stored market price.
            pass

        display_rarity = (
            view_utils.get_user_friendly_rarity_name(card_rarity_enum)
            if card_rarity_enum is not None
            else "未知"
        )
        pool_display_text = config_service.get_config('gacha_core_settings.pool_type_prefixes').get(
            card_pool, ""
        )  # Use prefix as per user feedback

        highest_star_user_id = card_data.get("highest_star_user_id")
        highest_star_level = card_data.get("highest_star_level", 0)
        highest_star_nickname = card_data.get("nickname", "無玩家")
        achieved_at = card_data.get("achieved_at")

        custom_description = card_data.get("custom_description")
        description_user_id = card_data.get("description_user_id")

        rarity_emoji = (
            view_utils.get_encyclopedia_rarity_emoji(
                card_rarity_enum,
                card_pool) if card_rarity_enum is not None else "❓")
        owner_count_text = self._format_owner_count(
            self.owner_count, format_type="display"
        )

        # Construct description according to new format
        description_lines = [
            # Card name with its rarity emoji
            f"{rarity_emoji} **{card_name}**",
            # Series in italics with ReplyCont
            f"<:ReplyCont:1357534065841930290> *{card_series}*",
            # MODIFIED: Removed "稀有度: "
            f"<:ReplyCont:1357534065841930290>{display_rarity} | {pool_display_text}",
        ]
        if owner_count_text:  # Only add if owner_count_text is not empty
            description_lines.append(
                f"<:ReplyCont:1357534065841930290>{owner_count_text}"
            )  # Owner count with ReplyCont
        description_lines.append(
            f"<:Reply:1357534074830590143>{price_label}: {sell_price_display_text}"
        )  # Price with Reply

        embed = self._create_base_embed(
            title="",  # Title is set by set_author
            description="\n".join(description_lines),
            color=rarity_color,
        )

        embed.set_author(
            name="全圖鑑紀錄",
            url="https://cdn.discordapp.com/attachments/1079179758069366945/1364902124588109835/151906-20250319-232405-000.gif",
            icon_url="https://cdn.discordapp.com/attachments/1079179758069366945/1364902124588109835/151906-20250319-232405-000.gif",
        )

        if highest_star_user_id:
            star_emoji = view_utils.get_star_emoji_string(highest_star_level)
            highest_star_title = "<a:pu:1365482490478989353>最高星級紀錄"
            if achieved_at:
                achieved_date = (
                    achieved_at.strftime("%Y-%m-%d")
                    if isinstance(achieved_at, datetime)
                    else str(achieved_at)
                )
                highest_star_title += f" `({achieved_date})`"
            embed.add_field(
                name=highest_star_title,
                value=f"<:Reply:1357534074830590143>{highest_star_nickname} {star_emoji}",
                inline=False,
            )
            if custom_description and description_user_id == highest_star_user_id:
                embed.add_field(
                    name="", value=f"「{custom_description}」", inline=False
                )

        if card_image:
            embed.set_image(url=card_image)

        self._set_rarity_thumbnail(embed, rarity_level=card_rarity_enum)
        # temp_card 的 rarity 也應該是 Enum
        temp_card_rarity_for_footer = (
            card_rarity_enum if card_rarity_enum is not None else None
        )  # Or a default RarityLevel
        temp_card = (
            Card(
                card_id=card_id,
                name="",
                series="",
                rarity=temp_card_rarity_for_footer,
                image_url="",
            )
            if card_id
            else None
        )
        self._set_common_footer(embed, card=temp_card, page_info=page_info)

        return embed
