from gacha.utils.interaction_utils import check_user_permission
'\nGacha系統抽卡視圖基類\n提供 DrawView 和 MultiDrawView 共用的邏輯和功能\n'
from typing import Any, Callable, Dict, Optional, Union
import discord
from utils.logger import logger
from gacha.models.models import Card, CardWithStatus
from gacha.views.collection.favorite_component import FavoriteComponent
from gacha.views.collection.collection_view.interaction_manager import InteractionManager

class BaseDrawView(discord.ui.View):
    """抽卡視圖基類，提供共用功能"""

    def __init__(self, user: discord.User, balance: int, timeout: int=180, on_draw_callback=None, pool_types=None, pool_config_key=None):
        """初始化基礎抽卡視圖

        參數:
            user: Discord用戶對象
            balance: 用戶剩餘油幣
            timeout: 視圖超時時間（秒）
            on_draw_callback: 抽卡回調函數
            pool_types: 卡池類型列表
            pool_config_key: 卡池配置鍵
        """
        super().__init__(timeout=timeout)
        self.user = user
        self.user_id = user.id
        self.balance = balance
        self.on_draw_callback = on_draw_callback
        self.pool_types = pool_types or ['main', 'special']
        self.pool_config_key = pool_config_key or 'all'
        self.interaction_manager = InteractionManager()

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """檢查交互是否來自原始用戶，不是則發送錯誤消息

        參數:
            interaction: Discord交互對象

        返回:
            bool: 是否是原始用戶的交互
        """
        return await check_user_permission(interaction, self.user_id, '只有原始用戶可以使用此按鈕')

    async def _process_draw_callback(self, interaction: discord.Interaction, is_multi_draw: bool=False):
        """處理抽卡回調的共用邏輯

        參數:
            interaction: Discord交互對象
            is_multi_draw: 是否為十連抽
        """
        if self.on_draw_callback:
            if not await self.interaction_manager.try_defer(interaction):
                return
            await self.on_draw_callback(self.pool_types, self.pool_config_key, is_multi_draw)

    @staticmethod
    def extract_card_info(card_data: Union[Card, CardWithStatus]) -> tuple:
        """從卡片數據中提取卡片信息

        參數:
            card_data: 卡片對象或帶狀態的卡片對象

        返回:
            tuple: (card, is_new_card, star_level, is_wish, is_favorite, pool_type, rarity_code) # rarity_code is now int
        """
        if hasattr(card_data, 'card') and hasattr(card_data, 'status'):
            card = card_data.card
            is_new_card = card_data.status.is_new_card
            star_level = card_data.status.star_level
            is_wish = card_data.status.is_wish
            is_favorite = card_data.status.is_favorite
            pool_type = card_data.pool_type
            rarity_code = card.rarity
        elif isinstance(card_data, dict) and 'card' in card_data:
            card = card_data['card']
            is_new_card = card_data.get('is_new_card', False)
            star_level = card_data.get('star_level', 0)
            is_wish = card_data.get('is_wish', False)
            is_favorite = card_data.get('is_favorite', False)
            pool_type = card_data.get('pool_type')
            rarity_code = card.rarity if card else None
        else:
            card = card_data
            is_new_card = False
            star_level = 0
            is_wish = False
            is_favorite = False
            pool_type = getattr(card, 'pool_type', None)
            rarity_code = card.rarity
        if rarity_code is not None:
            try:
                rarity_code = int(rarity_code)
            except (ValueError, TypeError):
                logger.warning('Invalid rarity format encountered: %s. Returning None.', rarity_code)
                rarity_code = None
        return (card, is_new_card, star_level, is_wish, is_favorite, pool_type, rarity_code)