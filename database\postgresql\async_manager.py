import asyncpg
import os # 保留 os 用於 os.getenv 讀取 DB_POOL 大小
import logging
# from dotenv import load_dotenv # 由 app_config 統一處理
# <<< settings 導入已移除，將在方法內部導入

# logging.getLogger(__name__) 已在 app_config 中配置，這裡可以直接使用
logger = logging.getLogger(__name__)

# load_dotenv() # <<< 移除

class AsyncPgManager:
    """管理 asyncpg 連接池的類別"""
    _pool: asyncpg.Pool = None

    @classmethod
    async def initialize_pool(cls):
        """
        初始化 asyncpg 連接池。
        通常在應用程式啟動時調用一次。
        """
        if cls._pool is None:
            try:
                from gacha.app_config import settings # <<< 在方法內部導入 settings
                if not settings.DATABASE_URL: # <<< 使用 settings
                    logger.error("DATABASE_URL is not configured in settings. Cannot create connection pool.")
                    raise ValueError("DATABASE_URL is not configured in settings.")

                # DSN 直接從 settings 獲取
                dsn = settings.DATABASE_URL

                # 從 settings 獲取連接池大小配置
                min_size = settings.database.pool_min_size
                max_size = settings.database.pool_max_size
                logger.info(f"Database pool configured with min_size: {min_size}, max_size: {max_size}")

                cls._pool = await asyncpg.create_pool(
                    dsn=dsn,
                    min_size=min_size,
                    max_size=max_size
                )
                # PG_HOST 和 GACHA_DB_NAME 可以從 settings 中獲取用於日誌記錄
                log_db_host = settings.PG_HOST
                log_db_name = settings.GACHA_DB_NAME
                logger.info(f"Asyncpg 連接池已成功初始化 (Host: {log_db_host}, DB: {log_db_name})")
            except Exception as e:
                logger.error(f"初始化 asyncpg 連接池失敗: {e}", exc_info=True)
                raise RuntimeError(f"無法初始化資料庫連接池: {e}")

    @classmethod
    def get_pool(cls) -> asyncpg.Pool:
        """
        獲取已初始化的 asyncpg 連接池。

        返回:
            asyncpg.Pool: 連接池實例。

        異常:
            RuntimeError: 如果連接池尚未初始化。
        """
        if cls._pool is None:
            logger.error("Asyncpg 連接池尚未初始化。")
            raise RuntimeError("Asyncpg 連接池未初始化。請先調用 initialize_pool()。")
        return cls._pool

    @classmethod
    async def close_pool(cls):
        """
        關閉 asyncpg 連接池。
        通常在應用程式關閉時調用。
        """
        if cls._pool:
            try:
                await cls._pool.close()
                cls._pool = None
                logger.info("Asyncpg 連接池已關閉。")
            except Exception as e:
                logger.error(f"關閉 asyncpg 連接池時出錯: {e}", exc_info=True)

# --- Helper Functions for Application Lifecycle ---

async def setup_database_pool():
    """在應用程式啟動時初始化資料庫連接池的輔助函數"""
    await AsyncPgManager.initialize_pool()

async def shutdown_database_pool():
    """在應用程式關閉時關閉資料庫連接池的輔助函數"""
    await AsyncPgManager.close_pool()