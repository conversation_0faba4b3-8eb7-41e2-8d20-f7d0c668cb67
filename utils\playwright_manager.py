import asyncio
from playwright.async_api import async_playwright, Playwright, <PERSON><PERSON><PERSON>, <PERSON>
import logging

logger = logging.getLogger(__name__)

class PlaywrightManager:
    """
    Manages a single Playwright browser instance (Chromium by default)
    and provides methods to acquire and release pages.
    Designed to be used as a singleton or a shared instance within an application.
    """
    def __init__(self, headless=True, browser_type='chromium'):
        self._playwright: Playwright | None = None
        self._browser: Browser | None = None
        self._headless = headless
        self._browser_type = browser_type
        self._lock = asyncio.Lock()
        self._initialized = False
        logger.info(f"PlaywrightManager created (headless={headless}, browser={browser_type}). Call initialize() to start.")

    async def initialize(self):
        """
        Initializes Playwright and launches the browser instance if not already initialized.
        This method is idempotent.
        """
        async with self._lock:
            if self._initialized:
                logger.info("PlaywrightManager already initialized.")
                return

            logger.info("Initializing PlaywrightManager...")
            try:
                self._playwright = await async_playwright().start()
                browser_launcher = getattr(self._playwright, self._browser_type)
                # Common arguments for running in containerized/headless environments
                launch_options = {
                    "headless": self._headless,
                    "args": [
                        "--no-sandbox",
                        "--disable-setuid-sandbox",
                        "--disable-dev-shm-usage", # Crucial for Docker
                        "--disable-accelerated-2d-canvas",
                        "--no-first-run",
                        "--no-zygote",
                        # "--single-process", # Generally avoid unless necessary
                        "--disable-gpu" # Often necessary in headless environments
                    ]
                }
                logger.info(f"Launching {self._browser_type} with options: {launch_options}")
                self._browser = await browser_launcher.launch(**launch_options)
                self._initialized = True
                logger.info(f"{self._browser_type.capitalize()} browser launched successfully (version: {self._browser.version}).")
            except Exception as e:
                logger.error(f"Failed to initialize Playwright or launch browser: {e}", exc_info=True)
                # Attempt cleanup if partially initialized
                await self._close_internal() # Use internal close to avoid lock issues
                raise  # Re-raise the exception to signal failure

    async def acquire_page(self) -> Page:
        """
        Acquires a new browser page.

        Ensures the manager is initialized before acquiring a page.
        Raises RuntimeError if initialization fails.

        Returns:
            A new Playwright Page object.
        """
        if not self._initialized or not self._browser:
            logger.warning("PlaywrightManager not initialized. Attempting lazy initialization...")
            await self.initialize()
            # Check again after attempting initialization
            if not self._initialized or not self._browser:
                 logger.error("Failed to acquire page: PlaywrightManager could not be initialized.")
                 raise RuntimeError("PlaywrightManager could not be initialized.")

        logger.debug("Acquiring a new browser page...")
        try:
            # Using a new context per page provides better isolation than sharing the default context.
            # It's slightly more overhead but generally safer.
            context = await self._browser.new_context(
                # You can configure context options here if needed (e.g., viewport, user agent)
                # viewport={'width': 1920, 'height': 1080},
                # user_agent='Mozilla/5.0 ...'
            )
            page = await context.new_page()
            logger.debug(f"Browser page acquired successfully (from context: {context}).")
            # You might want to attach the context to the page for easier closing later
            # page.context_ref = context # Or use a custom attribute
            return page
        except Exception as e:
            logger.error(f"Failed to acquire browser page: {e}", exc_info=True)
            raise

    async def release_page(self, page: Page):
        """
        Closes the given browser page and its associated context.
        """
        if not page:
            logger.warning("Attempted to release a null page.")
            return

        logger.debug("Releasing browser page and its context...")
        context = page.context
        try:
            await page.close()
            logger.debug("Browser page closed.")
            await context.close()
            logger.debug("Browser context closed.")
        except Exception as e:
            # Log error but don't necessarily stop execution, page/context might already be closed
            logger.warning(f"Error closing page or context: {e}", exc_info=True)

    async def _close_internal(self):
        """Internal closing logic without acquiring the lock."""
        logger.info("Performing internal close...")
        if self._browser:
            try:
                await self._browser.close()
                logger.info("Browser closed.")
            except Exception as e:
                logger.error(f"Error closing browser during internal close: {e}", exc_info=True)
            self._browser = None

        if self._playwright:
            try:
                await self._playwright.stop()
                logger.info("Playwright stopped.")
            except Exception as e:
                logger.error(f"Error stopping Playwright during internal close: {e}", exc_info=True)
            self._playwright = None

        self._initialized = False
        logger.info("Internal close finished.")


    async def close(self):
        """
        Closes the browser and stops Playwright gracefully.
        This method is idempotent.
        """
        async with self._lock:
            if not self._initialized:
                logger.info("PlaywrightManager already closed or not initialized.")
                return
            logger.info("Closing PlaywrightManager...")
            await self._close_internal()
            logger.info("PlaywrightManager closed successfully.")

# Example Usage (for testing or integration)
# async def example_task(manager: PlaywrightManager, url: str):
#     page = None
#     try:
#         page = await manager.acquire_page()
#         logger.info(f"Navigating to {url}")
#         await page.goto(url, wait_until='domcontentloaded')
#         title = await page.title()
#         logger.info(f"Page title: {title}")
#         # Perform actions like screenshotting
#         # await page.screenshot(path=f"screenshot_{url.split('//')[1]}.png")
#     except Exception as e:
#         logger.error(f"Error during example task for {url}: {e}", exc_info=True)
#     finally:
#         if page:
#             await manager.release_page(page)

# async def main():
#     logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
#     manager = PlaywrightManager()
#     try:
#         # Initialize explicitly at startup
#         await manager.initialize()
#
#         # Run example tasks concurrently
#         tasks = [
#             example_task(manager, "https://www.google.com"),
#             example_task(manager, "https://www.github.com")
#         ]
#         await asyncio.gather(*tasks)
#
#     except Exception as e:
#         logger.error(f"An error occurred in main: {e}", exc_info=True)
#     finally:
#         # Ensure cleanup happens
#         await manager.close()
#
# if __name__ == "__main__":
#     # Set higher log level for playwright library itself if too verbose
#     logging.getLogger("playwright").setLevel(logging.WARNING)
#     asyncio.run(main())