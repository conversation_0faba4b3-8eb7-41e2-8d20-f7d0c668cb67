"""
數據庫配置文件
用於存儲數據庫連接信息和配置選項
支持PVP(ladder)、PVE和GACHA三個系統的配置管理
"""

import logging
import os
import traceback
from typing import Any, Dict, Literal

logger = logging.getLogger(__name__)

# PostgreSQL 默認配置
DEFAULT_LADDER_PG_CONFIG = {
    "host": "127.0.0.1",
    "port": 5432,
    "database": "ladder_database",
    "user": "postgres",
    "password": "postgres",
    "max_connections": 100,
}

DEFAULT_PVE_PG_CONFIG = {
    "host": "127.0.0.1",
    "port": 5432,
    "database": "pve_database",
    "user": "postgres",
    "password": "postgres",
    "max_connections": 100,
}

DEFAULT_GACHA_PG_CONFIG = {
    "host": "127.0.0.1",
    "port": 5432,
    "database": "gacha_database",
    "user": "postgres",
    "password": "postgres",
    "max_connections": 100,
}

# PVE 數據庫配置（从core_pve.py移动过来）
PVE_DB_CONFIG = {
    "postgresql": {
        "connection_retry_delay": 5,  # 連接重試延遲(秒)
        "max_retry_attempts": 3,  # 最大重試次數
    },
    "redis": {
        "health_check_interval": 60,  # Redis 健康檢查間隔(秒)
        "connection_retry_delay": 5,  # 連接重試延遲(秒)
        "max_retry_attempts": 3,  # 最大重試次數
    },
}


def get_db_config(system_type: Literal["pve", "ladder", "gacha"] = "ladder") -> Dict[str, Any]:
    """獲取數據庫配置

    參數:
        system_type: 系統類型，可選值為 "pve"、"ladder" 或 "gacha"

    返回:
        Dict[str, Any]: 數據庫配置字典
    """
    # 從環境變數或配置檔案中讀取數據庫配置
    db_config = {}

    # 根據系統類型使用不同的配置
    if system_type == "pve":
        system_name = "PVE"
        db_name = os.environ.get("PVE_DB_NAME", "pve_database")
    elif system_type == "gacha":
        system_name = "GACHA"
        db_name = os.environ.get("GACHA_DB_NAME", "gacha_database")
    else:  # ladder 或 其他
        system_name = "Ladder"
        db_name = os.environ.get("LADDER_DB_NAME", "ladder_database")

    # 基本配置
    db_config = {
        "host": os.environ.get("PG_HOST", "localhost"),
        "port": int(os.environ.get("PG_PORT", 5432)),
        "user": os.environ.get("PG_USER", "postgres"),
        "password": os.environ.get("PG_PASSWORD", "postgres"),
        "database": db_name,
        "minconn": int(os.environ.get("PG_MIN_CONN", 1)),
        "maxconn": int(os.environ.get("PG_MAX_CONN", 10)),
        "autocommit": True,
    }

    # 創建配置緩存，用於追蹤配置變更
    if not hasattr(get_db_config, "_config_cache"):
        get_db_config._config_cache = {}

    # 創建配置快照並比較是否有變更
    config_key = f"{system_name}_{db_name}"
    config_snapshot = str(db_config)

    # 只有在首次獲取配置或配置有變更時才輸出日誌
    if (
        config_key not in get_db_config._config_cache
        or get_db_config._config_cache[config_key] != config_snapshot
    ):
        logger.info(f"===> {system_name}系統獲取數據庫配置: {db_name}")
        get_db_config._config_cache[config_key] = config_snapshot

    return db_config


def get_db_type(system_type: Literal["pve", "ladder", "gacha"] = "ladder") -> str:
    """
    獲取數據庫類型。
    始終返回"postgresql"

    Args:
        system_type: 系統類型，可選值為 "pve"、"ladder" 或 "gacha"

    Returns:
        str: 數據庫類型，始終為"postgresql"
    """
    # 直接返回 postgresql
    db_type = "postgresql"

    # 記錄使用的數據庫類型
    system_name = {
        "pve": "PVE",
        "ladder": "Ladder",
        "gacha": "GACHA"
    }.get(system_type, "Ladder")
    
    logger.info(f"{system_name}系統使用數據庫類型: {db_type}")

    return db_type
