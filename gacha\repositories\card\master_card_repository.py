import asyncpg
import json
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple, Any, Union, Set
from decimal import Decimal
import random
from database.base_repository import BaseRepository
from gacha.models.models import Card
from utils.logger import logger
from gacha.constants import RarityLevel
from gacha.exceptions import CardNotFoundError

class MasterCardRepository(BaseRepository):
    """卡片主表存储库 (Asyncpg 版本)，統一管理 gacha_master_cards 表"""

    def __init__(self, pool: asyncpg.Pool):
        """初始化卡片主表存储库 (Asyncpg 版本)

        参数:
            pool: asyncpg 連接池實例
        """
        super().__init__(pool)
        self.table_name = 'gacha_master_cards'

    async def get_cards_by_criteria(self, rarity: Optional[RarityLevel]=None, pool_types: Optional[List[str]]=None, card_ids: Optional[List[int]]=None, series: Optional[str]=None, search_query: Optional[str]=None, limit: Optional[int]=None, offset: Optional[int]=None, random_order: bool=False, return_ids_only: bool=False, connection: Optional[asyncpg.Connection]=None) -> Union[List[Card], List[int]]:
        """
        統一的卡片查詢方法，根據條件獲取卡片

        Args:
            rarity: 卡片稀有度
            pool_types: 卡池類型列表
            card_ids: 指定的卡片ID列表
            series: 卡片系列
            search_query: 卡片名稱搜索關鍵字
            limit: 最大返回數量
            offset: 開始偏移
            random_order: 是否隨機排序
            return_ids_only: 是否只返回ID而非完整卡片物件
            connection: 可選的數據庫連接

        Returns:
            根據return_ids_only返回卡片ID列表或卡片物件列表
        """
        params: List[Any] = []
        param_idx = 1
        where_clauses = ['is_active = TRUE']
        if rarity is not None:
            where_clauses.append(f'rarity = ${param_idx}')
            params.append(rarity.value)
            param_idx += 1
        if pool_types:
            where_clauses.append(f'pool_type = ANY(${param_idx}::text[])')
            params.append(pool_types)
            param_idx += 1
        if card_ids:
            where_clauses.append(f'card_id = ANY(${param_idx}::integer[])')
            params.append(card_ids)
            param_idx += 1
        if series:
            where_clauses.append(f'series = ${param_idx}')
            params.append(series)
            param_idx += 1
        if search_query:
            where_clauses.append(f'name ILIKE ${param_idx}')
            params.append(f'%{search_query}%')
            param_idx += 1
        where_condition = ' AND '.join(where_clauses)
        order_clause = 'ORDER BY RANDOM()' if random_order else 'ORDER BY card_id'
        pagination = ''
        if limit is not None:
            pagination += f' LIMIT ${param_idx}'
            params.append(limit)
            param_idx += 1
            if offset is not None:
                pagination += f' OFFSET ${param_idx}'
                params.append(offset)
                param_idx += 1
        columns = 'card_id' if return_ids_only else '*'
        query = f'\n            SELECT {columns}\n            FROM {self.table_name}\n            WHERE {where_condition}\n            {order_clause}\n            {pagination}\n        '
        results = await self._fetch(query, params, connection=connection)
        if return_ids_only:
            return [res['card_id'] for res in results if res]
        else:
            cards = []
            for res in results:
                if res:
                    try:
                        cards.append(Card.from_db_record(res))
                    except ValueError as e:
                        logger.error('[MasterCardRepository.get_cards_by_criteria] Error mapping card data: %s', e, exc_info=True)
                        continue
            return cards

    async def get_card(self, card_id: int, connection: Optional[asyncpg.Connection]=None) -> Card:
        """獲取單張卡片信息，如果不存在則拋出異常"""
        cards = await self.get_cards_by_criteria(card_ids=[card_id], connection=connection)
        if not cards:
            raise CardNotFoundError(f"找不到卡片 ID: {card_id}", card_id=card_id)
        return cards[0]

    async def get_card_optional(self, card_id: int, connection: Optional[asyncpg.Connection]=None) -> Optional[Card]:
        """獲取單張卡片信息，如果不存在則返回None"""
        try:
            return await self.get_card(card_id, connection=connection)
        except CardNotFoundError:
            return None

    async def get_cards_details_by_ids(self, card_ids: List[int], connection: Optional[asyncpg.Connection]=None) -> List[Card]:
        """根據卡片ID列表批量獲取卡片詳細信息"""
        if not card_ids:
            return []
        return await self.get_cards_by_criteria(card_ids=card_ids, connection=connection)

    async def get_cards_by_rarity(self, rarity: RarityLevel, pool_types: Optional[List[str]]=None, limit: Optional[int]=None, user_id: Optional[int]=None) -> List[int]:
        """根據稀有度隨機獲取指定數量的卡片 ID 列表"""
        if not limit or limit <= 0:
            return []
        if limit == 1 and pool_types and (len(pool_types) == 1):
            count_query = f'\n                SELECT COUNT(*)\n                FROM {self.table_name}\n                WHERE rarity = $1 AND pool_type = $2 AND is_active = TRUE\n            '
            count = await self._fetchval(count_query, (rarity.value, pool_types[0]))
            if not count or count == 0:
                return []
            random_offset = random.randint(0, count - 1)
            query = f'\n                SELECT card_id\n                FROM {self.table_name}\n                WHERE rarity = $1 AND pool_type = $2 AND is_active = TRUE\n                ORDER BY card_id\n                LIMIT 1 OFFSET $3\n            '
            result = await self._fetchval(query, (rarity.value, pool_types[0], random_offset))
            return [result] if result else []
        return await self.get_cards_by_criteria(rarity=rarity, pool_types=pool_types, limit=limit, random_order=True, return_ids_only=True)

    async def get_card_objects_by_rarity(self, rarity: RarityLevel, pool_types: Optional[List[str]]=None, limit: Optional[int]=None) -> List[Card]:
        """根據稀有度和卡池類型隨機獲取指定數量的卡片對象列表"""
        return await self.get_cards_by_criteria(rarity=rarity, pool_types=pool_types, limit=limit, random_order=True)

    async def get_cards_by_series(self, series: str, pool_types: List[str]=None) -> List[Card]:
        """根据系列获取卡片列表"""
        return await self.get_cards_by_criteria(series=series, pool_types=pool_types)

    async def get_all_series(self) -> List[str]:
        """获取所有系列"""
        query = f'SELECT DISTINCT series FROM {self.table_name} WHERE is_active = TRUE ORDER BY series'
        results = await self._fetch(query)
        return [result['series'] for result in results]

    async def get_series_card_count(self, series: str) -> int:
        """获取指定系列的卡片数量"""
        query = f'SELECT COUNT(*)::integer as count FROM {self.table_name} WHERE series = $1 AND is_active = TRUE'
        count = await self._fetchval(query, (series,))
        return count or 0

    async def get_all_cards_by_rarity(self, rarity: RarityLevel, pool_types: List[str]=None) -> List[int]:
        """獲取指定稀有度的所有卡片 ID"""
        return await self.get_cards_by_criteria(rarity=rarity, pool_types=pool_types, return_ids_only=True)

    async def get_card_details_for_pricing(self, card_id: int, connection: Optional[asyncpg.Connection]=None) -> Dict[str, Any]:
        """根據卡片ID獲取用於價格計算的特定卡片詳細信息，如果不存在則拋出異常"""
        query = f'''
            SELECT
                card_id,
                sell_price,
                pool_type,
                rarity,
                price_config
            FROM {self.table_name}
            WHERE card_id = $1
        '''
        result = await self._fetchrow(query, (card_id,), connection=connection)
        if not result:
            logger.warning('Card with ID %s not found in get_card_details_for_pricing.', card_id)
            raise CardNotFoundError(f"找不到卡片 ID: {card_id}", card_id=card_id)
            
        parsed_price_config = Card._parse_price_config(result['price_config'], card_id)
        return {
            'card_id': result['card_id'],
            'sell_price': result['sell_price'],
            'pool_type': result['pool_type'],
            'rarity': RarityLevel(result['rarity']),
            'price_config': parsed_price_config
        }
        
    async def get_card_details_for_pricing_optional(self, card_id: int, connection: Optional[asyncpg.Connection]=None) -> Optional[Dict[str, Any]]:
        """根據卡片ID獲取用於價格計算的特定卡片詳細信息，如果不存在則返回None"""
        try:
            return await self.get_card_details_for_pricing(card_id, connection=connection)
        except CardNotFoundError:
            return None

    async def get_paginated_cards_by_criteria(self, pool_type: Optional[str], rarity: Optional[RarityLevel], search_query: Optional[str]=None, specific_card_id: Optional[int]=None, page: int=1, per_page: int=1, connection: Optional[asyncpg.Connection]=None) -> Tuple[List[Card], int]:
        """根據多個條件獲取卡片分頁列表"""
        if page < 1:
            page = 1
        if per_page < 1:
            per_page = 1
        offset = (page - 1) * per_page
        pool_types = [pool_type] if pool_type else None
        card_ids = [specific_card_id] if specific_card_id else None
        count_params = []
        count_param_idx = 1
        count_where_clauses = ['is_active = TRUE']
        if rarity is not None:
            count_where_clauses.append(f'rarity = ${count_param_idx}')
            count_params.append(rarity.value)
            count_param_idx += 1
        if pool_types:
            count_where_clauses.append(f'pool_type = ANY(${count_param_idx}::text[])')
            count_params.append(pool_types)
            count_param_idx += 1
        if card_ids:
            count_where_clauses.append(f'card_id = ANY(${count_param_idx}::integer[])')
            count_params.append(card_ids)
            count_param_idx += 1
        if search_query:
            count_where_clauses.append(f'name ILIKE ${count_param_idx}')
            count_params.append(f'%{search_query}%')
            count_param_idx += 1
        count_where_condition = ' AND '.join(count_where_clauses)
        count_query = f'SELECT COUNT(*) FROM {self.table_name} WHERE {count_where_condition}'
        total_count = await self._fetchval(count_query, count_params, connection=connection)
        if not total_count or total_count == 0:
            return ([], 0)
        cards = await self.get_cards_by_criteria(rarity=rarity, pool_types=pool_types, card_ids=card_ids, search_query=search_query, limit=per_page, offset=offset, connection=connection)
        return (cards, total_count)

    async def get_card_page_number(self, target_card_id: int, pool_type: Optional[str], rarity: Optional[RarityLevel], connection: Optional[asyncpg.Connection]=None) -> Optional[int]:
        """獲取卡片在分頁中的頁碼"""
        conditions: List[str] = ['is_active = TRUE']
        params: List[Any] = []
        param_counter = 1
        if pool_type:
            conditions.append(f'pool_type = ${param_counter}')
            params.append(pool_type)
            param_counter += 1
        if rarity:
            conditions.append(f'rarity = ${param_counter}')
            params.append(rarity.value)
            param_counter += 1
        where_clause = f"WHERE {' AND '.join(conditions)}" if conditions else ''
        query = f'\n            SELECT row_num\n            FROM (\n                SELECT card_id, ROW_NUMBER() OVER (ORDER BY card_id ASC) - 1 as row_num\n                FROM {self.table_name}\n                {where_clause}\n            ) AS filtered_cards\n            WHERE card_id = ${param_counter};\n        '
        params.append(target_card_id)
        row_index = await self._fetchval(query, params, connection=connection)
        if row_index is not None:
            return row_index + 1
        return None

    async def get_first_matching_card_id_by_name(self, name_query: str, pool_type: Optional[str], rarity: Optional[RarityLevel], connection: Optional[asyncpg.Connection]=None) -> Optional[int]:
        """根據名稱獲取第一個匹配的卡片ID"""
        pool_types = [pool_type] if pool_type else None
        card_ids = await self.get_cards_by_criteria(rarity=rarity, pool_types=pool_types, search_query=name_query, limit=1, return_ids_only=True, connection=connection)
        return card_ids[0] if card_ids else None

    async def get_card_with_stored_price(self, card_id: int, connection: Optional[asyncpg.Connection]=None) -> Dict[str, Any]:
        """獲取卡片名稱和預存的市場價格，如果不存在則拋出異常"""
        query = 'SELECT card_id, name, current_market_sell_price FROM gacha_master_cards WHERE card_id = $1'
        row = await self._fetchrow(query, (card_id,), connection=connection)
        if not row:
            raise CardNotFoundError(f"找不到卡片 ID: {card_id}", card_id=card_id)
        return {
            'card_id': row['card_id'], 
            'name': row['name'], 
            'current_market_sell_price': row['current_market_sell_price']
        }
        
    async def get_card_with_stored_price_optional(self, card_id: int, connection: Optional[asyncpg.Connection]=None) -> Optional[Dict[str, Any]]:
        """獲取卡片名稱和預存的市場價格，如果不存在則返回None"""
        try:
            return await self.get_card_with_stored_price(card_id, connection=connection)
        except CardNotFoundError:
            return None

    async def get_cards_details_for_pricing_batch(self, card_ids: List[int], connection: Optional[asyncpg.Connection]=None) -> Dict[int, Dict[str, Any]]:
        """批量獲取卡片定價詳情"""
        if not card_ids:
            return {}
        query = f'\n            SELECT\n                card_id,\n                sell_price,\n                pool_type,\n                rarity,\n                price_config\n            FROM {self.table_name}\n            WHERE card_id = ANY($1::integer[])\n        '
        results_map: Dict[int, Dict[str, Any]] = {}
        records = await self._fetch(query, (card_ids,), connection=connection)
        if records:
            for record_data in records:
                fetched_card_id = record_data['card_id']
                parsed_price_config = Card._parse_price_config(record_data['price_config'], fetched_card_id)
                results_map[fetched_card_id] = {'card_id': fetched_card_id, 'sell_price': record_data['sell_price'], 'pool_type': record_data['pool_type'], 'rarity': RarityLevel(record_data['rarity']), 'price_config': parsed_price_config}
        if len(results_map) != len(set(card_ids)):
            found_ids = set(results_map.keys())
            missing_ids = [cid for cid in set(card_ids) if cid not in found_ids]
            logger.warning('Cards with IDs %s not found in get_cards_details_for_pricing_batch.', missing_ids)
        return results_map

    async def bulk_update_market_prices(self, price_updates: Dict[int, Decimal], connection: Optional[asyncpg.Connection]=None) -> int:
        """批量更新卡片市場價格"""
        if not price_updates:
            return 0
        card_ids = list(price_updates.keys())
        new_prices_for_db = [price_updates[cid] for cid in card_ids]
        current_time = datetime.now(timezone.utc)
        query = f'\n            WITH updates_data (card_id, new_price) AS (\n                SELECT * FROM unnest($1::integer[], $2::decimal[])\n            )\n            UPDATE {self.table_name} AS mc\n            SET\n                current_market_sell_price = ud.new_price,\n                last_price_update_at = $3\n            FROM updates_data ud\n            WHERE mc.card_id = ud.card_id;\n        '
        status = await self._execute(query, (card_ids, new_prices_for_db, current_time), connection=connection)
        if status and status.startswith('UPDATE '):
            try:
                return int(status.split(' ')[1])
            except (ValueError, IndexError):
                logger.warning('[DB_MASTER_CARD] Could not parse row count from status: %s', status)
                return 0
        return 0

    async def get_all_master_card_ids(self) -> List[int]:
        """獲取所有活躍卡片的ID列表"""
        return await self.get_cards_by_criteria(return_ids_only=True)

    async def bulk_update_favorite_counts(self, favorite_updates: List[Dict[str, Any]], connection: Optional[asyncpg.Connection]=None) -> int:
        """批量更新卡片收藏計數"""
        if not favorite_updates:
            return 0
        card_ids = [update['card_id'] for update in favorite_updates]
        deltas = [update['delta'] for update in favorite_updates]
        target_table_name = 'gacha_card_market_stats'
        query = f'\n            UPDATE {target_table_name} AS ms\n            SET favorite_count = COALESCE(ms.favorite_count, 0) + data.delta\n            FROM (\n                SELECT UNNEST($1::integer[]) AS card_id, UNNEST($2::integer[]) AS delta\n            ) AS data\n            WHERE ms.card_id = data.card_id;\n        '
        status = await self._execute(query, (card_ids, deltas), connection=connection)
        if status and status.startswith('UPDATE '):
            try:
                updated_rows = int(status.split(' ')[1])
                return updated_rows
            except (ValueError, IndexError):
                logger.warning('[DB_MASTER_CARD] Could not parse row count from status: %s', status)
                return 0
        return 0