"""
Gacha系統抽卡功能的COG
包含 /w 指令，用於處理用戶的抽卡請求。
"""
import discord
from discord.ext import commands
from discord import app_commands
from typing import Dict, List, Optional, Tuple, Any, Union
import asyncio
import time
from gacha.services.core.gacha_service import GachaService
from gacha.views.gacha.draw_view import DrawView
from gacha.views.gacha.multi_draw_view import MultiDrawView
from gacha.views.embeds.gacha.draw_embed_builder import DrawEmbedBuilder
from gacha.views.embeds.gacha.multi_draw_embed_builder import MultiDrawEmbedBuilder
from gacha.views.utils import common_pool_type_autocomplete as util_pool_autocomplete
from utils.logger import logger
from gacha.models.models import Card, CardStatus, CardWithStatus
from gacha.app_config import config_service
from gacha.exceptions import (
    InsufficientBalanceError, 
    UserNotFoundError, 
    GachaSystemError,
    NoCardsDeterminedError,
    BalanceUpdateError,
    CardEnrichmentError,
    InvalidPoolTypeError
)
from gacha.services.notification.draw_notifier import DrawNotifier
BotType = Union[commands.Bot, commands.AutoShardedBot]

class DrawCog(commands.Cog):
    """處理抽卡相關指令的 Cog"""

    def __init__(self, bot: BotType, draw_notifier: DrawNotifier):
        self.bot = bot
        self.draw_notifier = draw_notifier

    async def pool_type_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        """根據用戶輸入提供卡池選項的自動完成。"""
        return await util_pool_autocomplete(interaction, current, show_individual_pools_only=False)

    @app_commands.command(name='w', description='抽取卡片 - 混合池(50油)，典藏池(140油)，泳裝池(130油)，情人節卡池(150油) - 可進行十連抽')
    @app_commands.autocomplete(pool_type=pool_type_autocomplete)
    async def draw_command(self, interaction: discord.Interaction, pool_type: Optional[str]=None, multi: bool=False):
        """抽卡命令處理

        參數:
            interaction: Discord交互對象
            pool_type: 卡池選擇 (可選)
            multi: 是否為十連抽
        """
        await interaction.response.defer(thinking=True)
        if pool_type:
            config_key = pool_type
            pool_config_data = config_service.get_config('gacha_core_settings.pool_configurations').get(config_key)
            if not pool_config_data:
                await interaction.followup.send(f"錯誤：無效的卡池類型 '{config_key}'。", ephemeral=True)
                return
            actual_pool_types = pool_config_data.pools
        else:
            config_key = 'all'
            default_pool_config = config_service.get_config('gacha_core_settings.pool_configurations').get('all')
            if not default_pool_config:
                logger.error("默認卡池 'all' 未在配置中找到。")
                await interaction.followup.send('錯誤：默認卡池配置缺失，請聯繫管理員。', ephemeral=True)
                return
            actual_pool_types = default_pool_config.pools
        await self._execute_draw_operation(interaction, actual_pool_types, config_key, multi)

    async def _execute_draw_operation(self, interaction: discord.Interaction, pool_types: List[str], config_key: str, multi_draw: bool) -> None:
        """執行抽卡操作的統一邏輯"""
        operation_start_time = time.monotonic()
        try:
            user_id = interaction.user.id
            nickname = interaction.user.display_name
            gacha_service: GachaService = self.bot.gacha_service
            
            if multi_draw:
                result = await gacha_service.draw_multiple(user_id, pool_types, 10, nickname)
            else:
                result = await gacha_service.draw_cards(user_id, pool_types, 1, nickname)
                
            await self._process_draw_result(interaction, result, pool_types, config_key, multi_draw)
            
        except InsufficientBalanceError as e:
            # 餘額不足錯誤，顯示友好的訊息
            await interaction.followup.send(f'💰 {str(e)}', ephemeral=True)
            
        except UserNotFoundError as e:
            # 用戶不存在錯誤
            logger.error('用戶不存在錯誤: %s', e, exc_info=True)
            await interaction.followup.send('❌ 無法找到您的帳戶資訊，請稍後再試', ephemeral=True)
            
        except InvalidPoolTypeError as e:
            # 無效卡池類型錯誤
            logger.warning('無效卡池類型: %s', e)
            await interaction.followup.send('🎯 選擇的卡池類型無效，請重新選擇', ephemeral=True)
            
        except NoCardsDeterminedError as e:
            # 無法確定抽取卡片
            logger.error('無法確定抽取卡片: %s', e, exc_info=True)
            await interaction.followup.send('🎲 抽卡系統暫時無法確定要給您的卡片，請稍後再試', ephemeral=True)
            
        except BalanceUpdateError as e:
            # 餘額更新失敗
            logger.error('餘額更新失敗: %s', e, exc_info=True)
            await interaction.followup.send('💳 更新餘額時發生錯誤，請聯繫管理員', ephemeral=True)
            
        except CardEnrichmentError as e:
            # 卡片資訊豐富化失敗
            logger.error('卡片資訊處理失敗: %s', e, exc_info=True)
            await interaction.followup.send('📋 處理卡片資訊時發生錯誤，抽卡可能未完全完成', ephemeral=True)
            
        except GachaSystemError as e:
            # Gacha 系統特定錯誤
            logger.error('Gacha 系統錯誤: %s', e, exc_info=True)
            await interaction.followup.send(f'🎲 抽卡系統發生錯誤：{str(e)}', ephemeral=True)
            
        except Exception as e:
            # 其他未預期的錯誤
            logger.error('執行抽卡操作時出錯: %s', e, exc_info=True)
            await interaction.followup.send('❌ 抽卡過程中出現未預期的錯誤，請稍後再試', ephemeral=True)

    async def _process_draw_result(self, interaction: discord.Interaction, result: Dict[str, Any], pool_types: List[str], config_key: str, multi_draw: bool):
        """處理抽卡結果的共用邏輯"""
        cards_with_status = result.get('cards_with_status', [])
        new_balance = result.get('new_balance', 0)
        is_multi_draw_from_result = result.get('is_multi_draw', multi_draw)
        
        if is_multi_draw_from_result:
            await self._process_multi_draw_result(interaction, result, cards_with_status, new_balance, pool_types, config_key)
        elif cards_with_status:
            await self._process_single_draw_result(interaction, result, cards_with_status[0], new_balance, pool_types, config_key)
        else:
            logger.error("單抽結果中 'cards_with_status' 為空。Result: %s", result)
            await interaction.followup.send('抽卡結果異常，請聯繫管理員。')

    async def _handle_draw_callback(self, interaction: discord.Interaction, pool_types: List[str], config_key: str, is_multi_draw: bool):
        """處理來自 DrawView 或 MultiDrawView 的繼續抽卡請求的回調"""
        await self._execute_draw_operation(interaction=interaction, pool_types=pool_types, config_key=config_key, multi_draw=is_multi_draw)

    async def _process_single_draw_result(self, interaction: discord.Interaction, result: Dict[str, Any], card_with_status: CardWithStatus, new_balance: int, pool_types: List[str], config_key: str):
        """處理單抽結果的專用邏輯"""
        owner_counts = result.get('owner_counts', {})
        owner_count = owner_counts.get(card_with_status.card.card_id, 0)
        view = DrawView(user=interaction.user, card=card_with_status, balance=new_balance, owner_count=owner_count, on_draw_callback=lambda pt=None, ck=None, imd=False, inter=interaction: self._handle_draw_callback(inter, pt or pool_types, ck or config_key, imd), pool_types=pool_types, pool_config_key=config_key)
        embed = await view.get_current_page_embed(interaction=interaction)
        await interaction.followup.send(embed=embed, view=view)
        self._process_card_notification(interaction, interaction.user, card_with_status.card, card_with_status.status.is_new_card, card_with_status.status.is_wish, new_balance, owner_count)

    async def _process_multi_draw_result(self, interaction: discord.Interaction, result: Dict[str, Any], cards_with_status: List[CardWithStatus], new_balance: int, pool_types: List[str], config_key: str):
        """處理十連抽結果的專用邏輯"""
        sorted_cards = sorted(cards_with_status, key=lambda cws: cws.card.rarity, reverse=True)
        owner_counts = result.get('owner_counts', {})
        view = MultiDrawView(user=interaction.user, cards_results=sorted_cards, owner_counts=owner_counts, balance=new_balance, on_multi_draw_callback=lambda pt=None, ck=None, imd=True, inter=interaction: self._handle_draw_callback(inter, pt or pool_types, ck or config_key, imd), pool_types=pool_types, pool_config_key=config_key)
        combined_embed = await view.get_combined_embed(interaction=interaction)
        await interaction.followup.send(embed=combined_embed, view=view)
        for card_with_status_item in sorted_cards:
            self._process_card_notification(interaction, interaction.user, card_with_status_item.card, card_with_status_item.status.is_new_card, card_with_status_item.status.is_wish, new_balance, owner_counts.get(card_with_status_item.card.card_id, 0))

    def _process_card_notification(self, interaction: discord.Interaction, user: discord.User, card: Card, is_new_card: bool, is_wish: bool, new_balance: int=0, owner_count: int=0):
        """處理卡片通知"""
        should_send_notification = False
        try:
            if self.draw_notifier:
                should_send_notification = self.draw_notifier.should_notify(card, is_new_card, is_wish)
            else:
                logger.warning('[DrawCog] self.draw_notifier 未初始化，無法判斷是否發送通知。')
                return
        except Exception as e_should_notify:
            logger.error('[DrawCog] 調用 should_notify 時出錯: %s', e_should_notify, exc_info=True)
            return
        
        if should_send_notification:
            try:
                notify_builder = DrawEmbedBuilder(user=user, card=card, balance=new_balance, is_new_card=is_new_card, is_wish=is_wish, owner_count=owner_count)
                notify_embed = notify_builder.build_embed()

                async def send_actual_notification():
                    try:
                        if self.draw_notifier and hasattr(self.draw_notifier, 'notify_draw'):
                            await self.draw_notifier.notify_draw(user=user, embed=notify_embed, card=card, view=None, is_new_card=is_new_card)
                        else:
                            logger.warning('[DrawCog] self.draw_notifier 或其 notify_draw 方法未找到，無法發送通知。')
                    except Exception as e_async:
                        logger.error('[DrawCog] 非同步處理通知時發生錯誤: %s', str(e_async), exc_info=True)
                asyncio.create_task(send_actual_notification())
            except Exception as e:
                logger.error('[DrawCog] 準備發送抽卡通知失敗: %s', e, exc_info=True)

async def setup(bot: BotType):
    """將 Cog 添加到 Bot"""
    if not hasattr(bot, 'draw_notifier'):
        logger.error("CRITICAL: bot.draw_notifier 未通過 service_mappings 在 bot.py 中初始化 DrawNotifier 實例!")
        raise RuntimeError("DrawNotifier 未在 Bot 中正確初始化 (期望 bot.draw_notifier)，DrawCog 無法加載。")
    
    draw_notifier_instance = bot.draw_notifier
    await bot.add_cog(DrawCog(bot, draw_notifier_instance))
    logger.info('DrawCog 已成功加載。')