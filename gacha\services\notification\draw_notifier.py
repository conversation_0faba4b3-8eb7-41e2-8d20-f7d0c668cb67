"""
抽卡通知服務
負責檢測稀有卡片並發送通知到指定頻道
"""
import discord
from typing import Dict, Any, Optional, Union, Set, Tuple
import asyncio
import aiohttp # 新增導入
# from discord.ext import commands # 不再需要 commands
# from discord.ext.commands import Bot # 不再需要 Bot
from utils.logger import logger
from gacha.models.models import Card, CardWithStatus
from gacha.app_config import config_service

class DrawNotifier:
    """抽卡通知服務 (僅支援 Webhook)"""

    def __init__(self):
        notification_settings_model = config_service.get_config('gacha_notification_settings')
        
        if notification_settings_model is None:
            logger.warning("[GACHA_NOTIFIER] 'gacha_notification_settings' 未在配置中加載，將使用默認值。通知功能可能無法正常工作。")
            # 如果模型為None，我們需要手動創建一個帶有默認值的模型實例
            from gacha.app_config import GachaNotificationSettings # 確保能訪問到模型定義
            notification_settings_model = GachaNotificationSettings()

        self._enabled = notification_settings_model.enabled
        self._webhook_url = notification_settings_model.webhook_url if notification_settings_model.webhook_url is not None else ''
        raw_notify_rarities = notification_settings_model.notify_rarities
        #  確保 raw_notify_rarities 是字典，即使來自默認工廠的空字典
        self._notification_config = {k: set(v) for k, v in raw_notify_rarities.items()} 
        self._session = aiohttp.ClientSession() # 為 Webhook 請求創建 aiohttp session

    async def close_session(self):
        """關閉 aiohttp ClientSession."""
        if self._session and not self._session.closed:
            await self._session.close()

    def should_notify(self, card: Union[Card, CardWithStatus], is_new_card: bool=False, is_wish: bool=False) -> bool:
        """根據卡片卡池類型和稀有度決定是否發送通知

        參數:
            card: 卡片對象或CardWithStatus對象
            is_new_card: 不再使用
            is_wish: 不再使用

        返回:
            bool: 是否應該發送通知
        """
        if not self._enabled or not self._webhook_url: # 如果未啟用或未設定 webhook_url，則不通知
            return False

        pool_type, rarity_int = self._extract_card_info(card)
        if rarity_int is None:
            return False
        
        notify_rarities_for_pool = self._notification_config.get(pool_type, set())
        return rarity_int in notify_rarities_for_pool

    def _extract_card_info(self, card: Union[Card, CardWithStatus]) -> Tuple[str, Optional[int]]:
        """從卡片對象中提取卡池類型和稀有度

        參數:
            card: 卡片對象或CardWithStatus對象

        返回:
            Tuple[str, Optional[int]]: (卡池類型, 稀有度整數值)
        """
        if isinstance(card, CardWithStatus):
            actual_card = card.card
            rarity = actual_card.rarity
            pool_type = actual_card.pool_type
        else:
            actual_card = card
            rarity = actual_card.rarity if hasattr(actual_card, 'rarity') else None
            pool_type = actual_card.pool_type if hasattr(actual_card, 'pool_type') else 'main'
        if rarity is None:
            return (pool_type, None)
        try:
            rarity_int = int(rarity)
            return (pool_type, rarity_int)
        except (ValueError, TypeError):
            return (pool_type, None)

    def should_notify_from_card_with_status(self, card_with_status: CardWithStatus) -> bool:
        """從帶狀態的卡片對象判斷是否需要發送通知

        參數:
            card_with_status: CardWithStatus 對象

        返回:
            bool: 是否應該發送通知
        """
        return self.should_notify(card_with_status)

    async def notify_draw(self, user: discord.User, embed: discord.Embed, card: Union[Card, CardWithStatus], view: Optional[discord.ui.View]=None, is_new_card: bool=False) -> bool:
        """透過 Webhook 發送抽卡通知"""
        if not self._enabled:
            logger.debug('[GACHA_NOTIFIER] 通知功能未啟用。')
            return False

        if not self._webhook_url:
            logger.warning('[GACHA_NOTIFIER] 未配置 Webhook URL，無法發送通知')
            return False

        # should_notify 的檢查現在包含在 notify_draw 之前，或者在 should_notify 內部檢查 webhook_url
        # 這裡假設如果呼叫了 notify_draw，就已經通過了 should_notify 的判斷

        if isinstance(card, CardWithStatus):
            actual_card = card.card
            is_new_card_status = card.status.is_new_card
            pool_prefix = config_service.get_config('gacha_core_settings.pool_type_prefixes').get(card.card.pool_type, '')
        else:
            actual_card = card
            is_new_card_status = is_new_card
            pool_prefix = config_service.get_config('gacha_core_settings.pool_type_prefixes').get(getattr(actual_card, 'pool_type', 'main'), '')
        
        card_status_text = '新卡' if is_new_card_status else '重複'
        
        avatar_url = user.default_avatar.url # 預設頭像
        if hasattr(user, 'display_avatar') and user.display_avatar:
            avatar_url = user.display_avatar.url
        elif hasattr(user, 'avatar') and user.avatar: # 有些 User 物件可能沒有 display_avatar 但有 avatar
            avatar_url = user.avatar.url

        notification_embed = embed.copy()
        notification_embed.set_author(name='【媽的歐狗通知】', icon_url=avatar_url)
        notification_embed.title = f'{user.display_name} 抽到了{pool_prefix}{card_status_text}!'

        try:
            webhook_payload = {
                "embeds": [notification_embed.to_dict()]
            }
            if view:
                logger.warning("[GACHA_NOTIFIER] Webhook 通知不支持直接發送 View 組件。View 將被忽略。")

            async with self._session.post(self._webhook_url, json=webhook_payload) as response:
                if 200 <= response.status < 300:
                    logger.info('[GACHA_NOTIFIER] Webhook 通知已成功發送至 %s', self._webhook_url)
                    return True
                else:
                    logger.error('[GACHA_NOTIFIER] Webhook 通知失敗，狀態碼: %s, 回應: %s', response.status, await response.text())
                    return False
        except aiohttp.ClientError as e:
            logger.error('[GACHA_NOTIFIER] Webhook 請求時發生 aiohttp 客戶端錯誤: %s', e, exc_info=True)
            return False
        except Exception as e:
            logger.error('[GACHA_NOTIFIER] Webhook 發送通知時發生未知錯誤: %s', e, exc_info=True)
            return False