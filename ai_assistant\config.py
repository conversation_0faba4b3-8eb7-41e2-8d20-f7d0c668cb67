"""
配置模組 - 包含與API相關的設置和配置參數
"""
import os
import sys

# 確保可以導入根目錄的模組
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 使用統一的日誌系統
from utils.logger import logger

# 導入統一提示詞
from .prompts import (
    OUTFIT_SYSTEM_PROMPT,
    OUTFIT_USER_PROMPT,
    QA_SYSTEM_PROMPT
)

logger.info("ai_assistant.config: 模組開始載入...")

# --- 主 API 配置 ---
PRIMARY_API_KEY = None
PRIMARY_API_URL = None
PRIMARY_DEFAULT_MODEL = None

# --- 備用 API 配置 ---
BACKUP_API_KEY = "26015792"  # 硬編碼的備用 API Key
BACKUP_API_URL = None
BACKUP_DEFAULT_MODEL = None

try:
    logger.info("ai_assistant.config: 嘗試從 gacha.app_config 導入 settings...")
    from gacha.app_config import settings
    logger.info("ai_assistant.config: 成功導入 settings.")

    # 主 API Key 從環境變數 (由 settings 封裝)
    PRIMARY_API_KEY = getattr(settings, "AI_API_KEY", None)

    if hasattr(settings, 'ai_assistant') and settings.ai_assistant:
        logger.info("ai_assistant.config: 從 settings.ai_assistant 讀取 API 端點和模型配置...")
        # 直接從 Pydantic 模型獲取配置。如果 YAML 中未提供，這些值將為 None。
        PRIMARY_API_URL = settings.ai_assistant.primary_api_endpoint
        PRIMARY_DEFAULT_MODEL = settings.ai_assistant.primary_default_model
        BACKUP_API_URL = settings.ai_assistant.backup_api_endpoint
        BACKUP_DEFAULT_MODEL = settings.ai_assistant.backup_default_model
    else:
        logger.warning("ai_assistant.config: settings.ai_assistant 未配置或為空。API URL 和 Model 將為 None，可能導致 AI 功能不可用。")
        # PRIMARY_API_URL, PRIMARY_DEFAULT_MODEL, BACKUP_API_URL, BACKUP_DEFAULT_MODEL 保持為 None

    logger.info(f"ai_assistant.config: 主API - URL: {PRIMARY_API_URL}, Model: {PRIMARY_DEFAULT_MODEL}, Key 已配置: {bool(PRIMARY_API_KEY)}")
    logger.info(f"ai_assistant.config: 備用API - URL: {BACKUP_API_URL}, Model: {BACKUP_DEFAULT_MODEL}, Key 已配置: {bool(BACKUP_API_KEY)}")

    if not PRIMARY_API_KEY:
        logger.warning("ai_assistant.config: 主 AI_API_KEY (settings.AI_API_KEY) 未在 .env 中配置或從 settings 中獲取。主 API 可能無法使用。")
    if not PRIMARY_API_URL:
        logger.warning("ai_assistant.config: 主 AI API Endpoint (settings.ai_assistant.primary_api_endpoint) 未配置。")
    if not PRIMARY_DEFAULT_MODEL:
        logger.warning("ai_assistant.config: 主 AI Default Model (settings.ai_assistant.primary_default_model) 未配置。")
    
    if not BACKUP_API_URL:
        logger.warning("ai_assistant.config: 備用 AI API Endpoint (settings.ai_assistant.backup_api_endpoint) 未配置。備用 API 可能無法使用。")
    if not BACKUP_DEFAULT_MODEL:
        logger.warning("ai_assistant.config: 備用 AI Default Model (settings.ai_assistant.backup_default_model) 未配置。備用 API 可能無法使用。")

except ImportError as e:
    logger.error(f"ai_assistant.config: 無法從 gacha.app_config 導入 settings: {e}. AI 功能將嚴重受限或不可用。", exc_info=True)
    # 在這種情況下，所有 API 配置都將保持為 None 或其初始值，服務應該能夠優雅地處理這種情況。
    logger.warning("ai_assistant.config: settings導入失敗，所有 API 配置（URL, Model）將為 None。主 API Key 也將為 None。")

except AttributeError as e:
    logger.error(f"ai_assistant.config: 從 settings 物件讀取屬性時發生 AttributeError: {e}. 可能 settings 結構不完整或鍵名不匹配。", exc_info=True)
    logger.warning(f"ai_assistant.config: (AttributeError caught) settings 相關屬性讀取失敗，API URL 和 Model 將為 None。錯誤: {e}")
    # 與 ImportError 類似，所有相關配置應為 None。
    # PRIMARY_API_KEY 依賴 getattr，理論上如果 settings 本身存在但 ai_assistant 不存在，也可能觸發。
    # 但更常見的是 settings.ai_assistant.some_attribute 失敗。

# 圖像處理配置
MAX_IMAGE_SIZE = 4 * 1024 * 1024  # 4MB
MAX_IMAGE_DIMENSIONS = (2000, 2000)  # 最大寬x高

# Discord命令配置
COMMAND_NAME = "rate"  # Discord命令名稱
COMMAND_DESCRIPTION = "上傳並評分你的穿搭照片，獲取專業時尚建議"  # 命令描述

logger.info("ai_assistant.config: 模組載入完畢。") 