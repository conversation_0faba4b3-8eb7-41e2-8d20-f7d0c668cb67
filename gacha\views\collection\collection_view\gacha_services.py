"""
Gacha 服務容器
統一管理 CollectionView 相關的所有服務訪問
"""

from dataclasses import dataclass
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from gacha.services.core.collection_service import CollectionService
    from gacha.services.core.economy_service import EconomyService
    from gacha.services.sorting.sorting_service import SortingService
    from gacha.services.core.favorite_service import FavoriteService
    from gacha.services.core.wish_service import WishService
    from gacha.services.core.star_enhancement_service import StarEnhancementService
    from gacha.services.core.encyclopedia_service import EncyclopediaService
    from gacha.repositories.collection.user_collection_repository import (
        UserCollectionRepository,
    )


@dataclass
class GachaServices:
    """Gacha 系統服務容器

    提供統一的服務訪問接口，避免在各個組件中重複注入服務依賴
    """

    collection: "CollectionService"
    economy: "EconomyService"
    sorting: "SortingService"
    favorite: "FavoriteService"
    wish: "WishService"
    star_enhancement: "StarEnhancementService"
    encyclopedia: "EncyclopediaService"
    collection_repo: "UserCollectionRepository"

    @classmethod
    def from_bot(cls, bot) -> "GachaServices":
        """從 Bot 實例創建服務容器

        這是創建服務容器的標準方法，確保所有服務都正確注入

        Args:
            bot: Discord Bot 實例，包含所有必要的服務

        Returns:
            GachaServices: 配置完整的服務容器實例
        """
        return cls(
            collection=bot.collection_service,
            economy=bot.economy_service,
            sorting=bot.sorting_service,
            favorite=bot.favorite_service,
            wish=bot.wish_service,
            star_enhancement=bot.star_enhancement_service,
            encyclopedia=bot.encyclopedia_service,
            collection_repo=bot.user_collection_repo,
        )
