# gacha/services/common/validation_service.py
from __future__ import annotations

import asyncpg
from typing import List, Optional, TYPE_CHECKING

# 假設的路徑，後續可能需要調整
from gacha.models.models import GachaUser, Card
from gacha.exceptions import (
    UserNotFoundError,
    InsufficientBalanceError,
    CardNotFoundError,
    InvalidPoolTypeError,
    ConfigurationError,
)
from gacha.app_config import config_service  # 用於卡池名稱等配置

if TYPE_CHECKING:
    from gacha.repositories.user.user_repository import UserRepository
    from gacha.repositories.card.master_card_repository import MasterCardRepository


class ValidationService:
    def __init__(
        self,
        user_repo: UserRepository,
        master_card_repo: MasterCardRepository,
        # config_service 是全局導入的，通常不需要在此注入，除非有特殊原因
    ):
        self.user_repo = user_repo
        self.master_card_repo = master_card_repo
        # self.config_service = config_service # 通常直接使用導入的 config_service

    async def ensure_user_exists(
        self,
        user_id: int,
        nickname: Optional[str] = None,  # 在創建用戶時可能需要
        create_if_missing: bool = False,
        connection: Optional[asyncpg.Connection] = None,
    ) -> GachaUser:
        """
        Ensures a user exists. If not, and create_if_missing is True, creates the user.
        Otherwise, raises UserNotFoundError.
        """
        try:
            user = await self.user_repo.get_user(user_id, connection=connection)
            if user:
                return user
            # If user is None but get_user didn't raise UserNotFoundError (which it should based on repo design)
            # this path indicates an unexpected state, but we'll proceed to creation if flag is set.
        except UserNotFoundError:
            # This is the expected path if user does not exist.
            pass # Proceed to creation if create_if_missing is True

        if create_if_missing:
            created_user = await self.user_repo.create_user(
                user_id, nickname, connection=connection
            )
            # create_user should ideally return the created user object or raise on failure.
            # If it returns the user, we can return it directly.
            # If it returns some other success indicator, we might need to re-fetch.
            # For simplicity, assuming create_user returns the GachaUser object upon success.
            if created_user: # Assuming create_user returns the GachaUser object or raises an error
                # Attempt to get the user again to ensure it was created and to return a consistent object
                user_after_creation = await self.user_repo.get_user(user_id, connection=connection)
                if user_after_creation:
                    return user_after_creation
            
            # If creation failed or user still not found
            raise UserNotFoundError(
                f"Failed to create or find user {user_id} after creation attempt.",
                user_id=user_id,
            )
        else:
            # If not creating and user was not found (either initially or via exception)
            raise UserNotFoundError(
                f"User with user_id: {user_id} not found and create_if_missing is False.",
                user_id=user_id,
            )

    async def check_user_balance(
        self,
        user_id: int,
        required_balance: int,
        connection: Optional[asyncpg.Connection] = None,
    ) -> None:
        """
        Checks if the user has sufficient balance.
        Raises InsufficientBalanceError if the balance is not enough.
        Raises UserNotFoundError if the user does not exist (implicitly checked by get_user_balance_for_update or similar).
        """
        # 實現細節：
        # 1. 獲取用戶當前餘額 (可能需要事務性讀取，例如 user_repo.get_user_balance_for_update)。
        # 2. 如果餘額 < required_balance，拋出 InsufficientBalanceError。
        # 3. 如果在獲取餘額過程中發現用戶不存在（例如 get_user_balance_for_update 返回 None 或 Repository 層拋錯），
        #    則應轉化為或直接拋出 UserNotFoundError（或者由 ensure_user_exists 先行處理）。
        #    這裡的設計是，此方法預期用戶是存在的。
        # 獲取用戶當前餘額，get_user_balance_for_update 應處理用戶不存在的情況
        # (例如返回 None 或拋出 Repository 層的 RecordNotFoundError)
        # 這裡我們假設它返回餘額數字或 None
        current_balance = await self.user_repo.get_user_balance_for_update(
            user_id, connection=connection
        )

        if current_balance is None:
            # 如果 get_user_balance_for_update 在用戶不存在時返回 None
            raise UserNotFoundError(
                f"User with user_id: {user_id} not found when checking balance.",
                user_id=user_id,
            )

        if current_balance < required_balance:
            raise InsufficientBalanceError(
                f"你的餘額不足！需要 {required_balance} 油幣，但你只有 {current_balance} 油幣",
                required=required_balance,
                current=current_balance,
            )
        # 如果餘額足夠，則不執行任何操作 (方法簽名返回 None)

    async def ensure_card_exists(
        self,
        card_id: int,
        connection: Optional[asyncpg.Connection] = None,  # 卡片主數據通常不需要事務連接
    ) -> Card:
        """
        Ensures a card exists in the master card table.
        Raises CardNotFoundError if the card does not exist.
        """
        # 實現細節：
        # 1. 嘗試使用 self.master_card_repo.get_card() 獲取卡片。
        # 2. 如果卡片不存在，拋出 CardNotFoundError。
        # 3. 如果存在，返回卡片對象。
        card = await self.master_card_repo.get_card(
            card_id
        )  # connection is usually not needed for master data
        if card is None:
            raise CardNotFoundError(
                f"Card with card_id: {card_id} not found.", card_id=card_id
            )
        return card

    def validate_pool_types(self, pool_types: List[str]) -> List[str]:
        """
        Validates if the given pool types are known/configured.
        Returns a list of valid pool types.
        Raises InvalidPoolTypeError if no valid pool types are found or if any type is invalid.
        """
        known_pool_types = config_service.get_pool_type_names()
        if not known_pool_types:  # 防禦性程式碼，如果配置中沒有卡池名稱
            raise ConfigurationError("No pool types are configured in the system.")

        valid_input_pool_types = [
            ptype for ptype in pool_types if ptype in known_pool_types
        ]

        if not valid_input_pool_types:
            # 如果用戶提供的所有卡池類型都不在已知類型中
            raise InvalidPoolTypeError(
                f"None of the provided pool types are valid. Provided: {pool_types}, Known: {list(known_pool_types)}",
                pool_type=pool_types,
            )

        # 檢查是否所有提供的卡池類型都是有效的
        if len(valid_input_pool_types) != len(set(pool_types)):
            invalid_ones = [ptype for ptype in pool_types if ptype not in known_pool_types]
            raise InvalidPoolTypeError(
                f"Some provided pool types are invalid: {invalid_ones}. Known: {list(known_pool_types)}",
                pool_type=invalid_ones
            )

        return valid_input_pool_types
