import asyncio
import msgpack
import signal
import redis.asyncio as aioredis
import asyncpg
import os
from typing import List, Dict, Any, Tuple
from redis import exceptions as redis_exceptions
from utils.logger import logger
from database.postgresql.async_manager import AsyncPgManager
from gacha.services.market import direct_market_stats_updater
from gacha.constants import MarketStatsEventType
from gacha.services.market.direct_market_stats_updater import MarketStatsUpdateError

REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
REDIS_PORT = os.getenv('REDIS_PORT', '6379')
REDIS_DB = os.getenv('REDIS_DB', '0')
REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', '')
if REDIS_PASSWORD:
    REDIS_URL = f'redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{RED<PERSON>_DB}'
else:
    REDIS_URL = f'redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}'
logger.info('[Market Stats Consumer] 配置Redis URL: %s', REDIS_URL)
MAIN_STREAM_NAME = 'market_stats:updates'
DEAD_LETTER_STREAM_NAME = f'{MAIN_STREAM_NAME}:dead_letters'
CONSUMER_GROUP_NAME = 'market_stats_group'
CONSUMER_NAME_PREFIX = 'consumer'
CONSUME_BATCH_SIZE = 10
CONSUME_BLOCK_MS = 5000
shutdown_requested = False

class MarketStatsConsumerError(Exception):
    """Base exception for market stats consumer errors"""
    pass

class EventProcessingError(MarketStatsConsumerError):
    """Exception raised when event processing fails"""
    def __init__(self, event_type: str, message_id: str, original_error: Exception):
        self.event_type = event_type
        self.message_id = message_id
        self.original_error = original_error
        super().__init__(f"Failed to process event type '{event_type}' for message {message_id}: {original_error}")

class UnknownEventTypeError(MarketStatsConsumerError):
    """Exception raised when an unknown event type is received"""
    def __init__(self, event_type: str, message_id: str):
        self.event_type = event_type
        self.message_id = message_id
        super().__init__(f"Unknown event type '{event_type}' in message {message_id}")

class InvalidMessageFormatError(MarketStatsConsumerError):
    """Exception raised when a message has invalid format"""
    def __init__(self, message_id: str, reason: str):
        self.message_id = message_id
        self.reason = reason
        super().__init__(f"Invalid message format for message {message_id}: {reason}")

async def move_to_dead_letter_stream(redis: aioredis.Redis, stream_name: str, message_id: bytes, original_message_data: Dict[str, bytes], error_info: str):
    """將無法處理的消息移動到死信流"""
    try:
        packed_original_business_data = original_message_data.get(b'data', b'')
        dead_letter_fields = {
            b'original_stream': stream_name.encode('utf-8'),
            b'original_message_id': message_id,
            b'original_packed_data': packed_original_business_data,
            b'error_info': error_info.encode('utf-8'),
            b'failed_at_consumer': CONSUMER_NAME_PREFIX.encode('utf-8'),
            b'timestamp': str(asyncio.get_event_loop().time()).encode('utf-8')
        }
        await redis.xadd(DEAD_LETTER_STREAM_NAME, fields=dead_letter_fields, maxlen=10000, approximate=True)
        logger.warning("[CONSUMER] Message %s from stream '%s' moved to dead letter stream '%s' due to error: %s", message_id.decode(), stream_name, DEAD_LETTER_STREAM_NAME, error_info)
    except Exception as e_dls:
        logger.error("[CONSUMER] Failed to move message %s from stream '%s' to dead letter stream: %s", message_id.decode(), stream_name, e_dls, exc_info=True)

async def _handle_event_data(db_pool: Any, event_type: str, updates_list: List[Dict[str, Any]], message_id_for_log: str):
    """
    Handles the database update based on the event type and data.
    Raises specific exceptions for different error scenarios.
    """
    logger.info("[CONSUMER_HANDLER] Handling message %s: Event Type '%s', Updates: %s", message_id_for_log, str(event_type), updates_list)
    
    if not updates_list:
        logger.info("[CONSUMER_HANDLER] Event type '%s' for message %s had an empty updates list. No DB action taken.", event_type, message_id_for_log)
        return
    
    try:
        async with db_pool.acquire() as conn:
            logger.debug("[CONSUMER_HANDLER] Message %s: Attempting DB update for event type '%s'.", message_id_for_log, event_type)
            
            if event_type == MarketStatsEventType.TOTAL_OWNED_UPDATE.value:
                logger.info('[CONSUMER_HANDLER] Message %s: Calling bulk_update_total_owned with %s updates.', message_id_for_log, len(updates_list))
                await direct_market_stats_updater.bulk_update_total_owned(conn, updates_list)
                logger.info('[CONSUMER_HANDLER] Message %s: Successfully called bulk_update_total_owned.', message_id_for_log)
                
            elif event_type == MarketStatsEventType.UNIQUE_OWNER_UPDATE.value:
                logger.info('[CONSUMER_HANDLER] Message %s: Calling bulk_update_unique_owners with %s updates.', message_id_for_log, len(updates_list))
                await direct_market_stats_updater.bulk_update_unique_owners(conn, updates_list)
                logger.info('[CONSUMER_HANDLER] Message %s: Successfully called bulk_update_unique_owners.', message_id_for_log)
                
            elif event_type == MarketStatsEventType.FAVORITE_COUNT_UPDATE.value:
                await direct_market_stats_updater.bulk_update_favorite_counts_direct(conn, updates_list)
                
            elif event_type == MarketStatsEventType.WISHLIST_COUNT_UPDATE.value:
                await direct_market_stats_updater.bulk_update_wishlist_counts_direct(conn, updates_list)
                
            else:
                logger.warning("[CONSUMER_HANDLER] Unknown event_type '%s' in message %s. Data: %s...", event_type, message_id_for_log, updates_list[:5] if updates_list else 'N/A')
                raise UnknownEventTypeError(event_type, message_id_for_log)
                
    except MarketStatsUpdateError as update_error:
        logger.error("[CONSUMER_HANDLER] Message %s: Market stats update error for event type '%s': %s", message_id_for_log, event_type, update_error, exc_info=True)
        raise EventProcessingError(event_type, message_id_for_log, update_error)
    except Exception as db_exc:
        logger.error("[CONSUMER_HANDLER] Message %s: DB operation failed for event type '%s': %s", message_id_for_log, event_type, db_exc, exc_info=True)
        raise EventProcessingError(event_type, message_id_for_log, db_exc)

async def process_message(redis: aioredis.Redis, message_id: bytes, message_data: Dict[str, bytes], db_pool: Any) -> bool:
    """
    Processes a single message from the Redis Stream.
    Returns True if the message should be ACKed, False if it should be retried.
    Moves to DLS for unrecoverable errors.
    """
    message_id_str = message_id.decode()
    event_type_for_log = 'unknown_event_type'
    
    try:
        # Extract and validate message data
        packed_event_data = message_data.get(b'data')
        if not packed_event_data:
            error_msg = "Missing or empty 'data' field in message."
            logger.warning("[CONSUMER_PROCESS] Message %s has no 'data' field or 'data' field is empty.", message_id_str)
            await move_to_dead_letter_stream(redis, MAIN_STREAM_NAME, message_id, message_data, error_msg)
            return True
            
        # Unpack message data
        try:
            event = msgpack.unpackb(packed_event_data, raw=False)
        except msgpack.UnpackException as e_unpack:
            error_details = f'Msgpack unpack error: {str(e_unpack)}'
            logger.error('[CONSUMER_PROCESS] Failed to unpack msgpack data for message %s: %s', message_id_str, error_details, exc_info=True)
            await move_to_dead_letter_stream(redis, MAIN_STREAM_NAME, message_id, message_data, error_details)
            return True
            
        # Extract and validate event data
        event_type = event.get('event_type')
        logger.info('[CONSUMER_PROCESS] Message %s - Event data after unpack: %s', message_id_str, event)
        event_type_for_log = str(event_type) if event_type else 'unknown_event_type_in_payload'
        
        updates_list = event.get('payload')
        logger.info('[CONSUMER_PROCESS] Message %s - Event type: %s, Updates list: %s', message_id_str, str(event_type), updates_list)
        
        if event_type is None or updates_list is None:
            error_detail = f"Missing 'event_type' ({event_type}) or required 'payload' field ({updates_list}) for message {message_id_str}."
            logger.warning('[CONSUMER_PROCESS] %s Event data: %s', error_detail, event)
            await move_to_dead_letter_stream(redis, MAIN_STREAM_NAME, message_id, message_data, error_detail)
            return True
            
        # Process the event
        try:
            await _handle_event_data(db_pool, event_type, updates_list, message_id_str)
            return True
        except UnknownEventTypeError as e_unknown:
            await move_to_dead_letter_stream(redis, MAIN_STREAM_NAME, message_id, message_data, str(e_unknown))
            return True
        except EventProcessingError as e_processing:
            await move_to_dead_letter_stream(redis, MAIN_STREAM_NAME, message_id, message_data, str(e_processing))
            return True
            
    except (asyncpg.exceptions.InternalServerError, 
            asyncpg.exceptions.OperatorInterventionError, 
            asyncpg.exceptions.TooManyConnectionsError, 
            asyncpg.exceptions.ConnectionDoesNotExistError, 
            asyncpg.exceptions.InterfaceError, 
            redis_exceptions.RedisError, 
            TimeoutError, 
            asyncio.TimeoutError) as e_retryable:
        # These are retryable errors - don't ACK the message
        logger.warning(
            '[CONSUMER_PROCESS] Retryable error processing message %s (type: %s): %s - %s. Will not ACK, allowing for retry.', 
            message_id_str, event_type_for_log, type(e_retryable).__name__, str(e_retryable)
        )
        return False
        
    except asyncpg.exceptions.PostgresError as e_non_retryable_db:
        # Non-retryable database errors
        error_details = f'Non-retryable DB error: {type(e_non_retryable_db).__name__} - {str(e_non_retryable_db)}'
        logger.error(
            '[CONSUMER_PROCESS] Non-retryable Database error processing message %s (type: %s): %s', 
            message_id_str, event_type_for_log, error_details, exc_info=True
        )
        await move_to_dead_letter_stream(redis, MAIN_STREAM_NAME, message_id, message_data, error_details)
        return True
        
    except Exception as e_general:
        # General unexpected errors
        error_details = f'General unexpected error: {type(e_general).__name__} - {str(e_general)}'
        logger.error(
            '[CONSUMER_PROCESS] Unexpected error processing message %s (type: %s): %s', 
            message_id_str, event_type_for_log, error_details, exc_info=True
        )
        await move_to_dead_letter_stream(redis, MAIN_STREAM_NAME, message_id, message_data, error_details)
        return True

async def consume_events():
    """
    主消費循環。
    """
    redis = None
    db_pool = None
    consumer_name = f'{CONSUMER_NAME_PREFIX}-{asyncio.current_task().get_name()}'
    logger.info("[CONSUMER_LIFECYCLE] Consumer '%s' starting...", consumer_name)
    try:
        redis = await aioredis.from_url(REDIS_URL)
        logger.info("[CONSUMER_LIFECYCLE] Consumer '%s' connected to Redis.", consumer_name)
        await AsyncPgManager.initialize_pool()
        db_pool = AsyncPgManager.get_pool()
        if not db_pool:
            logger.error("[CONSUMER_LIFECYCLE] Consumer '%s': Database pool not available. Exiting.", consumer_name)
            return
        logger.info("[CONSUMER_LIFECYCLE] Consumer '%s' database pool initialized.", consumer_name)
        try:
            await redis.xgroup_create(name=MAIN_STREAM_NAME, groupname=CONSUMER_GROUP_NAME, id='0', mkstream=True)
            logger.info("[CONSUMER] Attempted XGROUP CREATE for stream '%s' and group '%s'.", MAIN_STREAM_NAME, CONSUMER_GROUP_NAME)
        except redis_exceptions.ResponseError as e:
            if 'BUSYGROUP Consumer Group name already exists' in str(e):
                logger.info("[CONSUMER] Consumer group '%s' already exists on stream '%s'. This is normal.", CONSUMER_GROUP_NAME, MAIN_STREAM_NAME)
                pass
            elif 'BUSYGROUP' in str(e):
                logger.warning("[CONSUMER] XGROUP CREATE for stream '%s' resulted in a BUSYGROUP error (but not 'already exists'): %s. Will proceed, but this might be an issue.", MAIN_STREAM_NAME, e)
                pass
            else:
                logger.error("[CONSUMER] Critical error creating/verifying consumer group for stream '%s': %s", MAIN_STREAM_NAME, e, exc_info=True)
                raise
        try:
            stream_exists = await redis.exists(MAIN_STREAM_NAME)
            logger.info("[CONSUMER_DIAG] Stream '%s' exists: %s", MAIN_STREAM_NAME, bool(stream_exists))
            if stream_exists:
                groups_info = await redis.xinfo_groups(MAIN_STREAM_NAME)
                group_is_present = False
                try:
                    for g in groups_info:
                        group_name = None
                        
                        if 'name' in g:
                            name_value = g['name']
                            if isinstance(name_value, bytes):
                                group_name = name_value.decode('utf-8')
                            else:
                                group_name = str(name_value)
                        
                        elif b'name' in g:
                            name_value = g[b'name']
                            if isinstance(name_value, bytes):
                                group_name = name_value.decode('utf-8')
                            else:
                                group_name = str(name_value)
                        
                        logger.debug("[CONSUMER_DIAG] 检查组信息: %s, 解析的组名: '%s'", g, group_name)
                        
                        if group_name == CONSUMER_GROUP_NAME:
                            group_is_present = True
                            logger.debug("[CONSUMER_DIAG] 找到匹配的消费者组: '%s'", group_name)
                            break
                    
                except Exception as parse_exc:
                    logger.warning("[CONSUMER_DIAG] 解析组信息时出现异常: %s, 将使用降级检查", parse_exc)
                    groups_info_str = str(groups_info)
                    if CONSUMER_GROUP_NAME in groups_info_str:
                        group_is_present = True
                        logger.info("[CONSUMER_DIAG] 通过降级检查找到消费者组")
                
                logger.info("[CONSUMER_DIAG] Group '%s' is present on stream '%s': %s", CONSUMER_GROUP_NAME, MAIN_STREAM_NAME, group_is_present)
                
                if not group_is_present:
                    logger.error("[CONSUMER_DIAG] CRITICAL DIAGNOSIS: Group '%s' was NOT found on stream '%s' after XGROUP CREATE attempt. Groups info: %s", CONSUMER_GROUP_NAME, MAIN_STREAM_NAME, groups_info)
                else:
                    logger.info("[CONSUMER_DIAG] SUCCESS: Group '%s' successfully verified on stream '%s'", CONSUMER_GROUP_NAME, MAIN_STREAM_NAME)
            else:
                logger.error("[CONSUMER_DIAG] CRITICAL DIAGNOSIS: Stream '%s' was NOT found after XGROUP CREATE attempt with mkstream=True.", MAIN_STREAM_NAME)
        except Exception as diag_exc:
            logger.error('[CONSUMER_DIAG] Error during diagnostic checks: %s', diag_exc, exc_info=True)
        while not shutdown_requested:
            try:
                messages = await redis.xreadgroup(groupname=CONSUMER_GROUP_NAME, consumername=consumer_name, streams={MAIN_STREAM_NAME: '>'}, count=CONSUME_BATCH_SIZE, block=CONSUME_BLOCK_MS)
                logger.debug("[CONSUMER_LOOP] Consumer '%s' read %s messages from stream.", consumer_name, len(messages) if messages else 0)
                if not messages:
                    continue
                message_ids_to_ack = []
                for _stream_name, message_list in messages:
                    logger.debug("[CONSUMER_LOOP] Consumer '%s' processing %s: %s messages.", consumer_name, _stream_name.decode(), len(message_list))
                    for message_id, message_data_item in message_list:
                        if await process_message(redis, message_id, message_data_item, db_pool):
                            message_ids_to_ack.append(message_id)
                        else:
                            logger.warning('[CONSUMER] Message %s processing failed (returned False), will not ACK immediately. Will be retried.', message_id.decode())
                if message_ids_to_ack:
                    ack_result = await redis.xack(MAIN_STREAM_NAME, CONSUMER_GROUP_NAME, *message_ids_to_ack)
                    logger.info("[CONSUMER_LOOP] Consumer '%s' ACKed %s messages. Result: %s", consumer_name, len(message_ids_to_ack), ack_result)
            except redis_exceptions.TimeoutError:
                continue
            except Exception as e:
                logger.error('[CONSUMER] Error in consumption loop: %s', e, exc_info=True)
                await asyncio.sleep(5)
    except Exception as e:
        logger.error("[CONSUMER_LIFECYCLE] Consumer '%s' failed to start or encountered a critical error: %s", consumer_name, e, exc_info=True)
    finally:
        logger.info("[CONSUMER_LIFECYCLE] Consumer '%s' shutting down.", consumer_name)
        if redis:
            try:
                await redis.aclose()
            except Exception as e:
                logger.error('[CONSUMER] Error closing Redis connection: %s', e, exc_info=True)
        if db_pool:
            try:
                await AsyncPgManager.close_pool()
            except Exception as e:
                logger.error('[CONSUMER] Error closing DB pool: %s', e, exc_info=True)

def handle_shutdown_signal(sig, frame):
    global shutdown_requested
    shutdown_requested = True
if __name__ == '__main__':
    signal.signal(signal.SIGINT, handle_shutdown_signal)
    signal.signal(signal.SIGTERM, handle_shutdown_signal)
    try:
        asyncio.run(consume_events())
    except KeyboardInterrupt:
        pass
    finally:
        pass