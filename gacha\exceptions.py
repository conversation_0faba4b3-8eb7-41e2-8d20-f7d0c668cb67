# gacha/exceptions.py
from typing import Optional, List, Dict, Any


class GachaError(Exception):
    """轉蛋系統基礎錯誤"""
    pass


class GachaSystemError(GachaError):
    """Base exception class for Gacha system errors."""

    pass


class RepositoryError(GachaSystemError):
    """Base exception for errors originating from repositories."""

    pass


class DatabaseOperationError(RepositoryError):
    """Raised when a database operation fails."""

    def __init__(
            self,
            message="A database operation failed.",
            original_exception=None):
        super().__init__(message)
        self.original_exception = original_exception


class CardDeletionError(RepositoryError):
    """Raised when deleting a card fails."""
    def __init__(self, message: str = "刪除卡片失敗。", card_id: Optional[int] = None, reason: Optional[str] = None):
        super().__init__(message)
        self.card_id = card_id
        self.reason = reason


class RecordNotFoundError(RepositoryError):
    """Raised when a specific record is expected but not found in the database."""

    pass


class DataMappingError(RepositoryError):
    """Raised when data from the database cannot be mapped to a Pydantic model or other structure."""

    pass


class ConfigurationError(GachaSystemError):
    """Raised for configuration-related errors."""

    pass


class CacheError(GachaSystemError):
    """Raised for errors related to caching operations (e.g., Redis)."""

    pass


class UserNotFoundError(RecordNotFoundError):
    """Raised when a specific user is expected but not found."""

    def __init__(
        self,
        message="咦？好像找不到您的帳號資訊耶，請確認一下喔！",
        user_id: Optional[int] = None,
    ):
        super().__init__(message)
        self.user_id = user_id


class InsufficientBalanceError(GachaSystemError):
    """Raised when a user's balance is insufficient for an operation."""

    def __init__(
        self,
        message="餘額不足，無法完成操作。",
        required: Optional[int] = None,
        current: Optional[int] = None,
    ):
        super().__init__(message)
        self.required = required
        self.current = current


class CardNotFoundError(RecordNotFoundError):
    """Raised when a specific card is expected but not found."""

    def __init__(self, message="Card not found.",
                 card_id: Optional[int] = None):
        super().__init__(message)
        self.card_id = card_id


class InvalidPoolTypeError(GachaSystemError):
    """Raised when an invalid pool type is provided or encountered."""

    def __init__(
        self,
        message="哎呀，選擇的卡池好像不存在或者暫時不能抽喔！Σ(°Д°;",
        pool_type: Optional[str | List[str]] = None,
    ):
        super().__init__(message)
        self.pool_type = pool_type


class StoredPriceError(GachaSystemError):
    """Raised when there is an issue with a stored price, e.g., not found or invalid."""

    pass


# New Gacha Service specific errors
class NoCardsDeterminedError(GachaSystemError):
    """Raised when no cards are determined after a draw attempt."""

    def __init__(self, message="未能確定抽取的卡片。"):
        super().__init__(message)


class BalanceUpdateError(GachaSystemError):
    """Raised when updating user balance fails during gacha process."""

    def __init__(self, message="更新餘額時發生錯誤。"):
        super().__init__(message)


class CardEnrichmentError(GachaSystemError):
    """Raised when enriching drawn card information fails."""

    def __init__(self, message="豐富卡片資訊時發生錯誤。"):
        super().__init__(message)


# Cooldown specific errors
class CooldownError(GachaSystemError):
    """Raised when a user attempts to perform an action that is on cooldown."""
    
    def __init__(self, message: str, next_available_time: Optional[int] = None, cooldown_type: Optional[str] = None):
        super().__init__(message)
        self.next_available_time = next_available_time
        self.cooldown_type = cooldown_type


# Game System specific errors
class GameSystemError(GachaSystemError):
    """Base exception for game system errors."""
    pass


class MinBetNotMetError(GameSystemError):
    """Raised when bet amount is below minimum requirement."""
    
    def __init__(self, bet_placed: int, min_bet: int):
        super().__init__(f"下注金額 {bet_placed} 低於最低要求 {min_bet}")
        self.bet_placed = bet_placed
        self.min_bet = min_bet


class InsufficientFundsError(GameSystemError):
    """Raised when user doesn't have enough funds for betting."""
    
    def __init__(self, required_amount: int, current_balance: int):
        super().__init__(f"餘額不足：需要 {required_amount}，目前 {current_balance}")
        self.required_amount = required_amount
        self.current_balance = current_balance


class GameNotFoundError(GameSystemError):
    """Raised when a game is not found or has expired."""
    
    def __init__(self, game_id: str):
        super().__init__(f"遊戲 {game_id} 不存在或已過期")
        self.game_id = game_id


class GameAlreadyOverError(GameSystemError):
    """Raised when trying to perform actions on a finished game."""
    
    def __init__(self, game_id: str):
        super().__init__(f"遊戲 {game_id} 已經結束")
        self.game_id = game_id


class InvalidGameActionError(GameSystemError):
    """Raised when an invalid game action is attempted."""
    
    def __init__(self, action: str, game_type: str):
        super().__init__(f"無效的 {game_type} 遊戲操作：{action}")
        self.action = action
        self.game_type = game_type


class GameSettlementError(GameSystemError):
    """Raised when game settlement fails."""
    
    def __init__(self, game_id: str, reason: str):
        super().__init__(f"遊戲 {game_id} 結算失敗：{reason}")
        self.game_id = game_id
        self.reason = reason


class DatabaseIntegrityError(DatabaseOperationError):
    """Raised when database integrity constraints are violated."""
    
    def __init__(self, message: str):
        super().__init__(f"資料庫完整性錯誤：{message}")


# Market System specific errors
class MarketSystemError(GachaSystemError):
    """Base exception for market system errors."""
    pass


class MarketCardNotFoundError(MarketSystemError):
    """Raised when a card is not found in market operations."""
    
    def __init__(self, message="找不到指定的卡片", card_query: Optional[str] = None):
        super().__init__(message)
        self.card_query = card_query


class MarketStockNotFoundError(MarketSystemError):
    """Raised when a stock is not found in market operations."""
    
    def __init__(self, message="找不到指定的股票", stock_symbol: Optional[str] = None):
        super().__init__(message)
        self.stock_symbol = stock_symbol


class MarketDataUnavailableError(MarketSystemError):
    """Raised when market data is temporarily unavailable."""
    
    def __init__(self, message="市場數據暫時無法獲取"):
        super().__init__(message)


class MarketServiceUnavailableError(MarketSystemError):
    """Raised when market services are unavailable."""
    
    def __init__(self, message="市場服務目前不可用"):
        super().__init__(message)


class InsufficientStockQuantityError(MarketSystemError):
    """Raised when user doesn't have enough stock quantity for an operation."""
    
    def __init__(self, message="股票數量不足", required: Optional[int] = None, current: Optional[int] = None):
        super().__init__(message)
        self.required = required
        self.current = current


class StockTradingError(MarketSystemError):
    """Raised when stock trading operations fail."""
    
    def __init__(self, message="股票交易失敗"):
        super().__init__(message)


class MarketPriceCalculationError(MarketSystemError):
    """Raised when market price calculation fails."""
    
    def __init__(self, message="市場價格計算失敗"):
        super().__init__(message)


class GachaRuntimeError(GachaSystemError):
    """Raised for general runtime errors in the gacha system."""
    
    def __init__(self, message="發生運行時錯誤"):
        super().__init__(message)


# Shop System specific errors
class ShopSystemError(GachaSystemError):
    """商店服務的基礎異常類別"""
    pass


class InvalidQuantityError(ShopSystemError):
    """數量無效時拋出的異常"""
    def __init__(self, quantity=None):
        self.quantity = quantity
        message = f"數量必須為正整數，收到: {quantity}"
        super().__init__(message)


class InvalidShopItemError(ShopSystemError):
    """商品項目無效時拋出的異常"""
    def __init__(self, item_id=None):
        self.item_id = item_id
        message = f"無效的商品項目 ID: {item_id}"
        super().__init__(message)


class InsufficientOilTicketsError(ShopSystemError):
    """油票餘額不足時拋出的異常"""
    def __init__(self, required_amount=None, current_balance=None):
        self.required_amount = required_amount
        self.current_balance = current_balance
        message = f"油票餘額不足。需要 {required_amount}，但只有 {current_balance}。"
        super().__init__(message)


class SessionCreationError(ShopSystemError):
    """創建會話時發生錯誤"""
    pass


class InvalidSessionError(ShopSystemError):
    """會話無效或已過期"""
    def __init__(self, session_id=None):
        self.session_id = session_id
        message = f"無效或已過期的兌換會話: {session_id}"
        super().__init__(message)


class MissingTicketDefinitionError(ShopSystemError):
    """會話中缺少票券定義"""
    def __init__(self, session_id=None):
        self.session_id = session_id
        message = f"會話 {session_id} 中缺少票券定義"
        super().__init__(message)


class InvalidTicketTypeError(ShopSystemError):
    """票券類型無效"""
    def __init__(self, ticket_type=None):
        self.ticket_type = ticket_type
        message = f"無效的票券類型: {ticket_type}"
        super().__init__(message)


class NoCardsSelectedError(ShopSystemError):
    """沒有選擇任何卡片"""
    def __init__(self):
        message = "沒有選擇任何卡片。"
        super().__init__(message)


class IncompleteSelectionError(ShopSystemError):
    """選擇尚未完成"""
    def __init__(self, needed=None, total=None):
        self.needed = needed
        self.total = total
        message = f"選擇尚未完成。還需要選擇 {needed} 張卡片，共 {total} 張。"
        super().__init__(message)


class MaxSelectionReachedError(ShopSystemError):
    """已達到最大選擇數量"""
    def __init__(self, max_count=None):
        self.max_count = max_count
        message = f"已選擇了最大數量的卡片 ({max_count})。"
        super().__init__(message)


class InvalidCardSelectionError(ShopSystemError):
    """卡片選擇無效"""
    def __init__(self, invalid_cards=None):
        self.invalid_cards = invalid_cards or []
        message = f"有 {len(self.invalid_cards)} 張卡片不符合兌換要求。"
        super().__init__(message)


class CardNotInSelectionError(ShopSystemError):
    """要取消選擇的卡片不在當前已選列表中"""
    def __init__(self, card_id=None):
        self.card_id = card_id
        message = f"要取消選擇的卡片 ID {card_id} 不在當前已選列表中。"
        super().__init__(message)


class ShopCardDetailsNotFoundError(ShopSystemError):
    """無法獲取卡片詳情"""
    def __init__(self, card_ids=None, missing_count=None):
        self.card_ids = card_ids
        self.missing_count = missing_count
        message = f"無法獲取所有選定卡片的詳情。缺少 {missing_count} 張卡片信息。"
        super().__init__(message)


class InvalidRarityError(ShopSystemError):
    """稀有度值無效"""
    def __init__(self, rarity=None):
        self.rarity = rarity
        message = f"兌換券定義中的稀有度值 '{rarity}' 無效。"
        super().__init__(message)


class ShopCardNotFoundError(ShopSystemError):
    """找不到符合條件的卡片"""
    def __init__(self, search_query=None, card_id=None):
        self.search_query = search_query
        self.card_id = card_id
        if card_id:
            message = f"卡片 ID {card_id} 不符合此兌換券的條件或不存在。"
        else:
            message = f"找不到符合名稱 '{search_query}' 的卡片。"
        super().__init__(message)


class TransactionError(ShopSystemError):
    """交易執行失敗"""
    pass


class OilTicketDeductionError(ShopSystemError):
    """油票扣除失敗時拋出的異常"""
    def __init__(self, message: str = "油票扣除失敗，可能餘額不足。"):
        super().__init__(message)


# 排序系統錯誤
class SortingError(GachaSystemError):
    """排序系統基礎錯誤類別"""
    pass

class CardNotMarkedAsFavoriteError(SortingError):
    """當卡片未被標記為最愛時拋出"""
    def __init__(self, card_id: int = None, message: str = "無法移動卡片：請先將卡片標記為最愛"):
        self.card_id = card_id
        super().__init__(message)

class InvalidPositionError(SortingError):
    """當目標位置無效時拋出"""
    def __init__(self, target_position: int = None, total_cards: int = None, 
                 message: str = None):
        self.target_position = target_position
        self.total_cards = total_cards
        if message is None and total_cards is not None:
            message = f"無效的位置，有效範圍: 1-{total_cards}"
        super().__init__(message or "無效的位置")

class CardAlreadyAtPositionError(SortingError):
    """當卡片已經在指定位置時拋出"""
    def __init__(self, position: int = None, page: int = None, 
                 message: str = "卡片已在指定位置"):
        self.position = position
        self.page = page
        super().__init__(message)

class CardAlreadyAtTopError(SortingError):
    """當卡片已經在最頂部時拋出"""
    def __init__(self, message: str = "卡片已在最頂部"):
        super().__init__(message)

class CardAlreadyAtBottomError(SortingError):
    """當卡片已經在最底部時拋出"""
    def __init__(self, position: int = None, page: int = None, 
                 message: str = "卡片已在最底部"):
        self.position = position
        self.page = page
        super().__init__(message)

class CardNotSortedError(SortingError):
    """當卡片未被排序或找不到時拋出"""
    def __init__(self, card_id: int = None, 
                 message: str = "無法移動卡片：卡片未排序或找不到"):
        self.card_id = card_id
        super().__init__(message)

class PreviousCardNotFoundError(SortingError):
    """當找不到前一張卡片時拋出"""
    def __init__(self, message: str = "操作失敗：無法找到前一張卡片"):
        super().__init__(message)

class NextCardNotFoundError(SortingError):
    """當找不到後一張卡片時拋出"""
    def __init__(self, message: str = "操作失敗：無法找到後一張卡片"):
        super().__init__(message)

class NoCardUpdatesProvidedError(SortingError):
    """當沒有提供卡片更新信息時拋出"""
    def __init__(self, message: str = "沒有提供卡片更新信息"):
        super().__init__(message)

class NoCardIdsProvidedError(SortingError):
    """當沒有提供卡片ID列表時拋出"""
    def __init__(self, message: str = "沒有提供卡片ID列表"):
        super().__init__(message)

# 許願系統錯誤
class WishError(GachaSystemError):
    """許願系統基礎錯誤類別"""
    pass

class CardAlreadyInWishListError(WishError):
    """當卡片已經在許願列表中時拋出"""
    def __init__(self, card_id: int = None, message: str = "該卡片已在許願列表中"):
        self.card_id = card_id
        super().__init__(message)

class WishListFullError(WishError):
    """當許願列表已滿時拋出"""
    def __init__(self, current_slots: int = None, message: str = None):
        self.current_slots = current_slots
        if message is None and current_slots is not None:
            message = f"許願列表已滿（{current_slots}/{current_slots}），請先移除一張卡片或擴充槽位"
        super().__init__(message or "許願列表已滿")

class CardNotInWishListError(WishError):
    """當卡片不在許願列表中時拋出"""
    def __init__(self, card_id: int = None, 
                 message: str = "該卡片不在許願列表中或移除失敗"):
        self.card_id = card_id
        super().__init__(message)

# 百科全書系統錯誤
class EncyclopediaError(GachaSystemError):
    """百科全書系統基礎錯誤類別"""
    pass

class CardDescriptionTooShortError(EncyclopediaError):
    """當卡片描述太短時拋出"""
    def __init__(self, min_length: int = 5, 
                 message: str = "卡片描述太短，至少需要5個字符"):
        self.min_length = min_length
        super().__init__(message)

class CardDescriptionTooLongError(EncyclopediaError):
    """當卡片描述太長時拋出"""
    def __init__(self, max_length: int = 200, 
                 message: str = "卡片描述過長，最多允許200個字符"):
        self.max_length = max_length
        super().__init__(message)

class UpdateCardDescriptionError(EncyclopediaError):
    """當更新卡片描述失敗時拋出"""
    def __init__(self, message: str = "更新卡片描述失敗"):
        super().__init__(message)

class CardPageNotFoundException(EncyclopediaError):
    """Raised when a card page is not found in the encyclopedia."""
    def __init__(self, card_id: Optional[int] = None, message: str = "找不到對應的卡片頁面"):
        super().__init__(message)
        self.card_id = card_id

# 礦坑遊戲錯誤
class MinesGameError(GameSystemError):
    """礦坑遊戲基礎錯誤類別"""
    pass

# Profile Service specific errors
class ProfileServiceError(GachaSystemError):
    """Base exception for profile service errors."""
    pass

class ProfileCardSetError(ProfileServiceError):
    """Raised when setting a profile card fails."""
    def __init__(self, message="設定個人檔案卡片失敗", card_id: Optional[int] = None):
        super().__init__(message)
        self.card_id = card_id

class ProfileBackgroundError(ProfileServiceError):
    """Raised when setting or resetting a profile background fails."""
    def __init__(self, message="設定個人檔案背景失敗"):
        super().__init__(message)

class LikeProfileError(ProfileServiceError):
    """Raised when liking a profile fails."""
    def __init__(self, message="按讚檔案失敗", cooldown: bool = False):
        super().__init__(message)
        self.cooldown = cooldown

class UserDoesNotOwnCardError(ProfileCardSetError):
    """當用戶嘗試對其不擁有的卡片執行操作時引發。"""
    def __init__(self, message: str, card_id: Optional[int] = None, user_id: Optional[int] = None):
        super().__init__(message, card_id=card_id)
        self.user_id = user_id

class FavoriteCardError(ProfileServiceError):
    """Raised when setting favorite card fails."""
    def __init__(self, message="設定最愛卡片失敗", card_id: Optional[int] = None, is_batch: bool = False):
        super().__init__(message)
        self.card_id = card_id
        self.is_batch = is_batch

class TradingError(GachaError): pass
class TradeNotFoundError(TradingError): pass
class TradeFinalizedError(TradingError): pass
class InvalidTradeOfferError(TradingError): pass
class TradeAlreadyExistsError(TradingError): pass

class EventPublishError(GachaError):
    """用於事件發布失敗的異常"""
    def __init__(self, message: str, original_exception: Optional[Exception] = None):
        super().__init__(message)
        self.original_exception = original_exception

class EconomyError(GachaError):
    """與經濟系統相關的錯誤基類"""
    def __init__(self, message: str, original_exception: Optional[Exception] = None):
        super().__init__(message)
        self.original_exception = original_exception

# Game Related Errors
class GameError(GachaError): pass
