import asyncpg
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from utils.logger import logger
from database.postgresql.async_manager import AsyncPgManager
from gacha.models.models import Card, UserCard
from gacha.repositories.card.master_card_repository import MasterCardRepository
from gacha.repositories.collection.user_collection_repository import UserCollectionRepository
from .user_service import UserService
from gacha.models.filters import CollectionFilters
from gacha.constants import MarketStatsEventType
from gacha.app_config import config_service
from gacha.utils.redis_publisher import RedisEventPublisher
from gacha.exceptions import UserNotFoundError, FavoriteCardError, CardNotFoundError, DatabaseOperationError

class FavoriteService:
    """最愛卡片服務 (Asyncpg 版本)，處理卡片標記為最愛的相關邏輯"""

    def __init__(self, pool: asyncpg.Pool, user_service: UserService, card_repo: MasterCardRepository, collection_repo: UserCollectionRepository, redis_publisher: RedisEventPublisher):
        """初始化最愛卡片服務 (Asyncpg 版本)"""
        if pool is None:
            err_msg = 'FavoriteService 初始化失敗：必須提供 asyncpg 連接池。'
            logger.error(err_msg)
            raise ValueError(err_msg)
        if card_repo is None or collection_repo is None:
            err_msg = 'FavoriteService 初始化失敗：必須提供 Repository 實例。'
            logger.error(err_msg)
            raise ValueError(err_msg)
        if redis_publisher is None:
            err_msg = 'FavoriteService 初始化失敗：必須提供 RedisEventPublisher 實例。'
            logger.error(err_msg)
            raise ValueError(err_msg)
        self.pool = pool
        self.user_service = user_service
        self.card_repo = card_repo
        self.collection_repo = collection_repo
        self.redis_publisher = redis_publisher

    async def toggle_favorite_card(self, user_id: int, card_id: int) -> bool:
        """
        切換卡片的最愛狀態，並更新 custom_sort_index。
        
        Args:
            user_id: 用戶ID
            card_id: 卡片ID
            
        Returns:
            bool: 新的最愛狀態
            
        Raises:
            CardNotFoundError: 找不到卡片或用戶不擁有此卡片
            FavoriteCardError: 更新最愛狀態失敗
        """
        # 檢查用戶和卡片
        master_card = await self.card_repo.get_card(card_id)
        if not master_card:
            raise CardNotFoundError(f'找不到此卡片 (ID: {card_id})', card_id=card_id)
            
        # 繼續之前的流程，獲取原始卡片方便錯誤訊息
        master_card_id_for_event = master_card.card_id
        
        # 檢查用戶是否擁有此卡
        user_card = await self.collection_repo.get_user_card(user_id, card_id)
        if not user_card:
            raise CardNotFoundError(f'您沒有這張卡片 (ID: {card_id})', card_id=card_id)
            
        # 檢查卡片的當前最愛狀態
        current_is_favorite = user_card.is_favorite
        new_is_favorite = not current_is_favorite
        sort_indexes_map = None
        
        async with self.collection_repo.pool.acquire() as connection:
            async with connection.transaction():
                # 檢查當前用戶最愛卡片數量
                if new_is_favorite:
                    max_index = await self.collection_repo.get_max_custom_sort_index(user_id, connection=connection)
                    base_index = (max_index if max_index is not None else -1000) + 1000
                    sort_indexes_map = {card_id: base_index}
                    
                try:
                    result_repo = await self.collection_repo.batch_set_favorite_status_raw(user_id, [card_id], new_is_favorite, sort_indexes_map if new_is_favorite else None, connection=connection)
                    updated_count = result_repo.get('updated_count', 0)
                    if updated_count == 0:
                        logger.warning('toggle_favorite_card: No rows updated for user %s, card %s', user_id, card_id)
                        # 如果沒有行被更新但沒有異常，我們假設卡片的狀態已經是我們想要的
                        # 這可能因為庫存操作導致卡片已經是想要的狀態
                except Exception as e:
                    logger.error('toggle_favorite_card: batch_set_favorite_status_raw failed for user %s, card %s: %s', user_id, card_id, str(e))
                    raise FavoriteCardError(f'更新卡片最愛狀態失敗: {str(e)}', card_id=card_id)
                    
        # 發布最愛更新事件
        payload = [(master_card_id_for_event, 1 if new_is_favorite else -1)]
        await self.redis_publisher.publish(event_type=MarketStatsEventType.FAVORITE_COUNT_UPDATE, payload=payload, batch_payload=False, user_id_for_log=user_id)
            
        return new_is_favorite

    async def set_favorite_status(self, user_id: int, card_id: int, status: bool) -> bool:
        """
        直接設置卡片的最愛狀態
        
        Args:
            user_id: 用戶ID
            card_id: 卡片ID
            status: 目標最愛狀態
            
        Returns:
            bool: 設置後的最愛狀態
            
        Raises:
            CardNotFoundError: 找不到卡片或用戶不擁有此卡片
            FavoriteCardError: 更新最愛狀態失敗
        """
        user_card = await self.collection_repo.get_user_card(user_id, card_id)
        if not user_card:
            raise CardNotFoundError('你沒有這張卡片', card_id=card_id)
            
        if user_card.is_favorite == status:
            return status
            
        return await self.toggle_favorite_card(user_id, card_id)

    async def get_favorite_cards(self, user_id: int) -> List[UserCard]:
        """(Async) 獲取用戶所有標記為最愛的卡片"""
        try:
            user = await self.user_service.get_user(user_id)
            if not user:
                logger.warning('[FavoriteService] User %s not found for get_favorite_cards.', user_id)
                return []
            return await self.collection_repo.get_favorite_user_cards(user_id)
        except Exception as e:
            logger.error('[GACHA] 獲取最愛卡片失敗: %s', str(e), exc_info=True)
            return []

    async def batch_favorite_cards(self, user_id: int, card_ids: List[int]) -> int:
        """
        批量將卡片加入最愛，並計算排序索引
        
        Args:
            user_id: 用戶ID
            card_ids: 要加入最愛的卡片ID列表
            
        Returns:
            int: 成功加入最愛的卡片數量
            
        Raises:
            ValueError: 未提供卡片ID
            CardNotFoundError: 用戶未擁有任何指定的卡片
            DatabaseOperationError: 數據庫操作失敗
        """
        if not card_ids:
            raise ValueError('沒有指定要加入最愛的卡片')
        
        updated_count_from_repo = 0
        actually_favorited_ids = []
        
        async with self.collection_repo.pool.acquire() as connection:
            async with connection.transaction():
                # 檢查哪些卡片已經是最愛
                status_query = f"""
                    SELECT card_id FROM {self.collection_repo.table_name}
                    WHERE user_id = $1 AND card_id = ANY($2::integer[]);
                """
                all_rows = await self.collection_repo._fetch(status_query, [user_id, card_ids], connection=connection)
                all_owned_ids = [row['card_id'] for row in all_rows] if all_rows else []
                
                if not all_owned_ids:
                    raise CardNotFoundError('用戶未擁有任何指定的卡片')
                    
                fav_status_query = f"""
                    SELECT card_id FROM {self.collection_repo.table_name}
                    WHERE user_id = $1 AND card_id = ANY($2::integer[]) AND is_favorite = TRUE;
                """
                fav_rows = await self.collection_repo._fetch(fav_status_query, [user_id, all_owned_ids], connection=connection)
                already_fav_ids = [row['card_id'] for row in fav_rows] if fav_rows else []
                
                # 過濾出未標記為最愛的卡片
                valid_card_ids_to_favorite = [cid for cid in all_owned_ids if cid not in already_fav_ids]
                
                if not valid_card_ids_to_favorite:
                    return 0  # 所有卡片都已經是最愛狀態，這不是錯誤
                    
                # 獲取當前最大排序索引
                max_index = await self.collection_repo.get_max_custom_sort_index(user_id, connection=connection)
                base_index = (max_index if max_index is not None else -1000) + 1000
                
                # 按稀有度排序，獲取卡片詳細信息
                cards_detail_query = f"""
                    SELECT uc.card_id, mc.rarity
                    FROM {self.collection_repo.table_name} uc
                    JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
                    WHERE uc.user_id = $1 AND uc.card_id = ANY($2::integer[])
                    ORDER BY mc.rarity DESC, mc.card_id ASC
                """
                card_details = await self.collection_repo._fetch(cards_detail_query, [user_id, valid_card_ids_to_favorite], connection=connection)
                
                if not card_details:
                    raise DatabaseOperationError('無法獲取卡片詳細信息')
                    
                sorted_cards = [{"card_id": row['card_id'], "rarity": row['rarity']} for row in card_details]
                
                sort_indexes_map: Dict[int, int] = {}
                
                for i, card_detail in enumerate(sorted_cards):
                    sort_indexes_map[card_detail['card_id']] = base_index + i * 1000
                    
                try:
                    result_repo = await self.collection_repo.batch_set_favorite_status_raw(user_id, valid_card_ids_to_favorite, True, sort_indexes_map, connection=connection)
                    updated_count_from_repo = result_repo.get('updated_count', 0)
                    
                    if updated_count_from_repo > 0:
                        actually_favorited_ids = valid_card_ids_to_favorite[:updated_count_from_repo]
                except Exception as e:
                    logger.error('batch_favorite_cards: Database operation failed: %s', e)
                    raise DatabaseOperationError(f'批量加入最愛失敗: {str(e)}')
                    
        if updated_count_from_repo > 0 and actually_favorited_ids:
            updates_payload = [(card_id, 1) for card_id in actually_favorited_ids]
            if updates_payload:
                await self.redis_publisher.publish(event_type=MarketStatsEventType.FAVORITE_COUNT_UPDATE, payload=updates_payload, batch_payload=False, user_id_for_log=user_id)
                
        return updated_count_from_repo

    async def batch_unfavorite_cards(self, user_id: int, card_ids: List[int]) -> int:
        """
        批量將卡片移出最愛，並觸發市場統計更新事件
        
        Args:
            user_id: 用戶ID
            card_ids: 要移出最愛的卡片ID列表
            
        Returns:
            int: 成功移出最愛的卡片數量
            
        Raises:
            ValueError: 未提供卡片ID
            DatabaseOperationError: 數據庫操作失敗
        """
        if not card_ids:
            raise ValueError('沒有指定要移出最愛的卡片')
        
        updated_count_from_repo = 0
        unfavorited_card_ids_for_event = []
        
        async with self.collection_repo.pool.acquire() as connection:
            async with connection.transaction():
                # 檢查哪些卡片已經是最愛
                status_query = f"""
                    SELECT card_id FROM {self.collection_repo.table_name}
                    WHERE user_id = $1 AND card_id = ANY($2::integer[]) AND is_favorite = TRUE;
                """
                fav_rows = await self.collection_repo._fetch(status_query, [user_id, card_ids], connection=connection)
                currently_favorited_ids_in_batch = [row['card_id'] for row in fav_rows] if fav_rows else []
                
                if not currently_favorited_ids_in_batch:
                    return 0  # 所有卡片都已非最愛狀態，這不是錯誤
                    
                ids_to_actually_unfavorite = [cid for cid in card_ids if cid in currently_favorited_ids_in_batch]
                
                if not ids_to_actually_unfavorite:
                    return 0  # 沒有有效的卡片可從最愛中移除，這不是錯誤
                
                try:
                    result_repo = await self.collection_repo.batch_set_favorite_status_raw(user_id, ids_to_actually_unfavorite, False, None, connection=connection)
                    updated_count_from_repo = result_repo.get('updated_count', 0)
                    unfavorited_card_ids_for_event = ids_to_actually_unfavorite[:updated_count_from_repo]
                except Exception as e:
                    logger.error('batch_unfavorite_cards: Database operation failed: %s', e)
                    raise DatabaseOperationError(f'批量移出最愛失敗: {str(e)}')
        
        if updated_count_from_repo > 0 and unfavorited_card_ids_for_event:
            updates_payload = [(card_id, -1) for card_id in unfavorited_card_ids_for_event]
            if updates_payload:
                await self.redis_publisher.publish(event_type=MarketStatsEventType.FAVORITE_COUNT_UPDATE, payload=updates_payload, batch_payload=False, user_id_for_log=user_id)
        
        return updated_count_from_repo

    def get_filtered_cards_description(self, pool_type: Optional[str]=None, rarity_filter: Optional[List[int]]=None, series_filter: Optional[str]=None) -> str:
        """獲取經過篩選的卡片描述文本 (處理數字稀有度)"""
        filters = []
        if pool_type:
            filters.append(f'卡池: {pool_type}')
        if rarity_filter:
            display_rarities = [config_service.get_rarity_display_codes().get(r, str(r)) for r in rarity_filter]
            filters.append(f"稀有度: {', '.join(display_rarities)}")
        if series_filter:
            filters.append(f'系列: {series_filter}')
        return ' | '.join(filters) if filters else '全部卡片'