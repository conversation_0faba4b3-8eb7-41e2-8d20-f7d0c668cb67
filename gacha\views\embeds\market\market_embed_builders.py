import discord
from typing import List, Optional, Union, Any
from decimal import Decimal
import io
from utils.logger import logger
from gacha.app_config import config_service
from gacha.constants import RarityLevel
from gacha.views import utils as view_utils
from gacha.services.market.market_view_service import StockListPageData, StockDetailData, NewsPageData, NewsItemData, CardInfoData, CardMarketStatsData, LinkedStockData
from gacha.models.market_models import StockLifecycleStatus
from ..base_embed_builder import BaseEmbedBuilder
MY2_ANIMATED_EMOJI = '<a:my2:1370641774950875146>'
REPLY_COUNT_EMOJI = '<:ReplyCont:1357534065841930290>'
REPLY_EMOJI = '<:Reply:1357534074830590143>'

class StockListEmbedBuilder(BaseEmbedBuilder):

    def __init__(self, page_data: StockListPageData, interaction: Optional[discord.Interaction]=None):
        super().__init__(data=page_data, interaction=interaction)

    def build(self) -> discord.Embed:
        page_data: StockListPageData = self.data
        title = f'💹 股市行情 (第 {page_data.current_page}/{page_data.total_pages} 頁)'
        embed = self._create_base_embed(title=title, color=discord.Color.blue())
        if not page_data.stocks:
            embed.description = '目前市場上沒有股票。'
            footer_text = f'總共 {page_data.total_stocks} 支股票，{page_data.total_pages} 頁'
            self._set_footer(embed, text=footer_text)
            return embed
        for stock in page_data.stocks:
            price = stock.current_price
            price_str = self._format_decimal(price, precision=2)
            percentage_str, trend_emoji = self._format_percentage_change(current_price=price, previous_price=stock.previous_price)
            trend_display_str = f'`{percentage_str}`{trend_emoji}'
            volume_24h_str = self._format_decimal(getattr(stock, 'volume_24h', None), precision=0, default_on_none='N/A')
            status_suffix = ''
            if stock.lifecycle_status == StockLifecycleStatus.ST:
                status_suffix = ''
            elif stock.lifecycle_status == StockLifecycleStatus.DELISTED:
                status_suffix = ' (已退市)'
            display_name = f'{self._truncate_text(stock.asset_name, 20)}{status_suffix}'
            field_name = '\u200b'
            field_value = f"{('<a:Error:1371096622053724292>' if stock.lifecycle_status == StockLifecycleStatus.ST else MY2_ANIMATED_EMOJI)} **{stock.asset_symbol}** - {display_name}\n{REPLY_COUNT_EMOJI} 價格 : `{price_str}` {config_service.get_oil_emoji()}\n{REPLY_COUNT_EMOJI} 趨勢 : {trend_display_str}\n{REPLY_EMOJI} 成交量 : `{volume_24h_str}` 股"
            self._add_field(embed, name=field_name, value=field_value, inline=True)
        footer_text = f'總共 {page_data.total_stocks} 支股票，{page_data.total_pages} 頁｜可以使用/news指令 查看最近新聞來確認股票時事。\n註：所有成交量數據均為過去24小時統計。'
        self._set_footer(embed, text=footer_text)
        return embed

class NewsEmbedBuilder(BaseEmbedBuilder):

    def __init__(self, news_data: Optional[NewsItemData], current_item_index: int, total_items: int, interaction: Optional[discord.Interaction]=None):
        super().__init__(data=news_data, interaction=interaction)
        self.current_item_index = current_item_index
        self.total_items = total_items

    def build(self) -> discord.Embed:
        news_item: Optional[NewsItemData] = self.data
        if not news_item:
            embed = self._create_base_embed(title='📰 市場快訊', description='沒有找到符合條件的新聞。', color=discord.Color.orange())
            page_info = '第 0/0 條'
            self._set_footer(embed, text=page_info)
            return embed
        news_type_raw = news_item.news_type
        default_color_int = config_service.get_news_type_colors_int().get('default_news_color', discord.Color.dark_grey().value)
        embed_color_int = config_service.get_news_type_colors_int().get(news_type_raw, default_color_int)
        embed_color = discord.Color(embed_color_int)
        status_suffix = ''
        if news_item.asset_symbol and news_item.asset_lifecycle_status == StockLifecycleStatus.ST:
            status_suffix = f' ({news_item.asset_symbol} <a:Error:1371096622053724292>)'
        embed_title = f'📰 {news_item.headline}{status_suffix}'
        embed_description = news_item.content
        embed = self._create_base_embed(title=embed_title, description=embed_description, color=embed_color)
        author_display = news_item.character_name or news_item.source or '未知來源'
        self._add_field(embed, name='發布者', value=author_display, inline=True)
        news_type_display = config_service.get_news_type_readable_names().get(news_type_raw, self._format_optional_value(news_type_raw, default_text='未分類'))
        self._add_field(embed, name='新聞類型', value=news_type_display, inline=True)
        published_time_emoji = '🗓️'
        published_at_str = discord.utils.format_dt(news_item.published_at, style='F') if news_item.published_at else self.DEFAULT_NO_DATA_TEXT
        self._add_field(embed, name=f'{published_time_emoji} 發布時間', value=published_at_str, inline=False)
        page_info_str = f'第 {self.current_item_index + 1}/{self.total_items} 條' if self.total_items > 0 else '無符合條件的新聞'
        self._set_footer(embed, text=page_info_str)
        return embed

class CardInfoEmbedBuilder(BaseEmbedBuilder):

    def __init__(self, card_info_data: CardInfoData, interaction: Optional[discord.Interaction]=None):
        super().__init__(data=card_info_data, interaction=interaction)

    def _build_market_stats_text(self, market_stats: Optional[CardMarketStatsData]) -> str:
        if not market_stats:
            return self._format_optional_value(None)
        lines = [f'總擁有量: `{self._format_optional_value(market_stats.total_owned_quantity)}`', f'獨立擁有者: `{self._format_optional_value(market_stats.unique_owner_count)}`', f'許願數量: `{self._format_optional_value(market_stats.wishlist_count)}`', f'收藏數量: `{self._format_optional_value(market_stats.favorite_count)}`']
        return '\n'.join(lines)

    def build(self) -> discord.Embed:
        card_data: CardInfoData = self.data
        if not card_data:
            embed = self._create_base_embed(title='錯誤', description='卡片資料遺失。', color=discord.Color.red())
            self._set_footer(embed, text='請稍後再試')
            return embed
        embed_title = f"{self._truncate_text(card_data.name, 200, field_type='卡片名稱')} (ID: {card_data.card_id})"
        rarity_level_enum_for_visuals: Optional[RarityLevel] = None
        if card_data.raw_card_data and hasattr(card_data.raw_card_data, 'rarity') and isinstance(card_data.raw_card_data.rarity, RarityLevel):
            rarity_level_enum_for_visuals = card_data.raw_card_data.rarity
        elif card_data.rarity is not None:
            try:
                rarity_level_enum_for_visuals = RarityLevel(card_data.rarity)
            except ValueError:
                logger.warning('CardInfoEmbedBuilder: Invalid integer rarity %s for card %s', card_data.rarity, card_data.card_id)
        embed_color = view_utils.get_rarity_color(rarity_level=rarity_level_enum_for_visuals, pool_type=card_data.pool_type)
        embed = self._create_base_embed(title=embed_title, color=embed_color)
        if card_data.image_url:
            embed.set_image(url=card_data.image_url)
        rarity_icon_url = view_utils.get_rarity_image(rarity_level_enum_for_visuals)
        self._set_thumbnail(embed, rarity_icon_url)
        self._add_field(embed, name='系列', value=self._format_optional_value(card_data.series), inline=True)
        rarity_to_display = self.DEFAULT_NO_DATA_TEXT
        if rarity_level_enum_for_visuals:
            rarity_to_display = view_utils.get_user_friendly_rarity_name(rarity_level_enum_for_visuals)
        elif card_data.rarity is not None:
            rarity_to_display = f'T{card_data.rarity}'
        self._add_field(embed, name='稀有度', value=rarity_to_display, inline=True)
        self._add_field(embed, name='卡池類型', value=self._format_optional_value(card_data.pool_type), inline=True)
        price_str = self._format_decimal(card_data.current_market_sell_price, precision=0, default_on_none='價格計算中')
        self._add_field(embed, name='目前市場價', value=f'`{price_str}` {config_service.get_oil_emoji()}', inline=True)
        if card_data.description:
            self._add_field(embed, name='描述', value=card_data.description, inline=False)
        if card_data.market_stats:
            stats_text = self._build_market_stats_text(card_data.market_stats)
            self._add_field(embed, name='市場統計', value=stats_text, inline=False)
        self._set_footer(embed, text='卡片基本信息')
        return embed

class LinkedStocksEmbedBuilder(BaseEmbedBuilder):

    def __init__(self, card_name: str, linked_stocks_data: List[LinkedStockData], interaction: Optional[discord.Interaction]=None):
        super().__init__(data=linked_stocks_data, interaction=interaction)
        self.card_name = card_name

    def build(self) -> discord.Embed:
        linked_stocks: List[LinkedStockData] = self.data
        embed_title = f"影響 {self._truncate_text(self.card_name, 200, field_type='卡片名稱')} 的部分關聯股票"
        embed = self._create_base_embed(title=embed_title, color=discord.Color.orange())
        if not linked_stocks:
            embed.description = f"目前沒有找到與 {self._truncate_text(self.card_name, 100, field_type='卡片名稱')} 明確關聯的股票信息。"
            return embed
        description_lines = []
        for stock in linked_stocks:
            asset_symbol_str = self._truncate_text(stock.asset_symbol, 50, field_type='股票代碼')
            asset_name_str = self._truncate_text(stock.asset_name, 100, field_type='股票名稱')
            price_str = self._format_decimal(stock.current_price, precision=2)
            status_suffix = ''
            if stock.lifecycle_status == StockLifecycleStatus.ST:
                status_suffix = ' <a:Error:1371096622053724292>'
            elif stock.lifecycle_status == StockLifecycleStatus.DELISTED:
                status_suffix = ' (已退市)'
            description_lines.append(f'**{asset_symbol_str}** ({asset_name_str}{status_suffix}): `{price_str}` {config_service.get_oil_emoji()}')
        final_description = '\n'.join(description_lines)
        embed.description = self._truncate_text(final_description, 4096, field_type='關聯股票列表')
        return embed