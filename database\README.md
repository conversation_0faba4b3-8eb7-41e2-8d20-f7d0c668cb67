# 数据库模块设计文档

## 单例模式与系统类型区分

本项目中实现了基于系统类型的单例模式，确保每个系统（如PVE、GACHA、LADDER等）有自己独立的数据库连接管理器和工作单元实例。

### PostgresqlManager

`PostgresqlManager`类实现了基于系统类型的单例模式，确保每个系统类型只有一个数据库管理器实例：

```python
class PostgresqlManager:
    """统一的PostgreSQL连接管理器"""
    
    _instances = {}  # 系统类型 -> 实例的映射
    _instance_lock = threading.Lock()
    
    def __new__(cls, system_type: str, db_config: Optional[Dict[str, Any]] = None):
        """确保每个系统类型有一个单独的实例"""
        system_type = system_type.upper()
        
        with cls._instance_lock:
            if system_type not in cls._instances:
                instance = super(PostgresqlManager, cls).__new__(cls)
                instance._initialized = False
                cls._instances[system_type] = instance
                logger.info(f"创建系统类型 [{system_type}] 的PostgresqlManager实例")
        
        return cls._instances[system_type]
```

### UnitOfWork

`UnitOfWork`类同样实现了基于系统类型的单例模式，确保每个系统有独立的工作单元实例：

```python
class UnitOfWork:
    """统一工作单元模式实现，提供一致的事务管理机制"""
    
    _instances = {}  # 系统类型 -> 实例的映射
    _instance_lock = threading.Lock()
    
    def __new__(cls, db_manager=None):
        """确保每个系统类型有一个单独的实例"""
        system_type = getattr(db_manager, 'system_type', 'DEFAULT') if db_manager else 'DEFAULT'
        
        with cls._instance_lock:
            if system_type not in cls._instances:
                instance = super(UnitOfWork, cls).__new__(cls)
                instance._initialized = False
                cls._instances[system_type] = instance
                logger.info(f"创建系统类型 [{system_type}] 的UnitOfWork实例")
            
            return cls._instances[system_type]
```

## 为什么系统类型区分很重要？

不同系统（PVE、GACHA、LADDER等）使用同一个单例实例会导致以下问题：

1. **数据库隔离性问题**：不同系统可能使用不同的数据库或不同的表，共享同一个连接会造成混乱。

2. **事务隔离问题**：一个系统的事务可能会意外影响另一个系统的操作，如GACHA系统的回滚操作可能会影响PVE系统的操作。

3. **并发安全问题**：多个系统共享同一个实例时，如果没有正确的线程同步机制，可能导致并发问题。

## 实现方式

1. **基于系统类型的实例映射**：使用静态字典`_instances`存储不同系统类型的实例。

2. **线程安全保证**：使用`threading.Lock()`确保在多线程环境下实例创建的安全性。

3. **延迟初始化**：实例在首次请求时才创建，提高资源利用效率。

## 使用示例

```python
# 获取PVE系统的数据库管理器
pve_db = PostgresqlManager('PVE')

# 获取GACHA系统的数据库管理器
gacha_db = PostgresqlManager('GACHA')

# 创建相应的工作单元
pve_uow = UnitOfWork(pve_db)
gacha_uow = UnitOfWork(gacha_db)

# pve_uow和gacha_uow是两个不同的实例，操作互不影响
```

这种设计确保了不同系统的数据库操作之间的隔离性和一致性，同时保持了单例模式的资源节约优势。 