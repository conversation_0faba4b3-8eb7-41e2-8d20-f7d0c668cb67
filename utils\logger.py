import logging
import os
import sys
import datetime
from typing import Optional

# 建立日誌目錄
LOG_DIR = 'logs'
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

# 設定日誌格式
FORMATTER_STRING = '%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s'
formatter = logging.Formatter(FORMATTER_STRING)

# 設定日誌文件
current_time_obj = datetime.datetime.now()
timestamp_str = current_time_obj.strftime('%Y-%m-%d_%H-%M-%S')
log_file_name_with_path = os.path.join(LOG_DIR, f'app_{timestamp_str}.log')

# 文件處理器
file_handler = logging.FileHandler(filename=log_file_name_with_path, mode='a', encoding='utf-8')
file_handler.setFormatter(formatter)

# 控制台處理器
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(formatter)

# 初始化日誌器，級別將由 init_logging 統一管理
root_logger = logging.getLogger()
root_logger.addHandler(file_handler)
root_logger.addHandler(console_handler)

# 設置應用日誌器
logger = logging.getLogger('app')
logger.propagate = True

# 從 init_logging.py 搬移過來的日誌級別映射表
LOG_LEVEL_MAP = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

def initialize_logging(force_level: Optional[str] = None) -> str:
    """初始化全局日誌系統
    
    Args:
        force_level: 強制設置的日誌級別，覆蓋環境變數
        
    Returns:
        str: 設置的日誌級別名稱
    """
    # 從環境變數或傳入參數獲取日誌級別
    env_log_level = force_level or os.getenv('LOG_LEVEL', 'INFO').upper()
    level = LOG_LEVEL_MAP.get(env_log_level, logging.INFO)
    
    # 設置根日誌級別
    # root_logger 已經在前面被取得並設定了 handlers
    root_logger.setLevel(level)
    
    # 設置常用模組的日誌級別
    logging.getLogger('app').setLevel(level)
    logging.getLogger('ai_assistant').setLevel(level)
    logging.getLogger('database').setLevel(level)
    logging.getLogger('gacha').setLevel(level)
    logging.getLogger('image_processing').setLevel(level)
    logging.getLogger('discord').setLevel(level)
    logging.getLogger('TimerUtils').setLevel(level)
    logging.getLogger('AI_Service_Base').setLevel(level)
    logging.getLogger('AI_Commands').setLevel(level)
    logging.getLogger('MessageHandler').setLevel(level)
    logging.getLogger('QAService').setLevel(level)
    logging.getLogger('ImageUtils').setLevel(level)
    logging.getLogger('OutfitRaterCommands').setLevel(level)
    logging.getLogger('RatingImageGenerator').setLevel(level)
    logging.getLogger('RatingHistory').setLevel(level)
    logging.getLogger('OutfitRater').setLevel(level)
    
    # 使用 root_logger 輸出，因為 'app' logger 可能還沒完全配置好
    # 或者，如果我們確定 'app' logger 的 handler 已經配置，也可以用 logger.info
    print(f'全局日誌級別已設定為: {env_log_level}') # 在 console_handler 初始化後執行是安全的
    return env_log_level

# 在模組導入時立即初始化日誌系統
current_log_level = initialize_logging()

# 導出
__all__ = ['logger', 'root_logger', 'current_log_level', 'initialize_logging']