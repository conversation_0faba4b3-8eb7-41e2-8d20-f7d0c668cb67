# Gacha 里程碑獎勵系統價格表 V23 (2025/06/15) - 統一期望值2.0倍非重置版

以下是調整後的里程碑獎勵價格表，所有卡池採用相同的2.0倍期望抽數作為里程碑觸發點，並明確為非重置型累計機制，旨在確保系統一致性、公平性及對長期玩家的穩定回饋。

| 卡池 (`pool_type`) | 抽卡成本 | 最高階 (Rarity) | 售價 (油) | 機率   | 隨機券 (票) | 指定卡 (票) | 次高階 (Rarity) | 售價 (油) | 機率   | 隨機券 (票) | 指定卡 (票) |
|--------------------|---------|----------------|-----------|--------|-------------|-------------|----------------|-----------|--------|-------------|-------------|
| `main`             | 50 油   | R7             | 55,000    | 0.01%  | 5,000       | 10,000      | R6             | 12,000    | 0.05%  | 1,000       | 2,000       |
| `special`          | 140 油  | R5             | 42,000    | 0.05%  | 2,800       | 5,600       | R4             | 13,000    | 0.15%  | 934         | 1,867       |
| `summer`           | 130 油  | R6             | 40,000    | 0.05%  | 2,600       | 5,200       | R5             | 12,000    | 0.15%  | 867         | 1,733       |
| `vd`               | 150 油  | R6             | 45,000    | 0.05%  | 3,000       | 6,000       | R5             | 15,000    | 0.15%  | 1,000       | 2,000       |
| `special_maid`     | 160 油  | R5             | 50,000    | 0.05%  | 3,200       | 6,400       | R4             | 17,000    | 0.15%  | 1,067       | 2,133       |
| `hololive`         | 150 油  | R7             | 60,000    | 0.01%  | 15,000      | 30,000      | R6             | 22,000    | 0.05%  | 3,000       | 6,000       |
| `ua`               | 120 油  | R7             | 50,000    | 0.01%  | 12,000      | 24,000      | R6             | 20,000    | 0.05%  | 2,400       | 4,800       |
| `ptcg`             | 90 油   | R7             | 35,000    | 0.01%  | 9,000       | 18,000      | R6             | 15,000    | 0.05%  | 1,800       | 3,600       |

### **計算公式說明**

本表格採用了以下統一設計原則：

1.  **核心設計理念**：
    *   此為 **非重置型里程碑獎勵系統**：玩家的抽卡次數會持續累計，不會因為中途抽到目標卡片而重置。
    *   所有卡池的里程碑獎勵（指定卡/隨機券）觸發抽數均為對應卡片期望抽數的 **2.0 倍**。
    *   票數價值直接基於機率和抽卡成本設計，使系統公平透明。

2.  **統一計算公式**：
    ```
    期望抽數 = 1 / 機率
    里程碑抽數 = 期望抽數 × 2.0
    指定卡票數 = 里程碑抽數 × (抽卡成本 / 100)
    隨機券票數 = 指定卡票數 ÷ 2
    ```
    （所有票數結果四捨五入到整數）

3.  **計算示例**：
    *   主卡池R7隨機券：
        *   期望抽數：1 / 0.0001 = 10,000
        *   里程碑抽數：10,000 × 2.0 = 20,000
        *   指定卡：20,000 × (50 / 100) = 10,000 票
        *   隨機券：10,000 ÷ 2 = 5,000 票
    *   special卡池R5指定卡：
        *   期望抽數：1 / 0.0005 = 2,000
        *   里程碑抽數：2,000 × 2.0 = 4,000
        *   指定卡：4,000 × (140 / 100) = 5,600 票
        *   隨機券：5,600 ÷ 2 = 2,800 票

4.  **設計原則**：
    *   **非重置累積**：抽卡次數為持續累積，達到里程碑抽數即可獲得獎勵，該計數不重置。
    *   **一致性回饋**：所有卡池統一使用2.0倍期望抽數作為里程碑觸發點，為持續投入的玩家提供穩定可預期的回饋。
    *   **價值平衡**：各卡池卡片售價仍保持差異，反映其價值定位。里程碑獎勵的獲取成本與卡片價值和稀有度相關聯。

### **備註**
*   油票獲取：每 100 油 = 1 票。
*   不提供 R4（若非次高階）及以下兌換（此處指表格未列出的更低階卡片）。
*   混合池沿用主卡池價格（50 油）及其對應的里程碑設定。
*   所有票數計算結果四捨五入到整數。

## 期望抽數與里程碑抽數分析

下表分析了各卡池最高稀有度的期望抽數與里程碑抽數的關係：

| 卡池           | 最高稀有度 | 機率   | 期望抽數 | 里程碑抽數 | 里程碑抽數/期望比 |
|----------------|------------|--------|----------|------------|-------------------|
| `main`         | R7         | 0.01%  | 10,000   | 20,000     | 2.00x             |
| `special`      | R5         | 0.05%  | 2,000    | 4,000      | 2.00x             |
| `summer`       | R6         | 0.05%  | 2,000    | 4,000      | 2.00x             |
| `vd`           | R6         | 0.05%  | 2,000    | 4,000      | 2.00x             |
| `special_maid` | R5         | 0.05%  | 2,000    | 4,000      | 2.00x             |
| `hololive`     | R7         | 0.01%  | 10,000   | 20,000     | 2.00x             |
| `ua`           | R7         | 0.01%  | 10,000   | 20,000     | 2.00x             |
| `ptcg`         | R7         | 0.01%  | 10,000   | 20,000     | 2.00x             |

### **分析結果**

1.  **系統一致性與透明度**：
    *   所有卡池統一使用2.0倍期望抽數作為里程碑獎勵的觸發條件。
    *   票數計算直接基於抽數、成本和機率，使用統一公式，確保系統的數學一致性、邏輯性和可預測性。
    *   明確的非重置機制，方便玩家理解和規劃。

2.  **里程碑獎勵的定位**：
    *   里程碑抽數設定為期望值的2倍，旨在為長期參與的玩家提供一個重要的、可預測的獎勵。
    *   它作為對持續投入的穩定回饋，補充而非完全取代隨機抽卡的價值和樂趣。
    *   這樣的設定使得里程碑獎勵雖然是重要的獲取途徑之一，但不至於完全主導卡片的整體獲取速度，玩家的運氣因素依然扮演著不可或缺的角色。此機制旨在平衡隨機性與確定性回饋。

3.  **卡池特色保留**：
    *   雖然里程碑獎勵的計算規則統一，但各卡池仍透過抽卡成本、卡片稀有度（及其對應的基礎機率）和售價來保留其特色和價值定位。
    *   合作IP（如`hololive`）或特殊主題卡池（如`ua`, `ptcg`）依然可以透過其基礎設定（如高稀有度卡的高油耗售價）來體現其獨特性和較高的價值。

