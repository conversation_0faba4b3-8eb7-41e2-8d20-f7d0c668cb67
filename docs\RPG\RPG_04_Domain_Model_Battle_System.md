**IV. 核心領域模型與戰鬥系統設計**

本章節詳細描述構成戰鬥系統核心的領域對象及其職責，旨在實現一個"簡單不複雜"且易於擴展的設計。

1.  **`Battle` (戰鬥實例)**
    *   **屬性：**
        *   `battle_id` (唯一標識)
        *   `player_team` (List of `Combatant` objects)
        *   `monster_team` (List of `Combatant` objects)
        *   `current_turn` (int)
        *   `battle_log` (List of `BattleLogEntry` objects)
        *   `battle_status` (Enum: `PENDING`, `IN_PROGRESS`, `PLAYER_WIN`, `MONSTER_WIN`, `DRAW`)
        *   `rng_seed` (可選, 用於可復現的戰鬥，調試時有用)
        *   `combatant_queue` (List of `Combatant` ids, 行動順序隊列)
    *   **方法：**
        *   `start()`: 初始化戰鬥，計算所有 `Combatant` 的初始屬性，決定先手並生成初始行動順序隊列。
        *   `next_turn()`: 推進到下一回合或下一行動單位，處理回合開始/結束邏輯。
        *   `get_acting_combatant()`: 從行動隊列中獲取當前行動的單位。
        *   `process_action(caster: Combatant, skill_id: str, target_ids: List[str])`: 核心方法，處理一個單位的行動。
        *   `check_win_condition()`: 檢查戰鬥是否結束。
        *   `add_log_entry(event_description, ...)`: 記錄戰鬥日誌。
        *   `update_combatant_queue()`: (可選) 根據速度變化等因素更新行動隊列。

2.  **`Combatant` (戰鬥單位 - 卡牌或怪物)**
    *   **靜態/初始化時設定的屬性 (Static/Initialization Attributes):**
        *   `instance_id` (唯一實例ID，例如玩家卡牌的數據庫ID或臨時生成的怪物實例ID)
        *   `definition_id` (卡牌ID或怪物ID，用於查找JSON配置，例如 `cards.json` 中的 `card_id` 或 `monsters.json` 中的 `monster_id`)
        *   `name` (字符串, 從配置或玩家數據中獲取)
        *   `is_player_side` (bool)
        *   `rpg_level` (整數, 對於玩家卡牌，來自 `gacha_user_collections.rpg_level`)
        *   `star_level` (整數, 0-35, 對於玩家卡牌，來自 `gacha_user_collections.star_level`)
        *   `skill_order_preference` (List of `skill_id`s, 玩家卡牌定義主動技能的優先釋放順序；對於怪物，則對應 `monsters.json` 中配置的 `active_skill_order`)
        *   `primary_attack_skill` (`SkillInstance`, 卡牌/怪物的普攻技能實例)
        *   `active_skills` (List of `SkillInstance`, 裝備的主動技能實例列表)
        *   `innate_passive` (`SkillInstance`, 天賦被動技能實例，效果受 `star_level` 影響)
        *   `common_passives` (List of `SkillInstance`, 裝備的通用被動技能實例列表)
        *   `position` (整數, 例如 0-2 代表前中後排，或更詳細的站位索引)
    *   **動態戰鬥屬性 (Dynamic Combat Attributes):**
        *   `max_hp`, `current_hp` (整數)
        *   `max_mp`, `current_mp` (整數)
        *   `current_stats` (字典，包含所有戰鬥中計算後的屬性: `patk`, `pdef`, `spd`, `crit_rate`, `crit_dmg_multiplier`, `accuracy`, `evasion` 等，會受Buff/Debuff影響)
        *   `status_effects` (List of `StatusEffectInstance` - 當前承受的Buff/Debuff實例列表)
    *   **方法：**
        *   `calculate_final_stats(all_configs: AllConfigsData)`: 初始化時根據戰鬥單位自身的 `definition_id`, `rpg_level`, `star_level` 以及裝備的技能等級，結合傳入的全局配置數據 (`all_configs`)，計算其最終戰鬥屬性 (`max_hp`, `max_mp`, `current_stats`)。此方法通常在戰鬥單位實例化後調用，以設定其戰鬥開始時的狀態。其核心計算邏輯通常委託給一個獨立的 `AttributeCalculator` 服務/模塊 (詳見 `RPG_05_Core_Runtime_Logic.md`)。
        *   `take_damage(amount: float, damage_type: str, is_crit: bool, battle_context: Battle)`: 處理傷害，更新 `current_hp`。可能觸發被動。
        *   `heal(amount: float, is_crit_heal: bool, battle_context: Battle)`: 處理治療，更新 `current_hp`。可能觸發被動。
        *   `consume_mp(amount: int)`: 消耗法力值，更新 `current_mp`。
        *   `add_status_effect(status_effect_instance: StatusEffectInstance, battle_context: Battle)`: 添加狀態效果。可能觸發被動。
        *   `remove_status_effect(status_effect_id: str, battle_context: Battle)`: 移除狀態效果。可能觸發被動。
        *   `get_available_action(battle_context: Battle) -> Tuple[str, List[str]]`: 核心決策邏輯。
            *   嚴格按照 `skill_order_preference` (對於玩家卡牌) 或 `active_skill_order` (對於怪物，來自 `monsters.json` 配置) 中定義的技能ID列表順序進行迭代。
            *   對於列表中的每一個 `skill_id`，檢查其對應的 `SkillInstance` 是否可用 (例如，通過調用 `skill_instance.is_usable(self.current_mp)`，判斷MP是否足夠、冷卻是否為0)。
            *   返回列表中第一個判定為可用的 `skill_id` 和一個建議的目標ID列表 (目標選擇的初步建議，最終目標由 `TargetSelector` 決定)。
            *   如果遍歷完整個技能順序列表後，沒有找到任何可用的主動技能，則選擇並返回其普攻技能 (`self.primary_attack_skill.skill_id`) 和對應的建議目標。
        *   `apply_turn_start_effects(battle_context: Battle)`: 應用回合開始時觸發的被動和狀態效果。
        *   `apply_turn_end_effects(battle_context: Battle)`: 應用回合結束時觸發的被動和狀態效果。
        *   `tick_cooldowns()`: 更新自身所有主動技能的冷卻時間。
        *   `tick_status_effects(battle_context: Battle)`: 更新自身狀態效果的持續時間並觸發其周期性效果 (如DOT傷害, HOT治療)。
        *   `is_alive() -> bool`: 判斷是否存活 (通常是 `current_hp > 0`)。
        *   `can_act(battle_context: Battle) -> bool`: 判斷是否能夠行動 (例如，是否被暈眩、冰凍等 `CANNOT_ACT` 狀態影響)。
        *   `get_skill_instance(skill_id: str) -> Optional[SkillInstance]`: 根據技能ID從其擁有的技能列表中獲取技能實例。

3.  **`SkillInstance` (技能實例 - 在`Combatant`中使用)**
    *   **屬性：**
        *   `skill_id` (字符串, 指向 `active_skills.json` 或 `passive_skills.json` 或 `innate_passive_skills.json` 中的技能定義鍵)
        *   `skill_type` (枚舉: `ACTIVE`, `PASSIVE`, `INNATE_PASSIVE`, `PRIMARY_ATTACK`)
        *   `current_level` (整數):
            *   對於通用主動/被動技能 (`ACTIVE`, `PASSIVE`) 以及普攻技能 (`PRIMARY_ATTACK`)，此等級來源於玩家對該 `skill_id` 的全局熟練度等級 (從 `gacha_user_learned_global_skills` 獲取)。
            *   對於天賦被動技能 (`INNATE_PASSIVE`)，此等級與卡牌的 `star_level` (0-35) 關聯，或者在配置中直接定義（如果天賦效果不隨星級變化）。
            *   對於怪物技能，通常是其在 `monsters.json` 中配置的固定等級。
        *   `current_cooldown` (整數, 僅適用於 `skill_type` 為 `ACTIVE` 或 `PRIMARY_ATTACK` 的技能, 表示剩餘的冷卻回合數)
    *   **方法：**
        *   `get_definition(all_configs: AllConfigsData) -> dict`: 從加載的全局配置數據 (`all_configs`) 中，根據 `skill_id` 和 `skill_type` 找到對應的JSON文件 (如 `active_skills.json`)，然後根據 `current_level` (對於天賦而言，則基於卡牌 `star_level` 找到對應的 `effects_by_star_level` 檔位) 獲取該技能在當前等級/檔位下的詳細定義 (包括效果列表 `effect_definitions`、MP消耗 `mp_cost`、基礎冷卻 `cooldown_turns` 等)。
        *   `is_usable(caster_mp: int) -> bool`: 判斷此技能當前是否可用 (主要針對主動技能，檢查 `caster_mp` 是否足夠以及 `current_cooldown` 是否為0)。
        *   `put_on_cooldown(all_configs: AllConfigsData)`: 根據技能定義將此技能置於冷卻狀態。

4.  **`EffectApplier` (效果應用器 - 領域服務或靜態工具類)**
    *   **職責：** 根據技能/狀態效果的JSON定義 (`effect_definitions`)，將其應用到目標 `Combatant` 上。這是"效果引擎"的核心。
    *   **方法 (示例，可根據 `effect_type` 擴展)：**
        *   `apply_effect(effect_def: dict, caster: Combatant, target: Combatant, battle_context: Battle, skill_instance: Optional[SkillInstance] = None)`: 通用入口，內部根據 `effect_def.effect_type` 分發。`skill_instance` 可用於傳遞觸發此效果的技能信息。
        *   `_apply_damage_effect(effect_def: dict, caster: Combatant, target: Combatant, battle_context: Battle, skill_instance: Optional[SkillInstance] = None)`: 處理傷害效果，將調用 `DamageHandler`。
        *   `_apply_heal_effect(effect_def: dict, caster: Combatant, target: Combatant, battle_context: Battle, skill_instance: Optional[SkillInstance] = None)`: 處理治療效果。
        *   `_apply_stat_modification_effect(effect_def: dict, target: Combatant, battle_context: Battle, skill_instance: Optional[SkillInstance] = None)`: 處理屬性修改效果。
        *   `_apply_status_effect_application(effect_def: dict, caster: Combatant, target: Combatant, battle_context: Battle, skill_instance: Optional[SkillInstance] = None)`: 處理施加狀態效果的邏輯，將調用 `StatusEffectHandler`。
        *   *... 其他效果類型 ...*
    *   **內部邏輯：** 每個方法內部會解析傳入的 `effect_def` (JSON片段)，通過公式引擎計算具體數值 (如果定義中包含公式)，然後調用 `target` (`Combatant`對象) 的相應方法 (如 `take_damage`, `add_status_effect`) 或協調其他處理器。

5.  **`TargetSelector` (目標選擇器 - 領域服務或靜態工具類)**
    *   **職責：** 根據技能定義中的 `target_type` 和 `target_logic_details` (來自 `active_skills.json`)，以及當前戰場情況 (`battle_context`)，選擇合適的目標 `Combatant`。
    *   **方法：**
        *   `select_targets(caster: Combatant, battle_context: Battle, skill_definition: dict) -> List[Combatant]`: 返回一個目標 `Combatant` 列表。
        *   `select_passive_targets(triggering_combatant: Combatant, event_data: dict, passive_effect_block: dict, battle_context: Battle) -> List[Combatant]`: 根據被動效果塊中的 `target_override` 和事件數據來確定被動效果的目標。

6.  **`BattleLogEntry` (戰鬥日誌條目 - 數據對象)**
    *   **屬性 (建議)：** `turn_number`, `timestamp`, `actor_type` (`PLAYER_CARD` / `MONSTER`), `actor_instance_id`, `actor_name`, `action_type` (`SKILL_CAST` / `PASSIVE_PROC` / `STATUS_EFFECT_TICK` / `ENVIRONMENT`), `action_name` (技能名/效果名), `targets` (列表 of `{"target_instance_id", "target_name", "effects_received": [...]}`), `message_template_key` (可選，指向本地化文本模板), `log_details` (任意額外數據)。

7.  **戰鬥流程簡化 (高層次)**
    *   **`BattleCoordinatorService.start_new_battle(player_id, floor_id)`:**
        1.  從 `Repository` 加載玩家隊伍卡牌數據 (`gacha_user_collections`) 和全局技能等級 (`gacha_user_learned_global_skills`)。
        2.  從 `ConfigLoader` 加載 `floors.json` 和 `monsters.json` 來確定怪物隊伍。
        3.  創建玩家方 (`Player_Team`) 和怪物方 (`Monster_Team`) 的 `Combatant` 對象列表。在創建 `Combatant` 時，會傳入必要的初始數據 (如 `definition_id`, `rpg_level`, `star_level`, 裝備的技能ID和等級等)。
        4.  對每個 `Combatant` 調用其 `calculate_final_stats(all_configs)` 方法，完成屬性初始化。
        5.  創建 `Battle` 對象，傳入隊伍，並調用 `battle.start()`。
    *   **戰鬥循環 (由 `BattleCoordinatorService` 或一個新的 `BattleRunner` 類控制)：**
        ```
        while battle.battle_status == IN_PROGRESS:
            acting_combatant = battle.get_acting_combatant()
            if not acting_combatant: break // 安全退出, 或處理回合結束

            // 回合開始效果 (狀態tick, CD減少等)
            acting_combatant.apply_turn_start_effects(battle) // 包含tick_status_effects, tick_cooldowns
            // PassiveTriggerHandler.check_and_apply_passives(ON_TURN_START, event_data, [acting_combatant], battle)
            
            if acting_combatant.is_alive() and acting_combatant.can_act(battle):
                (selected_skill_id, potential_target_ids_suggestion) = acting_combatant.get_available_action(battle)
                
                skill_instance = acting_combatant.get_skill_instance(selected_skill_id)
                if not skill_instance or not skill_instance.is_usable(acting_combatant.current_mp):
                    // 如果選擇的技能不可用 (例如MP不足或仍在冷卻)，則通常使用普攻
                    // 或者 AIController/Player 選擇另一個技能
                    // 這裡簡化為：若首選不可用，則嘗試普攻
                    skill_instance = acting_combatant.primary_attack_skill 
                    selected_skill_id = skill_instance.skill_id
                    // (普攻的目標選擇可能也需要重新評估)

                skill_definition_for_level = skill_instance.get_definition(all_configs)

                actual_targets = TargetSelector.select_targets(acting_combatant, battle, skill_definition_for_level)

                battle.process_action(acting_combatant, selected_skill_id, [t.instance_id for t in actual_targets]):
                    // 內部:
                    // 1. acting_combatant.consume_mp(skill_definition_for_level.mp_cost)
                    // 2. skill_instance.put_on_cooldown(all_configs)
                    // 3. 對於 skill_definition_for_level 中的每個 effect_definition:
                    //      EffectApplier.apply_effect(effect_def, acting_combatant, target, battle, skill_instance)
                    //          - apply_effect 內部會處理傷害、治療、狀態施加等
                    //          - DamageHandler, StatusEffectHandler 等會被調用
                    //          - PassiveTriggerHandler 可能在傷害/治療/狀態施加後被觸發
                    // 4. 記錄日誌。
            
            battle.check_win_condition()
            if battle.battle_status != IN_PROGRESS: break

            // 回合結束效果
            acting_combatant.apply_turn_end_effects(battle)
            // PassiveTriggerHandler.check_and_apply_passives(ON_TURN_END, event_data, [acting_combatant], battle)

            battle.next_turn() // 切換行動單位，或增加回合數，更新行動隊列
        ```
    *   **戰鬥結束：**
        1.  從 `battle` 對象獲取結果 (`battle_status`, `battle_log`)。
        2.  `BattleCoordinatorService` 處理戰後事宜：更新 `UserProgressRepository`，通過 `EconomyService` 發放獎勵等。
        3.  將戰鬥結果和日誌返回給 `Cog` 展示。

8.  **如何保持"簡單不複雜"和"好擴展"**
    *   **核心模型職責清晰：** `Combatant` 負責自身狀態和能做什麼，`Battle` 負責組織戰鬥和流程，`EffectApplier` 負責效果如何實現。
    *   **配置驅動的威力：**
        *   新增技能/卡牌/怪物： 主要是修改JSON文件，代碼層面可能只需要確保 `EffectApplier` 支持新的 `effect_type` (如果有的話)。
        *   平衡調整： 直接修改JSON中的數值、MP消耗、冷卻等。
    *   **`EffectApplier` 的擴展性：**
        *   當需要新的效果類型時，只需要在 `EffectApplier` 中添加一個新的 `_apply_xxx_effect` 私有方法，並在通用的 `apply_effect` 方法中加入對新 `effect_type` 的處理分支。
        *   JSON中技能效果的 `effect_type` 字符串直接對應到 `EffectApplier` 中的處理邏輯。
    *   **`TargetSelector` 的擴展性：** 類似地，增加新的目標選擇邏輯。
    *   **避免過度設計：**
        *   初期不需要非常複雜的事件系統或消息總線，可以通過在關鍵點調用 `PassiveTriggerHandler` 來處理被動。
        *   `EffectApplier` 和 `TargetSelector` 可以先做成包含一堆靜態方法的工具類，如果後續需要狀態或更複雜的依賴注入，再重構成服務類。
    *   **測試：**
        *   對 `Combatant.calculate_final_stats` 進行單元測試。
        *   對 `EffectApplier` 中的每個效果應用方法進行單元測試。
        *   對 `Combatant.get_available_action` 的決策邏輯進行單元測試。
        *   模擬完整的戰鬥場景進行集成測試。

9.  **設計優化：進一步減少重複與提升複用性**
    在當前設計基礎上，可通過以下原則進一步減少潛在的代碼重複，提升核心組件的複用性：
    *   **統一的公式求值引擎 (Formula Evaluation Engine):**
        *   對於JSON配置中所有 `*_formula` 字段（如被動技能的 `chance_formula`、效果定義中的 `base_power_multiplier_formula` 等），應實現一個中央化、安全且功能健壯的公式求值器。
        *   此求值器負責解析包含算術運算、預定義變量（如 `skill_level`, `star_level`, `caster_stat_patk`, `target_current_hp_percent`）和簡單函數（`min`, `max`）的字符串公式。
        *   `PassiveTriggerHandler`（計算觸發機率時）和 `EffectApplier`（或其協調的組件如 `DamageHandler`，在計算效果具體數值時）等應**共享並調用此同一個引擎**。
        *   **益處：** 避免在多處重複實現公式解析和計算邏輯，便於統一管理和擴展公式功能。
    *   **通用的條件評估邏輯 (Generic Condition Evaluation Logic):**
        *   被動技能的 `trigger_condition.additional_conditions` 以及主動技能中可能存在的條件性效果（如 `CONDITIONAL_BOOST` 修正器）均依賴條件判斷。
        *   應創建一個**通用的條件評估模塊/服務**，能夠接收標準化的條件定義對象和相關上下文（如 `caster`, `target`, `event_data`），並根據條件類型（如 `hp_above_percent`, `has_status_effect`）執行判斷。
        *   `PassiveTriggerHandler` 及任何需要條件判斷的 `EffectApplier` 邏輯分支都應使用此通用模塊。
        *   **益處：** 將條件判斷的實現細節集中管理，易於維護和添加新的條件檢查類型。
    *   **`TargetSelector` 的進一步抽象與複用:**
        *   主動技能的 `target_type` 和被動技能的 `target_override.selector_type` 存在語義重疊。
        *   應確保 `TargetSelector` 的設計足夠通用，能同時服務於主動技能的目標選擇和被動技能觸發後根據 `target_override` 及 `event_data` 推斷目標的場景。核心選擇算法（如"選擇血量百分比最低的敵人"）應可共享。
        *   **益處：** 避免為相似的目標選擇邏輯編寫重複代碼。
    *   **標準化的事件數據結構 (`event_data`):**
        *   對於傳遞給 `PassiveTriggerHandler` 的 `event_data`，不同 `event_type` 下的關鍵信息（如攻擊者、受擊者、技能施放者等）應盡可能標準化命名和結構。
        *   **益處：** 使 `PassiveTriggerHandler` 和條件評估模塊能更一致、簡便地從事件數據中提取信息。
    *   **`PassiveEffectBlock` 處理邏輯的一致性：**
        *   `PassiveTriggerHandler` 在處理天賦 (`innate_passive_skills.json`) 和通用被動 (`passive_skills.json`) 時，其迭代 `PassiveEffectBlock`、檢查觸發、評估公式、確定目標、調用 `EffectApplier` 的核心流程應高度一致。主要差異（如公式中是 `star_level` 還是 `skill_level`）可通過向公式引擎傳遞不同上下文參數解決。
        *   **益處：** `PassiveTriggerHandler` 的主體邏輯更通用，減少針對不同被動來源的特化代碼。

    遵循這些原則，可以使系統的各個組件職責更單一，核心邏輯更易複用，從而提升整體代碼的簡潔性、可維護性和擴展性。 