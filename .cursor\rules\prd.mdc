---
description: 
globs: 
alwaysApply: true
---
# Cursor AI Script - DICKPK Project

## 1. System Architecture

This project is a Discord bot primarily focused on a Gacha system. Its architecture can be broadly divided into the following main parts:

*   **Core Bot (`bot.py`)**: The program's entry point, responsible for loading cogs, initializing services, etc.
*   **Command Modules (`cogs/`)**: Contains the concrete implementations of Discord commands.
*   **Service Layer (`gacha/services/`)**: Handles business logic, coordinating Repositories and other services.
*   **Repository Layer (`gacha/repositories/`)**: Responsible for interacting with the database, performing CRUD operations.
*   **View Layer (`gacha/views/`)**: Manages UI components, Embeds, Modals, etc., and handles user interactions.
*   **Database (`database/`)**: Contains configurations and migrations for PostgreSQL and Redis.
*   **Utilities (`utils/`)**: Provides various helper functions, such as logging, reaction handling, etc.
*   **AI Assistant (`ai_assistant/`)**: Contains AI-related functionalities like outfit rating and a QA system.
*   **Image Processing (`image_processing/`)**: Handles image-related tasks such as GIF modification.

## 2. Reusable Components

The following are some widely used reusable components in the project. Please prioritize understanding their functionality and usage:

*   **`gacha/views/ui_components/confirmation.py`**: Provides functionality for confirmation dialogs or buttons.
*   **`gacha/exceptions.py`**: Defines custom exception types used in the project. **Error handling must strictly follow the guidelines in `GACHA_ERROR_HANDLING_REFACTOR.md` (referring to the Chinese version provided by the user).**
*   **`gacha/views/modals/modals.py`** (and other files in the directory): Provides the base and specific implementations for various interactive modal windows.
*   **`gacha/views/embeds/base_embed_builder.py`**: Serves as a base class for creating Discord Embeds, standardizing Embed appearance and common fields.
*   **`BasePaginationJumpModal` in `gacha/views/collection/collection_view/base_pagination.py`**: A Modal used in paginated views to jump to a specific page.
*   **`gacha/views/collection/collection_view/button_factory.py`**: Used for generating standardized button components.
*   **`gacha/views/collection/collection_view/interaction_manager.py`**: Likely used for managing complex user interaction flows.

## 3. Key File Locations

*   **SQL Schema**: `D:\DICKPK\schema.sql` (local absolute path) or `schema.sql` (workspace relative path)
*   **Redis Configuration**:
    *   `database/redis/config.py`
    *   `database/redis/manager.py`
    *   `database/redis/service.py`
*   **Logger Configuration**:
    *   `init_logging.py`
    *   `utils/logger.py`
    *   `utils/operation_logger.py`
*   **Main Configuration Files**:
    *   `config.yaml`
    *   `gacha_settings.yaml`
    *   `ui_settings.yaml`

## 4. System Layering (Gacha Core Functionality)

Gacha-related functionalities primarily follow this layered pattern:

*   **Cogs (`cogs/`)**:
    *   Receive user commands (Slash commands).
    *   Invoke relevant Services.
    *   Handle results or exceptions returned by Services.
    *   Format and send results back to the user (usually via Views).
*   **Services (`gacha/services/`)**:
    *   Contain core business logic.
    *   Do not directly handle Discord interactions or contexts (ctx).
    *   Invoke Repositories for data access.
    *   May invoke other Services.
    *   Return pure data on success, throw exceptions on failure (following `GACHA_ERROR_HANDLING_REFACTOR.md`).
*   **Views (`gacha/views/`)**:
    *   Responsible for UI presentation and interaction.
    *   Include Embed builders, Modals, Buttons, Selects, etc.
    *   Handle UI events like button clicks.
    *   Invoke relevant Services to perform actions.
    *   Handle results or exceptions from Services, and update the UI or display messages to the user (following `GACHA_ERROR_HANDLING_REFACTOR.md`).
*   **Repositories (`gacha/repositories/`)**:
    *   The sole point of database access.
    *   Execute SQL queries or Redis operations.
    *   Transform raw data returned from the database into Python objects or dictionaries.
    *   Return pure data on success, throw exceptions (like `DatabaseError`) on failure (following `GACHA_ERROR_HANDLING_REFACTOR.md`).

## 5. Error Handling

**Strictly adhere to the error handling refactoring guidelines described in the `GACHA_ERROR_HANDLING_REFACTOR.md` document (referring to the Chinese version provided by the user).**

Core Principles:

*   **Pure Exception Mode**: Completely abandon returning status codes or result dictionaries (`{'success': True/False, ...}`).
*   **Consistency First**: Use the same error handling pattern across all layers.
*   **Clear Responsibility**: Each layer only handles exceptions within its scope of responsibility.
*   **Simplicity is Key**: Reduce boilerplate code, avoid over-engineering.

**Exception Hierarchy**:

*   Use exceptions defined in `gacha/exceptions.py`.
*   Basic types should include: `GachaError`, `DatabaseError`, `UserNotFoundError`, `CardNotFoundError`, `OperationError` (or more specific ones).

**Implementation Guide for Each Layer**:

*   **Repository Layer**:
    *   No longer returns `{'success': True/False, ...}`.
    *   Throws specific exceptions directly (usually `DatabaseError` or its subclasses).
    *   Returns pure data on success.
*   **Service Layer**:
    *   Does not catch exceptions and convert them into return values.
    *   Only catches exceptions when needing to convert exception types (e.g., converting a low-level `DatabaseError` to a more specific business-level exception like `CardNotFoundError`).
    *   Returns only the actual result (pure data) on success.
*   **View Layer (View/UI) and Command Layer (Cogs)**:
    *   Handle all exceptions from the Service layer in these two layers.
    *   Provide user-friendly error messages.
    *   Log unexpected errors.

**Example (Service Layer)**:
```python
async def toggle_favorite_card(self, user_id, card_id):
    # ... (checking logic) ...
    if not user_card:
        raise CardNotFoundError(f'You do not own this card (ID: {card_id})')
    # ... (update logic) ...
    return new_is_favorite # Directly return the new state
```

**Example (View/Cog Layer)**:
```python
try:
    is_favorite = await self.favorite_service.toggle_favorite_card(user_id, card_id)
    # ... (success handling) ...
except CardNotFoundError as e:
    await interaction_utils.safe_send_message(interaction, str(e), ephemeral=True)
except GachaError as e: # Catch more general Gacha errors
    await interaction_utils.safe_send_message(interaction, f"Operation failed: {e}", ephemeral=True)
except Exception as e: # Catch all other unexpected errors
    logger.error("Operation failed: %s", e, exc_info=True)
    await interaction_utils.safe_send_message(interaction, "An unknown error occurred", ephemeral=True)
```

**Key Reminders**:

1.  **Utilize Existing Exceptions**: Do not create new exception types lightly unless necessary.
2.  **Ensure Consistency**: Maintain a consistent error handling style throughout the system.
3.  **Keep it Simple**: Avoid overly complex error handling frameworks.

4.  **Clear Error Messages**: Ensure that both users and developers can understand the cause of errors.