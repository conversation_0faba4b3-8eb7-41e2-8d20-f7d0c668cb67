# RPG 任務 08：戰鬥系統核心邏輯

本文檔詳細列出了 RPG 系統中 `Battle` 類核心戰鬥流程邏輯的實現任務。這包括戰鬥的啟動、回合管理、行動處理、以及勝負條件的判斷。

**參考設計文檔：**
*   `RPG_04_Domain_Model_Battle_System.md` (1. `Battle` 類的方法存根，6. `BattleLogEntry`)
*   `RPG_05_Core_Runtime_Logic.md` (4. Battle Preparation，5. PVE Battle Flow)
*   `RPG_TASK_04_Core_Domain_Models.md` (已定義的 `Battle` 類結構和初步方法)
*   `RPG_TASK_06_Effect_System_Handlers.md` (`EffectApplier`, `TargetSelector` 將被大量使用)
*   `RPG_TASK_07_Event_System_and_Passive_Trigger_Handler.md` (事件派發將集成到戰鬥流程中)

**目標位置：** `rpg_system/battle_system/models/battle.py` (主要針對 `Battle` 類中之前標註為存根的方法)

## 1. `Battle` 類初始化與依賴

- [ ] **確認 `Battle` 類的 `__init__` 方法：**
    - [ ] 接收 `player_team: List[Combatant]`, `monster_team: List[Combatant]`, `rng_seed: Optional[int] = None`。
    - [ ] 初始化 `battle_id`, `battle_log`, `battle_status`。
    - [ ] 正確初始化 `_rng = random.Random(rng_seed)`。
    - [ ] `combatant_queue` 和 `_acting_combatant_index` 初始化為空/無效狀態。
    - [ ] **新增依賴注入 (重要)：** `Battle` 類需要能夠訪問 `ConfigLoader`, `EffectApplier`, `TargetSelector`, `PassiveTriggerHandler`, 和 `FormulaEvaluator`。
        *   方案A：在 `__init__` 中接收這些服務的實例。
        *   方案B：這些服務通過一個「服務定位器」或「上下文對象」傳遞給需要它們的方法 (如 `process_action`, `start`)。
        *   **建議方案A**，在 `Battle` 實例化時傳入這些核心服務，使其在整個戰鬥生命週期中可用。
        ```python
        # In battle.py
        def __init__(self, ..., 
                     config_loader: 'ConfigLoader', 
                     effect_applier: 'EffectApplier', 
                     target_selector: 'TargetSelector',
                     passive_trigger_handler: 'PassiveTriggerHandler',
                     formula_evaluator: 'FormulaEvaluator'):
            # ... existing initializations ...
            self.config_loader = config_loader
            self.effect_applier = effect_applier
            self.target_selector = target_selector
            self.passive_trigger_handler = passive_trigger_handler
            self.formula_evaluator = formula_evaluator # Potentially passed to TargetSelector/EffectApplier already
        ```

## 2. 戰鬥準備與啟動 (`Battle.start()`)

- [ ] **實現 `Battle.start(self)` 方法 (之前是存根)：**
    - [ ] 設置 `self.battle_status = BattleStatus.IN_PROGRESS`。
    - [ ] `self.current_turn = 1`。
    - [ ] **行動順序計算：** 調用 `self._calculate_initial_combatant_queue()`。
    - [ ] `self._acting_combatant_index = 0` (如果隊列非空)。
    - [ ] **觸發戰鬥開始事件：**
        - [ ] `event_data = EventOnBattleStartData(battle_context=self)`
        - [ ] `self.dispatch_event(EVENT_ON_BATTLE_START, event_data)` (新增 `dispatch_event` 輔助方法)
    - [ ] **應用戰鬥開始時的自身效果 (來自星級等)：**
        - [ ] 遍歷所有 `Combatant` in `player_team` and `monster_team`。
        - [ ] `card_config = self.config_loader.get_card_config(combatant.definition_id)` (if player card)
        - [ ] `star_effects_key = card_config.star_level_effects_key`
        - [ ] `star_level_effects_config_group = self.config_loader.get_star_level_effects_config(star_effects_key)`
        - [ ] `for s_level in range(1, combatant.star_level + 1):`
            - [ ] `star_effect_detail = star_level_effects_config_group.get(str(s_level))`
            - [ ] `if star_effect_detail and star_effect_detail.apply_self_effect_on_battle_start:`
                - [ ] `self.effect_applier.apply_effect_definitions(caster=combatant, initial_targets=[combatant], effect_definitions=star_effect_detail.apply_self_effect_on_battle_start, battle_context=self, source_skill_tags=["STAR_LEVEL_EFFECT"], source_skill_instance=None)`
    - [ ] 記錄戰鬥開始的日誌條目 (`self.add_log_entry(...)`)。

## 3. 行動順序計算 (`Battle._calculate_initial_combatant_queue()`)

- [ ] **實現 `Battle._calculate_initial_combatant_queue(self)` 方法 (之前是存根)：**
    - [ ] `live_combatants = self.get_all_alive_combatants()`。
    - [ ] **排序：**
        - [ ] 主要按 `spd` 降序 (`combatant.get_stat('spd')`)。
        - [ ] 若 `spd` 相同，次要排序規則 (e.g., 玩家優先，然後按隊伍中的原始位置，或隨機以破壞平局)。
            *   `live_combatants.sort(key=lambda c: (c.get_stat('spd'), c.is_player_side, c.position_in_team_for_tie_break), reverse=True)` (假設 `position_in_team_for_tie_break` 存在)
    - [ ] `self.combatant_queue = [c.instance_id for c in live_combatants]`。
    - [ ] 如果行動順序因任何原因改變 (e.g. 速度buff/debuff)，此方法需要被再次調用以更新隊列。

## 4. 回合推進與行動者管理

- [ ] **實現 `Battle.get_acting_combatant(self) -> Optional[Combatant]` (之前是存根)：**
    - [ ] `if not self.combatant_queue or not (0 <= self._acting_combatant_index < len(self.combatant_queue)): return None`
    - [ ] `acting_id = self.combatant_queue[self._acting_combatant_index]`
    - [ ] `return self.get_combatant_by_id(acting_id)`
- [ ] **實現 `Battle.next_turn_or_combatant(self)` (取代舊的 `next_turn`)：**
    - [ ] `acting_combatant = self.get_acting_combatant()`
    - [ ] **1. 當前行動者回合結束效果：**
        - [ ] `if acting_combatant and acting_combatant.is_alive():`
            - [ ] `acting_combatant.apply_turn_end_effects(self, self.config_loader)` (這會觸發 `ON_TURN_END` 事件和狀態效果 tick)
    - [ ] **2. 推進到下一個行動者：**
        - [ ] `self._acting_combatant_index += 1`
    - [ ] **3. 檢查是否一個邏輯回合結束 (所有人都行動過一次)：**
        - [ ] `if self._acting_combatant_index >= len(self.combatant_queue):`
            - [ ] `self.current_turn += 1`
            - [ ] `self.add_log_entry(...)` 記錄新回合開始。
            - [ ] `self._calculate_initial_combatant_queue()` (重新計算行動順序，因為速度等可能已改變)
            - [ ] `self._acting_combatant_index = 0`
            - [ ] 如果 `self.combatant_queue` 為空 (e.g. 所有人都死了)，則 `self.check_win_condition()` 並提前結束。
    - [ ] **4. 獲取新的行動者：**
        - [ ] `new_acting_combatant = self.get_acting_combatant()`
    - [ ] **5. 新行動者回合開始效果：**
        - [ ] `if new_acting_combatant and new_acting_combatant.is_alive():`
            - [ ] `new_acting_combatant.apply_turn_start_effects(self, self.config_loader)` (這會觸發 `ON_TURN_START` 事件，tick CD 和狀態效果)
    - [ ] **6. 檢查戰鬥是否結束：**
        - [ ] `self.check_win_condition()`
        - [ ] 如果 `self.battle_status`不再是 `IN_PROGRESS`，則戰鬥結束。
    - [ ] 返回 `new_acting_combatant` 或一個指示戰鬥結束的狀態。

## 5. 行動處理 (`Battle.process_action()`)

- [ ] **實現 `Battle.process_action(self, caster: 'Combatant', skill_id: str, explicit_target_ids: Optional[List[str]] = None)` (修改參數，之前是存根)：**
    - [ ] **參數：**
        - `caster`: 行動的戰鬥單位。
        - `skill_id`: 使用的技能ID。
        - `explicit_target_ids`: (可選) 由玩家或 AI 明確指定的目標 `instance_id` 列表。如果為 `None`，則需靠技能的目標邏輯選擇。
    - [ ] **1. 獲取技能實例和定義：**
        - [ ] `skill_instance = caster.get_skill_instance(skill_id)`
        - [ ] `if not skill_instance: log_error; return`
        - [ ] `skill_definition = skill_instance.get_definition(self.config_loader)` (處理星級/等級)
    - [ ] **2. 檢查技能可用性 (MP, 冷卻)：**
        - [ ] `if not skill_instance.is_usable(caster.current_mp, self.config_loader):`
            - [ ] `self.add_log_entry(...)` 記錄技能無法使用 (e.g., MP不足, 冷卻中)。
            - [ ] (可選) 讓 caster 執行一次普攻作為替代。
            - [ ] `return`
    - [ ] **3. 消耗資源，技能進入冷卻：**
        - [ ] `caster.consume_mp(skill_definition.get('mp_cost', 0))`
        - [ ] `skill_instance.put_on_cooldown(self.config_loader)`
    - [ ] **4. 觸發「技能使用前」事件：**
        - [ ] `event_data = EventOnBeforeSkillUseData(...)`
        - [ ] `self.dispatch_event(EVENT_ON_BEFORE_SKILL_USE, event_data)`
    - [ ] **5. 選擇目標：**
        - [ ] `if explicit_target_ids:`
            - [ ] `selected_targets = [self.get_combatant_by_id(tid) for tid in explicit_target_ids if self.get_combatant_by_id(tid) and self.get_combatant_by_id(tid).is_alive()]`
        - [ ] `else:`
            - [ ] `target_logic_detail = skill_definition.get('target_logic')` (假設 `TargetLogicDetail` 模型在技能定義中)
            - [ ] `selected_targets = self.target_selector.select_targets(caster, target_logic_detail, self, self.formula_evaluator, self.config_loader)`
    - [ ] **6. 記錄技能施放日誌：**
        - [ ] `self.add_log_entry(BattleLogEntry(..., action_type="SKILL_CAST", action_name=skill_definition.get('name'), targets_details=...))`
    - [ ] **7. 應用技能效果：**
        - [ ] `self.effect_applier.apply_skill_effects(caster, selected_targets, skill_instance, self)`
    - [ ] **8. 觸發「技能使用後」事件：**
        - [ ] `event_data = EventOnAfterSkillUseData(...)`
        - [ ] `self.dispatch_event(EVENT_ON_AFTER_SKILL_USE, event_data)`
    - [ ] **9. 檢查勝負條件：**
        - [ ] `self.check_win_condition()`

## 6. 勝負條件判斷 (`Battle.check_win_condition()`)

- [ ] **實現 `Battle.check_win_condition(self)` 方法 (之前是存根)：**
    - [ ] `if self.battle_status != BattleStatus.IN_PROGRESS: return` (如果戰鬥已結束則不重複判斷)
    - [ ] `player_team_alive = any(c.is_alive() for c in self.player_team)`
    - [ ] `monster_team_alive = any(c.is_alive() for c in self.monster_team)`
    - [ ] `new_status = None`
    - [ ] `if not player_team_alive and not monster_team_alive: new_status = BattleStatus.DRAW`
    - [ ] `elif not player_team_alive: new_status = BattleStatus.MONSTER_WIN`
    - [ ] `elif not monster_team_alive: new_status = BattleStatus.PLAYER_WIN`
    - [ ] **(可選) 最大回合數限制：**
        - [ ] `elif self.current_turn > MAX_BATTLE_TURNS: new_status = BattleStatus.DRAW`
    - [ ] `if new_status is not None:`
        - [ ] `self.battle_status = new_status`
        - [ ] `self.add_log_entry(...)` 記錄戰鬥結束狀態。
        - [ ] **觸發戰鬥結束事件：**
            - [ ] `event_data = EventOnBattleEndData(battle_context=self, winning_side=...)`
            - [ ] `self.dispatch_event(EVENT_ON_BATTLE_END, event_data)`

## 7. 輔助方法實現

- [ ] **實現 `Battle.add_log_entry(self, entry: BattleLogEntry)`:**
    - [ ] `self.battle_log.append(entry)`
    - [ ] (可選) 如果日誌過長，可以做截斷或優化處理。
- [ ] **實現 `Battle.get_combatant_by_id(self, instance_id: str) -> Optional[Combatant]` (之前是存根)：**
    - [ ] 遍歷 `self.player_team` 和 `self.monster_team` 查找。
- [ ] **實現 `Battle.get_all_alive_combatants(self) -> List[Combatant]`:**
    - [ ] 過濾 `self.player_team` 和 `self.monster_team` 中 `is_alive()` 的單位。
- [ ] **實現 `Battle.get_all_alive_enemies_of(self, combatant: Combatant) -> List[Combatant]`:**
    - [ ] 判斷 `combatant.is_player_side`，返回對立隊伍中存活的單位。
- [ ] **實現 `Battle.get_all_alive_allies_of(self, combatant: Combatant) -> List[Combatant]`:**
    - [ ] 返回同一隊伍中存活的單位 (包括自身，如果自身存活)。
- [ ] **新增 `Battle.dispatch_event(self, event_type: str, event_data: Dict[str, Any])`:**
    - [ ] `if self.passive_trigger_handler: self.passive_trigger_handler.handle_event(event_type, event_data, self)`
    - [ ] (未來可能還有其他事件監聽者)

## 8. AI 邏輯 (初步存根)

- [ ] **在 `Battle` 類中或一個新的 `BattleAIService` 中思考怪物 AI 的初步入口。**
- [ ] `if acting_combatant and not acting_combatant.is_player_side and self.battle_status == BattleStatus.IN_PROGRESS:`
    - [ ] `selected_skill_id, target_ids = acting_combatant.get_ai_action(self, self.config_loader)` ( `Combatant` 需要一個 `get_ai_action` 方法)
    - [ ] `self.process_action(acting_combatant, selected_skill_id, target_ids)`
- [ ] `Combatant.get_ai_action(...)` 方法 (在 `models/combatant.py`):
    - [ ] 初步實現：遍歷 `skill_order_preference`，選擇第一個可用的技能。
    - [ ] 目標選擇：初步選擇敵方第一個存活單位。
    - [ ] (詳細 AI 邏輯是後續任務)

## 9. 單元測試與集成測試

- [ ] **針對 `Battle` 類的核心流程編寫集成測試：**
    - [ ] 模擬完整的戰鬥場景 (e.g., 1v1, 2v2)。
    - [ ] Mock 所有外部依賴 (ConfigLoader, Services)。
    - [ ] **測試戰鬥啟動：**
        - [ ] 驗證初始行動順序。
        - [ ] 驗證戰鬥開始事件和效果。
    - [ ] **測試回合推進：**
        - [ ] 驗證行動者正確輪換。
        - [ ] 驗證回合數增加。
        - [ ] 驗證回合開始/結束效果和事件。
    - [ ] **測試行動處理 (`process_action`)：**
        - [ ] 技能成功施放 (資源消耗, 冷卻)。
        - [ ] 技能因 MP/冷卻無法施放。
        - [ ] 正確的目標選擇 (使用 `TargetSelector` 的 mock 或真實邏輯)。
        - [ ] 正確的效果應用 (使用 `EffectApplier` 的 mock 或真實邏輯)。
        - [ ] 事件 (`ON_BEFORE_SKILL_USE`, `ON_AFTER_SKILL_USE`) 派發。
    - [ ] **測試勝負條件：**
        - [ ] 玩家勝利、怪物勝利、平局 (包括最大回合數)。
        - [ ] 戰鬥結束事件派發。
    - [ ] **測試戰鬥日誌：** 驗證關鍵行動是否被記錄。
    - [ ] **測試 AI 行動：** (如果初步 AI 邏輯已集成) 驗證 AI 能否選擇並執行行動。
    - [ ] **測試被動觸發：** (關鍵) 確保在戰鬥流程中，各類事件能正確觸發 `PassiveTriggerHandler`，並導致被動技能效果的應用。
        *   例如：受擊回血被動，低血量加攻被動等。

## 10. 文檔與類型提示

- [ ] 更新 `Battle` 類及其所有方法的文檔字符串，反映實現的邏輯和依賴。
- [ ] 確保所有類型提示都已添加並正確處理循環依賴。

---
完成所有上述任務後，請勾選並更新 `RPG_IMPLEMENTATION_PLAN.md` 中的相應條目。 