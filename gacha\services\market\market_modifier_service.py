import asyncpg
from utils.logger import logger
import json
from decimal import Decimal
from typing import List, Dict, Any, Optional
from gacha.app_config import config_service
from gacha.repositories.market import CardMarketStatsRepository, VirtualAssetRepository, GachaCategoryStockInfluenceRepository

class MarketModifierService:

    def __init__(self, pool: asyncpg.Pool, card_market_stats_repo: CardMarketStatsRepository, virtual_asset_repo: VirtualAssetRepository, gacha_category_stock_influence_repo: GachaCategoryStockInfluenceRepository):
        if pool is None:
            raise ValueError('MarketModifierService requires an asyncpg connection pool.')
        self.pool = pool
        self.card_market_stats_repo = card_market_stats_repo
        self.virtual_asset_repo = virtual_asset_repo
        self.gacha_category_stock_influence_repo = gacha_category_stock_influence_repo

    def _parse_price_config(self, price_config_data: Any, card_id: int) -> Dict[str, Any]:
        if isinstance(price_config_data, str):
            try:
                return json.loads(price_config_data)
            except json.JSONDecodeError:
                logger.warning('CardID %s: Failed to parse price_config JSON: %s', card_id, price_config_data)
                return {}
        elif isinstance(price_config_data, dict):
            return price_config_data
        return {}

    async def calculate_supply_demand_modifier(self):
        logger.info('開始計算供需調節因子。')
        sd_config = config_service.get_supply_demand_config()
        async with self.pool.acquire() as conn:
            async with conn.transaction():
                try:
                    cards_to_recalc = await self.card_market_stats_repo.get_all_card_stats_for_supply_demand(conn)
                    if not cards_to_recalc:
                        logger.info('在 gacha_card_market_stats 中沒有找到卡片來重新計算供需。')
                        return
                    w_supply = Decimal(str(sd_config.weights.supply))
                    w_own_div_effect_on_supply = Decimal(str(sd_config.weights.owner_diversity_effect_on_supply))
                    w_wish = Decimal(str(sd_config.weights.wish))
                    w_fav = Decimal(str(sd_config.weights.fav))
                    smooth_factor_decimal = Decimal(str(sd_config.smooth_factor))
                    min_mod_default = Decimal(str(sd_config.modifier_defaults.min))
                    max_mod_default = Decimal(str(sd_config.modifier_defaults.max))
                    typical_max_supply_db = await self.card_market_stats_repo.get_max_stat_value(conn, 'total_owned_quantity')
                    typical_max_supply = max(Decimal('1.0'), typical_max_supply_db or Decimal('1000'))
                    typical_max_owners_db = await self.card_market_stats_repo.get_max_stat_value(conn, 'unique_owner_count')
                    typical_max_owners = max(Decimal('1.0'), typical_max_owners_db or Decimal('100'))
                    typical_max_wish_db = await self.card_market_stats_repo.get_max_stat_value(conn, 'wishlist_count')
                    typical_max_wish = max(Decimal('1.0'), typical_max_wish_db or Decimal('100'))
                    typical_max_fav_db = await self.card_market_stats_repo.get_max_stat_value(conn, 'favorite_count')
                    typical_max_fav = max(Decimal('1.0'), typical_max_fav_db or Decimal('100'))
                    updates_to_perform = []
                    for record in cards_to_recalc:
                        card_id = record['card_id']
                        stats_data = {'total_owned_quantity': Decimal(str(record['total_owned_quantity'])), 'unique_owner_count': Decimal(str(record['unique_owner_count'])), 'wishlist_count': Decimal(str(record['wishlist_count'])), 'favorite_count': Decimal(str(record['favorite_count']))}
                        old_modifier = Decimal(str(record['old_modifier']))
                        norm_supply = min(Decimal('1.0'), stats_data['total_owned_quantity'] / typical_max_supply)
                        norm_owners = min(Decimal('1.0'), stats_data['unique_owner_count'] / typical_max_owners)
                        norm_wish = min(Decimal('1.0'), stats_data['wishlist_count'] / typical_max_wish)
                        norm_fav = min(Decimal('1.0'), stats_data['favorite_count'] / typical_max_fav)
                        supply_effect = -(w_supply * norm_supply * (Decimal('1.0') - w_own_div_effect_on_supply * norm_owners))
                        demand_effect = w_wish * norm_wish + w_fav * norm_fav
                        raw_modifier_offset = demand_effect + supply_effect
                        raw_modifier = Decimal('1.0') + raw_modifier_offset
                        smoothed_modifier = old_modifier * (Decimal('1.0') - smooth_factor_decimal) + raw_modifier * smooth_factor_decimal
                        price_config_json = record['price_config']
                        parsed_price_config = self._parse_price_config(price_config_json, card_id)
                        min_mod = Decimal(str(parsed_price_config.get('min_modifier', str(min_mod_default))))
                        max_mod = Decimal(str(parsed_price_config.get('max_modifier', str(max_mod_default))))
                        clamped_modifier = max(min_mod, min(max_mod, smoothed_modifier))
                        updates_to_perform.append((clamped_modifier, old_modifier, card_id))
                    if updates_to_perform:
                        await self.card_market_stats_repo.batch_update_supply_demand_modifiers(conn, updates_to_perform)
                    else:
                        logger.info('在此週期中沒有需要更新的卡牌調節因子。')
                except Exception as e:
                    logger.error('計算供需調節因子時出錯: %s', e, exc_info=True)
        logger.info('供需調節因子計算完成。')

    async def calculate_gacha_category_stock_influence(self):
        logger.info('開始計算轉蛋類別庫存影響。')
        stock_config = config_service.get_stock_market_config()
        map_factor = Decimal(str(stock_config.price_change_to_modifier_offset_factor))
        clamp_min = Decimal(str(stock_config.category_influence_clamp.min))
        clamp_max = Decimal(str(stock_config.category_influence_clamp.max))
        async with self.pool.acquire() as conn:
            try:
                linked_assets_data = await self.virtual_asset_repo.get_linked_assets_with_history(conn)
                global_assets_data = await self.virtual_asset_repo.get_global_assets_with_history(conn)
                if not linked_assets_data and (not global_assets_data):
                    logger.info('沒有找到用於計算轉蛋類別庫存影響的資產。')
                    return
                asset_price_effects: Dict[int, Dict[str, Any]] = {}
                for asset_row in linked_assets_data:
                    current_price = Decimal(str(asset_row['current_price']))
                    previous_price = Decimal(str(asset_row['previous_price'])) if asset_row['previous_price'] is not None else None
                    influence_weight = Decimal(str(asset_row['influence_weight']))
                    price_change_percent = Decimal('0.0')
                    if previous_price is not None and previous_price > Decimal('0'):
                        price_change_percent = current_price / previous_price - Decimal('1.0')
                    modifier_offset_raw = price_change_percent * map_factor
                    asset_price_effects[asset_row['asset_id']] = {'raw_offset': modifier_offset_raw, 'influence_weight': influence_weight, 'type': asset_row['linked_criteria_type'], 'value': asset_row['linked_criteria_value'], 'pool': asset_row['linked_pool_context']}
                total_global_weighted_offset = Decimal('0.0')
                total_global_weight = Decimal('0.0')
                for asset_row in global_assets_data:
                    current_price = Decimal(str(asset_row['current_price']))
                    previous_price = Decimal(str(asset_row['previous_price'])) if asset_row['previous_price'] is not None else None
                    influence_weight = Decimal(str(asset_row['influence_weight']))
                    price_change_percent = Decimal('0.0')
                    if previous_price is not None and previous_price > Decimal('0'):
                        price_change_percent = current_price / previous_price - Decimal('1.0')
                    modifier_offset_raw = price_change_percent * map_factor
                    total_global_weighted_offset += modifier_offset_raw * influence_weight
                    total_global_weight += influence_weight
                global_impact_offset = total_global_weighted_offset / total_global_weight if total_global_weight > Decimal('0') else Decimal('0.0')
                categories_data: Dict[str, List[Dict[str, Decimal]]] = {}
                for asset_id, data in asset_price_effects.items():
                    key_parts = [data['type']]
                    if data['value']:
                        key_parts.append(data['value'])
                    if data['pool']:
                        key_parts.append(data['pool'])
                    category_key = ':'.join(filter(None, key_parts))
                    if category_key not in categories_data:
                        categories_data[category_key] = []
                    categories_data[category_key].append({'raw_offset': data['raw_offset'], 'weight': data['influence_weight']})
                db_category_keys = await self.gacha_category_stock_influence_repo.get_distinct_category_keys(conn)
                all_category_keys_to_process = db_category_keys.union(categories_data.keys())
                async with conn.transaction():
                    updated_cat_count = 0
                    for category_key in all_category_keys_to_process:
                        category_stock_effects = categories_data.get(category_key, [])
                        rule_specific_stock_impact_offset = Decimal('0.0')
                        if category_stock_effects:
                            sum_product = sum((item['raw_offset'] * item['weight'] for item in category_stock_effects))
                            sum_weights = sum((item['weight'] for item in category_stock_effects))
                            if sum_weights > Decimal('0'):
                                rule_specific_stock_impact_offset = sum_product / sum_weights
                        base_stock_modifier_val = Decimal('1.0') + rule_specific_stock_impact_offset + global_impact_offset
                        base_stock_modifier_val = max(clamp_min, min(clamp_max, base_stock_modifier_val))
                        await self.gacha_category_stock_influence_repo.upsert_category_influence(conn, category_key, base_stock_modifier_val)
                        updated_cat_count += 1
                    if updated_cat_count > 0:
                        logger.info('已成功為 %s 個轉蛋類別更新基礎庫存調節因子。', updated_cat_count)
                    else:
                        logger.info('在此週期中沒有需要更新的轉蛋類別基礎庫存調節因子。')
            except Exception as e:
                logger.error('計算轉蛋類別庫存影響時出錯: %s', e, exc_info=True)
        logger.info('已完成轉蛋類別庫存影響計算。')

    async def cleanup_expired_news_effects(self):
        logger.info('開始清理過期的新聞效果。')
        async with self.pool.acquire() as conn:
            try:
                async with conn.transaction():
                    updated_rows = await self.gacha_category_stock_influence_repo.cleanup_expired_news_effects(conn)
                    if updated_rows > 0:
                        logger.info('已從 gacha_category_stock_influence 清理了 %s 個過期的新聞效果。', updated_rows)
                    else:
                        logger.info('在 gacha_category_stock_influence 中未找到要清理的過期新聞效果。')
            except Exception as e:
                logger.error('清理過期的新聞效果時發生頂層錯誤: %s', e, exc_info=True)
        logger.info('已完成過期新聞效果的清理。')