# pre_download_master_card_images.py

import asyncio
import os
import aiohttp
import asyncpg
from urllib.parse import urlparse
import re
from dotenv import load_dotenv

# 尝试从 .env 文件加载环境变量
# 这会查找当前目录或父目录中的 .env 文件
load_dotenv()

# --- 配置部分 ---
# 读取单独的环境变量来构建连接信息
DB_HOST = os.environ.get("PG_HOST", "127.0.0.1")
DB_PORT = os.environ.get("PG_PORT", "5432")
DB_USER = os.environ.get("PG_USER", "postgres")
DB_PASSWORD = os.environ.get("PG_PASSWORD") # 密码最好不要有默认值，或者提示用户检查
DB_NAME = os.environ.get("GACHA_DB_NAME", "gacha_database")

# DATABASE_URL = os.environ.get("DATABASE_URL", "postgresql://user:password@host:port/database") # 不再使用单一URL
IMAGE_BASE_PATH = "downloaded_gacha_master_cards" # 图片下载的基础目录
CARD_TABLE_NAME = "gacha_master_cards"

# 稀有度整数到名称的映射
RARITY_MAP = {
    1: "C",
    2: "R",
    3: "SR",
    4: "SSR",
    5: "UR",
    6: "LR",
    7: "EX",
    # 根据您的系统添加更多
}
DEFAULT_RARITY_NAME = "UnknownRarity"

BATCH_SIZE = 1000  # 每批处理的记录数
MAX_RETRIES = 2 # 下载失败时的最大重试次数
RETRY_DELAY = 5 # 重试前等待秒数 (可以考虑指数退避)
DOWNLOAD_TIMEOUT_SECONDS = 180 # 单个文件下载超时时间

# --- 脚本实现 ---

def sanitize_filename_part(text: str, max_length: int = 50) -> str:
    """清理文本使其适合作为文件名的一部分"""
    if not text:
        return "unknown"
    # 移除或替换不安全字符
    text = re.sub(r'[\\/*?:"<>|]', "", text) # Windows and common unsafe chars
    text = text.replace(" ", "_")
    # 限制长度
    return text[:max_length]

async def download_image(session: aiohttp.ClientSession, url: str, filepath: str, retries: int = MAX_RETRIES):
    """下载单个图片并保存到文件，包含重试机制"""
    if not url or not url.startswith(('http://', 'https://')):
        print(f"[-] 无效或为空的 URL (跳过): {url}")
        return False
    
    attempt = 0
    while attempt <= retries:
        try:
            if attempt > 0:
                print(f"[*] 重试 {attempt}/{retries} 下载: {url}")
            
            timeout = aiohttp.ClientTimeout(total=DOWNLOAD_TIMEOUT_SECONDS)
            async with session.get(url, timeout=timeout) as response:
                if response.status == 200:
                    os.makedirs(os.path.dirname(filepath), exist_ok=True)
                    with open(filepath, 'wb') as f:
                        while True:
                            chunk = await response.content.read(8192)
                            if not chunk:
                                break
                            f.write(chunk)
                    # print(f"[+] 图片已下载 (尝试 {attempt+1}): {url} -> {filepath}") # 减少冗余日志
                    return True
                elif response.status == 404: # 对于404错误，通常不需要重试
                    print(f"[-] 文件未找到 (404 Not Found)，不重试: {url}")
                    return False
                else:
                    print(f"[-] 下载失败 (尝试 {attempt+1}，状态码 {response.status}): {url}")
                    # 对于某些可恢复的错误码，可以决定是否重试，这里简化为都重试（除了404）
            
            # 如果执行到这里，说明下载失败但不是200或404，准备重试
            if attempt < retries:
                 print(f"    将在 {RETRY_DELAY} 秒后重试...")
                 await asyncio.sleep(RETRY_DELAY)

        except asyncio.TimeoutError:
            print(f"[-] 下载超时 (尝试 {attempt+1}): {url}")
            if attempt < retries:
                print(f"    将在 {RETRY_DELAY} 秒后重试...")
                await asyncio.sleep(RETRY_DELAY)
        except aiohttp.ClientError as e:
            print(f"[-] 下载时发生 ClientError (尝试 {attempt+1}): {url} ({type(e).__name__}: {e})")
            if attempt < retries: # 对于某些ClientError也值得重试
                print(f"    将在 {RETRY_DELAY} 秒后重试...")
                await asyncio.sleep(RETRY_DELAY)
            else: # 如果是最后一次尝试的ClientError，则返回失败
                return False 
        except Exception as e:
            print(f"[-] 下载时发生未知错误 (尝试 {attempt+1}): {url} ({type(e).__name__}: {e})")
            # 未知错误通常不建议自动重试过多，这里在最后一次尝试后会失败
            if attempt >= retries: # 确保即使是未知错误，在最后一次尝试后也标记为失败
                 return False
        
        attempt += 1 # 进入下一次尝试或结束循环

    print(f"[!] 达到最大重试次数后下载依然失败: {url}")
    return False

async def main():
    print("--- 开始 gacha_master_cards 图片预下载脚本 (分批处理) ---")
    os.makedirs(IMAGE_BASE_PATH, exist_ok=True)

    conn = None
    successful_downloads_total = 0
    failed_downloads_total = 0
    skipped_downloads_total = 0
    processed_records_total = 0
    
    offset = 0
    has_more_records = True

    try:
        # 使用独立参数连接数据库
        conn = await asyncpg.connect(
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME,
            host=DB_HOST,
            port=DB_PORT
        )
        print(f"[DB] 成功连接到数据库 {DB_NAME} on {DB_HOST}:{DB_PORT} as {DB_USER}.")

        while has_more_records:
            print(f"--- 处理批次: 从记录 {offset} 开始，最多 {BATCH_SIZE} 条 ---")
            
            query = f"""
                SELECT card_id, name, series, image_url, rarity
                FROM {CARD_TABLE_NAME}
                WHERE image_url IS NOT NULL AND image_url != '' AND is_active = TRUE
                ORDER BY card_id  -- 确保分页顺序一致性
                LIMIT {BATCH_SIZE} OFFSET {offset};
            """
            
            rows = await conn.fetch(query)

            if not rows:
                print("当前批次未查询到记录。")
                has_more_records = False
                if offset == 0: # 如果第一批就没数据
                    print("数据库中没有符合条件的卡片记录可供下载。")
                else:
                    print("所有记录已处理完毕。")
                break

            current_batch_record_count = len(rows)
            processed_records_total += current_batch_record_count
            print(f"[DB] 当前批次查询到 {current_batch_record_count} 条记录。")

            # 使用 ssl=False (之前设置过) 如果需要的话, 也可以配置更细致的SSL上下文
            # connector_args = {'limit_per_host': 10, 'limit': 50}
            # if "ssl=False" was critical: connector_args['ssl'] = False 
            # connector = aiohttp.TCPConnector(**connector_args)
            
            # 简化connector创建，如果默认的SSL行为有问题再调整
            connector = aiohttp.TCPConnector(limit_per_host=15, limit=75) # 稍微增加并发，因为现在是分批
            async with aiohttp.ClientSession(connector=connector) as session:
                tasks = []
                for row in rows:
                    card_id = row["card_id"]
                    card_name = row["name"]
                    card_series = row["series"]
                    image_url = row["image_url"]
                    rarity_int = row["rarity"]
                    rarity_name = RARITY_MAP.get(rarity_int, DEFAULT_RARITY_NAME)
                    
                    try:
                        parsed_url = urlparse(image_url)
                        original_filename_from_url = os.path.basename(parsed_url.path)
                        _, extension = os.path.splitext(original_filename_from_url)
                        
                        valid_extensions = {".jpg", ".jpeg", ".png", ".gif", ".webp"}
                        if not extension or extension.lower() not in valid_extensions or len(extension) > 5:
                            lower_url = image_url.lower()
                            found_ext = False
                            for ext_type in valid_extensions:
                                if lower_url.endswith(ext_type):
                                    extension = ext_type
                                    found_ext = True
                                    break
                            if not found_ext: extension = ".png"
                        
                        name_part = sanitize_filename_part(card_name if card_name else card_series)
                        image_filename = f"{card_id}_{name_part}{extension}"
                        rarity_dir = os.path.join(IMAGE_BASE_PATH, rarity_name)
                        filepath = os.path.join(rarity_dir, image_filename)
                    except Exception as e:
                        print(f"[-] 处理元数据 (ID: {card_id}, URL: {image_url}) 时出错: {e}")
                        failed_downloads_total +=1
                        continue
                    
                    if os.path.exists(filepath) and os.path.getsize(filepath) > 0:
                        skipped_downloads_total +=1
                        continue
                    
                    tasks.append(download_image(session, image_url, filepath))

                if tasks:
                    print(f"本批次准备下载 {len(tasks)} 张新图片...")
                    batch_results = await asyncio.gather(*tasks)
                    current_batch_successful = sum(1 for r in batch_results if r)
                    current_batch_failed = len(tasks) - current_batch_successful
                    
                    successful_downloads_total += current_batch_successful
                    failed_downloads_total += current_batch_failed
                    print(f"本批次下载尝试完成: {current_batch_successful} 成功, {current_batch_failed} 失败.")
                else:
                    print("本批次没有新的图片需要下载。")
            
            if current_batch_record_count < BATCH_SIZE:
                print("当前批次获取的记录数小于批次大小，已是最后一批。")
                has_more_records = False
            
            offset += current_batch_record_count # 使用实际获取的数量来增加偏移，而不是BATCH_SIZE

            if has_more_records: # 只有在确定还有更多记录时才暂停
                inter_batch_delay = 2 # 秒
                print(f"准备处理下一批次，暂停 {inter_batch_delay} 秒...")
                await asyncio.sleep(inter_batch_delay)
        
    except asyncpg.InterfaceError as e: # 更具体的 asyncpg 连接错误
        print(f"[DB-Error] 数据库连接失败: {e}. 请检查 PG_HOST, PG_PORT, PG_USER, PG_PASSWORD, GACHA_DB_NAME 环境变量是否正确设置以及数据库服务是否运行。")
    except asyncpg.exceptions.PostgresError as e:
        print(f"[DB-Error] 数据库操作失败: {e}")
    except Exception as e:
        print(f"[Error] 发生意外错误: {type(e).__name__} - {e}")
        import traceback
        traceback.print_exc()
    finally:
        if conn:
            await conn.close()
            print("[DB] 数据库连接已关闭。")
    
    print("--- 图片预下载脚本执行完毕 ---")
    print(f"总计处理记录: {processed_records_total}")
    print(f"成功下载: {successful_downloads_total}")
    print(f"下载失败 (包括重试后失败): {failed_downloads_total}")
    print(f"已存在跳过: {skipped_downloads_total}")

if __name__ == "__main__":
    if os.name == 'nt':
       asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # It's generally better to get the loop and run until complete, then close.
    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(main())
    finally:
        # Allow time for SSL connections to close gracefully if any were made.
        # loop.run_until_complete(asyncio.sleep(0.25)) # Optional: small delay for cleanup
        loop.close() 