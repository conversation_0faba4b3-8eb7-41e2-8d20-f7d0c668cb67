"""
Gacha系統卡片自訂描述模態
用於設置卡片描述的模態視窗
"""
from typing import Any, Callable, Dict, Optional, Union
import discord
import asyncpg
from discord import TextStyle
from utils.logger import logger
from database.postgresql.async_manager import AsyncPgManager
from gacha.models.models import Card
from gacha.services.core.encyclopedia_service import EncyclopediaService
from gacha.exceptions import (
    CardDescriptionTooShortError,
    CardDescriptionTooLongError,
    UpdateCardDescriptionError,
    EncyclopediaError
)

class CardDescriptionModal(discord.ui.Modal):
    """卡片描述設置模態視窗"""

    def __init__(self, user_id: int, card_id: int, card_name: str, current_star: int, min_star: int=10, encyclopedia_service: Optional[EncyclopediaService]=None):
        """初始化卡片描述設置模態

        參數:
            user_id: 用戶ID
            card_id: 卡片ID
            card_name: 卡片名稱
            current_star: 當前星級
            min_star: 最低需要星級
            encyclopedia_service: 百科全書服務實例
        """
        super().__init__(title=f'設置「{card_name}」的描述', timeout=None)
        self.user_id = user_id
        self.card_id = card_id
        self.card_name = card_name
        self.current_star = current_star
        self.min_star = min_star
        self.description_input = discord.ui.TextInput(label=f'為這張卡片設置描述 (當前星級: {current_star})', placeholder='請輸入對這張卡片的描述...', style=TextStyle.paragraph, min_length=5, max_length=200, required=True)
        self.add_item(self.description_input)
        self.encyclopedia_service = encyclopedia_service
        if not self.encyclopedia_service:
            logger.error('[GACHA] CardDescriptionModal 初始化失敗：未提供 encyclopedia_service 實例')
            raise ValueError('未提供 encyclopedia_service 實例')

    async def on_submit(self, interaction: discord.Interaction):
        """提交處理"""
        try:
            if self.current_star < self.min_star:
                await interaction.response.send_message(f'設置描述失敗！你的卡片星級 ({self.current_star}) 不足 {self.min_star} 星。', ephemeral=True)
                return
                
            description = self.description_input.value.strip()
            
            # 不需要在這裡檢查長度，service 會拋出適當的異常
            await self.encyclopedia_service.update_card_description(
                self.user_id, 
                self.card_id, 
                description
            )
            
            # 如果沒有拋出異常，表示更新成功
            await interaction.response.send_message(
                f'成功為「{self.card_name}」設置描述！\n\n描述內容：「{description}」', 
                ephemeral=True
            )
            
        except CardDescriptionTooShortError as e:
            await interaction.response.send_message(str(e), ephemeral=True)
        except CardDescriptionTooLongError as e:
            await interaction.response.send_message(str(e), ephemeral=True)
        except UpdateCardDescriptionError as e:
            await interaction.response.send_message(f"設置描述時發生錯誤：{str(e)}", ephemeral=True)
        except EncyclopediaError as e:
            await interaction.response.send_message(f"圖鑑操作錯誤：{str(e)}", ephemeral=True)
        except Exception as e:
            logger.error('[GACHA] 設置卡片描述時發生錯誤: %s', str(e), exc_info=True)
            await interaction.response.send_message('設置描述時發生未知錯誤，請稍後再試。', ephemeral=True)