"""
Discord 互動相關的輔助函數
"""
from typing import Any, Optional
import discord
from utils.logger import logger

async def check_user_permission(interaction: discord.Interaction, expected_user_id: int, error_message_on_fail: str='您沒有權限執行此操作。') -> bool:
    """
    檢查互動用戶是否為預期的用戶

    參數:
        interaction: Discord 互動對象
        expected_user_id: 預期的用戶 ID
        error_message_on_fail: 權限檢查失敗時發送的錯誤訊息

    返回:
        bool: 如果用戶有權限則返回 True，否則返回 False 並發送錯誤訊息
    """
    if interaction.user.id != expected_user_id:
        if interaction.response.is_done():
            await interaction.followup.send(error_message_on_fail, ephemeral=True)
        else:
            await interaction.response.send_message(error_message_on_fail, ephemeral=True)
        return False
    return True

async def safe_defer(interaction: discord.Interaction, thinking: bool=False, ephemeral: bool=False):
    """
    安全地延遲互動，處理已經回應的情況

    參數:
        interaction: Discord 互動對象
        thinking: 是否顯示 "正在思考..." 狀態
        ephemeral: 是否僅對發起者可見
    """
    try:
        if not interaction.response.is_done():
            await interaction.response.defer(thinking=thinking, ephemeral=ephemeral)
            logger.debug('[InteractionDefer] Interaction %s deferred successfully.', interaction.id)
    except discord.NotFound:
        logger.warning('[InteractionDefer] Interaction %s not found, likely already responded or timed out.', interaction.id)
    except discord.HTTPException as e:
        logger.error('[InteractionDefer] Failed to defer interaction %s: %s', interaction.id, e, exc_info=True)
    except Exception as e:
        logger.error('[InteractionDefer] An unexpected error occurred while deferring interaction %s: %s', interaction.id, e, exc_info=True)

async def safe_edit_message(interaction: discord.Interaction, **kwargs: Any) -> Optional[discord.Message]:
    """
    安全地編輯原始訊息，使用 followup.edit_message

    參數:
        interaction: Discord 互動對象
        **kwargs: 傳遞給 followup.edit_message 的參數 (例如 content, embed, view)，
                  也可能包含 'message_to_edit' 來指定要編輯的訊息對象。
    返回:
        Optional[discord.Message]: 成功編輯則返回 Message 對象，否則返回 None
    """
    message_object_to_edit = kwargs.pop('message_to_edit', None)
    target_message_for_id = message_object_to_edit or interaction.message
    if not target_message_for_id:
        logger.error('safe_edit_message: No message found to determine message_id for interaction %s', interaction.id)
        return None
    message_id = target_message_for_id.id
    try:
        if message_id:
            edited_message = await interaction.followup.edit_message(message_id=message_id, **kwargs)
            logger.debug('[InteractionEdit] Edited message %s for interaction %s successfully.', message_id, interaction.id)
            return edited_message
        else:
            logger.warning('[InteractionEdit] Interaction %s does not have a valid message_id to edit.', interaction.id)
            return None
    except discord.NotFound:
        logger.warning('[InteractionEdit] Could not find original message %s for interaction %s to edit.', message_id, interaction.id)
        return None
    except discord.HTTPException as e:
        logger.error('[InteractionEdit] Failed to edit message %s for interaction %s: %s', message_id, interaction.id, e, exc_info=True)
        return None
    except Exception as e:
        logger.error('[InteractionEdit] An unexpected error occurred while editing message %s for interaction %s: %s', message_id, interaction.id, e, exc_info=True)
        return None

async def safe_send_message(interaction: discord.Interaction, content: Optional[str]=None, **kwargs: Any) -> Optional[discord.Message]:
    """
    安全地發送回應訊息，根據互動狀態選擇 response.send_message 或 followup.send，
    並嘗試返回 discord.Message 對象。

    參數:
        interaction: Discord 互動對象
        content: 訊息內容
        **kwargs: 傳遞給 send_message 或 followup.send 的其他參數 (例如 embed, view, ephemeral)

    返回:
        Optional[discord.Message]: 成功發送則返回 Message 對象，否則返回 None
    """
    message: Optional[discord.Message] = None
    try:
        effective_kwargs = kwargs.copy()
        if effective_kwargs.get('view') is None:
            effective_kwargs.pop('view', None)
        if not interaction.response.is_done():
            await interaction.response.send_message(content=content, **effective_kwargs)
            logger.debug('[InteractionSend] Sent response for interaction %s successfully.', interaction.id)
            message = await interaction.original_response()
            if message:
                logger.debug('[InteractionSend] Successfully fetched original response message for interaction %s.', interaction.id)
            else:
                logger.warning('[InteractionSend] Failed to fetch original response message for interaction %s. original_response() returned None.', interaction.id)
        else:
            is_ephemeral = kwargs.get('ephemeral', False)
            if is_ephemeral:
                logger.debug('[InteractionFollowup] Sending ephemeral followup for interaction %s. Forcing wait=True to attempt message object retrieval.', interaction.id)
            message = await interaction.followup.send(content=content, **effective_kwargs, wait=True)
            if message:
                logger.debug('[InteractionFollowup] Sent followup for interaction %s successfully with wait=True. Message ID: %s', interaction.id, message.id)
            else:
                logger.warning('[InteractionFollowup] interaction.followup.send with wait=True returned None for interaction %s.', interaction.id)
    except discord.NotFound:
        logger.warning('[InteractionSend/Followup] Interaction %s not found, could not send message.', interaction.id)
        return None
    except discord.HTTPException as e:
        logger.error('[InteractionSend/Followup] Failed to send message for interaction %s: %s', interaction.id, e, exc_info=True)
        return None
    except Exception as e:
        logger.error('[InteractionSend/Followup] An unexpected error occurred while sending message for interaction %s: %s', interaction.id, e, exc_info=True)
        return None
    return message

async def disable_view_items(view: discord.ui.View) -> None:
    """
    禁用指定 View 中的所有 UI 組件 (例如按鈕、選擇菜單)。

    參數:
        view: 要禁用其組件的 discord.ui.View 實例。
    """
    if not isinstance(view, discord.ui.View):
        logger.warning('[DisableViewItems] 傳遞的對象不是 discord.ui.View 的實例: %s', type(view))
        return
    for item in view.children:
        if hasattr(item, 'disabled') and isinstance(item, (discord.ui.Button, discord.ui.Select)):
            item.disabled = True
    logger.debug('[DisableViewItems] Disabled items for view: %s', view)