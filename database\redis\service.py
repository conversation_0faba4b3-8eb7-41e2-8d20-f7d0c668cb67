"""
Redis 服務管理模塊
用於自動啟動和管理 Redis 服務
"""

import os
import platform
import subprocess
import time
from typing import Optional, Tuple

from utils.logger import logger


class RedisService:
    """Redis 服務管理類，提供自動啟動和管理 Redis 服務的功能"""

    def __init__(self):
        """初始化 Redis 服務管理器"""
        self.system = platform.system().lower()
        self.redis_server_process = None
        self.is_running = False

    def check_redis_running(self) -> bool:
        """檢查 Redis 服務是否正在運行

        Returns:
            是否正在運行
        """
        try:
            # 嘗試執行 redis-cli ping 命令
            result = subprocess.run(
                ["redis-cli", "ping"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=2,
            )
            return result.stdout.strip() == "PONG"
        except (subprocess.SubprocessError, FileNotFoundError):
            return False

    def check_redis_installed(self) -> bool:
        """檢查 Redis 是否已安裝

        Returns:
            是否已安裝
        """
        try:
            # 嘗試執行 redis-cli --version 命令
            subprocess.run(
                ["redis-cli", "--version"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True,
                timeout=2,
            )
            return True
        except (subprocess.SubprocessError, FileNotFoundError):
            return False

    def get_redis_server_path(self) -> Optional[str]:
        """獲取 Redis 服務器可執行文件路徑

        Returns:
            Redis 服務器路徑，如果找不到則返回 None
        """
        if self.system == "windows":
            # Windows 上 Redis 服務器可能的路徑
            possible_paths = [
                "C:\\Program Files\\Redis\\redis-server.exe",
                "C:\\Program Files (x86)\\Redis\\redis-server.exe",
                os.path.join(
                    os.environ.get("ProgramFiles", "C:\\Program Files"),
                    "Redis",
                    "redis-server.exe",
                ),
                os.path.join(
                    os.environ.get("ProgramFiles(x86)", "C:\\Program Files (x86)"),
                    "Redis",
                    "redis-server.exe",
                ),
            ]

            # 檢查 PATH 環境變量
            if "PATH" in os.environ:
                for path_dir in os.environ["PATH"].split(os.pathsep):
                    possible_paths.append(os.path.join(path_dir, "redis-server.exe"))

            # 檢查每個可能的路徑
            for path in possible_paths:
                if os.path.exists(path):
                    return path

            # 如果找不到，嘗試使用 where 命令
            try:
                result = subprocess.run(
                    ["where", "redis-server"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=2,
                )
                if result.returncode == 0 and result.stdout.strip():
                    return result.stdout.strip().split("\n")[0]
            except (subprocess.SubprocessError, FileNotFoundError):
                pass
        else:
            # Linux/macOS 上使用 which 命令
            try:
                result = subprocess.run(
                    ["which", "redis-server"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=2,
                )
                if result.returncode == 0 and result.stdout.strip():
                    return result.stdout.strip()
            except (subprocess.SubprocessError, FileNotFoundError):
                pass

        return None

    def start_redis_service(self) -> Tuple[bool, str]:
        """啟動 Redis 服務

        Returns:
            (是否成功, 錯誤信息)
        """
        # 檢查 Redis 是否已經在運行
        if self.check_redis_running():
            logger.info("Redis 服務已經在運行")
            self.is_running = True
            return True, "Redis 服務已經在運行"

        # 檢查 Redis 是否已安裝
        if not self.check_redis_installed():
            logger.warning("Redis 未安裝，無法啟動服務")
            return False, "Redis 未安裝，請先安裝 Redis"

        # 根據操作系統啟動 Redis 服務
        if self.system == "windows":
            return self._start_redis_windows()
        elif self.system == "linux":
            return self._start_redis_linux()
        elif self.system == "darwin":  # macOS
            return self._start_redis_mac()
        else:
            logger.error(f"不支持的操作系統: {self.system}")
            return False, f"不支持的操作系統: {self.system}"

    def _start_redis_windows(self) -> Tuple[bool, str]:
        """在 Windows 上啟動 Redis 服務

        Returns:
            (是否成功, 錯誤信息)
        """
        # 嘗試啟動 Windows 服務
        try:
            subprocess.run(
                ["net", "start", "Redis"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=5,
            )

            # 檢查服務是否成功啟動
            time.sleep(1)
            if self.check_redis_running():
                logger.info("Redis Windows 服務已啟動")
                self.is_running = True
                return True, "Redis Windows 服務已啟動"
        except subprocess.SubprocessError as e:
            logger.warning(f"啟動 Redis Windows 服務失敗: {str(e)}")

        # 如果服務啟動失敗，嘗試直接啟動 redis-server
        redis_server_path = self.get_redis_server_path()
        if not redis_server_path:
            logger.error("找不到 redis-server 可執行文件")
            return False, "找不到 redis-server 可執行文件"

        try:
            # 使用 subprocess.Popen 啟動 Redis 服務器，不阻塞主進程
            self.redis_server_process = subprocess.Popen(
                [redis_server_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW,  # 不顯示命令行窗口
            )

            # 等待服務啟動
            time.sleep(2)

            # 檢查服務是否成功啟動
            if self.check_redis_running():
                logger.info("Redis 服務器已啟動")
                self.is_running = True
                return True, "Redis 服務器已啟動"
            else:
                if self.redis_server_process:
                    self.redis_server_process.terminate()
                    self.redis_server_process = None
                logger.error("Redis 服務器啟動失敗")
                return False, "Redis 服務器啟動失敗"
        except Exception as e:
            logger.error(f"啟動 Redis 服務器時發生錯誤: {str(e)}")
            return False, f"啟動 Redis 服務器時發生錯誤: {str(e)}"

    def _start_redis_linux(self) -> Tuple[bool, str]:
        """在 Linux 上啟動 Redis 服務

        Returns:
            (是否成功, 錯誤信息)
        """
        try:
            # 嘗試使用 systemctl 啟動 Redis 服務
            subprocess.run(
                ["sudo", "systemctl", "start", "redis"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=5,
            )

            # 檢查服務是否成功啟動
            time.sleep(1)
            if self.check_redis_running():
                logger.info("Redis 服務已通過 systemctl 啟動")
                self.is_running = True
                return True, "Redis 服務已通過 systemctl 啟動"
        except subprocess.SubprocessError:
            logger.warning("通過 systemctl 啟動 Redis 服務失敗，嘗試使用 service")

        try:
            # 嘗試使用 service 啟動 Redis 服務
            subprocess.run(
                ["sudo", "service", "redis-server", "start"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=5,
            )

            # 檢查服務是否成功啟動
            time.sleep(1)
            if self.check_redis_running():
                logger.info("Redis 服務已通過 service 啟動")
                self.is_running = True
                return True, "Redis 服務已通過 service 啟動"
        except subprocess.SubprocessError:
            logger.warning(
                "通過 service 啟動 Redis 服務失敗，嘗試直接啟動 redis-server"
            )

        # 如果服務啟動失敗，嘗試直接啟動 redis-server
        redis_server_path = self.get_redis_server_path()
        if not redis_server_path:
            logger.error("找不到 redis-server 可執行文件")
            return False, "找不到 redis-server 可執行文件"

        try:
            # 使用 subprocess.Popen 啟動 Redis 服務器，不阻塞主進程
            self.redis_server_process = subprocess.Popen(
                [redis_server_path], stdout=subprocess.PIPE, stderr=subprocess.PIPE
            )

            # 等待服務啟動
            time.sleep(2)

            # 檢查服務是否成功啟動
            if self.check_redis_running():
                logger.info("Redis 服務器已啟動")
                self.is_running = True
                return True, "Redis 服務器已啟動"
            else:
                if self.redis_server_process:
                    self.redis_server_process.terminate()
                    self.redis_server_process = None
                logger.error("Redis 服務器啟動失敗")
                return False, "Redis 服務器啟動失敗"
        except Exception as e:
            logger.error(f"啟動 Redis 服務器時發生錯誤: {str(e)}")
            return False, f"啟動 Redis 服務器時發生錯誤: {str(e)}"

    def _start_redis_mac(self) -> Tuple[bool, str]:
        """在 macOS 上啟動 Redis 服務

        Returns:
            (是否成功, 錯誤信息)
        """
        try:
            # 嘗試使用 brew services 啟動 Redis 服務
            subprocess.run(
                ["brew", "services", "start", "redis"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=5,
            )

            # 檢查服務是否成功啟動
            time.sleep(1)
            if self.check_redis_running():
                logger.info("Redis 服務已通過 brew services 啟動")
                self.is_running = True
                return True, "Redis 服務已通過 brew services 啟動"
        except subprocess.SubprocessError:
            logger.warning(
                "通過 brew services 啟動 Redis 服務失敗，嘗試直接啟動 redis-server"
            )

        # 如果服務啟動失敗，嘗試直接啟動 redis-server
        redis_server_path = self.get_redis_server_path()
        if not redis_server_path:
            logger.error("找不到 redis-server 可執行文件")
            return False, "找不到 redis-server 可執行文件"

        try:
            # 使用 subprocess.Popen 啟動 Redis 服務器，不阻塞主進程
            self.redis_server_process = subprocess.Popen(
                [redis_server_path], stdout=subprocess.PIPE, stderr=subprocess.PIPE
            )

            # 等待服務啟動
            time.sleep(2)

            # 檢查服務是否成功啟動
            if self.check_redis_running():
                logger.info("Redis 服務器已啟動")
                self.is_running = True
                return True, "Redis 服務器已啟動"
            else:
                if self.redis_server_process:
                    self.redis_server_process.terminate()
                    self.redis_server_process = None
                logger.error("Redis 服務器啟動失敗")
                return False, "Redis 服務器啟動失敗"
        except Exception as e:
            logger.error(f"啟動 Redis 服務器時發生錯誤: {str(e)}")
            return False, f"啟動 Redis 服務器時發生錯誤: {str(e)}"

    def stop_redis_service(self) -> Tuple[bool, str]:
        """停止 Redis 服務

        Returns:
            (是否成功, 錯誤信息)
        """
        # 如果是由本程序啟動的 Redis 服務器，直接終止進程
        if self.redis_server_process:
            try:
                self.redis_server_process.terminate()
                self.redis_server_process = None
                self.is_running = False
                logger.info("Redis 服務器已停止")
                return True, "Redis 服務器已停止"
            except Exception as e:
                logger.error(f"停止 Redis 服務器時發生錯誤: {str(e)}")
                return False, f"停止 Redis 服務器時發生錯誤: {str(e)}"

        # 根據操作系統停止 Redis 服務
        if self.system == "windows":
            try:
                subprocess.run(
                    ["net", "stop", "Redis"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    timeout=5,
                )
                self.is_running = False
                logger.info("Redis Windows 服務已停止")
                return True, "Redis Windows 服務已停止"
            except subprocess.SubprocessError as e:
                logger.error(f"停止 Redis Windows 服務失敗: {str(e)}")
                return False, f"停止 Redis Windows 服務失敗: {str(e)}"
        elif self.system == "linux":
            try:
                subprocess.run(
                    ["sudo", "systemctl", "stop", "redis"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    timeout=5,
                )
                self.is_running = False
                logger.info("Redis 服務已通過 systemctl 停止")
                return True, "Redis 服務已通過 systemctl 停止"
            except subprocess.SubprocessError:
                try:
                    subprocess.run(
                        ["sudo", "service", "redis-server", "stop"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        timeout=5,
                    )
                    self.is_running = False
                    logger.info("Redis 服務已通過 service 停止")
                    return True, "Redis 服務已通過 service 停止"
                except subprocess.SubprocessError as e:
                    logger.error(f"停止 Redis 服務失敗: {str(e)}")
                    return False, f"停止 Redis 服務失敗: {str(e)}"
        elif self.system == "darwin":  # macOS
            try:
                subprocess.run(
                    ["brew", "services", "stop", "redis"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    timeout=5,
                )
                self.is_running = False
                logger.info("Redis 服務已通過 brew services 停止")
                return True, "Redis 服務已通過 brew services 停止"
            except subprocess.SubprocessError as e:
                logger.error(f"停止 Redis 服務失敗: {str(e)}")
                return False, f"停止 Redis 服務失敗: {str(e)}"
        else:
            logger.error(f"不支持的操作系統: {self.system}")
            return False, f"不支持的操作系統: {self.system}"

    def __del__(self):
        """析構函數，確保在對象被銷毀時停止 Redis 服務"""
        if self.redis_server_process:
            try:
                self.redis_server_process.terminate()
            except:
                pass


# 創建全局 Redis 服務管理器實例
redis_service = RedisService()
