@echo off
chcp 65001 > nul
echo 設定環境變數...
set GACHA_DB_NAME=gacha_database
set DEV_MODE=false
echo LOG_LEVEL=ERROR
(
echo DISCORD_TOKEN=MTIyMTIzMDczNDYwMjE0MTcyNw.G5uxa8.nCy4baPoCd7YD_qe1BTfsuptJzz_hzj9UeLGI8
echo GACHA_DB_NAME=gacha_database
echo LOG_LEVEL=ERROR
echo PG_HOST=127.0.0.1
echo PG_PORT=5432
echo PG_USER=postgres
echo PG_PASSWORD=26015792
echo PVE_DB_TYPE=postgresql
echo LADDER_DB_TYPE=postgresql
echo LADDER_DATABASE=ladder_database
echo DEV_MODE=true
echo PVE_DATABASE=pve_database
echo ENABLE_PVE_SYSTEM=false
echo ENABLE_LADDER_SYSTEM=true
echo ENABLE_GACHA_SYSTEM=true
echo AI_API_KEY=26015792
echo REDIS_HOST=localhost
echo REDIS_PORT=6379
echo REDIS_DB=0
echo REDIS_PASSWORD=
echo REDIS_ENABLED=True
) > .env
python bot.py