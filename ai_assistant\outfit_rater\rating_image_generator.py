"""
評分圖片生成器模組 - 負責生成穿搭評分可視化圖片
"""

import logging
import os
import io
from typing import Dict, Any
import json
import asyncio
from PIL import Image
from playwright.async_api import Page # Import Page for type hinting
from urllib.parse import urljoin # To construct file URIs correctly

# Import the new PlaywrightManager
# Ensure 'utils' directory is accessible from where this script is run,
# or adjust the import path accordingly (e.g., using relative imports if structured as a package)
# Assuming 'utils' is at the root level along with 'ai_assistant'
import sys
# This adds the project root (d:/DICKPK) to the path if needed
# Adjust if your project structure is different
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)
from utils.playwright_manager import PlaywrightManager


# 設置日誌
logger = logging.getLogger(__name__) # Use __name__ for logger

class RatingImageGenerator:
    """穿搭評分圖像生成器，使用 Playwright 和 HTML 模板生成美觀的評分圖片"""

    def __init__(self, playwright_manager: PlaywrightManager):
        """
        初始化評分圖像生成器
        Args:
            playwright_manager: An instance of PlaywrightManager.
        """
        self.playwright_manager = playwright_manager
        # 獲取HTML模板路徑
        self.template_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "outfit_rating_simple_template.html")
        logger.info(f"評分圖像生成器初始化完成，使用模板: {self.template_path}")

    async def generate_rating_image(
        self,
        rating_result: Dict[str, Any],
        user_image_path: str,
        output_path: str,
        user_name: str | None = None  # 增加用戶名稱參數
    ) -> str:
        """
        使用 Playwright 生成評分圖片

        參數:
            rating_result (Dict[str, Any]): 評分結果
            user_image_path (str): 用戶圖像路徑 (絕對或相對路徑)
            output_path (str): 輸出圖像路徑 (WebP 格式)
            user_name (str, optional): 用戶名稱，如果不提供則使用默認值

        返回:
            str: 生成的圖片文件路徑
        Raises:
            Exception: If image generation fails at any step.
        """
        page: Page | None = None # Initialize page to None for finally block
        request_id_from_result = rating_result.get("request_id", "unknown_req_id") # Try to get request_id if available
        logger.info(f"[{request_id_from_result}] generate_rating_image called. User: {user_name}, Image: {user_image_path}, Output: {output_path}")
        logger.debug(f"[{request_id_from_result}] Rating result for image generation: {json.dumps(rating_result, ensure_ascii=False)}")
        try:
            # Acquire page from the manager passed during initialization
            page = await self.playwright_manager.acquire_page()
            logger.info(f"Playwright Page acquired for image generation. Request ID hint: {request_id_from_result}")

            # 提取評分、評價和建議
            score = rating_result.get("score", "?/10")
            review = rating_result.get("review", "無法獲取評價")
            suggestion = rating_result.get("suggestion", "無法獲取建議")
            conclusion = rating_result.get("conclusion", "超讚!")
            user_name = user_name or "用戶"

            # Construct the file URI for the HTML template
            abs_template_path = os.path.abspath(self.template_path)
            template_file_uri = urljoin('file:', abs_template_path.replace('\\', '/'))
            if not template_file_uri.startswith('file:///'):
                 template_file_uri = template_file_uri.replace('file:/','file:///', 1)
            logger.debug(f"Loading template from URI: {template_file_uri}. Request ID hint: {request_id_from_result}")

            # Load HTML template
            await page.goto(template_file_uri, wait_until='domcontentloaded')

            # Construct the file URI for the user image
            abs_user_image_path = os.path.abspath(user_image_path)
            image_file_uri = urljoin('file:', abs_user_image_path.replace('\\', '/'))
            if not image_file_uri.startswith('file:///'):
                image_file_uri = image_file_uri.replace('file:/','file:///', 1)
            logger.debug(f"Setting user image URI: {image_file_uri}. Request ID hint: {request_id_from_result}")

            # Use page.evaluate to set the image source attribute
            await page.evaluate(
                "([selector, value]) => { document.querySelector(selector).setAttribute('src', value); }",
                ['#user-image', image_file_uri]
            )
            # 使用正确的Playwright API设置文本内容
            await page.evaluate("([selector, value]) => { document.querySelector(selector).innerText = value; }", ['#username-text', user_name])
            await page.evaluate("([selector, value]) => { document.querySelector(selector).innerText = value; }", ['#rating-score', score])
            await page.evaluate("([selector, value]) => { document.querySelector(selector).innerText = value; }", ['#rating-content', review])
            await page.evaluate("([selector, value]) => { document.querySelector(selector).innerText = value; }", ['#suggestion-content', suggestion])
            await page.evaluate("([selector, value]) => { document.querySelector(selector).innerText = value; }", ['.text-sm.text-pink-600.font-medium', conclusion])

            # Find the card element using locator
            card_locator = page.locator(".max-w-md.mx-auto")

            # Wait for the element to be visible and stable if needed
            await card_locator.wait_for(state="visible")

            # Take screenshot of the specific element
            logger.info(f"Taking screenshot for card element. Output: {output_path}. Request ID hint: {request_id_from_result}")
            screenshot_bytes = await card_locator.screenshot(type="png")

            # Process the image using PIL
            img = Image.open(io.BytesIO(screenshot_bytes))

            # Save the image as WebP
            img.save(output_path, 'WEBP', quality=100)
            logger.info(f"評分圖像已保存到 (WebP): {output_path}. Request ID hint: {request_id_from_result}")

            return output_path # Return the actual output path

        except Exception as e:
            logger.error(f"[{request_id_from_result}] 生成評分圖像時出錯: {e}", exc_info=True)
            raise # Re-raise after finally block handles release
        finally:
            if page:
                logger.info(f"[{request_id_from_result}] Releasing Playwright Page after image generation.")
                # Release page using the manager passed during initialization
                await self.playwright_manager.release_page(page)

# Removed old example_usage function and other unused code.