"""
Gacha系統 - 收藏相關指令的 Cog
"""
import discord
from discord import app_commands
from discord.ext import commands
from typing import Optional, List, Dict, Any
from utils.logger import logger
from gacha.services.core.collection_service import CollectionService
from gacha.services.core.user_service import UserService
from gacha.services.core.economy_service import EconomyService
from gacha.services.core.favorite_service import FavoriteService
from gacha.services.core.wish_service import WishService
from gacha.services.core.star_enhancement_service import StarEnhancementService
from gacha.services.core.encyclopedia_service import EncyclopediaService
from gacha.services.sorting.sorting_service import SortingService
from gacha.views.collection.collection_view import CollectionView, SeriesListView
from gacha.views.collection.encyclopedia_view import EncyclopediaView
from gacha.views.embeds.collection.rarity_embed_builder import RarityEmbedBuilder
from gacha.views.embeds.collection.series_embed_builder import SeriesEmbedBuilder
from gacha.models.models import Card
from gacha.models.filters import CollectionFilters
from gacha.views.utils import common_pool_type_autocomplete, common_series_autocomplete, common_rarity_autocomplete
from gacha.exceptions import UserNotFoundError, RecordNotFoundError, GachaSystemError, CardNotFoundError, FavoriteCardError, CardPageNotFoundException, DatabaseOperationError
from gacha.utils import interaction_utils
from gacha.utils.cog_error_handler import handle_gacha_error # 導入 handle_gacha_error

class CollectionCog(commands.Cog):
    """處理與卡片收藏相關的指令，例如查看卡冊、圖鑑、系列和標記最愛。"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        logger.info('CollectionCog 已成功加載並初始化。')

    @app_commands.command(name='mw', description='查看你的卡冊')
    @app_commands.describe(page='頁碼（從1開始）', sort_by='排序方式（rarity稀有度，name名稱，series系列，quantity數量）', sort_order='排序順序（desc降序，asc升序）', pool_type='卡池類型（不選則顯示全部）', rarity='稀有度篩選（C、R、SR、SSR、UR、LR、EX）', series_name='按系列名稱篩選', card_id='按卡片ID查詢', card_name='按卡片名稱查詢（支援模糊搜尋）', show_duplicates='是否只顯示重複擁有的卡片（數量 > 1）')
    @app_commands.choices(sort_by=[app_commands.Choice(name='按稀有度排序', value='rarity'), app_commands.Choice(name='按名稱排序', value='name'), app_commands.Choice(name='按系列排序', value='series'), app_commands.Choice(name='按數量排序', value='quantity'), app_commands.Choice(name='按星級排序', value='star')], sort_order=[app_commands.Choice(name='降序（從高到低）', value='desc'), app_commands.Choice(name='升序（從低到高）', value='asc')])
    async def view_collection(self, interaction: discord.Interaction, page: Optional[int]=1, sort_by: Optional[str]='rarity', sort_order: Optional[str]='desc', pool_type: Optional[str]=None, rarity: Optional[int]=None, series_name: Optional[str]=None, card_id: Optional[int]=None, card_name: Optional[str]=None, show_duplicates: Optional[bool]=False):
        """查看卡冊命令處理"""
        await interaction.response.defer(thinking=True)
        
        try:
            collection_service: CollectionService = self.bot.collection_service
            user_id = interaction.user.id
            
            # 建立篩選條件
            filters = CollectionFilters()
            if pool_type == 'all':
                filters.pool_type = None
            else:
                filters.pool_type = pool_type
                
            if rarity is not None:
                filters.rarity_in = [rarity]
                
            filters.series = series_name
            if show_duplicates:
                filters.quantity_greater_than = 1
            filters.card_id = card_id
            filters.card_name = card_name
            
            target_page_number = page
            pre_fetched_unique_cards_for_call: Optional[int] = None
            
            # 處理卡片ID搜尋
            if card_id is not None:
                id_search_filters = CollectionFilters(
                    pool_type=filters.pool_type, 
                    rarity_in=filters.rarity_in, 
                    series=filters.series, 
                    quantity_greater_than=filters.quantity_greater_than if show_duplicates else None, 
                    card_id=None, 
                    card_name=None
                )
                page_from_find, unique_cards_for_id_filter = await collection_service.find_card_page(
                    user_id, card_id, sort_by, sort_order, id_search_filters
                )
                if page_from_find > 0:
                    target_page_number = page_from_find
                    pre_fetched_unique_cards_for_call = unique_cards_for_id_filter
                    filters.card_id = None
                else:
                    embed = discord.Embed(
                        title='查找卡片', 
                        description=f'未找到ID為 `{card_id}` 的卡片，或在此篩選條件下你尚未擁有此卡', 
                        color=discord.Color.red()
                    )
                    await interaction.followup.send(embed=embed)
                    return
                    
            # 處理卡片名稱搜尋
            elif card_name is not None:
                name_search_filters = CollectionFilters(
                    pool_type=filters.pool_type, 
                    rarity_in=filters.rarity_in, 
                    series=filters.series, 
                    quantity_greater_than=filters.quantity_greater_than if show_duplicates else None, 
                    card_name=card_name
                )
                # 直接調用，讓異常傳播
                card_result = await collection_service.collection_repo.find_user_card_by_name(user_id, name_search_filters)
                found_card_id = card_result['card_id']
                page_from_find, unique_cards_for_name_filter = await collection_service.find_card_page(
                    user_id, found_card_id, sort_by, sort_order, name_search_filters
                )
                if page_from_find > 0:
                    target_page_number = page_from_find
                    pre_fetched_unique_cards_for_call = unique_cards_for_name_filter
                else:
                    embed = discord.Embed(
                        title='查找卡片', 
                        description=f'找到了名為 `{card_name}` 的卡片，但無法定位其頁面。', 
                        color=discord.Color.orange()
                    )
                    await interaction.followup.send(embed=embed)
                    return
            
            # 獲取卡冊數據 - 直接調用，讓異常傳播
            collection_data = await collection_service.get_user_cards_paginated(
                user_id, target_page_number, sort_by, sort_order, filters, 
                pre_fetched_unique_cards=pre_fetched_unique_cards_for_call
            )
            
            # 檢查是否有卡片
            if collection_data.get('unique_cards', 0) == 0:
                embed = discord.Embed(
                    title='空的卡冊', 
                    description='你還沒有收集到任何符合條件的卡片，使用`/w`命令可以抽卡', 
                    color=discord.Color.light_gray()
                )
                await interaction.followup.send(embed=embed)
                return
            
            # 創建視圖
            from gacha.views.collection.collection_view.gacha_services import GachaServices
            services = GachaServices.from_bot(self.bot)
            
            view = CollectionView(
                user=interaction.user, 
                cards=collection_data['cards'], 
                current_page=collection_data['current_page'], 
                total_pages=collection_data['total_pages'], 
                total_cards=collection_data['total_cards'], 
                unique_cards=collection_data['unique_cards'], 
                sort_by=sort_by, 
                sort_order=sort_order, 
                services=services, 
                filters=filters, 
                interaction=interaction
            )
            
            await view.async_init()
            embed = await view.get_current_page_embed()
            message = await interaction.followup.send(embed=embed, view=view)
            view.message = message
            
        except RecordNotFoundError as e:
            # 處理找不到記錄的錯誤（如卡片名稱搜尋失敗）
            if card_name:
                embed = discord.Embed(
                    title='查找卡片', 
                    description=f'未找到名稱包含 `{card_name}` 的卡片，或在此篩選條件下你尚未擁有此卡', 
                    color=discord.Color.red()
                )
            else:
                embed = discord.Embed(
                    title='查看卡冊失敗', 
                    description='找不到相關的卡片記錄', 
                    color=discord.Color.red()
                )
            await interaction.followup.send(embed=embed)
            
        except UserNotFoundError:
            # 處理用戶不存在的錯誤
            embed = discord.Embed(
                title='帳戶錯誤', 
                description='找不到你的帳戶資訊，請稍後再試', 
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
            
        except CardNotFoundError as e:
            # 處理找不到卡片的錯誤
            card_id_value = getattr(e, 'card_id', None)
            embed = discord.Embed(
                title='卡片錯誤', 
                description=f'找不到ID為 `{card_id_value}` 的卡片' if card_id_value else '找不到指定的卡片', 
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            # 處理其他未預期的錯誤
            logger.error('[COG][mw] 查看卡冊時發生未預期錯誤: %s', e, exc_info=True)
            await handle_gacha_error(interaction, e, '查看卡冊時')

    @view_collection.autocomplete('pool_type')
    async def mw_pool_type_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        return await common_pool_type_autocomplete(interaction, current, show_individual_pools_only=True)

    @view_collection.autocomplete('series_name')
    async def mw_series_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        collection_service: CollectionService = self.bot.collection_service
        return await common_series_autocomplete(interaction, current, collection_service)

    @view_collection.autocomplete('rarity')
    async def mw_rarity_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[int]]:
        return await common_rarity_autocomplete(interaction, current)

    @app_commands.command(name='mwr', description='查看你的稀有度收藏情況')
    @app_commands.describe(pool_type='卡池類型（不選則顯示全部）')
    async def view_rarity_collection(self, interaction: discord.Interaction, pool_type: Optional[str]=None):
        """查看稀有度收藏情況命令處理"""
        collection_service: CollectionService = self.bot.collection_service
        await interaction.response.defer(thinking=True)
        try:
            user_id = interaction.user.id
            if pool_type == 'all':
                pool_type = None
            cards_summary = await collection_service.collection_repo.get_user_cards_summary(user_id, pool_type)
            if not cards_summary:
                embed = discord.Embed(title='稀有度收藏統計', description='你的卡冊是空的，使用`/w`命令可以抽卡', color=discord.Color.light_gray())
                await interaction.followup.send(embed=embed)
                return
            collection_stats = await collection_service.collection_repo.get_user_collection_stats(user_id, CollectionFilters(pool_type=pool_type))
            rarities_total = await collection_service.collection_repo.get_rarities_total_count(pool_type)
            builder = RarityEmbedBuilder(user=interaction.user, cards_summary=cards_summary, collection_stats=collection_stats, rarities_total=rarities_total)
            await interaction.followup.send(embed=builder.build_embed())
        except UserNotFoundError:
            embed = discord.Embed(
                title='帳戶錯誤', 
                description='找不到你的帳戶資訊，請稍後再試', 
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
        except Exception as e:
            logger.error('[COG][mwr] Error in view_rarity_collection: %s', e, exc_info=True)
            await handle_gacha_error(interaction, e, '查看稀有度收藏情況時')

    @view_rarity_collection.autocomplete('pool_type')
    async def mwr_pool_type_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        return await common_pool_type_autocomplete(interaction, current, show_individual_pools_only=False)

    @app_commands.command(name='mws', description='查看特定系列的收集情況')
    @app_commands.describe(series='系列名稱（留空則列出所有可用系列）', page='頁碼（從1開始，僅當未指定系列名稱時有效）', pool_type='卡池類型（不選則顯示全部）')
    async def view_series(self, interaction: discord.Interaction, series: Optional[str]=None, page: Optional[int]=1, pool_type: Optional[str]=None):
        """查看系列收集情況命令處理"""
        collection_service: CollectionService = self.bot.collection_service
        await interaction.response.defer(thinking=True)
        try:
            if pool_type == 'all':
                pool_type = None
            user_id = interaction.user.id
            if not series:
                series_data = await collection_service.get_all_series_paginated(page, pool_type)
                from gacha.views.collection.collection_view.gacha_services import GachaServices
                services = GachaServices.from_bot(self.bot)
                view = await SeriesListView.create_view(user=interaction.user, series_data=series_data, services=services, pool_type=pool_type)
                message = await interaction.followup.send(embed=view.build_embed(), view=view)
                view.message = message
                return
            all_series = await collection_service.get_all_series(pool_type)
            if series not in all_series:
                embed = discord.Embed(title='查找系列', description=f'未找到名為「{series}」的系列，使用`/mws`命令查看所有可用系列', color=discord.Color.red())
                await interaction.followup.send(embed=embed)
                return
            # 使用 get_user_series_collections_batch 方法獲取系列收集情況
            series_collections = await collection_service.collection_repo.get_user_series_collections_batch(user_id, [series], pool_type)
            if not series_collections or series not in series_collections:
                embed = discord.Embed(title='查看系列收集情況', description=f'未找到「{series}」系列的收集記錄', color=discord.Color.red())
                await interaction.followup.send(embed=embed)
                return
            series_collection = series_collections[series]
            builder = SeriesEmbedBuilder(user=interaction.user, series_name=series, series_collection=series_collection, pool_type=pool_type)
            await interaction.followup.send(embed=builder.build_embed())
        except UserNotFoundError:
            embed = discord.Embed(
                title='帳戶錯誤', 
                description='找不到你的帳戶資訊，請稍後再試', 
                color=discord.Color.red()
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
        except Exception as e:
            logger.error('[COG][mws] Error in view_series: %s', e, exc_info=True)
            await handle_gacha_error(interaction, e, '查看系列收集情況時')

    @view_series.autocomplete('series')
    async def mws_series_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        collection_service: CollectionService = self.bot.collection_service
        return await common_series_autocomplete(interaction, current, collection_service)

    @view_series.autocomplete('pool_type')
    async def mws_pool_type_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        return await common_pool_type_autocomplete(interaction, current, show_individual_pools_only=False)

    @app_commands.command(name='aw', description='查看全圖鑑')
    @app_commands.describe(page='頁碼（默認1）', sort_by='排序方式（rarity: 稀有度, name: 名稱, creation_date: 創建日期）', sort_order='排序順序（desc: 降序, asc: 升序）', pool_type='卡池類型（main: 主卡池, special: 典藏卡池, summer: 泳裝卡池）', rarity='稀有度', series_name='系列名稱', card_id='按卡片ID查詢', card_name='按卡片名稱查詢（支援模糊搜尋）')
    @app_commands.choices(sort_by=[app_commands.Choice(name='稀有度', value='rarity'), app_commands.Choice(name='名稱', value='name'), app_commands.Choice(name='創建日期', value='creation_date'), app_commands.Choice(name='星級', value='star')], sort_order=[app_commands.Choice(name='降序', value='desc'), app_commands.Choice(name='升序', value='asc')])
    async def encyclopedia_command(self, interaction: discord.Interaction, page: int=1, sort_by: str='rarity', sort_order: str='desc', pool_type: Optional[str]=None, rarity: Optional[int]=None, series_name: Optional[str]=None, card_id: Optional[int]=None, card_name: Optional[str]=None):
        """全圖鑑指令"""
        try:
            await interaction.response.defer(thinking=True)
            if pool_type == 'all':
                pool_type = None
            rarity_internal_value: Optional[int] = None
            if rarity is not None:
                rarity_internal_value = rarity
            encyclopedia_service: EncyclopediaService = self.bot.encyclopedia_service
            target_page_for_view = page
            card_id_for_view_creation = card_id
            card_name_for_view_creation = card_name
            
            if card_id is not None:
                try:
                    found_page = await encyclopedia_service.find_card_page_info_by_id(
                        target_card_id=card_id, 
                        sort_by=sort_by, 
                        sort_order=sort_order, 
                        pool_type=pool_type, 
                        rarity=rarity_internal_value, 
                        series_name=series_name
                    )
                    target_page_for_view = found_page
                    card_id_for_view_creation = None # 找到了就清除card_id,避免重複篩選
                    card_name_for_view_creation = None
                except CardPageNotFoundException as e:
                    await interaction.followup.send(f'在目前的篩選條件下，圖鑑中找不到ID為 `{card_id}` 的卡片。 ({str(e)})', ephemeral=True)
                    return
                except DatabaseOperationError as e:
                    await interaction.followup.send(f'查詢卡片頁面時發生資料庫錯誤：{str(e)}', ephemeral=True)
                    return

            view = await EncyclopediaView.create(
                interaction=interaction, 
                page=target_page_for_view, 
                sort_by=sort_by, 
                sort_order=sort_order, 
                pool_type=pool_type, 
                rarity=rarity_internal_value, 
                series_name=series_name, 
                card_id=card_id_for_view_creation, 
                card_name=card_name_for_view_creation
            )
            
            if view is None:
                # EncyclopediaView.create 內部已處理錯誤並發送訊息
                return
                
            embed = await view.get_current_page_embed()
            await interaction.followup.send(embed=embed, view=view)
            
        except CardPageNotFoundException as e:
            logger.error('[COG][aw] 全圖鑑指令執行失敗 (CardPageNotFoundException): %s', str(e), exc_info=False) # 預期內的錯誤，無需完整堆疊
            await interaction_utils.safe_send_message(interaction, f"圖鑑查詢錯誤：{str(e)}", ephemeral=True)

        except DatabaseOperationError as e:
            logger.error('[COG][aw] 全圖鑑指令執行失敗 (DatabaseOperationError): %s', str(e), exc_info=True)
            await interaction_utils.safe_send_message(interaction, f"圖鑑資料庫操作失敗：{str(e)}", ephemeral=True)
            
        except Exception as e:
            logger.error('[COG][aw] 全圖鑑指令執行失敗: %s', str(e), exc_info=True)
            await interaction_utils.safe_send_message(interaction, '查詢圖鑑時發生未知錯誤，請稍後再試。', ephemeral=True)

    @encyclopedia_command.autocomplete('pool_type')
    async def aw_pool_type_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        return await common_pool_type_autocomplete(interaction, current, show_individual_pools_only=True)

    @encyclopedia_command.autocomplete('series_name')
    async def aw_series_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        try:
            encyclopedia_service: EncyclopediaService = self.bot.encyclopedia_service
            all_series = await encyclopedia_service.get_card_series_list()
            filtered_series = [s for s in all_series if current.lower() in s.lower()][:25]
            return [app_commands.Choice(name=s, value=s) for s in filtered_series]
        except Exception as e:
            logger.error('[COG][aw] 系列自動完成失敗: %s', str(e), exc_info=True)
            return []

    @encyclopedia_command.autocomplete('rarity')
    async def aw_rarity_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[int]]:
        return await common_rarity_autocomplete(interaction, current)

    @app_commands.command(name='favorite', description='標記/取消標記一張卡片為最愛，最愛的卡片不會被賣出')
    @app_commands.describe(card_id='要標記/取消標記的卡片ID（可在卡冊頁面底部找到）')
    async def toggle_favorite_command(self, interaction: discord.Interaction, card_id: int):
        """標記/取消標記一張卡片為最愛"""
        await interaction.response.defer(ephemeral=True)
        user_id = interaction.user.id
        favorite_service: FavoriteService = self.bot.favorite_service
        
        try:
            # 使用新的純異常模式
            is_favorite = await favorite_service.toggle_favorite_card(user_id, card_id)
            
            # 根據結果創建訊息
            status_text = '標記為最愛' if is_favorite else '取消最愛標記'
            await interaction.followup.send(f"已將卡片{status_text}！", ephemeral=True)
        except CardNotFoundError as e:
            await interaction.followup.send(f"操作失敗：{str(e)}", ephemeral=True)
        except FavoriteCardError as e:
            await interaction.followup.send(f"設置最愛狀態失敗：{str(e)}", ephemeral=True)
        except Exception as e:
            logger.error('標記最愛失敗: %s', e, exc_info=True)
            await interaction.followup.send(f"操作失敗：發生未知錯誤", ephemeral=True)

async def setup(bot: commands.Bot):
    """將 CollectionCog 添加到機器人中。"""
    await bot.add_cog(CollectionCog(bot))
    logger.info('CollectionCog 已成功註冊到機器人。')