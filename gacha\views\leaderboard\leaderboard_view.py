"""
Gacha系統排行榜視圖
提供排行榜展示的Discord嵌入消息生成方法
"""
from datetime import datetime
import time
from typing import Any, Dict, List, Optional
import asyncio
import discord
from utils.logger import logger
from gacha.app_config import config_service
from gacha.services.leaderboard.leaderboard_service import LeaderboardService
from gacha.views.collection.collection_view.base_pagination import BasePaginationView
from gacha.views.embeds.leaderboard.leaderboard_embed_builder import LeaderboardEmbedBuilder
from gacha.utils.interaction_utils import check_user_permission

class PlayerSearchModal(discord.ui.Modal, title='查詢玩家'):
    player_name_input = discord.ui.TextInput(label='玩家名稱', placeholder='輸入要查詢的玩家名稱', required=True, min_length=1, max_length=32)

    def __init__(self, view: 'LeaderboardView'):
        super().__init__()
        self.leaderboard_view = view

    async def on_submit(self, interaction: discord.Interaction):
        player_name_to_search = self.player_name_input.value
        try:
            await interaction.response.defer(ephemeral=True, thinking=True)
            player_data = await self.leaderboard_view.leaderboard_service.search_player_in_leaderboard(leaderboard_type=self.leaderboard_view.current_leaderboard_type, player_name=player_name_to_search, asset_symbol=self.leaderboard_view.current_asset_symbol)
            if player_data and 'rank' in player_data:
                player_rank = player_data['rank']
                items_per_page = self.leaderboard_view.leaderboard_service.items_per_page
                target_page = (player_rank - 1) // items_per_page + 1 if items_per_page > 0 else 1
                await interaction.followup.send(f'已找到玩家「{player_name_to_search}」，排名第 {player_rank} 位，在第 {target_page} 頁。排行榜將刷新至該頁面。', ephemeral=True)
                trigger_interaction = self.leaderboard_view.message.interaction_metadata if self.leaderboard_view.message and hasattr(self.leaderboard_view.message, 'interaction_metadata') else interaction
                if not self.leaderboard_view.message:
                    logger.warning('PlayerSearchModal: LeaderboardView.message is None. Cannot edit original message.')
                    return
                await self.leaderboard_view._reload_leaderboard_data(trigger_interaction, new_page=target_page, player_to_focus=player_name_to_search)
            else:
                await interaction.followup.send(f'未找到名稱包含「{player_name_to_search}」的玩家。', ephemeral=True)
        except Exception as e:
            logger.error('搜尋玩家時發生錯誤: %s', e, exc_info=True)
            if not interaction.response.is_done():
                try:
                    await interaction.response.send_message('搜尋玩家時發生錯誤，請稍後再試。', ephemeral=True)
                except discord.InteractionResponded:
                    await interaction.followup.send('搜尋玩家時發生錯誤，請稍後再試。', ephemeral=True)
            else:
                await interaction.followup.send('搜尋玩家時發生錯誤，請稍後再試。', ephemeral=True)

class LeaderboardView(BasePaginationView):

    def __init__(self, user: discord.User, initial_category: str, initial_leaderboard_type: str, initial_asset_symbol: Optional[str], leaderboard_data: Dict[str, Any], bot: discord.Client):
        self.bot = bot
        try:
            self.leaderboard_service: LeaderboardService = self.bot.leaderboard_service
        except AttributeError:
            logger.error("LeaderboardService not found on bot instance. Ensure it's initialized and attached to the bot.")
            raise RuntimeError('LeaderboardService not initialized on bot.')
        self.current_category = initial_category
        self.current_leaderboard_type = initial_leaderboard_type
        self.current_asset_symbol = initial_asset_symbol
        self.leaderboard_data = leaderboard_data or {}
        current_page = self.leaderboard_data.get('current_page', 1)
        total_pages = self.leaderboard_data.get('total_pages', 1)
        logger.debug('[VIEW INIT] User: %s, Category: %s, Type: %s, Asset: %s, Page: %s', user.id, initial_category, initial_leaderboard_type, initial_asset_symbol, current_page)
        super().__init__(user=user, current_page=current_page, total_pages=total_pages, timeout=300)
        logger.debug('[VIEW INIT] After super().__init__ - Children count: %s', len(self.children))
        for i, child in enumerate(self.children):
            logger.debug('[VIEW INIT] Child %s (after super): type=%s, row=%s, custom_id=%s', i, type(child).__name__, getattr(child, 'row', 'N/A'), getattr(child, 'custom_id', 'N/A'))
        self._log_row_widths('After super().__init__')
        self._adjust_pagination_button_row(2)
        self._log_row_widths('After _adjust_pagination_button_row')
        logger.debug('[VIEW INIT] Before adding custom components. Current children count: %s', len(self.children))
        self._log_row_widths('Before custom components')
        self._add_type_select_menu()
        self._log_row_widths('After _add_type_select_menu')
        self._add_asset_select_menu()
        self._log_row_widths('After _add_asset_select_menu')
        self._add_search_button(row=3)
        self._log_row_widths('After _add_search_button')
        if self.current_leaderboard_type == 'stock_holding':
            asyncio.create_task(self._update_asset_select_options(pre_select_symbol=self.current_asset_symbol))
        else:
            self.asset_select_menu.disabled = True
            self.asset_select_menu.options = [discord.SelectOption(label='無需選擇', value='_no_asset_needed_', default=True)]
            self.asset_select_menu.placeholder = '無需選擇股票'
        logger.debug('[VIEW INIT] Finished __init__. Final children count: %s', len(self.children))
        self._log_row_widths('End of __init__')

    def _log_row_widths(self, stage: str):
        if hasattr(self, '_weights'):
            try:
                row_details = []
                for i, width in enumerate(self._weights.weights):
                    if width > 0:
                        row_details.append(f'Row {i}: width={width}')
                logger.debug('[VIEW INIT STAGE] %s - Row Widths: [%s]', stage, ', '.join(row_details))
                children_rows_info = []
                for item_idx, item_child in enumerate(self.children):
                    child_info = f'Child {item_idx}: (Type: {type(item_child).__name__}, '
                    child_info += f"Row: {getattr(item_child, 'row', 'N/A')}, "
                    child_info += f"Width: {getattr(item_child, 'width', 'N/A')}, "
                    child_info += f"ID: {getattr(item_child, 'custom_id', 'N/A')})"
                    children_rows_info.append(child_info)
                logger.debug('[VIEW INIT STAGE] %s - Children details: %s', stage, children_rows_info)
            except Exception as e:
                logger.error('Error in _log_row_widths at stage %s: %s', stage, e, exc_info=True)
        else:
            logger.debug('[VIEW INIT STAGE] %s - ViewWeights (_weights) not found on self. Children count: %s', stage, len(self.children))

    def _adjust_pagination_button_row(self, new_row: int):
        items_to_move_and_re_add = []
        logger.debug('[_adjust_pagination_button_row] Attempting to move pagination items to row %s.', new_row)
        current_children_info = [(type(c).__name__, getattr(c, 'custom_id', 'N/A'), getattr(c, 'row', 'N/A')) for c in self.children]
        logger.debug('[_adjust_pagination_button_row] Current children before processing: %s', current_children_info)
        known_pagination_ids = ['first_page', 'prev_page', 'jump', 'next_page', 'last_page', 'stop_view']
        temp_removed_buttons = {}
        for item in list(self.children):
            if isinstance(item, discord.ui.Button) and hasattr(item, 'custom_id') and (item.custom_id in known_pagination_ids):
                logger.debug("[_adjust_pagination_button_row] Identified button '%s' (current row: %s) for moving.", item.custom_id, item.row)
                self.remove_item(item)
                item.row = new_row
                temp_removed_buttons[item.custom_id] = item
        logger.debug('[_adjust_pagination_button_row] Children after removal pass: %s', [(type(c).__name__, getattr(c, 'custom_id', 'N/A')) for c in self.children])
        self._log_row_widths('Inside _adjust_pagination_button_row - After removals')
        for custom_id in known_pagination_ids:
            button_to_re_add = temp_removed_buttons.get(custom_id)
            if button_to_re_add:
                logger.debug('[_adjust_pagination_button_row] Re-adding button: %s (custom_id: %s) to row %s', type(button_to_re_add).__name__, button_to_re_add.custom_id, button_to_re_add.row)
                self.add_item(button_to_re_add)
        logger.debug('[_adjust_pagination_button_row] Finished re-adding buttons.')
        self._log_row_widths('Inside _adjust_pagination_button_row - After re-adding')

    def _get_leaderboard_types_for_category(self, category: str) -> List[discord.SelectOption]:
        options = []
        for type_key, config_data in config_service.get_leaderboard_config().items():
            if config_data.category == category:
                label = config_data.title
                replacements = ['排行榜', '🏆', '📈', '📊', '💰', '⚖️']
                for rep in replacements:
                    label = label.replace(rep, '')
                label = label.strip()
                if not label:
                    label = type_key
                options.append(discord.SelectOption(label=label, value=type_key, description=config_data.description[:100]))
        return options

    def _add_type_select_menu(self):
        options = self._get_leaderboard_types_for_category(self.current_category)
        category_display_name = self.current_category.capitalize()
        self.type_select_menu = discord.ui.Select(placeholder=f'切換 {category_display_name} 分類排行...', options=options if options else [discord.SelectOption(label='無可用類型', value='_no_types_', default=True)], custom_id='leaderboard_type_select', row=0)
        if not options:
            self.type_select_menu.disabled = True
        is_current_type_in_options = False
        for option_idx, option in enumerate(self.type_select_menu.options):
            if option.value == self.current_leaderboard_type:
                option.default = True
                is_current_type_in_options = True
            else:
                option.default = False
        if not is_current_type_in_options and self.type_select_menu.options and (self.type_select_menu.options[0].value != '_no_types_'):
            self.type_select_menu.options[0].default = True
        self.type_select_menu.callback = self.type_select_callback
        logger.debug('[VIEW INIT] Attempting to add type_select_menu to row 0. Options count: %s. Current children on row 0 before add: %s', len(self.type_select_menu.options), [(type(i).__name__, getattr(i, 'custom_id', 'N/A')) for i in self.children if hasattr(i, 'row') and i.row == 0])
        self._log_row_widths('Just before adding type_select_menu')
        self.add_item(self.type_select_menu)
        logger.debug('[VIEW INIT] Successfully added type_select_menu.')

    async def _update_asset_select_options(self, interaction: Optional[discord.Interaction]=None, pre_select_symbol: Optional[str]=None):
        assets = await self.leaderboard_service.get_all_market_assets()
        current_selected_asset = self.current_asset_symbol
        if not assets:
            self.asset_select_menu.disabled = True
            self.asset_select_menu.placeholder = '市場上暫無股票'
            self.asset_select_menu.options = [discord.SelectOption(label='無可用股票', value='_NO_ASSETS_', default=True)]
        else:
            self.asset_select_menu.disabled = False
            self.asset_select_menu.placeholder = '選擇要查詢的股票...'
            select_options = []
            found_pre_select_in_new_options = False
            for asset in assets[:25]:
                is_default = pre_select_symbol == asset['symbol'] or (not pre_select_symbol and current_selected_asset == asset['symbol'])
                if is_default:
                    found_pre_select_in_new_options = True
                select_options.append(discord.SelectOption(label=f"{asset['name']} ({asset['symbol']})", value=asset['symbol'], default=is_default))
            if not select_options:
                self.asset_select_menu.options = [discord.SelectOption(label='無可用股票', value='_NO_ASSETS_', default=True)]
            else:
                if not found_pre_select_in_new_options and (not pre_select_symbol) and (not current_selected_asset) and select_options:
                    select_options[0].default = True
                self.asset_select_menu.options = select_options
        if interaction and (not interaction.is_expired()):
            try:
                await interaction.edit_original_response(view=self)
            except discord.InteractionResponded:
                if interaction.message:
                    await interaction.edit_original_response(view=self)
            except discord.NotFound:
                logger.warning('嘗試更新 asset_select_options 時消息未找到')
            except Exception as e:
                logger.error('更新 asset_select_options 時發生未知錯誤: %s', e, exc_info=True)

    def _add_asset_select_menu(self):
        initial_options = [discord.SelectOption(label='---', value='_initial_asset_placeholder_', default=True)]
        self.asset_select_menu = discord.ui.Select(placeholder='選擇股票...', options=initial_options, custom_id='leaderboard_asset_select', row=1, disabled=True)
        self.asset_select_menu.callback = self.asset_select_callback
        self.add_item(self.asset_select_menu)

    async def type_select_callback(self, interaction: discord.Interaction):
        if not await check_user_permission(interaction, self.user_id):
            return
        await interaction.response.defer()
        selected_type = interaction.data['values'][0]
        for option in self.type_select_menu.options:
            option.default = option.value == selected_type
        self.current_leaderboard_type = selected_type
        self.current_page = 1
        if selected_type == 'stock_holding':
            self.asset_select_menu.disabled = False
            await self._update_asset_select_options(interaction, pre_select_symbol=self.current_asset_symbol)
            if self.current_asset_symbol and self.asset_select_menu.options and (self.asset_select_menu.options[0].value not in ['_NO_ASSETS_', '_initial_asset_placeholder_']):
                await self._reload_leaderboard_data(interaction, new_page=1)
        else:
            self.asset_select_menu.disabled = True
            self.asset_select_menu.options = [discord.SelectOption(label='無需選擇', value='_no_asset_needed_', default=True)]
            self.asset_select_menu.placeholder = '無需選擇股票'
            self.current_asset_symbol = None
            await self._reload_leaderboard_data(interaction, new_page=1)

    async def asset_select_callback(self, interaction: discord.Interaction):
        if not await check_user_permission(interaction, self.user_id):
            return
        await interaction.response.defer()
        self.current_asset_symbol = interaction.data['values'][0]
        for option in self.asset_select_menu.options:
            option.default = option.value == self.current_asset_symbol
        for option in self.type_select_menu.options:
            option.default = option.value == self.current_leaderboard_type
        self.current_page = 1
        await self._reload_leaderboard_data(interaction, new_page=1)

    async def _reload_leaderboard_data(self, interaction: Optional[discord.Interaction], new_page: int, player_to_focus: Optional[str]=None):
        target_page_for_service = new_page
        asset_symbol_for_service = self.current_asset_symbol
        leaderboard_type_for_service = self.current_leaderboard_type
        start_time_for_query = time.time()
        if player_to_focus:
            player_data = await self.leaderboard_service.search_player_in_leaderboard(leaderboard_type=leaderboard_type_for_service, player_name=player_to_focus, asset_symbol=asset_symbol_for_service)
            if player_data and 'rank' in player_data:
                player_rank = player_data['rank']
                items_per_page = self.leaderboard_service.items_per_page
                target_page_for_service = (player_rank - 1) // items_per_page + 1 if items_per_page > 0 else 1
                logger.info("Player '%s' found at rank %s, page %s", player_to_focus, player_rank, target_page_for_service)
            else:
                logger.info("Player '%s' not found for leaderboard type %s.", player_to_focus, leaderboard_type_for_service)
                pass
        results_list, total_pages_from_service, total_users_from_service = await self.leaderboard_service.get_leaderboard(leaderboard_type=leaderboard_type_for_service, page=target_page_for_service, asset_symbol=asset_symbol_for_service)
        query_time_taken = time.time() - start_time_for_query
        if results_list is not None:
            self.leaderboard_data = {'results': results_list, 'current_page': target_page_for_service, 'total_pages': total_pages_from_service, 'total_users': total_users_from_service, 'leaderboard_type': leaderboard_type_for_service, 'asset_symbol': asset_symbol_for_service, 'query_time': query_time_taken, 'updated_at': datetime.now()}
            self.current_page = self.leaderboard_data.get('current_page')
            self.total_pages = self.leaderboard_data.get('total_pages')
            for option in self.type_select_menu.options:
                option.default = option.value == self.current_leaderboard_type
            if self.current_leaderboard_type == 'stock_holding':
                await self._update_asset_select_options(None, pre_select_symbol=self.current_asset_symbol)
            else:
                self.asset_select_menu.disabled = True
                self.asset_select_menu.options = [discord.SelectOption(label='無需選擇', value='_no_asset_needed_', default=True)]
                self.asset_select_menu.placeholder = '無需選擇股票'
            self.update_buttons_state()
            embed = self.get_current_page_embed()
            try:
                if interaction and hasattr(interaction, 'edit_original_response'):
                    await interaction.edit_original_response(embed=embed, view=self)
                elif self.message:
                    await self.message.edit(embed=embed, view=self)
                else:
                    logger.error('Could not find a message or interaction to edit in _reload_leaderboard_data')
            except discord.NotFound:
                logger.warning('嘗試在 _reload_leaderboard_data 中編輯消息失敗：消息未找到')
            except discord.HTTPException as e:
                logger.error('編輯消息時發生 HTTP 錯誤 (可能是 view 內容問題): %s', e, exc_info=True)
                if interaction and (not interaction.response.is_done()):
                    await interaction.followup.send('更新排行榜時發生錯誤。', ephemeral=True)
        else:
            error_content = '獲取數據失敗'
            try:
                if interaction:
                    await interaction.edit_original_response(content=error_content, embed=None, view=None)
                elif self.message:
                    await self.message.edit(content=error_content, embed=None, view=None)
            except discord.NotFound:
                logger.warning('嘗試在 _reload_leaderboard_data 中編輯失敗消息失敗：消息未找到')

    def update_buttons_state(self):
        """Updates the state of the pagination buttons based on current_page and total_pages."""
        first_page_button = discord.utils.get(self.children, custom_id='first_page')
        prev_page_button = discord.utils.get(self.children, custom_id='prev_page')
        jump_button = discord.utils.get(self.children, custom_id='jump')
        next_page_button = discord.utils.get(self.children, custom_id='next_page')
        last_page_button = discord.utils.get(self.children, custom_id='last_page')
        if first_page_button:
            first_page_button.disabled = self.current_page == 1
        if prev_page_button:
            prev_page_button.disabled = self.current_page == 1
        if jump_button:
            jump_button.label = f'{self.current_page}/{(self.total_pages if self.total_pages > 0 else 1)}'
        if next_page_button:
            next_page_button.disabled = self.current_page == self.total_pages or self.total_pages == 0
        if last_page_button:
            last_page_button.disabled = self.current_page == self.total_pages or self.total_pages == 0

    def _add_search_button(self, row: int=1):
        logger.debug('[_add_search_button] Adding search button to row %s.', row)
        self._log_row_widths(f'Before adding search_button to row {row}')
        search_button = discord.ui.Button(label='查詢玩家', style=discord.ButtonStyle.success, emoji='🔍', custom_id='search_player', row=row)
        search_button.callback = self.search_player_callback
        self.add_item(search_button)
        logger.debug('[_add_search_button] Successfully added search_button to row %s.', row)
        self._log_row_widths(f'After adding search_button to row {row}')

    async def search_player_callback(self, interaction: discord.Interaction):
        if not await check_user_permission(interaction, self.user_id, '只有原始查詢者可以使用此按鈕'):
            return
        modal = PlayerSearchModal(self)
        await interaction.response.send_modal(modal)

    def build_embed(self) -> discord.Embed:
        items_per_page = self.leaderboard_data.get('items_per_page', self.leaderboard_service.items_per_page if hasattr(self.leaderboard_service, 'items_per_page') else 5)
        builder = LeaderboardEmbedBuilder(self.leaderboard_data, self.current_leaderboard_type, items_per_page, self.current_asset_symbol)
        return builder.build_embed()

    def get_current_page_embed(self) -> discord.Embed:
        return self.build_embed()

    async def _update_page(self, page: int, interaction: discord.Interaction):
        await self._reload_leaderboard_data(interaction, new_page=page)