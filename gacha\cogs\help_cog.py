"""
Gacha系統幫助命令 COG
實現/help指令，為用戶提供完整的gacha系統功能教學
使用兩層下拉式選單系統：主分類 → 子分類
"""

import discord
from discord import app_commands
from discord.ext import commands
from typing import Optional, Dict, List
from utils.logger import logger


class HelpCog(commands.Cog):
    """處理 /help 指令的 Cog，使用兩層下拉式選單系統"""

    # 主分類和對應的子分類
    HELP_CATEGORIES = {
        "gacha_system": {
            "name": "🎯 抽卡系統",
            "description": "抽卡、收藏、圖鑑、許願相關功能",
            "subcategories": {
                "draw": {"name": "抽卡", "description": "單抽、十連抽功能"},
                "collection": {"name": "收藏", "description": "查看卡冊、稀有度統計"},
                "profile": {"name": "檔案", "description": "個人檔案展示和設定"},
                "encyclopedia": {"name": "圖鑑", "description": "查看全圖鑑"},
                "wish": {"name": "許願", "description": "卡片許願系統"},
            },
        },
        "economy_system": {
            "name": "💰 經濟系統",
            "description": "交易、股票、獎勵、商店相關功能",
            "subcategories": {
                "trade": {"name": "交易", "description": "賣卡和玩家交易"},
                "stock_market": {"name": "股票", "description": "股票市場投資"},
                "daily_hourly": {
                    "name": "每日/時獎勵",
                    "description": "每日和每小時獎勵",
                },
                "shop": {"name": "商店", "description": "油票商店"},
            },
        },
        "mini_games": {
            "name": "🎮 小遊戲",
            "description": "挖礦、21點、骰子等遊戲",
            "subcategories": {
                "mines": {"name": "挖礦", "description": "尋寶礦區遊戲"},
                "blackjack": {"name": "21點", "description": "21點遊戲"},
                "dice": {"name": "骰子", "description": "骰子遊戲"},
            },
        },
        "entertainment": {
            "name": "🎭 娛樂指令",
            "description": "AI問答與穿搭評分、GIF圖製作",
            "subcategories": {
                "ai_qa": {"name": "AI問答", "description": "智能問答助手"},
                "outfit_rating": {"name": "穿搭評分", "description": "AI穿搭評分系統"},
                "gif_creation": {"name": "GIF製作", "description": "頭像GIF圖製作"},
            },
        },
    }

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        logger.info("HelpCog loaded successfully.")

    def _create_main_help_embed(self, user: discord.User) -> discord.Embed:
        """創建主幫助頁面 embed"""
        embed = discord.Embed(
            title="",
            description="歡迎來到Gacha系統！請使用下方的選單來探索各種功能。\n",
            color=discord.Color.blue(),
        )
        embed.set_author(name="Gacha系統功能說明", icon_url=user.display_avatar.url)
        embed.set_thumbnail(
            url="https://cdn.discordapp.com/attachments/1336020673730187334/1375186010798948463/notification.webp?ex=6830c546&is=682f73c6&hm=bb708ce4a788afebba8c1d8451720090b578497608c673b5f6106d615be686c1&"
        )

        # 改善排版：使用更好的視覺分隔和格式
        categories_list = list(self.HELP_CATEGORIES.items())

        # 第一行：抽卡系統和經濟系統
        if len(categories_list) >= 2:
            embed.add_field(
                name=categories_list[0][1]["name"],
                value=f"📋 {categories_list[0][1]['description']}\n\u200b",
                inline=True,
            )
            embed.add_field(
                name=categories_list[1][1]["name"],
                value=f"📋 {categories_list[1][1]['description']}\n\u200b",
                inline=True,
            )
            # 空欄位強制換行
            embed.add_field(name="\u200b", value="\u200b", inline=True)

        # 第二行：小遊戲和娛樂指令
        if len(categories_list) >= 4:
            embed.add_field(
                name=categories_list[2][1]["name"],
                value=f"📋 {categories_list[2][1]['description']}\n\u200b",
                inline=True,
            )
            embed.add_field(
                name=categories_list[3][1]["name"],
                value=f"📋 {categories_list[3][1]['description']}\n\u200b",
                inline=True,
            )
            # 空欄位強制換行
            embed.add_field(name="\u200b", value="\u200b", inline=True)

        # 如果還有更多分類，繼續添加
        for i in range(4, len(categories_list)):
            category_data = categories_list[i][1]
            embed.add_field(
                name=category_data["name"],
                value=f"📋 {category_data['description']}\n\u200b",
                inline=True,
            )

        # 添加使用說明
        embed.add_field(
            name="📖 使用說明",
            value=(
                "• 選擇**主分類**查看該分類下的所有功能\n"
                "• 選擇**子分類**查看具體功能的詳細說明\n"
                "• 隨時可以切換不同的分類和子分類"
            ),
            inline=False,
        )

        embed.set_footer(text="💡 使用下方的主分類選單開始探索功能")
        return embed

    def _create_category_help_embed(
        self,
        user: discord.User,
        category_key: str,
        subcategory_key: Optional[str] = None,
    ) -> discord.Embed:
        """創建特定分類或子分類的幫助 embed"""
        category_data = self.HELP_CATEGORIES[category_key]

        embed = discord.Embed(color=discord.Color.blue())
        embed.set_author(name="Gacha系統功能說明", icon_url=user.display_avatar.url)
        embed.set_thumbnail(
            url="https://cdn.dev.conquest.bot/thumbnails/transfer.png")

        if subcategory_key:
            # 顯示特定子分類的詳細說明
            subcategory_data = category_data["subcategories"][subcategory_key]
            embed.title = f"{category_data['name']} - {subcategory_data['name']}"

            # 根據子分類顯示具體內容
            content = self._get_subcategory_content(subcategory_key)
            embed.add_field(
                name=content["title"], value=content["content"], inline=False
            )
        else:
            # 顯示該主分類下的所有子分類
            embed.title = category_data["name"]
            embed.description = (
                f"{category_data['description']}\n\n請選擇子分類來查看詳細說明："
            )

            for sub_key, sub_data in category_data["subcategories"].items():
                embed.add_field(
                    name=f"📋 {sub_data['name']}",
                    value=sub_data["description"],
                    inline=True,
                )

        embed.set_footer(text="使用下方的選單切換分類或子分類")
        return embed

    def _get_subcategory_content(self, subcategory_key: str) -> Dict[str, str]:
        """獲取子分類的詳細內容"""
        content_map = {
            "draw": {
                "title": "🎯 抽卡系統 - `/w`",
                "content": (
                    "**基本功能：**\n"
                    "`/w` - 單抽卡片 (50油)\n"
                    "`/w multi:True` - 十連抽 (500油)\n\n"
                    "**卡池類型：**\n"
                    "- 混合池 - 50油\n"
                    "- 典藏池 - 140油\n"
                    "- 泳裝池 - 130油\n"
                    "- 情人節卡池 - 150油\n"
                    "- 典藏女僕卡池 - 160油\n"
                    "- Hololive卡池 - 150油\n"
                    "- UNION ARENA卡池 - 120油\n\n"
                    "稀有度: C, R, SR, SSR, UR, LR, EX"
                ),
            },
            "collection": {
                "title": "📚 收藏系統 - `/mw`、`/mwr` 和 `/mws`",
                "content": (
                    "**查看指令：**\n"
                    "`/mw` - 查看卡冊\n"
                    "`/mwr` - 查看稀有度統計\n"
                    "`/mws` - 查看系列收集\n\n"
                    "**主要篩選：**\n"
                    "- 頁碼、排序、稀有度\n"
                    "- 卡池、系列、ID、名稱\n"
                    "- 顯示重複卡片"
                ),
            },
            "profile": {
                "title": "🎴 檔案系統 - `/profile`",
                "content": (
                    "**檔案指令：**\n"
                    "`/profile` - 查看自己的檔案。\n"
                    "`/profile user:@某人` - 查看其他人的檔案。\n"
                    "`/profile edit:True` - 開啟自己的檔案設定介面 (用於設定展示卡片、個性簽名等)。\n"
                    "`/profile background_image:<圖片附件>` - 設定自己的檔案背景圖片。\n"
                    "    **注意：** `edit` 和 `background_image` 選項不能同時對他人使用。\n\n"
                    "**檔案功能：**\n"
                    "• **個人資訊展示** - 顯示油量、油票、總抽卡數、圖鑑完成度等統計\n"
                    "• **主展示卡片** - 設定一張主要展示的卡片 (在 `edit:True` 介面中設定)\n"
                    "• **副展示卡片** - 設定最多4張副展示卡片 (在 `edit:True` 介面中設定)\n"
                    "• **自訂背景** - 上傳個人化背景圖片 (透過 `background_image` 選項或在 `edit:True` 介面中重置)\n"
                    "• **個性簽名** - 設定您的個性簽名 (在 `edit:True` 介面中設定)\n"
                    "• **按讚互動** - 為其他玩家的檔案按讚 (1小時冷卻)\n\n"
                    "**設定說明：**\n"
                    "- 設定展示卡片、個性簽名等請使用 `/profile edit:True` 開啟設定介面。\n"
                    "- 卡片需要在您的收藏中才能設定。\n"
                    "- 背景圖片建議使用 1536x864 比例，大小限制 8MB。\n"
                    "- 檔案圖片會自動快取，變更設定後會自動重新生成。"
                ),
            },
            "encyclopedia": {
                "title": "📖 圖鑑系統 - `/aw`",
                "content": (
                    "**圖鑑指令：**\n"
                    "`/aw` - 查看全圖鑑\n\n"
                    "**篩選項目：**\n"
                    "- 頁碼、排序、稀有度\n"
                    "- 卡池、系列、ID、名稱"
                ),
            },
            "wish": {
                "title": "✨ 許願系統 - `/wish`",
                "content": (
                    "**許願指令：**\n"
                    "`/wish` - 開啟許願系統界面\n\n"
                    "**許願功能：**\n"
                    "• **添加許願** - 點擊按鈕輸入卡片ID來添加許願\n"
                    "• **移除許願** - 點擊按鈕輸入卡片ID來移除許願\n"
                    "• **擴充槽位** - 花費油幣增加許願槽位數量\n"
                    "• **提升力度** - 花費油幣提升許願權重倍數\n\n"
                    "**許願機制：**\n"
                    "• 許願卡片在抽卡時有更高機率被抽中\n"
                    "• 力度等級越高，權重倍數越大\n"
                    "• 抽中許願卡時會有特殊標記和通知\n"
                    "• 默認1個槽位，最多可擴充到10個槽位\n"
                    "• 默認1級力度，最多可提升到10級力度"
                ),
            },
            "trade": {
                "title": "💱 交易系統",
                "content": (
                    "**交易指令：**\n"
                    "`/sw` - 賣出卡片換油幣\n"
                    "`/favorite` - 設為最愛(防誤賣)\n"
                    "`/trade` - 與其他玩家交換卡片\n\n"
                    "**賣卡模式：**\n"
                    "- 指定ID、名稱、系列、稀有度\n"
                    "- 賣1張、賣到剩1張、全部賣出\n\n"
                    "**常用範例：**\n"
                    "`/sw operation=all` - 賣全部非最愛\n"
                    "`/sw operation=leave_one` - 只保留1張\n\n"
                    "**玩家交易功能：**\n"
                    "`/trade user:玩家 offer_card_id:卡片ID` - 與玩家交換特定卡片\n"
                    "`/trade user:玩家 offer_card_id:卡片ID request_card_id:對方卡片ID` - 指定想要的卡片\n"
                    "`/trade user:玩家 offer_card_id:卡片ID price:金額` - 售賣卡片給特定玩家"
                ),
            },
            "stock_market": {
                "title": "📈 股票市場系統 - `/stock`、`/portfolio` 和 `/news`",
                "content": (
                    "**股市指令：**\n"
                    "`/stock` - 查看所有可交易的股票列表\n"
                    "`/stock [股票代碼]` - 查看特定股票詳情、價格走勢圖和交易選項\n"
                    "`/portfolio` - 查看您的投資組合和持倉盈虧\n"
                    "`/news` - 查看最新的市場新聞和事件\n"
                    "`/cardinfo [卡片ID]` - 查看卡片最新市場價格\n\n"
                    "**股市特色：**\n"
                    "- 股票價格會根據市場條件自動波動\n"
                    "- 卡片價格受供需關係和股市波動雙重影響\n"
                    "- 股市新聞會直接影響相關股票價格和卡片價值\n\n"
                    "**購買與賣出：**\n"
                    "- 在股票詳情頁面點擊「買入」或「賣出」按鈕\n"
                    "- 低買高賣，賺取價差收益\n"
                    "- 分散投資不同類型股票降低風險"
                ),
            },
            "daily_hourly": {
                "title": "💰 每日/時獎勵系統",
                "content": (
                    "**獎勵指令：**\n"
                    "`/balance` - 查看油幣餘額\n"
                    "`/daily` - 領取每日獎勵\n"
                    "`/hourly` - 領取每小時獎勵\n\n"
                    "**使用說明：**\n"
                    "- 每日簽到可獲得豐富油幣獎勵\n"
                    "- 每小時都可以領取少量油幣\n"
                    "- 油幣用途：抽卡、小遊戲、交易"
                ),
            },
            "shop": {
                "title": "🛍️ 油票商店系統 - `/shop`",
                "content": (
                    "**商店指令：**\n"
                    "`/shop` - 開啟油票商店\n\n"
                    "**商店功能：**\n"
                    "- 使用油票兌換各種卡片兌換券\n"
                    "- 特殊物品和裝備（未來擴展）\n"
                    "- 限時優惠商品\n\n"
                    "**使用說明：**\n"
                    "在商店中選擇商品分類，然後選擇想要的物品進行兌換"
                ),
            },
            "mines": {
                "title": "⛏️ 尋寶礦區遊戲 - `/mines`",
                "content": (
                    "**遊戲指令：**\n"
                    "`/mines` - 開始尋寶礦區遊戲，會顯示難度選擇界面\n"
                    "`/mines mine_count:[難度] bet:[油幣]` - 直接以指定難度和下注金額開始遊戲\n\n"
                    "**遊戲難度：**\n"
                    "- 🟢 簡單：3顆地雷\n"
                    "- 🟡 中等：7顆地雷\n"
                    "- 🔴 困難：12顆地雷\n\n"
                    "**遊戲規則：**\n"
                    "- 選擇一塊土地挖掘，避開地雷\n"
                    "- 每找到一塊安全土地，獎勵倍數增加\n"
                    "- 可隨時提現獲取當前獎勵\n"
                    "- 如果踩到地雷，遊戲結束且損失下注金額\n"
                    "- 有機會挖到金幣或星星獲得額外獎勵"
                ),
            },
            "blackjack": {
                "title": "🃏 21點遊戲 - `/blackjack`",
                "content": (
                    "**遊戲指令：**\n"
                    "`/blackjack` - 開始21點遊戲\n\n"
                    "**遊戲規則：**\n"
                    "- 目標是讓手牌點數接近21點但不超過\n"
                    "- A可算作1點或11點\n"
                    "- J、Q、K都算作10點\n"
                    "- 可選擇要牌或停牌\n"
                    "- 贏取油幣獎勵"
                ),
            },
            "dice": {
                "title": "🎲 骰子遊戲 - `/dice`",
                "content": (
                    "**遊戲指令：**\n"
                    "`/dice` - 開始骰子遊戲\n\n"
                    "**遊戲規則：**\n"
                    "- 投擲骰子獲得點數\n"
                    "- 根據點數獲得不同的油幣獎勵\n"
                    "- 簡單有趣的運氣遊戲"
                ),
            },
            "ai_qa": {
                "title": "🤖 AI問答系統 - `/ask`",
                "content": (
                    "**問答指令：**\n"
                    "`/ask question:[問題]` - 向AI助手提問\n"
                    "`/ask image:[圖片] question:[問題]` - 上傳圖片並提問\n"
                    "`/ask use_modal:True` - 使用大型文本輸入框\n\n"
                    "**功能特色：**\n"
                    "• **智能回答** - 提供準確、實用的資訊和建議\n"
                    "• **圖像分析** - 可以分析上傳的圖片並回答相關問題\n"
                    "• **多種格式** - 支持代碼、表格、列表等多種回答格式\n"
                    "• **分類標籤** - 自動識別問題類型（技術、生活、教育等）\n\n"
                    "**使用技巧：**\n"
                    "- 問題描述越詳細，回答越準確\n"
                    "- 可以要求特定格式的回答（如步驟、列表等）\n"
                    "- 支持繁體中文和多種語言"
                ),
            },
            "outfit_rating": {
                "title": "👗 穿搭評分系統 - `/rate`",
                "content": (
                    "**評分指令：**\n"
                    "`/rate image:[圖片]` - 上傳穿搭照片進行AI評分\n\n"
                    "**評分系統：**\n"
                    "• **評分範圍** - 1-10分制，客觀評估穿搭效果\n"
                    "• **詳細評價** - 提供具體的穿搭分析和點評\n"
                    "• **改善建議** - 根據評分給出實用的穿搭建議\n"
                    "• **風格識別** - 自動識別穿搭風格和特色\n\n"
                    "**評分標準：**\n"
                    "- 色彩搭配和諧度\n"
                    "- 款式組合合理性\n"
                    "- 整體視覺效果\n"
                    "- 場合適宜性\n\n"
                    "**注意事項：**\n"
                    "- 請上傳清晰的全身或半身穿搭照片\n"
                    "- AI評分僅供參考，穿搭風格因人而異"
                ),
            },
            "gif_creation": {
                "title": "🎬 GIF圖製作系統 - `/gif`",
                "content": (
                    "**GIF製作指令：**\n"
                    "`/gif` - 查看可用的GIF模板\n"
                    "`/gif template:[模板名稱]` - 使用指定模板製作GIF\n\n"
                    "**可用模板：**\n"
                    "• **青蛙模板** - 可愛青蛙動畫，頭像會跟著搖擺\n"
                    "• **阿北模板** - 經典阿北動作，頭像會有震動效果\n"
                    "• **打拳模板** - 拳擊動畫，頭像會隨拳頭移動和縮放\n"
                    "• **踢垃圾桶** - 踢飛頭像進垃圾桶的搞笑動畫\n\n"
                    "**製作特色：**\n"
                    "- 自動將您的Discord頭像融入GIF動畫\n"
                    "- 支持透明背景和高質量輸出\n"
                    "- 每個模板都有獨特的動畫效果\n"
                    "- 可調整頭像大小和位置\n\n"
                    "**使用說明：**\n"
                    "1. 選擇喜歡的GIF模板\n"
                    "2. 系統會自動獲取您的頭像\n"
                    "3. 生成個人化的GIF動畫\n"
                    "4. 可以保存或分享給朋友"
                ),
            },
        }

        return content_map.get(
            subcategory_key, {"title": "未知分類", "content": "此分類暫無詳細說明。"}
        )

    @app_commands.command(
        name="help", description="查看Gacha系統的各項功能說明和使用教學"
    )
    async def help_command(self, interaction: discord.Interaction):
        """幫助命令處理（新版本，無參數）"""
        # 延遲響應，避免Discord超時
        await interaction.response.defer(thinking=True)

        # 創建主幫助 embed
        embed = self._create_main_help_embed(interaction.user)

        # 創建帶有下拉選單的視圖
        view = HelpView(self, interaction.user.id)

        # 發送幫助消息
        await interaction.followup.send(embed=embed, view=view)


class HelpView(discord.ui.View):
    """幫助系統的UI視圖，包含兩個下拉式選單"""

    def __init__(self, help_cog: HelpCog, user_id: int):
        super().__init__(timeout=300)  # 5分鐘超時
        self.help_cog = help_cog
        self.user_id = user_id
        self.current_category = None
        self.current_subcategory = None

        # 添加主分類選單
        self.main_category_select = MainCategorySelect(self)
        self.add_item(self.main_category_select)

        # 子分類選單（初始為空）
        self.subcategory_select = SubcategorySelect(self)
        self.add_item(self.subcategory_select)

    async def interaction_check(
            self, interaction: discord.Interaction) -> bool:
        """檢查互動權限"""
        if interaction.user.id != self.user_id:
            await interaction.response.send_message(
                "您無法操作此幫助選單！", ephemeral=True
            )
            return False
        return True


class MainCategorySelect(discord.ui.Select):
    """主分類選擇下拉選單"""

    def __init__(self, help_view: HelpView):
        self.help_view = help_view

        options = [
            discord.SelectOption(
                label=category_data["name"],
                value=category_key,
                description=category_data["description"],
            )
            for category_key, category_data in help_view.help_cog.HELP_CATEGORIES.items()
        ]

        super().__init__(
            placeholder="請選擇主分類...",
            min_values=1,
            max_values=1,
            options=options,
            custom_id="main_category_select",
        )

    async def callback(self, interaction: discord.Interaction):
        """主分類選擇回調"""
        selected_category = self.values[0]
        self.help_view.current_category = selected_category
        self.help_view.current_subcategory = None

        # 更新選單顯示的選擇
        for option in self.options:
            option.default = option.value == selected_category

        # 更新子分類選單
        self.help_view.subcategory_select.update_options(selected_category)

        # 創建新的 embed 顯示該分類的概覽
        embed = self.help_view.help_cog._create_category_help_embed(
            interaction.user, selected_category
        )

        await interaction.response.edit_message(embed=embed, view=self.help_view)


class SubcategorySelect(discord.ui.Select):
    """子分類選擇下拉選單"""

    def __init__(self, help_view: HelpView):
        self.help_view = help_view

        # 初始時沒有選項
        super().__init__(
            placeholder="請先選擇主分類...",
            min_values=1,
            max_values=1,
            options=[discord.SelectOption(label="無可用選項", value="none")],
            disabled=True,
            custom_id="subcategory_select",
        )

    def update_options(self, category_key: str):
        """更新子分類選項"""
        category_data = self.help_view.help_cog.HELP_CATEGORIES[category_key]

        self.options.clear()
        for sub_key, sub_data in category_data["subcategories"].items():
            self.options.append(
                discord.SelectOption(
                    label=sub_data["name"],
                    value=sub_key,
                    description=sub_data["description"],
                    default=False,  # 重置所有選項為未選中
                )
            )

        self.placeholder = "請選擇子分類..."
        self.disabled = False

    async def callback(self, interaction: discord.Interaction):
        """子分類選擇回調"""
        if not self.help_view.current_category:
            await interaction.response.send_message("請先選擇主分類！", ephemeral=True)
            return

        selected_subcategory = self.values[0]
        self.help_view.current_subcategory = selected_subcategory

        # 更新選單顯示的選擇
        for option in self.options:
            option.default = option.value == selected_subcategory

        # 創建新的 embed 顯示該子分類的詳細內容
        embed = self.help_view.help_cog._create_category_help_embed(
            interaction.user, self.help_view.current_category, selected_subcategory)

        await interaction.response.edit_message(embed=embed, view=self.help_view)


async def setup(bot: commands.Bot):
    """將 HelpCog 添加到 bot"""
    await bot.add_cog(HelpCog(bot))
    logger.info("HelpCog has been added to the bot.")
