import discord
from discord.ext import commands
from discord import app_commands, Interaction
from typing import Optional, List, Dict, Any, Tuple, Callable
import time
import asyncio
from datetime import datetime
import functools
from gacha.services.leaderboard.leaderboard_service import LeaderboardService
from gacha.views.leaderboard.leaderboard_view import LeaderboardView
from utils.logger import logger
from gacha.app_config import config_service
ongoing_operations = {}

def prevent_duplicate_operation(operation_key_fn):
    """防重複操作裝飾器

    參數:
        operation_key_fn: 生成操作鍵的函數
    """

    def decorator(func):

        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            operation_key = operation_key_fn(*args, **kwargs)
            if operation_key in ongoing_operations:
                interaction = args[0] if len(args) > 0 else None
                if isinstance(interaction, discord.Interaction):
                    try:
                        await interaction.response.defer(ephemeral=True)
                    except BaseException:
                        pass
                logger.debug('[GACHA] 用戶重複操作: %s，忽略', operation_key)
                return
            ongoing_operations[operation_key] = True
            try:
                return await func(*args, **kwargs)
            finally:
                if operation_key in ongoing_operations:
                    del ongoing_operations[operation_key]
        return wrapper
    return decorator

def create_loading_embed(leaderboard_type: str, asset_symbol: Optional[str]=None) -> discord.Embed:
    """創建載入中的嵌入消息

    參數:
        leaderboard_type: 排行榜類型
        asset_symbol: 股票代碼 (可選)

    返回:
        discord.Embed: 載入中的嵌入消息
    """
    config_setting = config_service.get_config('gacha_core_settings.leaderboard_config').get(leaderboard_type)
    if not config_setting:
        title = f'{leaderboard_type} 排行榜'
        description = '正在載入排行榜數據...'
        color_val = 7506394
    else:
        title = config_setting.title
        description = config_setting.description
        color_val = config_setting.color
    if leaderboard_type == 'stock_holding' and asset_symbol:
        # 這裡可以添加特定股票排行的標題修改邏輯，如果需要的話
        pass
    embed = discord.Embed(title=title, description=f'{description}\n\n⏳ **載入中，請稍候...**', color=discord.Color(color_val))
    return embed

class LeaderboardCog(commands.Cog):

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        try:
            self.leaderboard_service: LeaderboardService = bot.leaderboard_service
        except AttributeError:
            logger.error('LeaderboardService 未在 bot 實例上找到。請確保它已被初始化並附加到 bot。')
            raise RuntimeError('LeaderboardCog 初始化失敗：LeaderboardService 未注入。')
        logger.info('LeaderboardCog initialized.')

    @app_commands.command(name='lb', description='查看排行榜')
    @app_commands.describe(category='選擇排行榜分類', leaderboard_type='指定排行榜類型 (可選, 若不指定則顯示分類預設)', asset_symbol='股票代碼 (僅用於特定股票排行)', page='頁碼 (從1開始)')
    @app_commands.choices(category=[app_commands.Choice(name='綜合排行', value='general'), app_commands.Choice(name='卡牌收集排行', value='collection'), app_commands.Choice(name='股票市場排行', value='stock_market')])
    @prevent_duplicate_operation(lambda _self, interaction, category, leaderboard_type=None, asset_symbol=None, page=1: f"{interaction.user.id}:{category}:{leaderboard_type or 'default'}:{asset_symbol or 'none'}")
    async def leaderboard(self, interaction: Interaction, category: str, leaderboard_type: Optional[str]=None, asset_symbol: Optional[str]=None, page: Optional[int]=1):
        """排行榜命令處理

        參數:
            interaction: Discord交互對象
            category: 排行榜主分類
            leaderboard_type: 具體排行榜類型 (可選)
            asset_symbol: 股票代碼 (可選, 用於特定股票排行)
            page: 頁碼（從1開始）
        """
        start_time = time.time()
        await interaction.response.defer(thinking=True)
        try:
            actual_leaderboard_type = leaderboard_type
            if not actual_leaderboard_type:
                for type_key, config_data_item in config_service.get_config('gacha_core_settings.leaderboard_config').items():
                    if config_data_item.category == category and config_data_item.default_for_category:
                        actual_leaderboard_type = type_key
                        break
                if not actual_leaderboard_type:
                    for type_key, config_data_item in config_service.get_config('gacha_core_settings.leaderboard_config').items():
                        if config_data_item.category == category:
                            actual_leaderboard_type = type_key
                            break
                if not actual_leaderboard_type:
                    await interaction.followup.send(f"錯誤：找不到分類 '{category}' 的預設排行榜類型。", ephemeral=True)
                    return
            loading_embed = create_loading_embed(actual_leaderboard_type, asset_symbol)
            loading_message = await interaction.followup.send(embed=loading_embed)
            if page < 1:
                page = 1
            leaderboard_results, total_pages_from_service, total_users_for_this_query = await self.leaderboard_service.get_leaderboard(leaderboard_type=actual_leaderboard_type, page=page, asset_symbol=asset_symbol)
            leaderboard_data = {'results': leaderboard_results, 'current_page': page, 'total_pages': total_pages_from_service, 'total_users': total_users_for_this_query, 'leaderboard_type': actual_leaderboard_type, 'asset_symbol': asset_symbol}
            query_time = time.time() - start_time
            leaderboard_data['query_time'] = query_time
            leaderboard_data['updated_at'] = datetime.now()
            if not leaderboard_results:
                error_embed = discord.Embed(title='⚠️ 排行榜獲取失敗', description='找不到該排行榜的數據或數據為空，請檢查輸入或稍後再試。', color=discord.Color.red())
                await loading_message.edit(embed=error_embed)
                return
            view = LeaderboardView(user=interaction.user, initial_category=category, initial_leaderboard_type=actual_leaderboard_type, initial_asset_symbol=asset_symbol, leaderboard_data=leaderboard_data, bot=self.bot)
            embed = view.get_current_page_embed()
            await loading_message.edit(embed=embed, view=view)
            view.message = loading_message
        except Exception as e:
            logger.error('執行 /lb 指令時發生錯誤 (category: %s, type: %s, asset: %s, page: %s): %s', category, leaderboard_type, asset_symbol, page, e, exc_info=True)
            if interaction.response.is_done():
                await interaction.followup.send('顯示排行榜時發生錯誤，請稍後再試。', ephemeral=True)
            else:
                try:
                    await interaction.response.send_message('顯示排行榜時發生錯誤，請稍後再試。', ephemeral=True)
                except discord.InteractionResponded:
                    await interaction.followup.send('顯示排行榜時發生錯誤，請稍後再試。', ephemeral=True)

async def setup(bot: commands.Bot):
    if not hasattr(bot, 'leaderboard_service'):
        logger.error("無法加載 LeaderboardCog：Bot 實例缺少 'leaderboard_service' 屬性。請確保 LeaderboardService 已被正確初始化並附加到 Bot 上。")
        return
    await bot.add_cog(LeaderboardCog(bot))
    logger.info('LeaderboardCog added to bot.')