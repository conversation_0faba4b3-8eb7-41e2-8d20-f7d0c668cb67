"""
Gacha系統卡片描述功能處理器
"""

from typing import TYPE_CHECKING

import discord

from utils.logger import logger
from gacha.services.core.collection_service import (
    CollectionService,
)  # 根據任務說明，應該是 CollectionService
from gacha.views.collection.collection_view.card_description_modal import (
    CardDescriptionModal,
)
from .base_handler import BaseHandler

if TYPE_CHECKING:
    from gacha.views.collection.collection_view.card_view import CollectionView


class CardDescriptionHandler(BaseHandler):
    """處理卡片描述相關操作

    採用統一的服務訪問模式，所有服務都通過 view.services 統一訪問，
    保持系統架構的一致性和可維護性。
    """

    def __init__(self, view: "CollectionView"):
        super().__init__(view)

    @property
    def collection_service(self) -> CollectionService:
        """統一的收藏服務訪問接口

        遵循系統的服務容器模式，通過 view.services 訪問服務
        """
        return self.view.services.collection

    async def handle_set_description_button(
            self, interaction: discord.Interaction):
        """處理設置描述按鈕的點擊事件"""
        # 權限檢查和卡片可用性檢查 - 使用繼承自BaseHandler的統一方法
        if not await self.check_interaction_permission(interaction):
            return
        if not await self.check_cards_available(interaction):
            return

        # 使用統一接口獲取當前卡片
        card_data = self.current_card
        card_id = card_data.card.card_id
        card_name = card_data.card.name
        current_star = card_data.star_level

        # 檢查星級是否足夠（最低需要10星）
        MIN_STAR_LEVEL = 10
        if current_star < MIN_STAR_LEVEL:
            await self._send_error_message(
                interaction,
                f"設置描述失敗！您的卡片星級 ({current_star}) 不足 {MIN_STAR_LEVEL} 星。",
            )
            return

        # 打開描述設置模態
        # 注意：CardDescriptionModal 原本接收 encyclopedia_service。
        # 現在我們傳遞 collection_service。需要確認 CardDescriptionModal 的依賴。
        # 假設 CollectionService 提供了更新描述的方法，或者 CardDescriptionModal 內部邏輯會調整。
        # 根據 Mixin 的實現，Modal 似乎是用 encyclopedia_service 來 *保存* 描述。
        # 如果 CollectionService 負責保存，那麼傳遞它是合理的。
        modal = CardDescriptionModal(
            user_id=self.user_id,  # 使用BaseHandler的統一屬性
            card_id=card_id,
            card_name=card_name,
            current_star=current_star,
            min_star=MIN_STAR_LEVEL,
            encyclopedia_service=self.view.services.encyclopedia,
        )
        await interaction.response.send_modal(modal)
