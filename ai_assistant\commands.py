"""
Discord命令註冊模組 - 負責註冊和路由所有AI助手相關的Discord命令
"""

import discord
from discord import app_commands
# from discord.ui import Modal, TextInput # LongTextInputModal 將被移除
import logging
import asyncio
from typing import Optional

# 更新導入以匹配服務模塊中的函數和變量
from .outfit_rater.outfit_service import init_services as init_outfit_services
from .outfit_rater import outfit_service # 用於訪問 outfit_service.rater
from .qa_system.qa_service import init_services as init_qa_services
from .qa_system import qa_service # 用於訪問 qa_service.qa_system_instance
# ask_question_slash 將直接從 qa_service 模塊使用
# rate_outfit_slash 將直接從 outfit_service 模塊使用

from . import config

# 設置日誌
logger = logging.getLogger("AI_Commands")

# 移除 LongTextInputModal，因為 qa_service.py 中已有 QAModal
# class LongTextInputModal(Modal): ...

# 旗標用於控制初始化和指令註冊邏輯
_ai_services_initialized_this_setup_call = False 
_commands_registered_this_bot_lifecycle = False

async def setup(bot):
    """設置AI助手相關的Discord命令並註冊清理函數"""
    global _ai_services_initialized_this_setup_call, _commands_registered_this_bot_lifecycle
    
    if _ai_services_initialized_this_setup_call:
        logger.info("AI services setup function called again within the same load, but services were already attempted to init. Skipping re-init logic here.")
        # 依賴 init_outfit_services 和 init_qa_services 自身的健壯性來處理重複調用（如果來自其他地方）
        return 

    logger.info("Executing AI Assistant setup...")

    # Get the playwright_manager from the bot instance
    playwright_manager = getattr(bot, 'playwright_manager', None)
    if not playwright_manager:
        logger.error("PlaywrightManager not found on bot instance during AI Assistant setup. Outfit rater service cannot be initialized.")
        outfit_ok = False
    else:
        # Pass the manager to the init function
        outfit_ok = await init_outfit_services(playwright_manager=playwright_manager)

    # QA service initialization remains unchanged (assuming it doesn't need playwright)
    qa_ok = await init_qa_services()

    if not outfit_ok or not qa_ok:
        logger.error(f"One or more AI services failed to initialize during setup. Outfit OK: {outfit_ok}, QA OK: {qa_ok}")
        # 即使失敗，也標記為已嘗試初始化，避免下次 setup 調用時再次嘗試（除非是 Cog 重載）
        _ai_services_initialized_this_setup_call = True 
        # 根據策略，如果服務初始化失敗，可能不應註冊指令或應註冊帶有錯誤處理的指令
        # 此處我們繼續註冊指令，指令內部會檢查服務可用性
    else:
        logger.info("AI services initialized successfully during setup.")
        _ai_services_initialized_this_setup_call = True

    # 註冊指令 (確保只在機器人整個生命週期中註冊一次，除非 Cog 被卸載並重新加載)
    if not _commands_registered_this_bot_lifecycle: 
        logger.info("Registering AI Assistant commands...")
        
        # === 穿搭評分命令 ===
        @bot.tree.command(name=config.COMMAND_NAME, description=config.COMMAND_DESCRIPTION)
        @app_commands.describe(
            image="上傳穿搭照片進行評分",
            url="或直接提供圖片URL進行評分 (優先使用上傳的圖片)"
        )
        async def rate_cmd(interaction: discord.Interaction, image: Optional[discord.Attachment] = None, url: Optional[str] = None):
            logger.info(f"User {interaction.user.id} ({interaction.user.display_name}) initiated /{config.COMMAND_NAME} command with image: {image.filename if image else 'None'}, url: {url if url else 'None'}.")
            
            if not outfit_service.rater or not outfit_service.image_generator: # 檢查 outfit_service 的核心組件
                 await interaction.response.send_message("❌ 穿搭評分服務暫時不可用 (組件未加載)，請稍後再試。", ephemeral=True)
                 logger.warning(f"/{config.COMMAND_NAME} called but outfit_service.rater or outfit_service.image_generator is not available.")
                 return
            
            # 直接調用 outfit_service 中的處理函數
            # outfit_service.rate_outfit_slash 已經更新以包含 url 參數
            await outfit_service.rate_outfit_slash(interaction, image, url)
            logger.info(f"/{config.COMMAND_NAME} call to outfit_service.rate_outfit_slash completed for user {interaction.user.id}")

        # === QA系統命令 ===
        @bot.tree.command(name="ask", description="向AI助手提問，可以附帶圖片")
        @app_commands.describe(
            question="您想問的問題 (如果選擇 use_modal:True，此處可留空)",
            image="可選：上傳相關圖片以獲得更準確的回答",
            use_modal="選擇True以使用彈窗輸入長問題 (預設為False)"
        )
        async def ask_cmd(
            interaction: discord.Interaction, 
            question: Optional[str] = None, 
            image: Optional[discord.Attachment] = None,
            use_modal: Optional[bool] = False # 默認為 False
        ):
            logger.info(f"User {interaction.user.id} ({interaction.user.display_name}) initiated /ask command with question: {'Present' if question else 'None'}, image: {image.filename if image else 'None'}, use_modal: {use_modal}.")
            
            if not qa_service.qa_system_instance: # 檢查 qa_service 的核心實例
                 await interaction.response.send_message("❌ 問答服務暫時不可用 (組件未加載)，請稍後再試。", ephemeral=True)
                 logger.warning("/ask called but qa_service.qa_system_instance is not available.")
                 return

            # 直接調用 qa_service 中的處理函數，它內部處理 Modal 邏輯
            await qa_service.ask_question_slash(interaction, question, image, use_modal)
            logger.info(f"/ask call to qa_service.ask_question_slash completed for user {interaction.user.id}")

        _commands_registered_this_bot_lifecycle = True # 標記指令已定義
        logger.info("AI Assistant commands registered.")
    else:
        logger.info("AI Assistant commands appear to be already registered in this bot lifecycle. Skipping re-registration.")
        
    # on_disconnect 保持註釋掉 (即不包含該事件處理器)
    # 相關的 shutdown_outfit_services 等將通過 bot.py 的 on_close 調用

# 注意：之前版本中的 on_disconnect 監聽器已被移除。
# 服務的關閉現在應該由 bot.py 中的 on_close 事件處理器統一管理，
# 調用各服務模塊 (outfit_service.py, qa_service.py) 中定義的 shutdown_services() 函數。

