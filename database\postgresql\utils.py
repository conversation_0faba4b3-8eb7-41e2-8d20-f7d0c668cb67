"""
PostgreSQL数据库工具函数
提供统一的数据库管理器获取接口
"""

from typing import Dict, Any, Optional
import logging # Added import
from database.postgresql.manager import PostgresqlManager

# Removed battles.utils logger import
# logger = logging.getLogger(__name__) # Define logger locally if needed, or use logging.getLogger directly

def get_db_manager(system_type: str, db_config: Optional[Dict[str, Any]] = None) -> PostgresqlManager:
    """获取指定系统类型的数据库管理器实例

    Args:
        system_type: 系统类型 ("pve", "ladder", "gacha")
        db_config: 可选的数据库配置，如不提供则自动获取

    Returns:
        PostgresqlManager: 数据库管理器实例
    """
    return PostgresqlManager.get_instance(system_type, db_config)

def test_db_connection(system_type: str) -> Dict[str, Any]:
    """测试数据库连接

    Args:
        system_type: 系统类型

    Returns:
        Dict[str, Any]: 测试结果
    """
    return PostgresqlManager.test_connection(system_type)

def get_pool_stats(system_type: str = None) -> Dict[str, Any]:
    """获取指定系统或所有系统的连接池统计信息

    Args:
        system_type: 可选的系统类型，如不提供则返回所有系统的统计信息

    Returns:
        Dict[str, Any]: 连接池统计信息
    """
    if system_type:
        db_manager = get_db_manager(system_type)
        return {system_type: db_manager.get_pool_stats()}

    stats = {}
    for system_type, instance in PostgresqlManager._instances.items():
        stats[system_type] = instance.get_pool_stats()

    return stats

def diagnose_connection_issues(system_type: str = None) -> Dict[str, Any]:
    """诊断连接池相关问题

    检查连接池使用情况，并提供潜在问题的诊断信息

    Args:
        system_type: 可选的系统类型，如不提供则诊断所有系统

    Returns:
        Dict[str, Any]: 诊断结果
    """
    stats = get_pool_stats(system_type)
    results = {}

    for sys_type, pool_stats in stats.items():
        diagnosis = {
            "system_type": sys_type,
            "status": "healthy",
            "issues": [],
            "recommendations": []
        }

        if not pool_stats.get("pool_initialized", False):
            diagnosis["status"] = "critical"
            diagnosis["issues"].append("连接池未初始化")
            diagnosis["recommendations"].append("检查数据库配置和初始化流程")
            results[sys_type] = diagnosis
            continue

        used = pool_stats.get("used_connections", 0)
        total = pool_stats.get("total_connections", 0)

        if total == 0:
            diagnosis["status"] = "critical"
            diagnosis["issues"].append("未检测到连接池")
            diagnosis["recommendations"].append("检查数据库配置")
        elif used / total > 0.9:
            diagnosis["status"] = "critical"
            diagnosis["issues"].append(f"连接池使用率极高 ({used}/{total})")
            diagnosis["recommendations"].append("考虑增加连接池大小")
            diagnosis["recommendations"].append("检查是否存在连接泄漏")
        elif used / total > 0.7:
            diagnosis["status"] = "warning"
            diagnosis["issues"].append(f"连接池使用率较高 ({used}/{total})")
            diagnosis["recommendations"].append("监控连接池使用情况")

        results[sys_type] = diagnosis

    return results

def reset_pool(system_type: str) -> Dict[str, Any]:
    """重置指定系统的连接池

    警告：此操作会关闭所有连接并重新初始化连接池，可能导致活动事务失败
    仅在调试环境或紧急情况下使用

    Args:
        system_type: 系统类型

    Returns:
        Dict[str, Any]: 操作结果，包含重置前后的状态信息

    Raises:
        Exception: 当连接池重置失败时
    """
    db_manager = get_db_manager(system_type)
    if not db_manager.pool:
        raise RuntimeError(f"[{system_type}] 连接池未初始化")

    # 获取连接池状态
    before_stats = db_manager.get_pool_stats()

    # 关闭所有连接
    db_manager.close()

    # 重新初始化
    from database.config import get_db_config
    config = get_db_config(system_type=system_type)
    db_manager.__init__(system_type, config)

    # 获取新状态
    after_stats = db_manager.get_pool_stats()

    logging.getLogger(__name__).warning(f"[{system_type}] 数据库连接池已重置") # Use standard logging
    return {
        "system_type": system_type,
        "before": before_stats,
        "after": after_stats
    }
