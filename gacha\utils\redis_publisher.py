import asyncio
import msgpack
from typing import Any, List, Optional, Tuple, Dict
import logging
from database.redis.manager import RedisManager
from gacha.constants import MarketStatsEventType
from utils.logger import logger

class RedisEventPublisher:
    """
    通用的 Redis Stream 事件發布器。
    """
    DEFAULT_BATCH_SIZE = 5000
    DEFAULT_MAXLEN = 1000000

    def __init__(self, redis_manager: RedisManager):
        """
        初始化 RedisEventPublisher。

        Args:
            redis_manager: RedisManager 的實例。
        """
        if not isinstance(redis_manager, RedisManager):
            raise TypeError('redis_manager 必須是 RedisManager 的實例')
        self.redis_manager = redis_manager
        self.redis_client = redis_manager.redis_client
        
        # 延遲導入 settings 避免循環導入
        from gacha.app_config import settings
        self.default_stream_name = settings.MARKET_STATS_UPDATES_STREAM_NAME
        self.batch_size = settings.REDIS_EVENT_BATCH_SIZE
        self.maxlen = settings.REDIS_STREAM_MAXLEN
        
        if not self.default_stream_name:
            logger.warning('[RedisPublisher] MARKET_STATS_UPDATES_STREAM_NAME 未配置!')
        logger.info("[RedisPublisher] Initialized. Default Stream: '%s', Batch Size: %s, Maxlen: %s", self.default_stream_name, self.batch_size, self.maxlen)

    async def publish(self, event_type: MarketStatsEventType, payload: Any, *, stream_name: Optional[str]=None, batch_payload: bool=False, user_id_for_log: Optional[int]=None) -> bool:
        """
        發布事件到 Redis Stream。

        Args:
            event_type: 事件類型 (MarketStatsEventType Enum)。
            payload: 事件的數據負載，對於市場統計事件應為元組列表。
            stream_name: 目標 Stream 名稱 (可選, 默認使用配置的 default_stream_name)。
            batch_payload: 是否將 payload (假設為列表) 進行批次處理 (可選, 默認 False)。
            user_id_for_log: 用於日誌記錄的用戶 ID (可選)。

        Returns:
            bool: 發布是否至少嘗試執行 (不保證 Redis 接收成功，但客戶端存在且嘗試發送)。
                  如果客戶端不可用或未配置 Stream 名稱，則返回 False。
        """
        target_stream = stream_name if stream_name else self.default_stream_name
        log_prefix = f'[RedisPublisher][User:{user_id_for_log}]' if user_id_for_log else '[RedisPublisher]'
        if not self.redis_client:
            logger.error("%s Redis client not available. Cannot publish event '%s' to stream '%s'.", log_prefix, event_type.value, target_stream)
            return False
        if not target_stream:
            logger.error("%s Target stream name is not configured. Cannot publish event '%s'.", log_prefix, event_type.value)
            return False
        if not isinstance(event_type, MarketStatsEventType):
            logger.error('%s Invalid event_type provided: %s. Must be MarketStatsEventType.', log_prefix, type(event_type))
            return False
        actual_payload = payload
        logger.debug('%s[RedisPublisherTrace] Event: %s, Publishing Payload: %s', log_prefix, event_type.value, actual_payload)
        try:
            if batch_payload and isinstance(actual_payload, list):
                num_items = len(payload)
                num_batches = (num_items + self.batch_size - 1) // self.batch_size if self.batch_size > 0 else 1 if num_items > 0 else 0
                logger.debug("%s Batching payload for event '%s' into %s batches (size=%s). Stream: '%s'", log_prefix, event_type.value, num_batches, self.batch_size, target_stream)
                for i in range(num_batches):
                    batch_start_index = i * self.batch_size
                    batch_end_index = batch_start_index + self.batch_size
                    current_batch_data = payload[batch_start_index:batch_end_index]
                    if not current_batch_data:
                        continue
                    event_message_payload = {'event_type': event_type.value, 'payload': current_batch_data}
                    await self._send_to_redis(target_stream, event_message_payload, log_prefix, batch_num=i + 1, total_batches=num_batches)
            else:
                if batch_payload and (not isinstance(payload, list)):
                    logger.warning("%s 'batch_payload' is True but payload is not a list for event '%s'. Sending as single event. Stream: '%s'", log_prefix, event_type.value, target_stream)
                event_message_payload = {'event_type': event_type.value, 'payload': actual_payload}
                await self._send_to_redis(target_stream, event_message_payload, log_prefix)
            return True
        except Exception as e:
            logger.error("%s Unexpected error during event publishing preparation for event '%s' to stream '%s': %s", log_prefix, event_type.value, target_stream, e, exc_info=True)
            return False

    async def _send_to_redis(self, stream_name: str, message_payload: Dict[str, Any], log_prefix: str, batch_num: Optional[int]=None, total_batches: Optional[int]=None):
        """
        (Private Helper) 實際將序列化後的數據發送到 Redis Stream。
        """
        try:
            serialized_data = msgpack.packb(message_payload, use_bin_type=True)
            message_field_value_pair = {'data': serialized_data}
            batch_log_suffix = f' (Batch {batch_num}/{total_batches})' if batch_num is not None else ''
            await self.redis_client.xadd(name=stream_name, fields=message_field_value_pair, maxlen=self.maxlen, approximate=True)
            logger.debug("%s Successfully published event '%s'%s to stream '%s'.", log_prefix, message_payload.get('event_type', 'UNKNOWN'), batch_log_suffix, stream_name)
        except msgpack.PackException as e_pack:
            logger.error("%s Failed to serialize payload with msgpack for event '%s' to stream '%s': %s", log_prefix, message_payload.get('event_type', 'UNKNOWN'), stream_name, e_pack, exc_info=False)
        except Exception as e_redis:
            batch_log_suffix = f' (Batch {batch_num}/{total_batches})' if batch_num is not None else ''
            logger.error("%s Failed to publish event '%s'%s to stream '%s': %s", log_prefix, message_payload.get('event_type', 'UNKNOWN'), batch_log_suffix, stream_name, e_redis, exc_info=True)