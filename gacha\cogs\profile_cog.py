"""
Profile COG - 處理用戶檔案相關的 Discord 指令
"""
import discord
from discord import app_commands
from discord.ext import commands
from typing import Optional, List, Tuple
import asyncio
import io

from utils.logger import logger
from gacha.services.core.profile_service import ProfileService
from gacha.services.ui.profile_image_generator import ProfileImageGenerator
from gacha.views.profile.profile_view import ProfileLikeView
from gacha.views.profile.profile_settings_view import ProfileSettingsView
from gacha.app_config import settings
from gacha.exceptions import ProfileCardSetError, ProfileBackgroundError, LikeProfileError, UserDoesNotOwnCardError, UserNotFoundError, DatabaseOperationError
from utils.playwright_manager import PlaywrightManager


class ProfileCog(commands.Cog):
    """處理用戶檔案相關指令的 Cog (使用 Playwright)"""

    def __init__(self, bot: commands.Bo<PERSON>, playwright_manager: PlaywrightManager):
        self.bot = bot
        self.profile_service: Optional[ProfileService] = None
        if not playwright_manager:
             logger.error("PlaywrightManager instance is required for ProfileCog initialization.")
             raise ValueError("PlaywrightManager instance is required.")
        self.image_generator = ProfileImageGenerator(playwright_manager=playwright_manager)
        
        # Simplified direct access to private_channel_id
        # settings.discord is guaranteed by Pydantic's default_factory for DiscordConfig
        # and DiscordConfig.private_channel_id defaults to None if not set.
        self.private_channel_id = settings.discord.private_channel_id
            
        if self.private_channel_id:
            logger.info(f'從配置檔案中讀取私密頻道ID: {self.private_channel_id}')
        else:
            logger.warning('未在配置中找到 private_channel_id，某些功能可能受限。')
        
        logger.info('ProfileCog 已成功加載並初始化。')

    async def cog_load(self):
        """COG 載入時的初始化"""
        self.profile_service = getattr(self.bot, 'profile_service', None)
        if not self.profile_service:
            logger.warning('ProfileService 未在 bot 中找到，Profile 功能可能無法正常工作')
        
        if self.private_channel_id:
            self.bot.private_channel_id = self.private_channel_id
            logger.info(f'已設定私密頻道ID: {self.private_channel_id}')

    async def _handle_set_main_card(self, user_id: int, card_id: int):
        """处理设置主展示卡片的逻辑，遵循纯异常模式"""
        try:
            result = await self.profile_service.set_main_card(user_id, card_id)
            return result
        except UserDoesNotOwnCardError:
            raise  # 直接向上抛出异常
        except ProfileCardSetError:
            raise  # 直接向上抛出异常
        except (UserNotFoundError, DatabaseOperationError) as e:
            logger.error(f"設定主卡片時發生錯誤 (user: {user_id}, card: {card_id}): {e}", exc_info=True)
            raise  # 向上抛出原始异常
        except Exception as e:
            logger.error(f"設定主卡片時發生未預期錯誤 (user: {user_id}, card: {card_id}): {e}", exc_info=True)
            raise ProfileCardSetError(f"設定主卡片時發生嚴重錯誤。") from e  # 转换为具体业务异常

    async def _handle_set_sub_cards(self, user_id: int, sub_cards_str: str):
        """处理设置副展示卡片的逻辑，遵循纯异常模式"""
        results = []
        card_id_strs = [c.strip() for c in sub_cards_str.split(',') if c.strip()]
        
        if not (1 <= len(card_id_strs) <= 4):
            raise ValueError("請提供1到4個有效的副卡片ID，用逗號分隔。")

        for i, card_id_str_item in enumerate(card_id_strs):
            slot_to_set = i + 1
            try:
                card_id_int_item = int(card_id_str_item)
                result = await self.profile_service.set_sub_card(user_id, slot_to_set, card_id_int_item)
                results.append(result)
            except ValueError:
                raise ValueError(f"設定副卡片 (ID: {card_id_str_item}) 失敗: 卡片ID必須是有效的數字。")
            except UserDoesNotOwnCardError:
                raise  # 直接向上抛出异常
            except ProfileCardSetError:
                raise  # 直接向上抛出异常
            except (UserNotFoundError, DatabaseOperationError) as e:
                logger.error(f"設定副卡片時發生錯誤 (user: {user_id}, card_str: {card_id_str_item}, slot: {slot_to_set}): {e}", exc_info=True)
                raise  # 向上抛出原始异常
            except Exception as e:
                logger.error(f"設定副卡片時發生未預期錯誤 (user: {user_id}, card_str: {card_id_str_item}, slot: {slot_to_set}): {e}", exc_info=True)
                raise ProfileCardSetError(f"設定副卡片 (ID: {card_id_str_item}, 位置 {slot_to_set}) 時發生嚴重錯誤。") from e

        return results

    async def _handle_clear_sub_card_slot(self, user_id: int, slot_to_clear: int):
        """处理清除副卡片槽位的逻辑，遵循纯异常模式"""
        try:
            cleared_slot = await self.profile_service.clear_sub_card(user_id, slot_to_clear)
            return cleared_slot
        except ProfileCardSetError:
            raise  # 直接向上抛出异常
        except (UserNotFoundError, DatabaseOperationError) as e:
            logger.error(f"清除副卡片時發生錯誤 (user: {user_id}, slot: {slot_to_clear}): {e}", exc_info=True)
            raise  # 向上抛出原始异常
        except Exception as e:
            logger.error(f"清除副卡片時發生未預期錯誤 (user: {user_id}, slot: {slot_to_clear}): {e}", exc_info=True)
            raise ProfileCardSetError(f"清除副卡片 (位置 {slot_to_clear}) 時發生嚴重錯誤。") from e

    async def _handle_reset_background(self, user_id: int):
        """处理重置背景的逻辑，遵循纯异常模式"""
        try:
            await self.profile_service.reset_background(user_id)
            return None  # 没有需要返回的数据
        except ProfileBackgroundError:
            raise  # 直接向上抛出异常
        except (UserNotFoundError, DatabaseOperationError) as e:
            logger.error(f"重置背景時發生錯誤 (user: {user_id}): {e}", exc_info=True)
            raise  # 向上抛出原始异常
        except Exception as e:
            logger.error(f"重置背景時發生未預期錯誤 (user: {user_id}): {e}", exc_info=True)
            raise ProfileBackgroundError("重置背景時發生嚴重錯誤。") from e

    async def _handle_set_background_image_to_private_channel(
        self, 
        user_id: int, 
        image_attachment: discord.Attachment,
        interaction: discord.Interaction # Pass interaction for responding
    ):
        """
        Handles downloading a user-uploaded background image, 
        uploading it to the configured private channel, 
        and then updating the user's profile with the new URL.
        """
        if self.private_channel_id is None:
            logger.error("Cannot set background: ProfileCog.private_channel_id is None.")
            # Raise an exception that the command handler can catch
            raise ProfileBackgroundError("背景圖私密上傳頻道未配置。")

        if not image_attachment.content_type or not image_attachment.content_type.startswith("image/"):
            raise ProfileBackgroundError("請上傳有效的圖片文件 (PNG, JPG, GIF 等)。")

        # Discord's free tier file upload limit is around 8MB, but bot uploads might have higher limits (e.g., 25MB). 
        # Let's stick to a reasonable limit like 8MB for now.
        if image_attachment.size > 8 * 1024 * 1024: 
            raise ProfileBackgroundError("圖片文件過大，請上傳小於 8MB 的圖片。")

        try:
            storage_channel = self.bot.get_channel(self.private_channel_id)
            if not storage_channel:
                # fetch_channel can raise discord.NotFound or discord.Forbidden
                storage_channel = await self.bot.fetch_channel(self.private_channel_id) 
            
            if not isinstance(storage_channel, discord.TextChannel):
                logger.error(f"Configured private channel ID ({self.private_channel_id}) is not a TextChannel.")
                raise ProfileBackgroundError("配置的背景圖私密上傳頻道不是有效的文字頻道。")

            image_bytes = await image_attachment.read()
            discord_file = discord.File(io.BytesIO(image_bytes), filename=image_attachment.filename or "background.png")
            
            log_message_content = (
                f"User profile background upload.\n"
                f"User: {interaction.user} ({user_id})\n"
                f"Original Filename: {image_attachment.filename}\n"
                f"Size: {image_attachment.size} bytes\n"
                f"Content Type: {image_attachment.content_type}"
            )
            # Send the image to the private channel
            sent_message = await storage_channel.send(
                content=log_message_content,
                file=discord_file
            )
            
            if not sent_message.attachments or len(sent_message.attachments) == 0:
                logger.error(f"Failed to get attachment URL after uploading background for user {user_id} to channel {storage_channel.id}")
                raise ProfileBackgroundError("上傳圖片到私密頻道後未能獲取附件 URL。")
                
            persistent_image_url = sent_message.attachments[0].url
            
            if not self.profile_service:
                logger.error("ProfileService not available in ProfileCog for setting background image.")
                raise ProfileBackgroundError("檔案服務目前無法使用。")

            # Update the background image URL in the profile service
            await self.profile_service.set_background_image(user_id, persistent_image_url)
            # Invalidate the profile cache to ensure the new background is used next time
            await self.profile_service.invalidate_profile_cache(user_id) 

            logger.info(f"User {user_id} set new profile background. Stored in private channel {storage_channel.id}, message {sent_message.id}. URL: {persistent_image_url}")
            return persistent_image_url

        except discord.NotFound:
            logger.error(f"Configured private channel ID ({self.private_channel_id}) was not found.")
            raise ProfileBackgroundError("配置的背景圖私密上傳頻道無效或無法找到。")
        except discord.Forbidden:
            logger.error(f"Bot lacks permissions for the configured private channel ID ({self.private_channel_id}).")
            raise ProfileBackgroundError("Bot 無權限訪問配置的背景圖私密上傳頻道。")
        except discord.HTTPException as e: # Covers network issues, API errors during upload
            logger.error(f"Discord HTTP Exception during background image processing for user {user_id}: {e}", exc_info=True)
            raise ProfileBackgroundError(f"與 Discord 通信時發生錯誤: {e}")
        # It's good practice to let ProfileBackgroundError propagate if it's already that type
        # For other specific exceptions from profile_service (like DatabaseOperationError), 
        # they should ideally be caught here or in the command and re-raised as ProfileBackgroundError or handled.

    async def _handle_set_status(self, user_id: int, status_text: str):
        """处理设置个性签名的逻辑，遵循纯异常模式"""
        try:
            if len(status_text) > 150:
                raise ValueError("個性簽名不能超過150個字符。")

            cleaned_status = await self.profile_service.set_user_status(user_id, status_text)
            return cleaned_status
        except ProfileBackgroundError:
            raise  # 直接向上抛出异常
        except (UserNotFoundError, DatabaseOperationError) as e:
            logger.error(f"設定個性簽名時發生錯誤 (user: {user_id}): {e}", exc_info=True)
            raise  # 向上抛出原始异常
        except Exception as e:
            logger.error(f"設定個性簽名時發生未預期錯誤 (user: {user_id}): {e}", exc_info=True)
            raise ProfileBackgroundError("設定個性簽名時發生嚴重錯誤。") from e

    @app_commands.command(name='profile', description='查看或編輯用戶檔案')
    @app_commands.describe(
        user='要查看檔案的用戶 (不填則查看自己的檔案)',
        edit='是否開啟檔案設定介面',
        background_image='要設定為背景的圖片'
    )
    async def profile_command(
        self, 
        interaction: discord.Interaction, 
        user: Optional[discord.Member] = None,
        edit: bool = False,
        background_image: Optional[discord.Attachment] = None
    ):
        """顯示或編輯用戶個人檔案的指令"""
        
        # 處理背景圖片上傳
        if background_image:
            if user and user.id != interaction.user.id: # Ensure user is editing their own profile
                await interaction.response.send_message("❌ 您只能設定自己的檔案。", ephemeral=True)
                return
            
            # Defer early as image processing and upload can take time.
            # ephemeral=True because this part of the command is a direct action with a direct success/fail response.
            await interaction.response.defer(ephemeral=True) 
            
            target_user_id_for_bg = interaction.user.id # Background is always for the interacting user
            
            try:
                # Call the new handler method
                await self._handle_set_background_image_to_private_channel(
                    target_user_id_for_bg, 
                    background_image,
                    interaction 
                )
                await interaction.followup.send(
                    "✅ 已成功設定新的背景圖片。\n"
                    "新的背景圖將在下次個人檔案刷新或重新生成時顯示。",
                    ephemeral=True
                )
            except ProfileBackgroundError as e: # Catch the specific error from our handler
                await interaction.followup.send(f"❌ 設定背景圖片失敗: {e}", ephemeral=True)
            except Exception as e: # Catch any other unexpected errors
                logger.error(f"Unexpected error setting background image for user {target_user_id_for_bg}: {e}", exc_info=True)
                await interaction.followup.send("❌ 設定背景圖片時發生未預期錯誤，請稍後再試。", ephemeral=True)
            return # Important to return after handling background image
        
        # 如果是編輯模式
        if edit:
            # 檢查是否嘗試編輯他人的檔案
            if user and user.id != interaction.user.id:
                await interaction.response.send_message("❌ 您只能設定自己的檔案。", ephemeral=True)
                return
            
            await interaction.response.defer(ephemeral=True)
            
            if not self.profile_service:
                logger.error("ProfileService not available for setting profile.")
                await interaction.followup.send("❌ 檔案設定服務目前無法使用。", ephemeral=True)
                return
            
            # 創建設定視圖
            settings_view = ProfileSettingsView(self.bot, self.profile_service, interaction.user.id)
            
            try:
                # 獲取當前設定信息並創建嵌入消息
                settings_embed = await settings_view.create_settings_embed()
                
                # 發送嵌入消息和設定視圖
                await interaction.followup.send(
                    embed=settings_embed,
                    view=settings_view, 
                    ephemeral=True
                )
            except Exception as e:
                logger.error(f"顯示設定信息時發生錯誤: {e}", exc_info=True)
                await interaction.followup.send("請選擇您要修改的檔案項目：", view=settings_view, ephemeral=True)
            
            return
        
        # 常規檔案顯示模式
        await interaction.response.defer(ephemeral=False, thinking=True)
        
        target_user_for_view = user or interaction.user
        target_user_id_for_view = target_user_for_view.id
        
        try:
            logger.info(f"User {interaction.user.id} is viewing profile of {target_user_id_for_view}")
            
            if not self.profile_service:
                logger.error("ProfileService not available for displaying profile.")
                await interaction.followup.send("❌ 檔案服務目前無法使用。", ephemeral=True)
                return

            view = ProfileLikeView(
                owner_id=target_user_id_for_view,
                bot_instance=self.bot,
                profile_service=self.profile_service,
                image_generator=self.image_generator,
                private_channel_id=self.private_channel_id
            )
            
            cached_url = await self.profile_service.get_cached_profile_image_url(target_user_id_for_view)
            
            if cached_url:
                logger.info(f"Using cached profile image URL: {cached_url} for user {target_user_id_for_view}")
                await interaction.followup.send(cached_url, view=view)
            else:
                logger.info(f"No cached URL, generating new profile image for user {target_user_id_for_view}")
                user_avatar_url = str(target_user_for_view.display_avatar.url) if target_user_for_view.display_avatar else None
                discord_display_name = target_user_for_view.display_name
                
                profile_data = await self.profile_service.get_profile_data(
                    target_user_id_for_view, 
                    discord_display_name=discord_display_name, 
                    avatar_url=user_avatar_url
                )
                image_data = await self.image_generator.generate_profile_image(profile_data)
                
                image_file = discord.File(io.BytesIO(image_data), filename="profile.png")

                if self.private_channel_id:
                    private_channel = self.bot.get_channel(self.private_channel_id)
                    if private_channel and isinstance(private_channel, discord.TextChannel):
                        try:
                            temp_image_file = discord.File(io.BytesIO(image_data), filename="profile.png")
                            temp_message = await private_channel.send(file=temp_image_file)
                            discord_cdn_url = temp_message.attachments[0].url
                            await self.profile_service.update_cached_profile_image_url(target_user_id_for_view, discord_cdn_url)
                            logger.info(f"New image uploaded to private channel, CDN URL: {discord_cdn_url} for user {target_user_id_for_view}")
                            await interaction.followup.send(discord_cdn_url, view=view)
                        except Exception as e_upload:
                            logger.error(f"Failed to upload new image to private channel for user {target_user_id_for_view}: {e_upload}", exc_info=True)
                            await interaction.followup.send(file=discord.File(io.BytesIO(image_data), filename="profile.png"), view=view)
                    else:
                        logger.warning(f"Could not get private channel {self.private_channel_id}, sending file directly for user {target_user_id_for_view}")
                        await interaction.followup.send(file=discord.File(io.BytesIO(image_data), filename="profile.png"), view=view)
                else:
                    logger.info(f"No private channel ID configured, sending file directly for user {target_user_id_for_view}")
                    await interaction.followup.send(file=discord.File(io.BytesIO(image_data), filename="profile.png"), view=view)

        except UserNotFoundError as e:
            logger.warning(f"Profile command failed: User not found ({target_user_id_for_view}): {e}")
            await interaction.followup.send(f"❌ 找不到用戶 {target_user_for_view.mention} 的檔案資料。", ephemeral=True)
        except Exception as e:
            logger.error(f"Failed to process profile view for {target_user_id_for_view}: {e}", exc_info=True)
            if not interaction.response.is_done():
                 await interaction.followup.send("❌ 獲取檔案時發生嚴重錯誤，請稍後再試。", ephemeral=True)
            else: 
                try:
                    await interaction.edit_original_response(content="❌ 獲取檔案時發生嚴重錯誤，請稍後再試。", view=None, attachments=[])
                except discord.NotFound:
                     await interaction.followup.send("❌ 獲取檔案時發生嚴重錯誤，且原始訊息已無法編輯。",ephemeral=True)
                except Exception:
                     await interaction.followup.send("❌ 獲取檔案時發生嚴重錯誤，請稍後再試。", ephemeral=True)

    @commands.Cog.listener()
    async def on_interaction(self, interaction: discord.Interaction):
        if interaction.type != discord.InteractionType.component:
            return
            
        custom_id = interaction.data.get('custom_id')
        if not custom_id:
            return

        if custom_id == 'profile_like_button':
            pass


async def setup(bot: commands.Bot, **kwargs):
    # Get PlaywrightManager from the bot instance itself
    playwright_manager = getattr(bot, 'playwright_manager', None)
    
    if not playwright_manager:
        logger.error("PlaywrightManager not found on bot object (bot.playwright_manager). ProfileCog will not be loaded.")
        return # Do not load the cog if PlaywrightManager is missing
    
    # Check for ProfileService on the bot object (as before)
    if not hasattr(bot, 'profile_service'):
        logger.error("ProfileService not found on bot object during ProfileCog setup. Cog may not function correctly, but will still load as PlaywrightManager is present.")
        # Decide if you want to prevent loading if ProfileService is also critical, for now, it just logs a warning.

    try:
        await bot.add_cog(ProfileCog(bot, playwright_manager))
        logger.info("ProfileCog added to bot.")
    except Exception as e:
        logger.error(f"Failed to add ProfileCog to bot: {e}", exc_info=True)

# Ensure PlaywrightManager is imported if not already at the top