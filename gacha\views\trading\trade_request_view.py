import discord
from discord.ui import View, Button, button
import logging
from typing import Dict, TYPE_CHECKING
from gacha.utils.interaction_utils import check_user_permission
from gacha.services.trading.card_trading_service import InvalidTradeParametersError, UnauthorizedTradeAccessError, InsufficientCardQuantityError, TradeNotFoundError
from gacha.exceptions import InsufficientBalanceError, DatabaseOperationError
logger = logging.getLogger(__name__)

class TradeRequestView(View):

    def __init__(self, bot, trade_details: Dict, trading_service, embed_builder):
        super().__init__(timeout=1800)
        self.bot = bot
        self.trade_details = trade_details
        self.trading_service = trading_service
        self.embed_builder = embed_builder
        self.trade_id = trade_details.get('trade_id')
        self.initiator_id = trade_details.get('initiator_id')
        self.target_user_id = trade_details.get('receiver_id')
        self.accept_button = Button(label='接受交易', style=discord.ButtonStyle.green, custom_id=f'trade_accept:{self.trade_id}')
        self.reject_button = Button(label='拒絕交易', style=discord.ButtonStyle.red, custom_id=f'trade_reject:{self.trade_id}')
        self.accept_button.callback = self.accept_trade_callback
        self.reject_button.callback = self.reject_trade_callback
        self.add_item(self.accept_button)
        self.add_item(self.reject_button)

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if not await check_user_permission(interaction, self.target_user_id, '這不是給你的交易請求！😠'):
            logger.warning('TradeRequestView: Interaction check failed for trade %s. User %s (name: %s) is not target %s.', self.trade_id, interaction.user.id, interaction.user.name, self.target_user_id)
            return False
        logger.debug('TradeRequestView: Interaction check passed for trade %s. User: %s (name: %s)', self.trade_id, interaction.user.id, interaction.user.name)
        return True

    async def on_timeout(self):
        logger.info('Trade request %s timed out.', self.trade_id)
        self.disable_buttons()
        pass

    def disable_buttons(self):
        self.accept_button.disabled = True
        self.reject_button.disabled = True

    async def accept_trade_callback(self, interaction: discord.Interaction):
        logger.debug('TradeRequestView: accept_trade_callback for trade %s by user %s (name: %s).', self.trade_id, interaction.user.id, interaction.user.name)
        try:
            await interaction.response.defer(ephemeral=True)
            logger.debug('TradeRequestView: Interaction deferred for accept_trade_callback (trade %s).', self.trade_id)
        except discord.NotFound:
            logger.warning('Interaction for trade %s (accept) not found or already responded to during defer. Interaction ID: %s', self.trade_id, interaction.id)
            try:
                if interaction.followup and (not interaction.is_expired()):
                    await interaction.followup.send('抱歉，此交易互動已過期或無法處理 (defer failed)。', ephemeral=True)
            except Exception as e_followup:
                logger.error('Failed to send followup for NotFound error in accept_trade_callback (defer stage, trade %s): %s', self.trade_id, e_followup)
            return
        except discord.InteractionResponded:
            logger.warning('Interaction for trade %s (accept) has already been responded to during defer. Interaction ID: %s', self.trade_id, interaction.id)
            return
        except Exception as e_defer:
            logger.error('General error in accept_trade_callback for trade %s during defer: %s', self.trade_id, e_defer, exc_info=True)
            try:
                if interaction.followup and (not interaction.is_expired()):
                    await interaction.followup.send('處理您的請求時發生初步錯誤，請稍後再試。', ephemeral=True)
            except Exception as e_notify:
                logger.error('Failed to send error notification in accept_trade_callback (defer error stage, trade %s): %s', self.trade_id, e_notify)
            return
        try:
            if not self.trading_service:
                logger.error('TradeRequestView: trading_service is not initialized. Cannot process trade_accept for trade_id: %s.', self.trade_id)
                await interaction.followup.send('交易服務目前不可用，請稍後再試。', ephemeral=True)
                return
            if not self.embed_builder:
                logger.error('TradeRequestView: embed_builder is not initialized. Cannot process trade_accept for trade_id: %s.', self.trade_id)
                await interaction.followup.send('交易顯示服務目前不可用，請稍後再試。', ephemeral=True)
                return
            
            logger.info('TradeRequestView: Processing trade_accept for trade_id: %s, user: %s', self.trade_id, interaction.user.id)
            
            try:
                # 獲取最新的交易詳情
                current_trade_details = await self.trading_service.get_trade_details(self.trade_id)
                
                if current_trade_details.get('status') != 'pending':
                    logger.info('TradeRequestView: Trade %s is no longer pending (status: %s) (accept).', self.trade_id, current_trade_details.get('status'))
                    status_embed = await self.embed_builder.build_trade_status_embed(current_trade_details, current_trade_details.get('status', '未知狀態'))
                    if interaction.message:
                        await interaction.message.edit(embed=status_embed, view=None)
                    await interaction.followup.send(f"此交易狀態為「{current_trade_details.get('status')}」，無法再次操作。", ephemeral=True)
                    return
                
                # 接受交易
                await self.trading_service.accept_trade(current_trade_details, interaction.user.id)
                
                # 交易成功
                final_embed = await self.embed_builder.build_trade_status_embed(current_trade_details, '已接受', acting_user=interaction.user)
                if interaction.message:
                    await interaction.message.edit(embed=final_embed, view=None)
                
                notification_message = f'交易 (ID: {self.trade_id}) 已由 {interaction.user.mention} **接受**！ 🎉'
                if interaction.channel:
                    await interaction.channel.send(notification_message)
                else:
                    await interaction.followup.send(notification_message, ephemeral=False)
                
                logger.info('TradeRequestView: Trade %s accepted by %s.', self.trade_id, interaction.user.id)
                
            except TradeNotFoundError:
                logger.warning('TradeRequestView: Trade details not found for %s (accept).', self.trade_id)
                await interaction.followup.send('抱歉，找不到此交易的詳細資訊，可能已被清除或過期。', ephemeral=True)
                if interaction.message:
                    try:
                        await interaction.message.edit(content='此交易已失效。', view=None, embed=None)
                    except Exception as e_edit:
                        logger.warning('TradeRequestView: Failed to edit original message for non-existent trade %s: %s', self.trade_id, e_edit)
            
            except UnauthorizedTradeAccessError as e:
                error_message = str(e)
                logger.warning('TradeRequestView: Unauthorized access for trade %s by user %s: %s', self.trade_id, interaction.user.id, error_message)
                final_embed = await self.embed_builder.build_trade_status_embed(current_trade_details, '接受失敗', reason=error_message, acting_user=interaction.user)
                if interaction.message:
                    await interaction.message.edit(embed=final_embed, view=None)
                await interaction.followup.send(f'交易接受失敗：{error_message}', ephemeral=True)
            
            except InsufficientCardQuantityError as e:
                error_message = str(e)
                logger.warning('TradeRequestView: Insufficient card quantity for trade %s: %s', self.trade_id, error_message)
                final_embed = await self.embed_builder.build_trade_status_embed(current_trade_details, '接受失敗', reason=error_message, acting_user=interaction.user)
                if interaction.message:
                    await interaction.message.edit(embed=final_embed, view=None)
                await interaction.followup.send(f'交易接受失敗：{error_message}', ephemeral=True)
            
            except InsufficientBalanceError as e:
                error_message = str(e)
                logger.warning('TradeRequestView: Insufficient balance for trade %s: %s', self.trade_id, error_message)
                final_embed = await self.embed_builder.build_trade_status_embed(current_trade_details, '接受失敗', reason=error_message, acting_user=interaction.user)
                if interaction.message:
                    await interaction.message.edit(embed=final_embed, view=None)
                await interaction.followup.send(f'交易接受失敗：{error_message}', ephemeral=True)
            
            except InvalidTradeParametersError as e:
                error_message = str(e)
                logger.warning('TradeRequestView: Invalid trade parameters for trade %s: %s', self.trade_id, error_message)
                final_embed = await self.embed_builder.build_trade_status_embed(current_trade_details, '接受失敗', reason=error_message, acting_user=interaction.user)
                if interaction.message:
                    await interaction.message.edit(embed=final_embed, view=None)
                await interaction.followup.send(f'交易接受失敗：{error_message}', ephemeral=True)
            
            except DatabaseOperationError as e:
                error_message = str(e)
                logger.error('TradeRequestView: Database error during trade %s acceptance: %s', self.trade_id, error_message)
                final_embed = await self.embed_builder.build_trade_status_embed(current_trade_details, '接受失敗', reason='資料庫操作錯誤', acting_user=interaction.user)
                if interaction.message:
                    await interaction.message.edit(embed=final_embed, view=None)
                await interaction.followup.send(f'交易接受失敗：資料庫操作錯誤', ephemeral=True)
                
        except discord.NotFound:
            logger.warning('TradeRequestView: Interaction for trade %s (accept) not found or message deleted during business logic. Interaction ID: %s', self.trade_id, interaction.id)
            try:
                await interaction.followup.send('抱歉，處理您的交易接受請求時發生問題 (互動可能已過期)。', ephemeral=True)
            except Exception:
                pass
        except discord.Forbidden:
            logger.error('TradeRequestView: Forbidden error processing accept_trade for trade %s. Bot might lack permissions. Interaction ID: %s', self.trade_id, interaction.id)
            try:
                await interaction.followup.send('抱歉，我沒有足夠的權限來完成此操作。', ephemeral=True)
            except Exception:
                pass
        except Exception as e:
            logger.error('TradeRequestView: General error processing accept_trade for trade %s: %s', self.trade_id, e, exc_info=True)
            try:
                await interaction.followup.send('處理您的接受請求時發生未知錯誤，請稍後再試或聯繫管理員。', ephemeral=True)
            except Exception:
                pass

    async def reject_trade_callback(self, interaction: discord.Interaction):
        logger.debug('TradeRequestView: reject_trade_callback for trade %s by user %s (name: %s).', self.trade_id, interaction.user.id, interaction.user.name)
        try:
            await interaction.response.defer(ephemeral=True)
            logger.debug('TradeRequestView: Interaction deferred for reject_trade_callback (trade %s).', self.trade_id)
        except discord.NotFound:
            logger.warning('Interaction for trade %s (reject) not found or already responded to during defer. Interaction ID: %s', self.trade_id, interaction.id)
            try:
                if interaction.followup and (not interaction.is_expired()):
                    await interaction.followup.send('抱歉，此交易互動已過期或無法處理 (defer failed)。', ephemeral=True)
            except Exception as e_followup:
                logger.error('Failed to send followup for NotFound error in reject_trade_callback (defer stage, trade %s): %s', self.trade_id, e_followup)
            return
        except discord.InteractionResponded:
            logger.warning('Interaction for trade %s (reject) has already been responded to during defer. Interaction ID: %s', self.trade_id, interaction.id)
            return
        except Exception as e_defer:
            logger.error('General error in reject_trade_callback for trade %s during defer: %s', self.trade_id, e_defer, exc_info=True)
            try:
                if interaction.followup and (not interaction.is_expired()):
                    await interaction.followup.send('處理您的請求時發生初步錯誤，請稍後再試。', ephemeral=True)
            except Exception as e_notify:
                logger.error('Failed to send error notification in reject_trade_callback (defer error stage, trade %s): %s', self.trade_id, e_notify)
            return
        try:
            if not self.trading_service:
                logger.error('TradeRequestView: trading_service is not initialized. Cannot process trade_reject for trade_id: %s.', self.trade_id)
                await interaction.followup.send('交易服務目前不可用，請稍後再試。', ephemeral=True)
                return
            if not self.embed_builder:
                logger.error('TradeRequestView: embed_builder is not initialized. Cannot process trade_reject for trade_id: %s.', self.trade_id)
                await interaction.followup.send('交易顯示服務目前不可用，請稍後再試。', ephemeral=True)
                return
            
            logger.info('TradeRequestView: Processing trade_reject for trade_id: %s, user: %s', self.trade_id, interaction.user.id)
            
            try:
                # 獲取最新的交易詳情
                current_trade_details = await self.trading_service.get_trade_details(self.trade_id)
                
                if current_trade_details.get('status') != 'pending':
                    logger.info('TradeRequestView: Trade %s is no longer pending (status: %s) (reject).', self.trade_id, current_trade_details.get('status'))
                    status_embed = await self.embed_builder.build_trade_status_embed(current_trade_details, current_trade_details.get('status', '未知狀態'))
                    if interaction.message:
                        await interaction.message.edit(embed=status_embed, view=None)
                    await interaction.followup.send(f"此交易狀態為「{current_trade_details.get('status')}」，無法再次操作。", ephemeral=True)
                    return
                
                # 拒絕交易
                reason = await self.trading_service.cancel_trade(current_trade_details, interaction.user.id, reason='用戶拒絕交易')
                
                # 交易拒絕成功
                final_embed = await self.embed_builder.build_trade_status_embed(current_trade_details, '已拒絕', reason=reason, acting_user=interaction.user)
                if interaction.message:
                    await interaction.message.edit(embed=final_embed, view=None)
                
                notification_message = f'交易 (ID: {self.trade_id}) 已由 {interaction.user.mention} **拒絕**。 🚫'
                if interaction.channel:
                    await interaction.channel.send(notification_message)
                else:
                    await interaction.followup.send(notification_message, ephemeral=False)
                
                logger.info('TradeRequestView: Trade %s rejected by %s.', self.trade_id, interaction.user.id)
                
            except TradeNotFoundError:
                logger.warning('TradeRequestView: Trade details not found for %s (reject).', self.trade_id)
                await interaction.followup.send('抱歉，找不到此交易的詳細資訊，可能已被清除或過期。', ephemeral=True)
                if interaction.message:
                    try:
                        await interaction.message.edit(content='此交易已失效。', view=None, embed=None)
                    except Exception as e_edit:
                        logger.warning('TradeRequestView: Failed to edit original message for non-existent trade %s: %s', self.trade_id, e_edit)
            
            except UnauthorizedTradeAccessError as e:
                error_message = str(e)
                logger.warning('TradeRequestView: Unauthorized access for trade %s by user %s: %s', self.trade_id, interaction.user.id, error_message)
                final_embed = await self.embed_builder.build_trade_status_embed(current_trade_details, '拒絕失敗', reason=error_message, acting_user=interaction.user)
                if interaction.message:
                    await interaction.message.edit(embed=final_embed, view=None)
                await interaction.followup.send(f'交易拒絕失敗：{error_message}', ephemeral=True)
            
            except InvalidTradeParametersError as e:
                error_message = str(e)
                logger.warning('TradeRequestView: Invalid trade parameters for trade %s: %s', self.trade_id, error_message)
                final_embed = await self.embed_builder.build_trade_status_embed(current_trade_details, '拒絕失敗', reason=error_message, acting_user=interaction.user)
                if interaction.message:
                    await interaction.message.edit(embed=final_embed, view=None)
                await interaction.followup.send(f'交易拒絕失敗：{error_message}', ephemeral=True)
                
        except discord.NotFound:
            logger.warning('TradeRequestView: Interaction for trade %s (reject) in Cog not found or message deleted. Interaction ID: %s', self.trade_id, interaction.id)
            try:
                await interaction.followup.send('抱歉，處理您的交易拒絕請求時發生問題 (互動可能已過期)。', ephemeral=True)
            except Exception:
                pass
        except discord.Forbidden:
            logger.error('TradeRequestView: Forbidden error processing reject_trade for trade %s. Bot might lack permissions. Interaction ID: %s', self.trade_id, interaction.id)
            try:
                await interaction.followup.send('抱歉，我沒有足夠的權限來完成此操作。', ephemeral=True)
            except Exception:
                pass
        except Exception as e:
            logger.error('TradeRequestView: General error processing reject_trade for trade %s: %s', self.trade_id, e, exc_info=True)
            try:
                await interaction.followup.send('處理您的拒絕請求時發生未知錯誤，請稍後再試或聯繫管理員。', ephemeral=True)
            except Exception:
                pass