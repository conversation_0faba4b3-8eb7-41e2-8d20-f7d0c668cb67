import os
import yaml
from pydantic import BaseModel, Field, ValidationError, field_validator
from decimal import Decimal
from pydantic_settings import BaseSettings, SettingsConfigDict, PydanticBaseSettingsSource
from typing import Dict, List, Optional, Any, Type, Tuple, Union
# 簡化日誌導入
from utils.logger import logger
from gacha.models.shop_models import ShopItemDefinition

# 設定文件路徑
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
dotenv_path = os.path.join(project_root, '.env')

# 立即載入環境變數，確保所有後續讀取操作都能獲取到正確的值
from dotenv import load_dotenv
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path, override=True)
    logger.info(f"環境變數從 {dotenv_path} 載入完成")
    # 輸出幾個關鍵環境變數的值，方便除錯
    logger.info(f"GACHA_DB_NAME = {os.getenv('GACHA_DB_NAME', '未設置')}")
    logger.info(f"PG_HOST = {os.getenv('PG_HOST', '未設置')}")
    logger.info(f"PG_PORT = {os.getenv('PG_PORT', '未設置')}")
    logger.info(f"PG_USER = {os.getenv('PG_USER', '未設置')}")
else:
    logger.warning(f"找不到環境變數文件: {dotenv_path}")

CONFIG_FILE_PATH = os.path.join(project_root, 'config.yaml')
GACHA_SETTINGS_FILE_PATH = os.path.join(project_root, 'gacha_settings.yaml')
UI_SETTINGS_FILE_PATH = os.path.join(project_root, 'ui_settings.yaml')

# 推遲載入環境變數和配置 logger，確保其他模組先完成環境變數設定
# 將在 load_settings() 中正式初始化

# 新增 GachaNotificationSettings 模型
class GachaNotificationSettings(BaseModel):
    enabled: bool = False
    webhook_url: Optional[str] = None
    notify_rarities: Dict[str, List[int]] = Field(default_factory=dict)

# 暫時定義一個簡單的打印函數，以防在 logger 配置前需要輸出信息
def _safe_print(msg, *args):
    if args:
        print(msg % args)
    else:
        print(msg)

class PoolRarityConfigItem(BaseModel):
    """Represents an item in the pool rarity config list."""
    rarity_level: int
    probability: float

class PoolConfigItem(BaseModel):
    """Represents an item in the pool config list."""
    pool_name: str
    probability: float

class PoolConfigurationDetail(BaseModel):
    name: str
    description: str
    pools: List[str]

class LeaderboardConfigDetail(BaseModel):
    title: str
    description: str
    color: int
    category: str
    default_for_category: Optional[bool] = False

class ProfileSettings(BaseModel):
    image_local_base_path: str = "downloaded_gacha_master_cards"
    # Add other profile-specific settings here if needed in the future

class GachaCoreSettings(BaseModel):
    """Settings related to the core gacha mechanics, prices, pools etc."""
    rarity_sort_values: Dict[int, int] = Field(default_factory=dict)
    pool_rarity_prices: Dict[str, Dict[int, int]] = Field(default_factory=dict)
    pool_costs: Dict[str, int] = Field(default_factory=dict)
    all_pool_rarity_configs: Dict[str, List[PoolRarityConfigItem]] = Field(default_factory=dict)
    pool_config: List[PoolConfigItem] = Field(default_factory=list)
    max_star_level: int = 35
    stars_per_tier: int = 5
    pool_type_names: Dict[str, str] = Field(default_factory=dict)
    pool_type_prefixes: Dict[str, str] = Field(default_factory=dict)
    pool_configurations: Dict[str, PoolConfigurationDetail] = Field(default_factory=dict)
    rarity_mapping: Dict[str, List[int]] = Field(default_factory=dict)
    leaderboard_config: Dict[str, LeaderboardConfigDetail] = Field(default_factory=dict)
    leaderboard_items_per_page: int = 5
    mixed_pool_draw_config: List[PoolConfigItem] = Field(default_factory=list)
    trade_fee_percentage: float = 0.03
    min_trade_fee: int = 10
    ticket_shop_items: Dict[str, ShopItemDefinition] = Field(default_factory=dict)
    wish_max_slots: int = Field(default=10, description='Maximum number of wish slots a user can have.')
    wish_max_power_level: int = Field(default=10, description='Maximum wish power level a user can reach.')
    default_wish_slots: int = Field(default=1, description='Default number of wish slots for new users.')
    default_wish_power_level: int = Field(default=1, description='Default wish power level for new users.')
    default_wish_multiplier: float = Field(default=3.0, description='Default wish chance multiplier for power level 1.')
    wish_slot_costs: Dict[int, int] = Field(default_factory=lambda: {1: 10000, 2: 25000, 3: 50000, 4: 100000, 5: 250000, 6: 500000, 7: 1000000, 8: 2000000, 9: 5000000}, description='Costs to upgrade to the next wish slot (key is current_slots, value is cost for next).')
    wish_power_costs: Dict[int, int] = Field(default_factory=lambda: {1: 20000, 2: 40000, 3: 80000, 4: 160000, 5: 300000, 6: 500000, 7: 800000, 8: 1200000, 9: 2000000}, description='Costs to upgrade to the next wish power level (key is current_level, value is cost for next).')
    wish_power_multipliers: Dict[int, float] = Field(default_factory=lambda: {1: 3.0, 2: 4.0, 3: 5.0, 4: 6.0, 5: 8.0, 6: 10.0, 7: 12.0, 8: 15.0, 9: 18.0, 10: 20.0}, description='Wish chance multipliers for each power level.')
    economy_daily_reward: int = Field(default=5000, description='Amount of oil for daily reward.')
    economy_hourly_reward: int = Field(default=2000, description='Amount of oil for hourly reward.')
    economy_hourly_claim_key_prefix: str = Field(default='gacha:hourly_claim:', description='Redis key prefix for hourly claims.')
    profile: Optional[ProfileSettings] = Field(default_factory=ProfileSettings)

    @field_validator('ticket_shop_items', mode='before')
    @classmethod
    def convert_ticket_shop_items_to_model(cls, v: Any) -> Dict[str, ShopItemDefinition]:
        if isinstance(v, dict):
            validated_items: Dict[str, ShopItemDefinition] = {}
            for item_id, item_data in v.items():
                if isinstance(item_data, dict):
                    try:
                        validated_items[item_id] = ShopItemDefinition(**{'id': item_id, **item_data})
                    except ValidationError as e:
                        logger.error("Validation error for ticket_shop_item '%s': %s. Data: %s", item_id, e, item_data)
                else:
                    logger.warning("Skipping ticket_shop_item '%s' as its data is not a dictionary. Data: %s", item_id, item_data)
            return validated_items
        elif v is None:
            return {}
        logger.warning('ticket_shop_items in YAML is not a dictionary, received type: %s. Value: %s', type(v), v)
        return {}

    @field_validator('all_pool_rarity_configs', mode='before')
    @classmethod
    def convert_tuple_list_to_model_list(cls, v):
        if isinstance(v, dict):
            new_v = {}
            for pool_name, config_list in v.items():
                if isinstance(config_list, list):
                    new_v[pool_name] = [PoolRarityConfigItem(rarity_level=item[0], probability=item[1]) for item in config_list if isinstance(item, (list, tuple)) and len(item) == 2]
                else:
                    new_v[pool_name] = []
            return new_v
        return v

    @field_validator('pool_config', mode='before')
    @classmethod
    def convert_tuple_list_to_pool_config_list(cls, v):
        if isinstance(v, list):
            return [PoolConfigItem(pool_name=item[0], probability=item[1]) for item in v if isinstance(item, (list, tuple)) and len(item) == 2]
        return v

    @field_validator('mixed_pool_draw_config', mode='before')
    @classmethod
    def convert_tuple_list_to_mixed_pool_config_list(cls, v):
        if isinstance(v, list):
            return [PoolConfigItem(pool_name=item[0], probability=item[1]) for item in v if isinstance(item, (list, tuple)) and len(item) == 2]
        return v

class UIButtonEmojis(BaseModel):
    """Holds UI button emojis."""
    old_card: Optional[str] = '<a:sw:1365447243863429273>'
    favorite: Optional[str] = '<a:pu:1365482490478989353>'
    new_card: Optional[str] = '<a:sr:1357714854244515936>'
    next_page: Optional[str] = '➡️'
    prev_page: Optional[str] = '⬅️'
    first_page: Optional[str] = '⏪'
    last_page: Optional[str] = '⏩'
    confirm: Optional[str] = '✅'
    cancel: Optional[str] = '❌'

class UISettings(BaseModel):
    """Settings related to UI elements like emojis, colors, display names."""
    default_rarity_emojis: Dict[int, str] = Field(default_factory=dict)
    rarity_emojis: Dict[str, Dict[int, str]] = Field(default_factory=dict, alias='pool_specific_rarity_emojis')
    default_encyclopedia_rarity_emojis: Dict[int, str] = Field(default_factory=dict)
    encyclopedia_rarity_emojis: Dict[str, Dict[int, str]] = Field(default_factory=dict, alias='pool_specific_encyclopedia_rarity_emojis')
    rarity_images: Dict[str, Dict[int, str]] = Field(default_factory=dict, alias='rarity_images_url')
    star_emojis: Dict[int, str] = Field(default_factory=dict)
    ui_button_emojis: UIButtonEmojis = Field(default_factory=UIButtonEmojis)
    pool_type_emojis: Dict[str, str] = Field(default_factory=dict)
    user_friendly_display_names: Dict[str, str] = Field(default_factory=dict, alias='user_friendly_rarity_display_names')
    rarity_display_codes: Dict[int, str] = Field(default_factory=dict)
    rarity_colors_int: Dict[str, Dict[int, int]] = Field(default_factory=dict)
    completion_indicator_emojis: Dict[str, str] = Field(default_factory=dict)
    oil_emoji: str = '<:oil1:1368446294594424872>'
    news_type_readable_names: Dict[str, str] = Field(default_factory=dict)
    character_archetype_readable_names: Dict[str, str] = Field(default_factory=dict)
    news_type_colors_int: Dict[str, int] = Field(default_factory=dict)

class TaskFrequencies(BaseModel):
    calculate_supply_demand_modifier_hours: int = 1
    update_stock_prices_minutes: int = 10
    calculate_gacha_category_stock_influence_minutes: int = 15
    cleanup_expired_news_effects_minutes: int = 5
    full_price_recalculation_hours: int = 24
    news_scheduler_manager_minutes: int = 5
    calculate_daily_anchor_prices_hours: int = 24
    check_stock_lifecycle_minutes: int = 15

class TaskInitialDelays(BaseModel):
    calculate_supply_demand_modifier_seconds: int = 10
    update_stock_prices_seconds: int = 15
    news_scheduler_manager_seconds: int = 5
    calculate_gacha_category_stock_influence_seconds: int = 20
    cleanup_expired_news_effects_seconds: int = 25
    full_price_recalculation_seconds: int = 300
    calculate_daily_anchor_prices_seconds: int = 60
    check_stock_lifecycle_seconds: int = 75

class SupplyDemandWeights(BaseModel):
    supply: float = 0.5
    owner_diversity_effect_on_supply: float = 0.2
    wish: float = 0.3
    fav: float = 0.2

class SupplyDemandModifierDefaults(BaseModel):
    min: float = 0.1
    max: float = 10.0

class SupplyDemandConfig(BaseModel):
    weights: SupplyDemandWeights = Field(default_factory=SupplyDemandWeights)
    smooth_factor: float = 0.1
    modifier_defaults: SupplyDemandModifierDefaults = Field(default_factory=SupplyDemandModifierDefaults)

class StockMarketNewsImpact(BaseModel):
    positive_min: float = 0.015
    positive_max: float = 0.05
    negative_min: float = -0.05
    negative_max: float = -0.015
    neutral: float = 0.0

class StockMarketTempNewsEffect(BaseModel):
    offset_positive: float = 0.05
    offset_negative: float = -0.05
    duration_hours_min: int = 2
    duration_hours_max: int = 5

class StockMarketCategoryInfluenceClamp(BaseModel):
    min: float = 0.5
    max: float = 2.0

class BottomPriceSuppressionConfig(BaseModel):
    threshold_factor: float = 1.1
    upward_momentum_suppression_factor: float = 0.3
    downward_pressure_chance: float = 0.15
    downward_pressure_percent: float = 0.003

class StockMarketConfig(BaseModel):
    default_base_volatility: float = 0.01
    default_volatility_factor: float = 1.5
    news_impact: StockMarketNewsImpact = Field(default_factory=StockMarketNewsImpact)
    temp_news_effect: StockMarketTempNewsEffect = Field(default_factory=StockMarketTempNewsEffect)
    price_change_to_modifier_offset_factor: float = 0.5
    category_influence_clamp: StockMarketCategoryInfluenceClamp = Field(default_factory=StockMarketCategoryInfluenceClamp)
    asset_news_cooldown_minutes: int = 30
    default_news_type_generation_interval_minutes: int = 120
    news_type_generation_intervals_minutes: Dict[str, int] = Field(default_factory=dict)
    strong_news_impact_threshold: float = 0.035
    sentiment_inertia_window_minutes: int = 60
    conflicting_news_dampening_factor: float = 0.5
    transaction_fee_rate: float = 0.03
    minimum_transaction_fee: float = 5.0
    per_share_fee_amount: float = 0.05
    chart_thread_pool_workers: int = 2
    chart_figsize_width: float = 10.0
    chart_figsize_height: float = 5.0
    chart_date_format: str = '%Y-%m-%d %H:%M'
    chart_max_xticks: int = 7
    chart_dpi: int = 100
    price_history_days: int = 7
    default_initial_anchor_price: Decimal = Field(default=Decimal('300.00'))
    default_stock_influence_weight: Decimal = Field(default=Decimal('1.0'))
    news_relevance_window_hours: int = 24
    anchor_price_fallback_days_history: int = 3
    anchor_price_max_stale_days: int = 2
    anchor_price_min_trading_days_for_avg: int = 1
    global_min_asset_price: float = 1.0
    global_max_asset_price: float = 1000000.0
    max_price_change_percent: float = 0.2
    min_initial_total_shares: int = 15000
    max_initial_total_shares: int = 100000
    st_trigger_consecutive_checks_at_min_price: int = 72
    st_trigger_min_market_cap: int = 150000
    st_trigger_consecutive_checks_below_market_cap: int = 72
    st_trigger_low_price_threshold: float = 1.5
    st_recovery_min_price: float = 1.5
    st_recovery_min_market_cap: int = 100000
    delist_trigger_consecutive_checks_in_st: int = 432
    delist_trigger_min_market_cap: int = 20000
    delist_trigger_consecutive_checks_below_delist_market_cap: int = 72
    delisted_buyback_avg_price_checks: int = 10
    delisted_buyback_price_percentage: float = 0.1
    delisted_buyback_min_price: float = 0.01
    delisted_buyback_max_price: float = 0.1
    min_active_companies: int = 10
    st_bottom_price_suppression: Optional[BottomPriceSuppressionConfig] = None
    bottom_price_suppression: BottomPriceSuppressionConfig = Field(default_factory=BottomPriceSuppressionConfig)

class GachaStockIntegrationConfig(BaseModel):
    tasks: TaskFrequencies = Field(default_factory=TaskFrequencies)
    supply_demand: SupplyDemandConfig = Field(default_factory=SupplyDemandConfig)
    stock_market: StockMarketConfig = Field(default_factory=StockMarketConfig)
    task_initial_delays: TaskInitialDelays = Field(default_factory=TaskInitialDelays)

class MarketStatsUpdaterConfig(BaseModel):
    batch_size: int = 500
    batch_interval_seconds: float = 1.0
    single_event_queue_size: int = 50000
    favorite_batch_size: int = 1000

class PriceUpdateServiceConfig(BaseModel):
    queue_max_size: int = 10000
    batch_size: int = 100
    batch_interval_seconds: float = 5.0

class AiAssistantConfig(BaseModel):
    # 保留原有的字段作為可能的後備或通用配置
    api_endpoint: Optional[str] = 'https://qaz17899-vertex.hf.space' 
    default_model: Optional[str] = 'gemini-2.5-pro-exp-03-25'
    
    # 新增主 API 配置字段
    primary_api_endpoint: Optional[str] = None
    primary_default_model: Optional[str] = None
    
    # 新增備用 API 配置字段
    backup_api_endpoint: Optional[str] = None
    backup_default_model: Optional[str] = None

    # prompt_template_path: Optional[str] = "ai_assistant/prompts/market_news_prompt.txt"

    class Config:
        extra = 'ignore' # 確保 Pydantic 會忽略 YAML 中未在模型中定義的額外字段

class ApplicationConfig(BaseModel):
    """空的配置類，僅作為佔位符"""
    pass

class DatabaseConfig(BaseModel):
    pool_min_size: int = 5
    pool_max_size: int = 20

class DiscordConfig(BaseModel):
    """Discord相關配置"""
    private_channel_id: Optional[int] = None

# 恢復YAML配置源，但簡化實現
class YamlConfigSettingsSource(PydanticBaseSettingsSource):
    """從YAML文件加載配置變數"""

    def __init__(self, settings_cls: Type[BaseSettings], yaml_file_path: str):
        super().__init__(settings_cls)
        self.yaml_file_path = yaml_file_path
        self.yaml_data = self._load_yaml()
        if os.path.exists(yaml_file_path):
            logger.info(f'YAML配置源初始化: {yaml_file_path}')

    def _load_yaml(self) -> Dict[str, Any]:
        """加載YAML文件中的配置"""
        if os.path.exists(self.yaml_file_path):
            try:
                with open(self.yaml_file_path, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                    if data is None:
                        return {}
                    if isinstance(data, dict):
                        return data
                    return {}
            except Exception as e:
                logger.error(f'YAML文件加載錯誤 {self.yaml_file_path}: {e}')
        return {}

    def get_field_value(self, field: Any, field_name: str) -> Tuple[Any, str, bool]:
        """獲取字段值，簡化實現"""
        field_value = self.yaml_data.get(field_name)
        is_complex = False
        model_field = self.settings_cls.model_fields.get(field_name)
        if model_field:
            annotation = model_field.annotation
            if hasattr(annotation, '__origin__') and annotation.__origin__ is Union:
                args = [arg for arg in getattr(annotation, '__args__', []) if arg is not type(None)]
                if args and isinstance(args[0], type) and issubclass(args[0], BaseModel):
                    is_complex = True
            elif isinstance(annotation, type) and issubclass(annotation, BaseModel):
                is_complex = True
        return (field_value, field_name, is_complex and isinstance(field_value, dict))

    def prepare_field_value(self, field_name: str, field: Any, value: Any, value_is_complex: bool) -> Any:
        return value

    def __call__(self) -> Dict[str, Any]:
        return self.yaml_data

class AppSettings(BaseSettings):
    DISCORD_TOKEN: Optional[str] = None
    AI_API_KEY: Optional[str] = None
    DATABASE_URL: Optional[str] = None
    REDIS_URL: Optional[str] = None
    PG_HOST: str = 'localhost'
    PG_PORT: int = 5432
    PG_USER: str = 'postgres'
    PG_PASSWORD: str = 'postgres'
    GACHA_DB_NAME: str = 'gacha_database'
    REDIS_HOST: str = 'localhost'
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    MARKET_STATS_UPDATES_STREAM_NAME: str = 'market_stats:updates'
    REDIS_ENABLED: bool = False
    REDIS_EVENT_BATCH_SIZE: int = 5000
    REDIS_STREAM_MAXLEN: int = 1000000
    application: ApplicationConfig = Field(default_factory=ApplicationConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    discord: DiscordConfig = Field(default_factory=DiscordConfig)
    gacha_stock_integration: GachaStockIntegrationConfig = Field(default_factory=GachaStockIntegrationConfig)
    market_stats_updater: MarketStatsUpdaterConfig = Field(default_factory=MarketStatsUpdaterConfig)
    price_update_service: PriceUpdateServiceConfig = Field(default_factory=PriceUpdateServiceConfig)
    ai_assistant: AiAssistantConfig = Field(default_factory=AiAssistantConfig)
    gacha_core_settings: GachaCoreSettings = Field(default_factory=GachaCoreSettings)
    ui_settings: UISettings = Field(default_factory=UISettings)
    gacha_notification_settings: GachaNotificationSettings = Field(default_factory=GachaNotificationSettings)
    
    @classmethod
    def settings_customise_sources(cls, settings_cls: Type[BaseSettings], init_settings: PydanticBaseSettingsSource, env_settings: PydanticBaseSettingsSource, dotenv_settings: PydanticBaseSettingsSource, file_secret_settings: PydanticBaseSettingsSource) -> Tuple[PydanticBaseSettingsSource, ...]:
        """自定義設定源優先級，確保環境變數優先於YAML"""
        logger.info('設置配置源優先級: 環境變數 > .env > YAML')
        return (
            init_settings,    # 1. 初始化時直接提供的設定
            env_settings,     # 2. 環境變數
            dotenv_settings,  # 3. .env文件中的設定
            # YAML配置源放在環境變數之後，確保環境變數優先
            YamlConfigSettingsSource(settings_cls, CONFIG_FILE_PATH),        
            YamlConfigSettingsSource(settings_cls, GACHA_SETTINGS_FILE_PATH),
            YamlConfigSettingsSource(settings_cls, UI_SETTINGS_FILE_PATH),
            file_secret_settings
        )
    
    model_config = SettingsConfigDict(env_file=dotenv_path, env_file_encoding='utf-8', extra='ignore', validate_assignment=True)
    
    @field_validator('DATABASE_URL', mode='before')
    @classmethod
    def assemble_db_url(cls, v, info=None):
        """
        組裝數據庫連接URL - 強制使用環境變數
        
        我們的原則是：數據庫連接信息應該只從環境變數讀取，不應該存在於YAML配置文件中
        這確保了敏感信息的安全性，並允許在不同環境中輕鬆切換數據庫
        """
        # 從環境變數中獲取組件，確保優先使用環境變數
        host = os.getenv('PG_HOST', 'localhost')
        port_str = os.getenv('PG_PORT', '5432')
        user = os.getenv('PG_USER', 'postgres')
        password = os.getenv('PG_PASSWORD', 'postgres')
        db_name = os.getenv('GACHA_DB_NAME', 'gacha_database')
        port = int(port_str) if port_str and port_str.isdigit() else 5432
        
        logger.info(f"組裝數據庫URL (從環境變數): 主機={host}, 端口={port}, 用戶={user}, 數據庫={db_name}")
        
        # 組裝URL - 注意這裡我們直接使用環境變數的值，忽略任何可能從YAML中讀取的值
        return f'postgresql://{user}:{password}@{host}:{port}/{db_name}'

    @field_validator('REDIS_URL', mode='before')
    @classmethod
    def assemble_redis_url(cls, v, info=None):
        """
        組裝Redis URL - 同樣只使用環境變數
        
        與數據庫連接類似，Redis連接也應該只從環境變數讀取
        """
        # 從環境變數獲取Redis配置
        host = os.getenv('REDIS_HOST', 'localhost')
        port = os.getenv('REDIS_PORT', '6379')
        db = os.getenv('REDIS_DB', '0')
        password = os.getenv('REDIS_PASSWORD', '')
        redis_enabled = os.getenv('REDIS_ENABLED', 'False').lower() in ('true', '1', 'yes')
        
        # 如果Redis未啟用，返回None
        if not redis_enabled:
            logger.info("Redis未啟用，不生成連接URL")
            return None
        
        # 組裝URL
        if password:
            url = f'redis://:{password}@{host}:{port}/{db}'
        else:
            url = f'redis://{host}:{port}/{db}'
            
        logger.info(f"組裝Redis URL (從環境變數): {url}")
        return url

def load_settings() -> AppSettings:
    """
    載入應用程式設定
    
    配置優先級:
    1. 環境變數 (最高優先級)
    2. .env文件中的設定
    3. YAML配置文件中的設定
    4. 代碼中的默認值
    
    注意: 數據庫和Redis連接設定只從環境變數中讀取，忽略YAML中的相應設定
    """
    logger.info('載入設定，優先使用環境變數，數據庫設定僅從環境變數讀取')
    return AppSettings()

# 導入 ConfigurationService 和聲明全局變數
from .services.core.configuration_service import ConfigurationService
settings: Optional[AppSettings] = None
config_service: Optional[ConfigurationService] = None

def load_and_initialize_configs():
    """
    載入設定並初始化配置服務
    
    這個函數:
    1. 載入所有設定 (環境變數優先)
    2. 初始化配置服務
    3. 記錄關鍵設定值，便於調試
    """
    global settings, config_service
    current_settings = load_settings()
    settings = current_settings
    config_service = ConfigurationService(current_settings)
    
    # 記錄最終使用的重要配置值
    logger.info(f"最終使用的數據庫名稱: {settings.GACHA_DB_NAME}")
    logger.info(f"最終使用的數據庫URL: {settings.DATABASE_URL}")
    logger.info('設定載入並初始化完成')

try:
    load_and_initialize_configs()
except Exception as e:
    logger.critical(f'無法載入初始設定: {str(e)}')
    raise

if __name__ == '__main__':
    if settings:
        logger.info('測試載入的設定值')
        logger.info(f'數據庫名稱: {settings.GACHA_DB_NAME}')
        logger.info(f'數據庫URL: {settings.DATABASE_URL}')
        logger.info(f'環境變數GACHA_DB_NAME: {os.getenv("GACHA_DB_NAME", "未設置")}')
    else:
        logger.error('設定未能正確載入')