**Key Reminders**:

1.  **Utilize Existing Exceptions**: Do not create new exception types lightly unless necessary.
2.  **Ensure Consistency**: Maintain a consistent error handling style throughout the system.
3.  **Keep it Simple**: Avoid overly complex error handling frameworks.
4.  **Clear Error Messages**: Ensure that both users and developers can understand the cause of errors.

## 6. AI Assistant Tools

Please actively use the following AI assistant tools to improve development efficiency and code quality:
*   **Context7**: Use for parsing library names and retrieving library documentation when library information is needed.
*   **Sequential Thinking**: Use for decomposing, planning, and analyzing complex problems when multi-step thinking or solution exploration is required. 