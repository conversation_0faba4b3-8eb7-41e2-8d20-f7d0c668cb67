"""
處理卡片排序功能的 Handler 類
"""
from typing import TYPE_CHECKING, Any, Dict, Optional
import discord
from gacha.services.core.collection_service import CollectionService
from gacha.services.sorting.sorting_service import SortingService
from utils.logger import logger
from utils.operation_logger import OperationLogger
from ..sort_position_modal import SortPositionModal
from .base_handler import BaseHandler
from gacha.exceptions import (
    CardNotMarkedAsFavoriteError, 
    InvalidPositionError, 
    CardAlreadyAtPositionError,
    CardAlreadyAtTopError,
    CardAlreadyAtBottomError,
    CardNotSortedError,
    PreviousCardNotFoundError,
    NextCardNotFoundError,
    NoCardUpdatesProvidedError,
    NoCardIdsProvidedError,
    GachaSystemError
)

if TYPE_CHECKING:
    from ..card_view import CollectionView

class CardSortHandler(BaseHandler):
    """卡片排序功能處理器

    採用統一的服務訪問模式，所有服務都通過 view.services 統一訪問，
    保持系統架構的一致性和可維護性。
    """

    def __init__(self, view: 'CollectionView'):
        super().__init__(view)

    @property
    def sorting_service(self) -> SortingService:
        """統一的排序服務訪問接口

        遵循系統的服務容器模式，通過 view.services 訪問服務
        """
        return self.view.services.sorting

    @property
    def collection_service(self) -> CollectionService:
        """統一的收藏服務訪問接口

        遵循系統的服務容器模式，通過 view.services 訪問服務
        """
        return self.view.services.collection

    async def sort_mode_callback(self, interaction: discord.Interaction):
        """排序模式按鈕回調函數"""
        logger.info(f"[SORT_CALLBACK] Entered sort_mode_callback for user {interaction.user.id}")

        if not await self.view.interaction_check(interaction):
            logger.warning("[SORT_CALLBACK] interaction_check failed.")
            return

        if not await self.check_cards_available(interaction, '沒有卡片可排序'):
            logger.warning("[SORT_CALLBACK] check_cards_available failed.")
            return
        
        current_card = self.view.current_card
        logger.info(f"[SORT_CALLBACK] current_card id: {current_card.card_id if current_card else 'None'}")

        if not current_card or not hasattr(current_card, 'is_favorite'):
            logger.warning("[SORT_CALLBACK] current_card is None or lacks is_favorite.")
            await interaction.response.send_message('當前頁面卡片數據異常。', ephemeral=True)
            return
        
        logger.info(f"[SORT_CALLBACK] current_card.is_favorite: {current_card.is_favorite}")

        if not current_card.is_favorite:
            logger.warning("[SORT_CALLBACK] Card not favorite, sending ephemeral message.")
            await interaction.response.send_message(content='請先將卡片加入最愛以啟用排序功能。\n只有加入最愛的卡片才能使用自定義排序功能。', ephemeral=True)
            return
            
        logger.info("[SORT_CALLBACK] Entering custom sort mode...")
        try:
            await self._enter_custom_sort_mode(interaction)
            logger.info("[SORT_CALLBACK] Successfully entered custom sort mode.")
        except Exception as e:
            logger.error(f"[SORT_CALLBACK] Error in _enter_custom_sort_mode: {e}", exc_info=True)
            await OperationLogger.log_operation(interaction, "進入排序模式時發生錯誤。", ephemeral=True, level="error")

    async def back_to_function_callback(self, interaction: discord.Interaction):
        """返回功能列表按鈕回調函數 (from Sort Mode)"""
        if not await self.view.interaction_check(interaction):
            return
        await self._exit_custom_sort_mode(interaction)

    async def _refresh_view_for_mode_change(self, interaction: discord.Interaction, cache_message: str):
        """通用視圖刷新邏輯，用於模式切換"""
        self.view._clear_page_cache(cache_message)
        if not interaction.response.is_done():
            # Defer 是一個安全操作，如果已經 defer 或 responded，它會靜默失敗
            await interaction.response.defer(ephemeral=True) 
        
        self.view.clear_items()
        self.view._add_precreated_pagination_buttons()
        await self.view.button_manager.add_buttons()
        # 确保 _update_page 使用原始交互（如果可用）或当前交互
        # CollectionView 应该在其 _update_page 实现中处理 interaction 的最终来源
        await self.view._update_page(self.view.current_page, interaction) 

    async def _enter_custom_sort_mode(self, interaction: discord.Interaction):
        """進入排序功能模式"""
        self.view.state.enter_sort_mode()
        await self._refresh_view_for_mode_change(interaction, '進入排序功能模式')

    async def _exit_custom_sort_mode(self, interaction: discord.Interaction):
        """退出排序功能模式"""
        self.view.state.exit_to_function_mode()
        self.view.sort_by = 'rarity'  # Specific to exiting sort mode
        self.view.sort_order = 'desc' # Specific to exiting sort mode
        await self._refresh_view_for_mode_change(interaction, '退出排序功能模式')

    async def _handle_card_move(self, interaction: discord.Interaction, direction: str, action_text: str):
        """處理卡片移動的通用邏輯"""
        if not await self.view.interaction_check(interaction):
            return
        if not await self.check_cards_available(interaction, '當前頁面沒有卡片可移動'):
            return
        current_card = self.view.current_card
        if not current_card or not hasattr(current_card, 'card'):
            await OperationLogger.log_operation(interaction, '無法獲取當前卡片信息', ephemeral=True)
            return
        card_id = current_card.card.card_id
        current_page = self.view.current_page
        if not interaction.response.is_done():
            await interaction.response.defer(ephemeral=True)
        
        try:
            result = await self._execute_move_operation(self.user_id, card_id, direction)
            message = self._get_success_message(direction, result)
            await OperationLogger.log_operation(interaction, message, ephemeral=True)
            target_page = result.get('page', current_page)
            await self._update_after_move(interaction, target_page)
        except Exception as e:
            error_message = self._get_error_message_from_exception(e)
            await OperationLogger.log_operation(interaction, error_message, ephemeral=True)

    def _get_success_message(self, direction: str, result: Dict[str, Any]) -> str:
        """根據移動方向和結果獲取成功訊息"""
        if direction == 'top':
            return '已將卡片移至頂部'
        elif direction == 'up':
            position = result.get('position', 0)
            return f'已將卡片上移至第 {position} 位'
        elif direction == 'down':
            position = result.get('position', 0)
            return f'已將卡片下移至第 {position} 位'
        elif direction == 'bottom':
            return '已將卡片移至底部'
        else:
            return '卡片已移動'

    def _get_error_message_from_exception(self, exception: Exception) -> str:
        """從異常中獲取錯誤訊息"""
        if isinstance(exception, CardNotMarkedAsFavoriteError):
            return '無法移動卡片：請先將卡片標記為最愛'
        elif isinstance(exception, InvalidPositionError):
            return f'無效的位置，有效範圍: 1-{getattr(exception, "total_cards", "N/A")}'
        elif isinstance(exception, CardAlreadyAtTopError):
            return '卡片已在最頂部'
        elif isinstance(exception, CardAlreadyAtBottomError):
            return '卡片已在最底部'
        elif isinstance(exception, CardNotSortedError):
            return '無法移動卡片：卡片未排序或找不到'
        elif isinstance(exception, PreviousCardNotFoundError):
            return '操作失敗：無法找到前一張卡片'
        elif isinstance(exception, NextCardNotFoundError):
            return '操作失敗：無法找到後一張卡片'
        else:
            return f'執行移動操作時出錯: {str(exception)}'

    async def _execute_move_operation(self, user_id: int, card_id: int, direction: str) -> Dict[str, Any]:
        """執行卡片移動操作"""
        try:
            if direction == 'top':
                return await self.sorting_service.move_card_to_top(user_id, card_id)
            elif direction == 'up':
                return await self.sorting_service.move_card_up(user_id, card_id)
            elif direction == 'down':
                return await self.sorting_service.move_card_down(user_id, card_id)
            elif direction == 'bottom':
                return await self.sorting_service.move_card_to_bottom(user_id, card_id)
            else:
                raise GachaSystemError(f'未知的移動方向：{direction}')
        except Exception:
            # 直接向上傳播異常，由上層處理
            raise

    async def _update_after_move(self, interaction: discord.Interaction, target_page: int):
        """移動操作後更新視圖狀態和頁面"""
        self.view._clear_page_cache('卡片移動操作完成')
        await self.view._update_page(target_page, interaction)

    async def move_to_top_callback(self, interaction: discord.Interaction):
        """置頂按鈕回調函數"""
        await self._handle_card_move(interaction, 'top', '已將卡片移至頂部')

    async def move_up_callback(self, interaction: discord.Interaction):
        """上移按鈕回調函數"""
        await self._handle_card_move(interaction, 'up', '上移')

    async def move_down_callback(self, interaction: discord.Interaction):
        """下移按鈕回調函數"""
        await self._handle_card_move(interaction, 'down', '下移')

    async def move_to_bottom_callback(self, interaction: discord.Interaction):
        """置底按鈕回調函數"""
        await self._handle_card_move(interaction, 'bottom', '已將卡片移至底部')

    async def move_to_position_callback(self, interaction: discord.Interaction):
        """移至指定位置按鈕回調函數"""
        if not await self.view.interaction_check(interaction):
            return
        if not await self.check_cards_available(interaction, '當前頁面沒有卡片可移動'):
            return
        current_card = self.view.current_card
        if not current_card or not hasattr(current_card, 'card'):
            await OperationLogger.log_operation(interaction, '無法獲取當前卡片信息', ephemeral=True)
            return
        card_id = current_card.card.card_id
        favorite_card_count = await self.collection_service.get_favorite_card_count(self.user_id)
        modal = SortPositionModal(user_id=self.user_id, card_id=card_id, callback_func=self.sorting_service.move_card_to_position, update_view_func=self._handle_sort_position_result, max_position=favorite_card_count)
        await interaction.response.send_modal(modal)

    async def _handle_sort_position_result(self, result: Dict[str, Any], interaction: Optional[discord.Interaction]=None, exception: Optional[Exception]=None):
        """處理排序位置結果"""
        if not interaction:
            logger.warning('[GACHA][POSITION][HANDLER] 沒有互動對象，無法處理排序位置結果')
            return
        if not interaction.response.is_done():
            await interaction.response.defer(ephemeral=True)
        
        if exception:
            error_message = self._get_error_message_from_exception(exception)
            await OperationLogger.log_operation(interaction, error_message, ephemeral=True)
            return
            
        message = f'已將卡片移至第 {result.get("position", 0)} 位'
        await OperationLogger.log_operation(interaction, message, ephemeral=True)
        target_page = result.get('page')
        if target_page is None:
            logger.warning('[GACHA][POSITION][HANDLER] 移動成功但未返回目標頁面，使用當前頁面')
            target_page = self.view.current_page
        await self._update_after_move(interaction, target_page)