"""
Gacha系統21點遊戲COG
處理與21點遊戲相關的命令和交互。
使用純異常模式進行錯誤處理
"""
import discord
from discord.ext import commands
from discord import app_commands
from typing import Optional, TYPE_CHECKING, Dict, Any
from discord.ui import View, Button
from utils.logger import logger
from gacha.utils import interaction_utils
from gacha.services.games.blackjack_service import BlackjackGameService
from gacha.services.core.economy_service import EconomyService
from gacha.services.ui.game_display_service import GameDisplayService
from gacha.views.games.blackjack_view import BlackjackView
from gacha.views.embeds.games.blackjack_embed_builder import BlackjackEmbedBuilder
from gacha.views.collection.collection_view.interaction_manager import InteractionManager

if TYPE_CHECKING:
    from discord.ext.commands import Bot


class BlackjackCog(commands.Cog):
    """處理21點遊戲命令的COG - 使用純異常模式"""

    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self._blackjack_service: BlackjackGameService = bot.blackjack_game_service
        self._economy_service: EconomyService = bot.economy_service
        self._game_display_service: GameDisplayService = bot.game_display_service

    @app_commands.command(name='blackjack', description='開始一局21點遊戲')
    @app_commands.describe(bet='下注金額（最低10油幣）')
    async def blackjack_command(self, interaction: discord.Interaction, bet: int = 10):
        """處理21點遊戲命令 - 使用純異常模式"""
        from gacha.utils.cog_error_handler import handle_game_error
        
        if not interaction.response.is_done():
            await interaction_utils.safe_defer(interaction, thinking=True)
            
        try:
            user_id = interaction.user.id
            game_id = str(interaction.id)
            
            # 直接調用服務，異常會自動向上傳播
            game_state = await self._blackjack_service.start_new_game(interaction, user_id, bet, game_id)
            
            display_handled = game_state.get('display_handled', False)
            
            if not display_handled:
                # 顯示遊戲界面
                initial_balance_after_bet = game_state.get('new_balance')
                view = BlackjackView(
                    user=interaction.user, 
                    game_state=game_state, 
                    blackjack_service=self._blackjack_service, 
                    economy_service=self._economy_service, 
                    game_display_service=self._game_display_service, 
                    original_bet=bet
                )
                await view.init_buttons()
                
                builder = BlackjackEmbedBuilder(game_state, interaction.user, initial_balance_after_bet)
                embed = builder.build_embed()
                
                message = await interaction_utils.safe_send_message(interaction, embed=embed, view=view)
                if message is None:
                    logger.error('Failed to send initial Blackjack message for interaction %s. Game ID: %s', interaction.id, game_id)
                    fallback_error_embed = discord.Embed(
                        title='創建遊戲失敗', 
                        description='無法顯示遊戲界面，請聯繫管理員。', 
                        color=discord.Color.red()
                    )
                    await interaction_utils.safe_send_message(interaction, embed=fallback_error_embed, ephemeral=True)
                    return
                    
                view.message = message
                
        except Exception as e:
            # 使用統一的錯誤處理
            await handle_game_error(interaction, e, '處理 Blackjack 命令時')


async def setup(bot: commands.Bot):
    """將COG添加到Bot中"""
    await bot.add_cog(BlackjackCog(bot))
    logger.info('BlackjackCog 已成功加載並註冊。') 