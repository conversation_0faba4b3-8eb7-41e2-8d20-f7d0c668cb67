"""
Gacha系統收藏視圖包
提供卡冊和系列收集的視圖組件
"""

from .base_pagination import BasePaginationJumpModal, BasePaginationView
from .button_factory import ButtonFactory
from .card_view import CollectionView
from .collection_data_manager import CollectionDataManager
from gacha.views.ui_components.confirmation import (
    ConfirmationFactory,
    ConfirmationViewBase,
)
from .enhance_confirm_view import EnhanceConfirmView
from .interaction_manager import InteractionManager
from .series_view import SeriesListView
from .sort_position_modal import SortPositionModal
from gacha.views.utils import get_completion_indicator

__all__ = [
    "BasePaginationView",
    "BasePaginationJumpModal",
    "CollectionView",
    "SeriesListView",
    "EnhanceConfirmView",
    "SortPositionModal",
    "InteractionManager",
    "CollectionDataManager",
    "ConfirmationViewBase",
    "ConfirmationFactory",
    "ButtonFactory",
]
