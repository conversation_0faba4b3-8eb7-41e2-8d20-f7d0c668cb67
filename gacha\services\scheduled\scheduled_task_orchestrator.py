import asyncio
import asyncpg
from utils.logger import logger
from discord.ext import tasks
from typing import Any, Optional
from ai_assistant.ai_service_base import AIServiceBase
from ai_assistant.prompt_service import PromptService
import redis.asyncio as aioredis
from database.redis.service import RedisService
from gacha.services.market.price_update_service import PriceUpdateService
from gacha.services.scheduled.stock_news_service import StockNewsService
from gacha.services.market.stock_lifecycle_service import StockLifecycleService
from gacha.repositories.portfolio.player_portfolio_repository import PlayerPortfolioRepository
from gacha.repositories.market.stock_asset_repository import StockAssetRepository
from gacha.services.core.user_service import UserService
from gacha.services.market.stock_price_update_engine import StockPriceUpdateEngine
from gacha.services.market.anchor_price_service import AnchorPriceService
from gacha.services.market.market_modifier_service import MarketModifierService
from gacha.app_config import config_service
from gacha.repositories.market import CardMarketStatsRepository, VirtualAssetRepository, GachaCategoryStockInfluenceRepository

class ScheduledTaskOrchestrator:

    def __init__(self, pool: asyncpg.Pool, ai_service: AIServiceBase, prompt_service: PromptService, redis_client: aioredis.Redis, price_update_service: PriceUpdateService, redis_service: Optional[RedisService]=None):
        if pool is None:
            raise ValueError('ScheduledTaskOrchestrator requires an asyncpg connection pool.')
        if ai_service is None:
            raise ValueError('ScheduledTaskOrchestrator requires an AIServiceBase instance.')
        if prompt_service is None:
            raise ValueError('ScheduledTaskOrchestrator requires a PromptService instance.')
        if redis_client is None:
            raise ValueError('ScheduledTaskOrchestrator requires an aioredis.Redis client instance.')
        if price_update_service is None:
            raise ValueError('ScheduledTaskOrchestrator requires a PriceUpdateService instance.')
        self.pool = pool
        self.ai_service = ai_service
        self.prompt_service = prompt_service
        self.redis_client = redis_client
        self.redis_service_manager = redis_service
        self.price_update_service = price_update_service
        self.card_market_stats_repo = CardMarketStatsRepository()
        self.virtual_asset_repo = VirtualAssetRepository()
        self.gacha_category_stock_influence_repo = GachaCategoryStockInfluenceRepository()
        self.anchor_price_service = AnchorPriceService(pool=self.pool)
        self.stock_news_service = StockNewsService(pool=self.pool, ai_service=self.ai_service, prompt_service=self.prompt_service, redis_client=self.redis_client, anchor_price_service=self.anchor_price_service)
        self.market_modifier_service = MarketModifierService(pool=self.pool, card_market_stats_repo=self.card_market_stats_repo, virtual_asset_repo=self.virtual_asset_repo, gacha_category_stock_influence_repo=self.gacha_category_stock_influence_repo)
        self.stock_price_update_engine = StockPriceUpdateEngine(pool=self.pool, redis_client=self.redis_client, anchor_price_service=self.anchor_price_service)
        self.player_portfolio_repo = PlayerPortfolioRepository(pool=self.pool)
        self.user_service = UserService(pool=self.pool)
        self.stock_asset_repo = StockAssetRepository(pool=self.pool)
        self.stock_lifecycle_service = StockLifecycleService(stock_asset_repository=self.stock_asset_repo, stock_news_service=self.stock_news_service, redis_client=self.redis_client, player_portfolio_repository=self.player_portfolio_repo, user_service=self.user_service, ai_service=self.ai_service, prompt_service=self.prompt_service)
        self._configure_tasks()
        self._start_all_tasks()

    def _configure_tasks(self):
        gacha_stock_config = config_service.get_gacha_stock_integration_config()
        self.calculate_supply_demand_modifier_loop.change_interval(hours=gacha_stock_config.tasks.calculate_supply_demand_modifier_hours)
        self.update_stock_prices_loop.change_interval(minutes=gacha_stock_config.tasks.update_stock_prices_minutes)
        self.news_scheduler_manager_loop.change_interval(minutes=gacha_stock_config.tasks.news_scheduler_manager_minutes)
        self.calculate_gacha_category_stock_influence_loop.change_interval(minutes=gacha_stock_config.tasks.calculate_gacha_category_stock_influence_minutes)
        self.cleanup_expired_news_effects_loop.change_interval(minutes=gacha_stock_config.tasks.cleanup_expired_news_effects_minutes)
        self.full_price_recalculation_loop.change_interval(hours=gacha_stock_config.tasks.full_price_recalculation_hours)
        self.calculate_daily_anchor_prices_loop.change_interval(hours=gacha_stock_config.tasks.calculate_daily_anchor_prices_hours)
        self.calculate_daily_anchor_prices_loop.add_exception_type(Exception)
        self.check_stock_lifecycle_loop.change_interval(minutes=gacha_stock_config.tasks.check_stock_lifecycle_minutes)
        self.check_stock_lifecycle_loop.add_exception_type(Exception)

    def _start_all_tasks(self):
        self.calculate_supply_demand_modifier_loop.start()
        self.update_stock_prices_loop.start()
        self.news_scheduler_manager_loop.start()
        self.calculate_gacha_category_stock_influence_loop.start()
        self.cleanup_expired_news_effects_loop.start()
        self.full_price_recalculation_loop.start()
        self.calculate_daily_anchor_prices_loop.start()
        self.check_stock_lifecycle_loop.start()
        logger.info('All scheduled tasks started by Orchestrator.')

    def cog_unload(self):
        self.calculate_supply_demand_modifier_loop.cancel()
        self.update_stock_prices_loop.cancel()
        self.news_scheduler_manager_loop.cancel()
        self.calculate_gacha_category_stock_influence_loop.cancel()
        self.cleanup_expired_news_effects_loop.cancel()
        self.full_price_recalculation_loop.cancel()
        self.calculate_daily_anchor_prices_loop.cancel()
        self.check_stock_lifecycle_loop.cancel()
        logger.info('All scheduled tasks cancelled by Orchestrator (cog_unload).')

    async def shutdown(self):
        self.cog_unload()
        logger.info('All scheduled tasks cancelled by Orchestrator (shutdown).')

    @tasks.loop(hours=1)
    async def calculate_supply_demand_modifier_loop(self):
        try:
            await self.market_modifier_service.calculate_supply_demand_modifier()
        except Exception as e:
            logger.error('Error in calculate_supply_demand_modifier_loop: %s', e, exc_info=True)

    @calculate_supply_demand_modifier_loop.before_loop
    async def before_calculate_supply_demand_modifier_loop(self):
        gacha_stock_config = config_service.get_gacha_stock_integration_config()
        await asyncio.sleep(gacha_stock_config.task_initial_delays.calculate_supply_demand_modifier_seconds)

    @tasks.loop(minutes=10)
    async def update_stock_prices_loop(self):
        try:
            await self.stock_price_update_engine.update_stock_prices()
        except Exception as e:
            logger.error('Error in update_stock_prices_loop: %s', e, exc_info=True)

    @update_stock_prices_loop.before_loop
    async def before_update_stock_prices_loop(self):
        gacha_stock_config = config_service.get_gacha_stock_integration_config()
        await asyncio.sleep(gacha_stock_config.task_initial_delays.update_stock_prices_seconds)

    @tasks.loop(minutes=1)
    async def news_scheduler_manager_loop(self):
        try:
            await self.stock_news_service.news_scheduler_manager()
        except Exception as e:
            logger.error('Error in news_scheduler_manager_loop: %s', e, exc_info=True)

    @news_scheduler_manager_loop.before_loop
    async def before_news_scheduler_manager_loop(self):
        gacha_stock_config = config_service.get_gacha_stock_integration_config()
        await asyncio.sleep(gacha_stock_config.task_initial_delays.news_scheduler_manager_seconds)

    @tasks.loop(minutes=15)
    async def calculate_gacha_category_stock_influence_loop(self):
        try:
            await self.market_modifier_service.calculate_gacha_category_stock_influence()
        except Exception as e:
            logger.error('Error in calculate_gacha_category_stock_influence_loop: %s', e, exc_info=True)

    @calculate_gacha_category_stock_influence_loop.before_loop
    async def before_calculate_gacha_category_stock_influence_loop(self):
        gacha_stock_config = config_service.get_gacha_stock_integration_config()
        await asyncio.sleep(gacha_stock_config.task_initial_delays.calculate_gacha_category_stock_influence_seconds)

    @tasks.loop(minutes=5)
    async def cleanup_expired_news_effects_loop(self):
        try:
            await self.market_modifier_service.cleanup_expired_news_effects()
        except Exception as e:
            logger.error('Error in cleanup_expired_news_effects_loop: %s', e, exc_info=True)

    @cleanup_expired_news_effects_loop.before_loop
    async def before_cleanup_expired_news_effects_loop(self):
        gacha_stock_config = config_service.get_gacha_stock_integration_config()
        await asyncio.sleep(gacha_stock_config.task_initial_delays.cleanup_expired_news_effects_seconds)

    @tasks.loop(hours=24)
    async def full_price_recalculation_loop(self):
        try:
            if not self.price_update_service:
                logger.error('PriceUpdateService not available in full_price_recalculation_loop. Skipping.')
                return
            await self.price_update_service.schedule_full_price_recalculation()
            logger.info('Full price recalculation scheduled successfully via Orchestrator.')
        except Exception as e:
            logger.error('Error in full_price_recalculation_loop: %s', e, exc_info=True)

    @full_price_recalculation_loop.before_loop
    async def before_full_price_recalculation_loop(self):
        gacha_stock_config = config_service.get_gacha_stock_integration_config()
        await asyncio.sleep(gacha_stock_config.task_initial_delays.full_price_recalculation_seconds)

    @tasks.loop(hours=24)
    async def calculate_daily_anchor_prices_loop(self):
        try:
            await self.anchor_price_service.calculate_daily_anchor_prices()
        except Exception as e:
            logger.error('Error in calculate_daily_anchor_prices_loop (orchestrator level): %s', e, exc_info=True)

    @calculate_daily_anchor_prices_loop.before_loop
    async def before_calculate_daily_anchor_prices_loop(self):
        gacha_stock_config = config_service.get_gacha_stock_integration_config()
        await asyncio.sleep(gacha_stock_config.task_initial_delays.calculate_daily_anchor_prices_seconds)

    @tasks.loop(minutes=15)
    async def check_stock_lifecycle_loop(self):
        try:
            await self.stock_lifecycle_service.check_and_update_all_stocks_lifecycle()
            logger.info('ScheduledTaskOrchestrator: Finished check_and_update_all_stocks_lifecycle. Now triggering new company creation check.')
            created_count = await self.stock_lifecycle_service.trigger_new_company_creation_if_needed()
            logger.info('ScheduledTaskOrchestrator: Triggered new company creation check. Created %s new companies.', created_count)
        except Exception as e:
            logger.error('Error in check_stock_lifecycle_loop or subsequent new company creation trigger: %s', e, exc_info=True)

    @check_stock_lifecycle_loop.before_loop
    async def before_check_stock_lifecycle_loop(self):
        gacha_stock_config = config_service.get_gacha_stock_integration_config()
        delay_seconds = getattr(gacha_stock_config.task_initial_delays, 'check_stock_lifecycle_seconds', 75)
        await asyncio.sleep(delay_seconds)