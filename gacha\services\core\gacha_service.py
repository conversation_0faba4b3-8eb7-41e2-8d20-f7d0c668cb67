from __future__ import annotations
'\nGacha系統抽卡服務\n\n負責處理抽卡流程的整體協調，包括：\n- 接收外部抽卡請求（單抽、多抽）\n- 計算抽卡成本\n- 調用 DrawEngineService 來決定抽取的卡片\n- 處理用戶數據更新（油幣扣除、卡片添加至收藏）\n- 管理數據庫事務\n- 將抽卡事件發布到消息隊列供其他服務消費\n- 格式化並返回最終的抽卡結果\n\n專注於業務流程和數據管理，而不是抽卡決策邏輯（由 DrawEngineService 處理）。\n'
import asyncpg
import random
import time
import asyncio
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Any, Union
from utils.logger import logger
from gacha.constants import MarketStatsEventType
from gacha.exceptions import UserNotFoundError, InsufficientBalanceError, InvalidPoolTypeError, GachaSystemError, NoCardsDeterminedError, BalanceUpdateError, CardEnrichmentError
from database.postgresql.async_manager import AsyncPgManager
from gacha.models.models import Card, CardStatus, CardWithStatus
from gacha.repositories.collection.user_collection_repository import UserCollectionRepository
from gacha.app_config import config_service

class GachaService:
    """
    抽卡服務類，負責協調整個抽卡流程，包括用戶數據更新、
    抽卡結果處理和市場事件發布
    """

    def __init__(self, pool: asyncpg.Pool, user_service, collection_repo: UserCollectionRepository, wish_service=None, card_status_service=None, draw_engine_service=None, validation_service=None, redis_publisher=None):
        """初始化抽卡服務"""
        if pool is None:
            err_msg = 'GachaService 初始化失敗：必須提供 asyncpg 連接池。'
            logger.error(err_msg)
            raise ValueError(err_msg)
        if user_service is None or collection_repo is None or draw_engine_service is None or (validation_service is None):
            err_msg = 'GachaService 初始化失敗：必須提供核心依賴服務。'
            logger.error(err_msg)
            raise ValueError(err_msg)
        self.pool = pool
        self.user_service = user_service
        self.collection_repo = collection_repo
        self.wish_service = wish_service
        self.card_status_service = card_status_service
        self.draw_engine_service = draw_engine_service
        self.validation_service = validation_service
        self.redis_publisher = redis_publisher

    async def draw_card(self, user_id: int, nickname: Optional[str]=None) -> Dict[str, Any]:
        """執行單抽"""
        return await self.draw_cards(user_id, ['main', 'special'], 1, nickname)

    async def draw_multiple(self, user_id: int, pool_types: List[str], count: int=10, nickname: Optional[str]=None) -> Dict[str, Any]:
        """執行多抽"""
        return await self.draw_cards(user_id, pool_types, count, nickname)

    def calculate_draw_cost(self, pool_types: List[str], count: int=1) -> int:
        """
        計算抽卡成本

        Args:
            pool_types: 卡池類型列表
            count: 抽卡次數

        Returns:
            抽卡總成本
        """
        if not pool_types:
            return config_service.get_pool_costs().get('main', 50) * count
        if 'all' in pool_types or len(pool_types) > 1:
            return config_service.get_pool_costs().get('all', 50) * count
        pool_type = pool_types[0]
        cost = config_service.get_pool_costs().get(pool_type)
        if cost is None:
            logger.warning("卡池類型 '%s' 在 pool_costs 中未找到，默認使用 'main' 卡池成本", pool_type)
            cost = config_service.get_pool_costs().get('main', 50)
        return cost * count

    async def draw_cards(self, user_id: int, pool_types: List[str], count: int=1, nickname: Optional[str]=None) -> Dict[str, Any]:
        """
        執行抽卡，這是整個抽卡流程的主要方法

        Args:
            user_id: 用戶ID
            pool_types: 卡池類型列表
            count: 抽卡次數
            nickname: 用戶暱稱（可選）

        Returns:
            抽卡結果字典
        """
        # 驗證卡池類型
        valid_pools = self.validation_service.validate_pool_types(pool_types)
        
        # 計算抽卡成本
        total_cost = self.calculate_draw_cost(valid_pools, count)
        
        # 決定抽取的卡片
        cards_results, card_ids = await self.draw_engine_service.determine_cards_to_draw(user_id, valid_pools, count)
        if not card_ids:
            raise NoCardsDeterminedError('抽卡失敗，未能獲取任何卡片')
            
        # 處理用戶數據和收藏更新
        new_balance, bulk_add_results = await self._process_user_and_collection_updates(user_id, nickname, total_cost, card_ids)
        
        # 豐富抽取的卡片信息
        enriched_cards, owner_counts = await self._enrich_drawn_cards_info(user_id, cards_results, bulk_add_results)
        
        # 構建結果數據
        result_data = {
            'cards_with_status': enriched_cards,
            'owner_counts': owner_counts,
            'new_balance': new_balance,
            'is_multi_draw': count > 1
        }
        
        return result_data

    async def _process_user_and_collection_updates(self, user_id: int, nickname: Optional[str], total_cost: int, card_ids: List[int]) -> Tuple[int, Dict[int, Dict[str, Any]]]:
        """
        處理用戶數據和收藏更新

        Args:
            user_id: 用戶ID
            nickname: 用戶暱稱
            total_cost: 抽卡總成本
            card_ids: 抽取的卡片ID列表

        Returns:
            Tuple[int, Dict[int, Dict[str, Any]]]: (新餘額, 批量添加結果)
        """
        new_balance = 0
        bulk_add_results = {}
        transaction_successful = False
        total_owned_updates_for_task = []
        owner_changes_for_task = []
        async with self.pool.acquire() as conn:
            async with conn.transaction():
                await self.validation_service.ensure_user_exists(user_id, nickname=nickname, create_if_missing=True, connection=conn)
                await self.validation_service.check_user_balance(user_id, total_cost, connection=conn)
                current_balance = await self.user_service.user_repo.get_user_balance_for_update(user_id, connection=conn)
                # 餘額檢查已經在 check_user_balance 中完成，如果不足會拋出 InsufficientBalanceError
                
                draw_count = len(card_ids)
                await self.user_service.user_repo.update_balance_and_draws(user_id, current_balance - total_cost, draw_count, connection=conn)
                new_balance = current_balance - total_cost
                
                oil_cost_for_tickets = Decimal(total_cost)
                tickets_to_add = oil_cost_for_tickets / Decimal('100.0')
                if tickets_to_add > Decimal('0'):
                    logger.debug('[GACHA][SERVICE] User %s: 消耗油幣 %s，增加油票 %s.', user_id, total_cost, tickets_to_add)
                    await self.user_service.user_repo.increment_oil_ticket_balance(user_id, tickets_to_add, connection=conn)
                
                if card_ids:
                    bulk_add_results = await self.collection_repo.bulk_add_cards_and_check_new(user_id, card_ids, connection=conn)
                    if bulk_add_results:
                        for card_id_added, op_res in bulk_add_results.items():
                            quantity_added = card_ids.count(card_id_added)
                            if quantity_added > 0:
                                total_owned_updates_for_task.append((card_id_added, quantity_added))
                            if op_res.get('is_new', False):
                                owner_changes_for_task.append((card_id_added, 1))
                transaction_successful = True
                
        if transaction_successful and self.redis_publisher:
            if total_owned_updates_for_task:
                await self.redis_publisher.publish(event_type=MarketStatsEventType.TOTAL_OWNED_UPDATE, payload=total_owned_updates_for_task, batch_payload=True, user_id_for_log=user_id)
            if owner_changes_for_task:
                await self.redis_publisher.publish(event_type=MarketStatsEventType.UNIQUE_OWNER_UPDATE, payload=owner_changes_for_task, batch_payload=True, user_id_for_log=user_id)
                
        return (new_balance, bulk_add_results)

    async def _enrich_drawn_cards_info(self, user_id: int, cards_results: List[Dict[str, Any]], bulk_add_results: Dict[int, Dict[str, Any]]) -> Tuple[List[CardWithStatus], Dict[int, int]]:
        """
        豐富抽取的卡片信息

        Args:
            user_id: 用戶ID
            cards_results: 卡片結果列表
            bulk_add_results: 批量添加結果

        Returns:
            Tuple[List[CardWithStatus], Dict[int, int]]: (豐富後的卡片列表, 擁有者數量字典)
        """
        all_drawn_card_ids = [cr['card'].card_id for cr in cards_results]
        wish_status_map = {}
        favorite_status_map = {}
        owner_counts = {}
        processed_cards = []
        
        # 獲取許願狀態
        if self.wish_service:
            wish_status_map = await self.wish_service.get_wishes_status_batch(user_id, all_drawn_card_ids)
        
        # 獲取收藏狀態
        favorite_status_map = await self.collection_repo.get_favorite_status_batch(user_id, all_drawn_card_ids)
        
        # 處理每張卡片
        for card_result in cards_results:
            card = card_result['card']
            card_id = card.card_id
            is_wish = wish_status_map.get(card_id, False) or card_result.get('is_wish', False)
            is_favorite = favorite_status_map.get(card_id, False)
            op_res = bulk_add_results.get(card_id, {})
            status = CardStatus(is_new_card=op_res.get('is_new', False), star_level=op_res.get('star_level', 0), is_wish=is_wish, is_favorite=is_favorite)
            card_with_status = CardWithStatus.create(card=card, status=status, pool_type=card_result['pool_type'])
            processed_cards.append(card_with_status)
        
        # 獲取擁有者數量
        if all_drawn_card_ids:
            owner_counts = await self.collection_repo.get_card_owner_counts_batch(all_drawn_card_ids)
            
        return (processed_cards, owner_counts)