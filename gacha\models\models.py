"""
Gacha系統基礎模型類
定義Gacha系統所需的數據結構和模型類
"""
from dataclasses import dataclass, field
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any, Mapping
from decimal import Decimal
from gacha.constants import RarityLevel
import json
from utils.logger import logger
import pytz

@dataclass
class GachaUser:
    """Gacha系統用戶模型"""
    user_id: int
    oil_balance: int = 0
    total_draws: int = 0
    last_daily_claim: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    nickname: Optional[str] = None
    wish_slots: int = 1
    wish_power_level: int = 1
    oil_ticket_balance: Decimal = Decimal('0.00')

    @property
    def can_claim_daily(self) -> bool:
        """檢查用戶是否可以領取每日獎勵 (基於台灣時區每日午夜重置)"""
        if not self.last_daily_claim:
            logger.debug('[can_claim_daily] User %s: No last_daily_claim. Allowing claim.', self.user_id)
            return True

        # tw_timezone is already imported at the top level of this module
        tw_timezone = pytz.timezone('Asia/Taipei')

        # last_daily_claim 應已是帶時區的 UTC datetime 物件 (從 DB 讀取時 asyncpg 處理)
        if self.last_daily_claim.tzinfo is None:
            logger.error('[can_claim_daily] User %s: last_daily_claim is a naive datetime (%s). This indicates a potential issue. Assuming UTC for conversion to TWT.', self.user_id, self.last_daily_claim)
            # Ensure last_daily_claim is timezone-aware (UTC) before converting to another timezone
            last_daily_claim_utc = self.last_daily_claim.replace(tzinfo=timezone.utc)
        else:
            last_daily_claim_utc = self.last_daily_claim.astimezone(timezone.utc)
        
        last_daily_claim_tw = last_daily_claim_utc.astimezone(tw_timezone)
        
        now_utc = datetime.now(timezone.utc)
        now_tw = now_utc.astimezone(tw_timezone)
        
        can_claim = now_tw.date() > last_daily_claim_tw.date()
        logger.debug('[can_claim_daily] User %s: Now_TW_date: %s, Last_Claim_TW_date: %s. Can Claim: %s', self.user_id, now_tw.date(), last_daily_claim_tw.date(), can_claim)
        return can_claim

@dataclass
class Card:
    """卡片模型 (已重構)"""
    card_id: int
    name: str
    series: str
    rarity: RarityLevel
    image_url: str
    description: Optional[str] = None
    creation_date: datetime = field(default_factory=datetime.now)
    pool_type: Optional[str] = None
    is_active: bool = True
    original_id: Optional[str] = None
    sell_price: Optional[int] = None
    price_config: Optional[Dict[str, Any]] = field(default_factory=dict)
    current_market_sell_price: Optional[Decimal] = None
    last_price_update_at: Optional[datetime] = None

    @staticmethod
    def _parse_price_config(raw_price_config: Any, card_id_for_logging: Optional[Any]='UNKNOWN') -> Dict[str, Any]:
        """輔助方法：解析 price_config 欄位。
        如果它是字串，則嘗試 JSON 解析；否則，如果是字典則直接返回，如果為 None 或解析失敗則返回空字典。
        """
        if isinstance(raw_price_config, dict):
            return raw_price_config
        if isinstance(raw_price_config, str):
            if raw_price_config.strip():
                try:
                    return json.loads(raw_price_config)
                except json.JSONDecodeError:
                    logger.warning("[Card._parse_price_config] Failed to parse price_config JSON for card_id '%s': %s", card_id_for_logging, raw_price_config)
                    return {}
            else:
                return {}
        if raw_price_config is not None:
            logger.warning("[Card._parse_price_config] Unexpected type for price_config for card_id '%s': %s. Returning empty dict.", card_id_for_logging, type(raw_price_config))
        return {}

    @staticmethod
    def from_db_record(record: Mapping[str, Any]) -> 'Card':
        """
        從資料庫記錄 (例如 asyncpg.Record) 創建 Card 物件。
        如果缺少必要欄位或類型轉換失敗，則拋出 DataMappingError。
        """
        if not record:
            raise ValueError('Input database record cannot be None or empty.')
        try:
            return Card(card_id=record['card_id'], name=record['name'], series=record['series'], rarity=RarityLevel(record['rarity']), image_url=record['image_url'], description=record.get('description'), creation_date=record['creation_date'], pool_type=record.get('pool_type'), is_active=record['is_active'], original_id=record.get('original_id'), sell_price=record.get('sell_price'), price_config=Card._parse_price_config(record.get('price_config'), record.get('card_id')), current_market_sell_price=record.get('current_market_sell_price'), last_price_update_at=record.get('last_price_update_at'))
        except KeyError as e:
            logger.error('[Card.from_db_record] 數據中缺少鍵 %s: %s', e, record, exc_info=True)
            raise ValueError(f'創建Card對象時數據欄位缺失: {e}') from e
        except ValueError as e:
            logger.error('[Card.from_db_record] RarityLevel 轉換失敗: %s. Record: %s', e, record, exc_info=True)
            raise ValueError(f'創建Card對象時RarityLevel轉換失敗: {e}') from e
        except Exception as e:
            logger.error('[Card.from_db_record] 創建Card對象時發生未知錯誤: %s. Record: %s', e, record, exc_info=True)
            raise ValueError(f'創建Card對象時發生未知錯誤: {e}') from e

@dataclass
class UserCard:
    """用戶卡片模型，表示用戶擁有的卡片及其詳細信息"""
    id: int
    user_id: int
    card_id: int
    quantity: int = 1
    first_acquired: datetime = field(default_factory=datetime.now)
    last_acquired: datetime = field(default_factory=datetime.now)
    is_favorite: bool = False
    star_level: int = 0
    custom_sort_index: Optional[int] = None
    custom_description: Optional[str] = None
    description_user_id: Optional[int] = None
    owner_count: Optional[int] = None
    card: Optional[Card] = None

@dataclass
class SeriesCollection:
    """系列收集信息"""
    series: str
    total_cards: int
    collected_cards: int

    @property
    def completion_rate(self) -> float:
        """計算系列完成率"""
        if self.total_cards == 0:
            return 0.0
        return self.collected_cards / self.total_cards * 100

@dataclass
class CardStatus:
    """卡片狀態模型，統一管理卡片的各種狀態

    重要說明：
    - is_new_card 的預設值為 False，表示卡片默認不是新卡
    - 此預設值應在所有使用該屬性的地方保持一致
    """
    is_new_card: bool = False
    star_level: int = 0
    is_wish: bool = False
    is_favorite: bool = False

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CardStatus':
        """從字典創建狀態對象"""
        return cls(is_new_card=data.get('is_new_card', False), star_level=data.get('star_level', 0), is_wish=data.get('is_wish', False), is_favorite=data.get('is_favorite', False))

@dataclass
class CardWithStatus:
    """包含狀態的卡片模型"""
    card: Card
    status: CardStatus
    pool_type: Optional[str] = None

    @classmethod
    def create(cls, card: Card, status: CardStatus, pool_type: Optional[str]=None) -> 'CardWithStatus':
        """創建帶狀態的卡片"""
        return cls(card=card, status=status, pool_type=pool_type or card.pool_type)

    @classmethod
    def from_result_dict(cls, result: Dict[str, Any]) -> 'CardWithStatus':
        """從抽卡結果字典創建對象"""
        card = result.get('card')
        status = CardStatus(is_new_card=result.get('is_new_card', False), star_level=result.get('star_level', 0), is_wish=result.get('is_wish', False), is_favorite=result.get('is_favorite', False))
        return cls.create(card=card, status=status, pool_type=result.get('pool_type'))

    @classmethod
    def from_operation_result(cls, card: Card, operation_result: Dict[str, Any], is_wish: bool=False, pool_type: Optional[str]=None) -> 'CardWithStatus':
        """從操作結果創建對象（適用於單抽結果）

        參數:
            card: 卡片對象
            operation_result: 操作結果字典
            is_wish: 是否為許願卡片
            pool_type: 卡池類型

        返回:
            CardWithStatus: 帶狀態的卡片對象
        """
        status = CardStatus(is_new_card=operation_result.get('is_new_card', False), star_level=operation_result.get('star_level', 0), is_favorite=operation_result.get('is_favorite', False), is_wish=is_wish)
        return cls.create(card=card, status=status, pool_type=pool_type or card.pool_type)

    @classmethod
    def from_multi_operation_result(cls, card: Card, operation_card_result: Dict[str, Any], is_wish: bool=False, pool_type: Optional[str]=None) -> 'CardWithStatus':
        """從多抽結果中的單卡結果創建對象（適用於十連抽中的單張卡片）

        參數:
            card: 卡片對象
            operation_card_result: 操作結果中的單卡結果
            is_wish: 是否為許願卡片
            pool_type: 卡池類型

        返回:
            CardWithStatus: 帶狀態的卡片對象
        """
        status = CardStatus(is_new_card=operation_card_result.get('is_new_card', False), star_level=operation_card_result.get('star_level', 0), is_favorite=operation_card_result.get('is_favorite', False), is_wish=is_wish)
        return cls.create(card=card, status=status, pool_type=pool_type or card.pool_type)

@dataclass
class NewsTypeGenerationLog:
    """記錄 AI 新聞類型上次生成時間的模型"""
    news_type: str
    last_generated_at: datetime