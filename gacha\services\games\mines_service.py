import random
import math
import uuid
import asyncpg
import logging
from typing import Dict, Any, List, Tuple, Optional, Set, TypedDict, Awaitable
import discord
from typing import Callable
from gacha.services.games.base_game_service import BaseGameService
from gacha.services.core.economy_service import EconomyService
from gacha.services.ui.game_display_service import GameDisplayService
from gacha.views.embeds.games.mines_embed_builder import build_mines_game_embed
from gacha.utils.mines_constants import BOARD_ROWS, BOARD_COLS, TOTAL_TILES, TILE_HIDDEN, TILE_MINE, TILE_SAFE, TILE_SPECIAL_COIN, TILE_SPECIAL_STAR, MIN_BET, MAX_BET, PAYOUT_FACTOR
from gacha.exceptions import (
    MinBetNotMetError, 
    InsufficientFundsError, 
    GameSettlementError,
    DatabaseOperationError,
    UserNotFoundError
)

class GameState(TypedDict):
    user_id: int
    game_instance_id: str
    message_id: Optional[int]
    bet_amount: int
    mine_count: int
    board_display: List[List[str]]
    revealed_tiles: List[Tuple[int, int]]
    current_multiplier: float
    found_safe_tiles_count: int
    start_time: float
    max_win_cap: int
    game_over: bool
    last_result: Optional[str]
    final_winnings: Optional[float]

def nCr(n: int, r: int) -> int:
    """計算組合數 C(n, r)"""
    if r < 0 or r > n:
        return 0
    try:
        return math.comb(n, r)
    except AttributeError:
        if r == 0 or r == n:
            return 1
        if r > n // 2:
            r = n - r
        res = 1
        for i in range(r):
            res = res * (n - i) // (i + 1)
        return res
    except ValueError:
        logging.error(f'Invalid input for nCr: n={n}, r={r}')
        return 0

class MinesGame:
    """管理單一一局踩地雷遊戲的狀態和邏輯"""

    def __init__(self, user_id: int, bet_amount: int, mine_count: int, game_instance_id: str):
        self.user_id: int = user_id
        self.bet_amount: int = bet_amount
        self.mine_count: int = mine_count
        self.game_instance_id: str = game_instance_id
        self.message_id: Optional[int] = None
        self.board_display: List[List[str]] = []
        self.mine_positions: Set[Tuple[int, int]] = set()
        self.special_coin_pos: Optional[Tuple[int, int]] = None
        self.special_star_pos: Optional[Tuple[int, int]] = None
        self._generate_board()
        self.revealed_tiles: List[Tuple[int, int]] = []
        self.current_multiplier: float = 1.0
        self.found_safe_tiles_count: int = 0
        self.start_time: float = discord.utils.utcnow().timestamp()
        self.max_win_cap: int = min(bet_amount * 500, 1000000)
        self.game_over: bool = False
        self.last_result: Optional[str] = None
        self.final_winnings: Optional[float] = None

    def _generate_board(self) -> None:
        if not 0 <= self.mine_count < TOTAL_TILES:
            raise ValueError('Invalid number of mines')
        self.board_display = [[TILE_HIDDEN for _ in range(BOARD_COLS)] for _ in range(BOARD_ROWS)]
        all_positions = [(r, c) for r in range(BOARD_ROWS) for c in range(BOARD_COLS)]
        random.shuffle(all_positions)
        self.mine_positions = set(all_positions[:self.mine_count])
        safe_positions = [pos for pos in all_positions if pos not in self.mine_positions]
        self.special_coin_pos = None
        self.special_star_pos = None
        if len(safe_positions) >= 1:
            idx = random.randrange(len(safe_positions))
            self.special_coin_pos = safe_positions.pop(idx)
        if len(safe_positions) >= 1:
            idx = random.randrange(len(safe_positions))
            self.special_star_pos = safe_positions.pop(idx)
        logging.debug(f'Game {self.game_instance_id}: Mines at {self.mine_positions}, Coin at {self.special_coin_pos}, Star at {self.special_star_pos}')

    def calculate_multiplier(self, k: int) -> float:
        total_tiles = TOTAL_TILES
        safe_tiles_total = total_tiles - self.mine_count
        if k <= 0:
            return 1.0
        if k > safe_tiles_total:
            logging.warning(f'Game {self.game_instance_id}: k={k} > total safe tiles={safe_tiles_total}. Returning 0.0 multiplier.')
            return 0.0
        comb_b_k = nCr(total_tiles, k)
        comb_s_k = nCr(safe_tiles_total, k)
        if comb_s_k == 0:
            logging.error(f'Game {self.game_instance_id}: nCr({safe_tiles_total}, {k}) returned 0. Cannot calculate theoretical multiplier. Returning 0.0.')
            return 0.0
        theoretical_multiplier = comb_b_k / comb_s_k
        actual_multiplier = theoretical_multiplier * PAYOUT_FACTOR
        return actual_multiplier

    def reveal_tile(self, row: int, col: int) -> Dict[str, Any]:
        result = {'result_type': 'ERROR', 'new_multiplier': self.current_multiplier, 'immediate_reward': 0, 'is_special_coin': False, 'is_special_star': False, 'is_max_win': False, 'final_winnings_on_max': 0.0}
        if self.game_over:
            logging.warning(f'Game {self.game_instance_id}: reveal_tile called but game is already over.')
            result['result_type'] = 'GAME_OVER'
            return result
        pos = (row, col)
        revealed_set = set((tuple(p) for p in self.revealed_tiles))
        if pos in revealed_set:
            logging.debug(f'Game {self.game_instance_id}: Tile ({row}, {col}) already revealed.')
            result['result_type'] = 'ALREADY_REVEALED'
            return result
        self.revealed_tiles.append(pos)
        is_mine = pos in self.mine_positions
        is_coin = pos == self.special_coin_pos
        is_star = pos == self.special_star_pos
        if is_mine:
            result['result_type'] = 'MINE'
            self.game_over = True
            self.last_result = 'LOSS'
            self.final_winnings = 0.0
            for r_idx, c_idx in self.mine_positions:
                if 0 <= r_idx < BOARD_ROWS and 0 <= c_idx < BOARD_COLS:
                    self.board_display[r_idx][c_idx] = TILE_MINE
        else:
            result['result_type'] = 'SAFE'
            self.found_safe_tiles_count += 1
            k = self.found_safe_tiles_count
            new_multiplier = self.calculate_multiplier(k)
            self.current_multiplier = new_multiplier
            result['new_multiplier'] = new_multiplier
            logging.debug(f'Game {self.game_instance_id}: User {self.user_id} found safe tile #{k}. New multiplier: {new_multiplier:.4f}')
            tile_emoji = TILE_SAFE
            immediate_reward = 0
            if is_coin:
                tile_emoji = TILE_SPECIAL_COIN
                immediate_reward = math.ceil(50 + self.bet_amount / 100)
                result['is_special_coin'] = True
                result['immediate_reward'] = immediate_reward
            elif is_star:
                tile_emoji = TILE_SPECIAL_STAR
                immediate_reward = math.ceil(200 + self.bet_amount / 50)
                result['is_special_star'] = True
                result['immediate_reward'] = immediate_reward
            self.board_display[row][col] = tile_emoji
            potential_winnings = self.bet_amount * new_multiplier
            if potential_winnings >= self.max_win_cap:
                result['is_max_win'] = True
                result['final_winnings_on_max'] = float(self.max_win_cap)
                self.game_over = True
                self.last_result = 'WIN'
                self.final_winnings = float(self.max_win_cap)
                for r_idx, c_idx in self.mine_positions:
                    if 0 <= r_idx < BOARD_ROWS and 0 <= c_idx < BOARD_COLS:
                        self.board_display[r_idx][c_idx] = TILE_MINE
        return result

    def get_state_summary(self) -> GameState:
        return GameState(user_id=self.user_id, game_instance_id=self.game_instance_id, message_id=self.message_id, bet_amount=self.bet_amount, mine_count=self.mine_count, board_display=self.board_display, revealed_tiles=self.revealed_tiles, current_multiplier=self.current_multiplier, found_safe_tiles_count=self.found_safe_tiles_count, start_time=self.start_time, max_win_cap=self.max_win_cap, game_over=self.game_over, last_result=self.last_result, final_winnings=self.final_winnings)

    def cash_out_game(self) -> float:
        if self.game_over:
            return self.final_winnings if self.final_winnings is not None else 0.0
        if self.found_safe_tiles_count == 0:
            self.game_over = True
            self.last_result = 'CASH_OUT_ZERO'
            self.final_winnings = 0.0
            return 0.0
        calculated_winnings = self.bet_amount * self.current_multiplier
        final_winnings_to_settle = min(calculated_winnings, float(self.max_win_cap))
        self.game_over = True
        self.last_result = 'CASH_OUT'
        self.final_winnings = final_winnings_to_settle
        return final_winnings_to_settle

class MinesService(BaseGameService):

    def __init__(self, economy_service: EconomyService, game_display_service: GameDisplayService, pool: Optional[asyncpg.Pool]=None):
        super().__init__(economy_service=economy_service, pool=pool)
        self.active_games: Dict[int, Dict[str, MinesGame]] = {}
        self._game_display_service = game_display_service

    def _get_game_by_instance_id(self, game_instance_id: str) -> Optional[MinesGame]:
        """Helper method to find a game by its instance ID across all users."""
        for user_games_dict in self.active_games.values():
            if game_instance_id in user_games_dict:
                return user_games_dict[game_instance_id]
        return None

    def _build_mines_result_message(self, game_outcome: str, game: MinesGame, **kwargs) -> str:
        """
        統一建構尋寶礦區遊戲的結果訊息。
        """
        bet_amount = game.bet_amount
        current_multiplier = game.current_multiplier
        final_winnings = kwargs.get('final_winnings', game.final_winnings if game.final_winnings is not None else 0.0)
        immediate_reward = kwargs.get('immediate_reward', 0)
        msg = '發生未知情況。'
        if game_outcome == 'GAME_CREATED':
            msg = f'尋寶礦區遊戲已開始！您的賭注是 {bet_amount:,.0f} 油幣，共有 {game.mine_count} 個地雷。祝您好運！'
        elif game_outcome == 'MINE_HIT':
            msg = '哎呀！您踩到了地雷 💥！遊戲結束，本次未能獲得任何油幣。'
        elif game_outcome == 'SAFE_TILE_NORMAL':
            msg = f'安全！您找到了一個安全格 💎。目前乘數: {current_multiplier:.2f}x。'
        elif game_outcome == 'SAFE_TILE_COIN':
            msg = f'幸運！您找到了一個特殊金幣 💰，額外獲得 {immediate_reward:,.0f} 油幣！目前乘數: {current_multiplier:.2f}x。'
        elif game_outcome == 'SAFE_TILE_STAR':
            msg = f'太棒了！您找到了一個特殊星星 ⭐，額外獲得 {immediate_reward:,.0f} 油幣！目前乘數: {current_multiplier:.2f}x。'
        elif game_outcome == 'MAX_WIN_REACHED':
            msg = f'恭喜！您已達到最大贏利上限！自動為您提現 {final_winnings:,.0f} 油幣！'
        elif game_outcome == 'CASH_OUT_SUCCESS':
            msg = f'提現成功！您成功帶走了 {final_winnings:,.0f} 油幣！'
        elif game_outcome == 'CASH_OUT_ZERO_TILES':
            msg = '您尚未找到任何寶藏，無法提現。遊戲已結束。'
        elif game_outcome == 'GAME_ALREADY_OVER':
            msg = '遊戲已經結束了。'
        elif game_outcome == 'NO_ACTIVE_GAME':
            msg = '找不到正在進行的遊戲。'
        elif game_outcome == 'IMMEDIATE_REWARD_SETTLED':
            reason = kwargs.get('reason', '特殊獎勵')
            msg = f'獲得 {reason} 即時獎勵 {immediate_reward:,.0f} 油幣！'
        elif game_outcome == 'IMMEDIATE_REWARD_FAILED':
            reason = kwargs.get('reason', '特殊獎勵')
            error_msg = kwargs.get('error_message', '獎勵發放失敗')
            msg = f'{reason}獎勵計算完成，但{error_msg}'
        return msg

    def has_active_game(self, user_id: int) -> bool:
        user_games_dict = self.active_games.get(user_id)
        if not user_games_dict:
            return False
        for game in user_games_dict.values():
            if not game.game_over:
                return True
        return False

    async def create_game(self, user_id: int, bet_amount: int, mine_count: int) -> MinesGame:
        # 假設 self._check_and_deduct_bet 拋出異常或返回 new_balance (int)
        # 在此重構版本中，_check_and_deduct_bet 返回 new_balance，但我們在這裡不直接使用它
        # 主要目的是確保賭注被扣除或拋出適當的異常
        await self._check_and_deduct_bet(user_id=user_id, bet=bet_amount, game_name='尋寶礦區', min_bet=MIN_BET)
        
        game_instance_id = uuid.uuid4().hex
        try:
            new_game_instance = MinesGame(user_id=user_id, bet_amount=bet_amount, mine_count=mine_count, game_instance_id=game_instance_id)
            if user_id not in self.active_games:
                self.active_games[user_id] = {}
            self.active_games[user_id][game_instance_id] = new_game_instance
            return new_game_instance
        except ValueError as ve:
            logging.error(f'[MINES] Failed to create game for user {user_id} due to invalid parameters: {ve}')
            try:
                await self.rollback_bet(user_id=user_id, bet_amount=bet_amount, game_name='尋寶礦區', reason=f'棋盤生成失敗: {ve}')
            except Exception as r_err:
                logging.error(f'[MINES] Failed to rollback bet for user {user_id} after game creation failure: {r_err}')
            raise GameSettlementError(game_id='', reason=f'創建遊戲失敗: {ve}')
        except Exception as e:
            logging.error(f'[MINES] Unexpected error creating game for user {user_id}: {e}', exc_info=True)
            try:
                await self.rollback_bet(user_id=user_id, bet_amount=bet_amount, game_name='尋寶礦區', reason=f'意外錯誤: {e}')
            except Exception as r_err:
                logging.error(f'[MINES] Failed to rollback bet for user {user_id} after unexpected game creation error: {r_err}')
            raise GameSettlementError(game_id='', reason=f'創建遊戲時發生意外錯誤: {e}')

    def get_game_state(self, game_instance_id: str) -> Optional[GameState]:
        """根據遊戲實例 ID 獲取遊戲狀態"""
        game = self._get_game_by_instance_id(game_instance_id)
        return game.get_state_summary() if game else None

    def end_game(self, game_instance_id: str) -> None:
        """根據遊戲實例 ID 結束遊戲，清理資源並從活動遊戲中移除"""
        game = self._get_game_by_instance_id(game_instance_id)
        if not game:
            logging.warning(f'Attempted to end non-existent game with ID {game_instance_id}')
            return
        user_id = game.user_id
        if user_id in self.active_games and game_instance_id in self.active_games[user_id]:
            del self.active_games[user_id][game_instance_id]
            if not self.active_games[user_id]:
                del self.active_games[user_id]
            logging.debug(f'Game {game_instance_id} for user {user_id} has been ended and removed from active games.')
        else:
            logging.warning(f'Game {game_instance_id} for user {user_id} not found in active_games during end_game call.')

    async def reveal_tile(self, interaction: discord.Interaction, game_instance_id: str, row: int, col: int) -> Tuple[MinesGame, Dict[str, Any], Optional[float]]:
        game = self._get_game_by_instance_id(game_instance_id)
        if not game:
            logging.error(f'reveal_tile called for game_instance_id {game_instance_id} with no active game.')
            raise GameSettlementError(game_id=game_instance_id, reason='遊戲不存在或已結束')
            
        reveal_result_from_game = game.reveal_tile(row, col)
        new_balance_after_reveal: Optional[float] = None # 使用 float 以匹配 _settle_winnings 和 settle_immediate_reward 的返回類型
        
        if reveal_result_from_game.get('immediate_reward', 0) > 0:
            reward_amount = reveal_result_from_game['immediate_reward']
            reason_key = '金幣' if reveal_result_from_game.get('is_special_coin') else '星星' if reveal_result_from_game.get('is_special_star') else '特殊格'
            reward_reason = f'尋寶礦區-{reason_key}獎勵'
            
            try:
                # 假設 settle_immediate_reward 返回 new_balance (float) 或拋出異常
                new_balance_after_reveal = await self.settle_immediate_reward(game.user_id, reward_amount, reward_reason)
            except Exception as e:
                logging.error(f"Immediate reward settlement failed for game {game_instance_id}, user {game.user_id}. Error: {e}")
                # 根據重構指南，UI 層應捕獲 GameSettlementError 等。
                # 如果即時獎勵結算失敗是一個可恢復的錯誤，或者不應中止主遊戲流程，則 pass。
                # 否則，應拋出異常，例如:
                # raise GameSettlementError(game_id=game_instance_id, reason=f"即時獎勵結算失敗: {e}")
                pass 
                
        if game.game_over:
            final_winnings_value = game.final_winnings if game.final_winnings is not None else 0.0
            try:
                # 假設 _settle_winnings 返回 {'new_balance': int, 'awarded_amount': float}
                settlement_result = await self._settle_winnings(
                    user_id=game.user_id, 
                    payout=final_winnings_value, 
                    bet=game.bet_amount, 
                    game_name=f"尋寶礦區-{(game.last_result if game.last_result else '結束')}"
                )
                game.final_winnings = settlement_result['awarded_amount'] # 更新遊戲內的最終獎金為實際結算數額
                new_balance_after_reveal = float(settlement_result['new_balance']) # 轉換為 float 以匹配類型提示

            except Exception as e:
                logging.error(f"Main game settlement failed for game {game_instance_id}, user {game.user_id}. Error: {e}")
                self.end_game(game_instance_id) # 確保遊戲結束
                raise GameSettlementError(game_id=game_instance_id, reason=f"遊戲結算失敗: {e}")
                
            self.end_game(game_instance_id)
        
        return game, reveal_result_from_game, new_balance_after_reveal

    async def cash_out(self, interaction: discord.Interaction, game_instance_id: str) -> Tuple[MinesGame, float, Optional[float]]:
        game = self._get_game_by_instance_id(game_instance_id)
        if not game:
            logging.error(f'cash_out called for game_instance_id {game_instance_id} with no active game.')
            raise GameSettlementError(game_id=game_instance_id, reason='遊戲不存在或已結束')
            
        user_id_for_settlement = game.user_id
        new_balance_after_cashout: Optional[float] = None

        if game.game_over:
            logging.warning(f'cash_out called for game {game_instance_id} (user {user_id_for_settlement}) but game is already over.')
            # 遊戲已結束，返回遊戲狀態、已結算獎金，以及 None 表示新餘額（因為已結算）
            return game, game.final_winnings if game.final_winnings is not None else 0.0, None 
            
        winnings_to_settle = game.cash_out_game() # 這會將 game.game_over 設為 True
        
        if game.last_result == 'CASH_OUT_ZERO':
            logging.info(f'cash_out for game {game_instance_id} (user {user_id_for_settlement}) with zero safe tiles. Winnings: {winnings_to_settle}')
            self.end_game(game_instance_id)
            # 獲取當前餘額以返回
            try:
                balance_info = await self.economy_service.get_balance(user_id_for_settlement)
                new_balance_after_cashout = float(balance_info['balance'])
            except Exception as e:
                logging.error(f"Failed to fetch balance for user {user_id_for_settlement} on cash_out_zero: {e}")
            return game, 0.0, new_balance_after_cashout
            
        try:
            # 假設 _settle_winnings 返回 {'new_balance': int, 'awarded_amount': float}
            settlement_result = await self._settle_winnings(
                user_id=user_id_for_settlement, 
                payout=winnings_to_settle, 
                bet=game.bet_amount, 
                game_name='尋寶礦區-提現'
            )
            game.final_winnings = settlement_result['awarded_amount'] # 更新遊戲內的最終獎金
            new_balance_after_cashout = float(settlement_result['new_balance'])
            
            self.end_game(game_instance_id)
            return game, game.final_winnings if game.final_winnings is not None else 0.0, new_balance_after_cashout
            
        except Exception as e:
            logging.error(f'Error settling winnings for user {user_id_for_settlement} on cashout: {e}', exc_info=True)
            self.end_game(game_instance_id) 
            raise GameSettlementError(game_id=game_instance_id, reason=f"提現結算失敗: {e}")

    async def settle_immediate_reward(self, user_id: int, reward_amount: int, reason: str) -> float:
        if reward_amount <= 0:
            try:
                # 假設 economy_service.get_balance 返回 balance (int)
                balance_int = await self.economy_service.get_balance(user_id)
                return float(balance_int)
            except UserNotFoundError:
                raise
            except Exception as e:
                logging.error(f'Failed to fetch balance for user {user_id} for zero immediate reward: {e}')
                raise DatabaseOperationError(f"獲取用戶 {user_id} 餘額失敗 (即時獎勵為0時): {e}")

        try:
            # 假設 _settle_winnings 返回 {'new_balance': int, 'awarded_amount': float}
            # 我們只需要 new_balance，awarded_amount 在這裡不直接使用，但 _settle_winnings 會處理獎勵
            settlement_result = await self._settle_winnings(
                user_id=user_id, 
                payout=float(reward_amount), 
                bet=0, 
                game_name=reason
            )
            return float(settlement_result['new_balance'])
        except Exception as e:
            logging.error(f'Failed to settle immediate reward for user {user_id}: {e}')
            raise GameSettlementError(game_id=reason, reason=f"結算即時獎勵失敗: {e}")