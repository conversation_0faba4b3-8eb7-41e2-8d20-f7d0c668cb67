"""
Profile 相關的資料模型
"""
from dataclasses import dataclass
from typing import Optional, Dict, Any
from datetime import datetime
from decimal import Decimal


@dataclass
class UserProfile:
    """用戶檔案資料模型"""
    user_id: int
    showcased_card_collection_id: Optional[int] = None
    sub_card_1_collection_id: Optional[int] = None
    sub_card_2_collection_id: Optional[int] = None
    sub_card_3_collection_id: Optional[int] = None
    sub_card_4_collection_id: Optional[int] = None
    background_image_url: Optional[str] = None
    like_count: int = 0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    user_status: Optional[str] = None


@dataclass
class ProfileCardInfo:
    """檔案卡片資訊"""
    collection_id: int
    card_id: int
    name: str
    series: str
    image_url: str
    rarity: int
    star_level: int
    quantity: int
    local_image_path: Optional[str] = None  # 卡片預下載的本地圖片路徑


@dataclass
class ProfileData:
    """完整的檔案資料，用於圖片生成"""
    # 用戶基本資訊
    user_id: int
    nickname: Optional[str]
    oil_balance: int
    oil_ticket_balance: Decimal
    total_draws: int
    like_count: int
    
    # 統計資訊
    collection_completion_rate: float
    total_owned_cards: int
    rarity_counts: Dict[int, int]
    
    # 卡片資訊
    main_card: Optional[ProfileCardInfo] = None
    sub_cards: Dict[int, ProfileCardInfo] = None  # 1-4
    background_image_url: Optional[str] = None
    
    # 用戶個性化信息
    user_status: Optional[str] = None
    avatar_url: Optional[str] = None
    
    def __post_init__(self):
        if self.sub_cards is None:
            self.sub_cards = {}


@dataclass
class ProfileImageGenerationData:
    """圖片生成所需的資料"""
    profile_data: ProfileData
    output_size: tuple = (1536, 864) 