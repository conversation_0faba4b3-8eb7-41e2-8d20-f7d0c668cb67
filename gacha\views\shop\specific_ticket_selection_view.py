import discord
from discord.ext import commands
from discord.ui import View, But<PERSON>, Select
from typing import List, Dict, Any, TYPE_CHECKING, Optional, Tuple
from utils.logger import logger
from gacha.services.shop.shop_service import ShopService
from gacha.models.shop_models import ExchangeSessionData
from gacha.models.models import Card
from gacha.views.embeds.shop.specific_ticket_selection_embed_builder import build_specific_ticket_selection_embed
from gacha.views.embeds.shop.specific_ticket_result_embed_builder import build_specific_ticket_result_embed
from gacha.utils.interaction_utils import check_user_permission
if TYPE_CHECKING:
    from gacha.cogs.shop_cog import ShopCog
from gacha.views.modals.card_search_modal import CardSearchModal
from gacha.views.shop.specific_ticket_result_view import SpecificTicketResultView
from gacha.views.collection.collection_view.base_pagination import BasePaginationView, BasePaginationJumpModal

class SpecificTicketSelectionView(BasePaginationView):
    SEARCH_CARDS_CUSTOM_ID = 'search_cards_specific'
    SELECT_CARD_PREFIX_CUSTOM_ID = 'select_card_specific_'
    UNSELECT_CARD_CUSTOM_ID = 'unselect_current_card_specific'
    CONFIRM_EXCHANGE_CUSTOM_ID = 'confirm_exchange_specific'
    CANCEL_SELECTION_CUSTOM_ID = 'cancel_selection_specific'

    def __init__(self, original_interaction: discord.Interaction, cog: 'ShopCog', shop_service: 'ShopService', session_id: str, initial_session_data: ExchangeSessionData, items_per_page: int=1, *, timeout: Optional[float]=300.0):
        self.original_interaction = original_interaction
        self.user_id = original_interaction.user.id
        self.cog = cog
        self.shop_service = shop_service
        self.session_id = session_id
        self.session_data = initial_session_data
        self.search_query: Optional[str] = None
        self.items_per_page = items_per_page
        self.current_page_cards: List[Card] = []
        current_page = 1
        total_pages = 1
        super().__init__(user=original_interaction.user, current_page=current_page, total_pages=total_pages, timeout=timeout)
        self._add_custom_action_buttons()

    def _add_custom_action_buttons(self):
        search_button = Button(label='🔍 搜尋 (ID/名稱)', custom_id=self.SEARCH_CARDS_CUSTOM_ID, style=discord.ButtonStyle.blurple, row=1)
        search_button.callback = self._search_button_callback
        self.add_item(search_button)
        confirm_exchange_button = Button(label='✅ 確認兌換', custom_id=self.CONFIRM_EXCHANGE_CUSTOM_ID, style=discord.ButtonStyle.success, row=2)
        confirm_exchange_button.callback = self._confirm_exchange_button_callback
        self.add_item(confirm_exchange_button)
        cancel_button = Button(label='❌ 取消兌換', custom_id=self.CANCEL_SELECTION_CUSTOM_ID, style=discord.ButtonStyle.danger, row=2)
        cancel_button.callback = self._cancel_button_callback
        self.add_item(cancel_button)
        self._update_select_unselect_buttons()

    def _update_select_unselect_buttons(self):
        self.remove_item_by_custom_id(f'{self.SELECT_CARD_PREFIX_CUSTOM_ID}.*')
        self.remove_item_by_custom_id(self.UNSELECT_CARD_CUSTOM_ID)
        if self.current_page_cards:
            card = self.current_page_cards[0]
            select_button = Button(label='➡️ 選擇此卡', custom_id=f'{self.SELECT_CARD_PREFIX_CUSTOM_ID}{card.card_id}', style=discord.ButtonStyle.primary, row=1)
            select_button.callback = self._card_button_callback
            self.add_item(select_button)
            unselect_current_card_button = Button(label='↩️ 取消選擇此卡', custom_id=self.UNSELECT_CARD_CUSTOM_ID, style=discord.ButtonStyle.secondary, row=1, disabled=True)
            if self.session_data and any((sel_card_id == card.card_id for sel_card_id in self.session_data.pending_selected_cards)):
                unselect_current_card_button.disabled = False
            unselect_current_card_button.callback = self._unselect_current_card_button_callback
            self.add_item(unselect_current_card_button)
        else:
            pass

    def remove_item_by_custom_id(self, custom_id_pattern: str):
        import re
        is_regex = custom_id_pattern.endswith('.*')
        if is_regex:
            pattern = re.compile(custom_id_pattern.replace('.*', '.*'))
        items_to_remove = []
        for item in self.children:
            if hasattr(item, 'custom_id') and item.custom_id:
                if is_regex and pattern.fullmatch(item.custom_id):
                    items_to_remove.append(item)
                elif not is_regex and item.custom_id == custom_id_pattern:
                    items_to_remove.append(item)
        for item in items_to_remove:
            self.remove_item(item)

    async def refresh_page_data(self, interaction: discord.Interaction, is_initial_call: bool=False):
        """Fetches data for the current page, updates view state, and rebuilds UI if necessary."""
        logger.debug("SpecificTicketSelectionView: Refreshing page data for page %s, session %s, search: '%s'", self.current_page, self.session_id, self.search_query)
        rebuild_components = False
        try:
            updated_data = await self.shop_service.get_specific_ticket_card_page(session_id=self.session_id, page_number=self.current_page)
            # 重構後的直接返回卡片數據而非包含status的字典
            page_data = updated_data
            card_list = page_data.get('cards', [])
            self.current_page_cards = card_list if isinstance(card_list, list) else []
            pagination_info = page_data.get('pagination', {})
            new_total_pages = pagination_info.get('total_pages', 1)
            if is_initial_call or new_total_pages != self.total_pages:
                self.total_pages = new_total_pages
                self.current_page = pagination_info.get('current_page', self.current_page)
                rebuild_components = True
            raw_session_data = page_data.get('session_data')
            if raw_session_data:
                if isinstance(raw_session_data, dict):
                    self.session_data = ExchangeSessionData(**raw_session_data)
                elif isinstance(raw_session_data, ExchangeSessionData):
                    self.session_data = raw_session_data
        except Exception as e:
            logger.warning('Failed to fetch page %s for session %s: %s', self.current_page, self.session_id, str(e))
            self.current_page_cards = []
            if is_initial_call:
                self.total_pages = 1
                rebuild_components = True
        
        if rebuild_components:
            self.clear_items()
            self._add_pagination_buttons()
            self._add_custom_action_buttons()
        self._update_select_unselect_buttons()
        self._refresh_button_states()

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """Overrides BasePaginationView._update_page to refresh data and edit the message."""
        self.current_page = page
        await self.refresh_page_data(interaction)
        embed = await self.get_current_page_embed()
        try:
            await interaction.edit_original_response(embed=embed, view=self)
        except discord.HTTPException as e:
            logger.error('SpecificTicketSelectionView: Failed to edit message on page update (page %s): %s', page, e, exc_info=True)

    async def get_current_page_embed(self) -> discord.Embed:
        """獲取當前頁的嵌入式視圖。"""
        cards_for_embed = []
        if self.current_page_cards and len(self.current_page_cards) > 0:
            cards_for_embed = self.current_page_cards
        pending_selected_card_objects: List[Card] = []
        if self.session_data and self.session_data.pending_selected_cards:
            for card_id in self.session_data.pending_selected_cards:
                try:
                    card_object = await self.shop_service.master_card_repo.get_card(card_id)
                    if card_object:
                        pending_selected_card_objects.append(card_object)
                    else:
                        logger.warning('Could not find card with ID %s while building embed for session %s', card_id, self.session_id)
                except Exception as e:
                    logger.error('Error fetching card ID %s for embed: %s', card_id, e, exc_info=True)
        return build_specific_ticket_selection_embed(interaction=self.original_interaction, session_data=self.session_data, cards_on_page=cards_for_embed, pending_selected_card_objects=pending_selected_card_objects, current_page=self.current_page, total_pages=self.total_pages, search_query=self.search_query)

    async def send_initial_message(self, interaction: discord.Interaction):
        if not self.current_page_cards and self.total_pages > 0:
            await self._update_page(self.current_page, interaction)
        embed = await self.get_current_page_embed()
        return (embed, self)

    async def prepare_initial_message_payload(self, interaction_for_initial_data_fetch: discord.Interaction) -> tuple[discord.Embed, 'SpecificTicketSelectionView']:
        self.current_page = 1
        await self.refresh_page_data(interaction_for_initial_data_fetch, is_initial_call=True)
        embed = await self.get_current_page_embed()
        return (embed, self)

    async def _search_button_callback(self, interaction: discord.Interaction):
        search_modal = CardSearchModal(parent_view=self)
        await interaction.response.send_modal(search_modal)

    async def handle_search_submit(self, interaction: discord.Interaction, search_query: str):
        """處理用戶的卡片搜索提交。"""
        self.search_query = search_query
        try:
            result = await self.shop_service.find_target_page_for_specific_exchange(session_id=self.session_id, search_query=search_query)
            target_page = result.get('target_page')
            
            if target_page is None:
                await interaction.response.send_message(f"找不到匹配 '{search_query}' 的卡片。", ephemeral=True)
                return
                
            self.current_page = target_page
            logger.info("Search query '%s' found target card on page %s for session %s", search_query, target_page, self.session_id)
            
            if not await self.interaction_manager.try_defer(interaction, ephemeral=True):
                logger.warning("handle_search_submit: Failed to defer interaction %s for search '%s'. It might have been responded to already.", interaction.id, search_query)
                
            await self.refresh_page_data(interaction)
            embed = await self.get_current_page_embed()
            try:
                await interaction.edit_original_response(embed=embed, view=self)
            except discord.HTTPException as e:
                logger.error('SpecificTicketSelectionView: Failed to edit message after search submit: %s', e, exc_info=True)
                if isinstance(e, discord.InteractionResponded) and (not interaction.response.is_done()):
                    try:
                        await interaction.followup.send('搜索完成，但無法更新訊息。請重試或刷新頁面。', ephemeral=True)
                    except discord.HTTPException:
                        pass
        except Exception as e:
            await interaction.response.send_message(f"搜索失敗: {str(e)}", ephemeral=True)
            logger.warning('Search query failed: %s', str(e))

    async def _card_button_callback(self, interaction: discord.Interaction):
        if not await self.interaction_manager.try_defer(interaction):
            logger.warning('Card selection: Failed to defer interaction %s for session %s. It might have been responded to already.', interaction.id, self.session_id)
        selected_card_id_str = interaction.data['custom_id'].replace(self.SELECT_CARD_PREFIX_CUSTOM_ID, '')
        try:
            selected_card_id = int(selected_card_id_str)
        except ValueError:
            logger.error('Invalid card ID format: %s', selected_card_id_str)
            await interaction.followup.send('選取的卡片ID無效。', ephemeral=True)
            return
            
        try:
            result = await self.shop_service.process_specific_ticket_selection(session_id=self.session_id, selected_card_id=selected_card_id)
            logger.debug('Card selection result: %s', result)
            # 重構後的返回不再包含status字段
            message = result.get('message')
            next_step = result.get('next_step')
            
            # 檢查是否有新的會話數據
            session_data_summary = result.get('session_data_summary')
            if session_data_summary:
                # 更新當前會話數據
                await self.refresh_page_data(interaction)
                
            final_embed = await self.get_current_page_embed()
            try:
                content = None
                await interaction.edit_original_response(content=content, embed=final_embed, view=self)
            except discord.HTTPException as e:
                logger.error('SpecificTicketSelectionView: Failed to edit message after card selection: %s', e, exc_info=True)
        except Exception as e:
            error_message = f'選卡失敗: {str(e)}'
            await interaction.followup.send(error_message, ephemeral=True)
            logger.error('Card selection error: %s', str(e))

    async def _unselect_current_card_button_callback(self, interaction: discord.Interaction):
        if not await self.interaction_manager.try_defer(interaction):
            logger.warning('Card unselection: Failed to defer interaction %s for session %s.', interaction.id, self.session_id)
        if not self.current_page_cards:
            await interaction.followup.send('當前頁面沒有可取消選擇的卡片。', ephemeral=True)
            return
            
        try:
            card_to_unselect = self.current_page_cards[0]
            result = await self.shop_service.unselect_card_for_exchange(session_id=self.session_id, card_id_to_unselect=card_to_unselect.card_id)
            logger.debug('Card unselection result: %s', result)
            
            # 更新視圖
            await self.refresh_page_data(interaction)
            final_embed = await self.get_current_page_embed()
            try:
                await interaction.edit_original_response(embed=final_embed, view=self)
            except discord.HTTPException as e:
                logger.error('SpecificTicketSelectionView: Failed to edit message after card unselection: %s', e, exc_info=True)
        except Exception as e:
            error_message = f'取消選卡失敗: {str(e)}'
            await interaction.followup.send(error_message, ephemeral=True)
            logger.error('Card unselection error: %s', str(e))

    async def _confirm_exchange_button_callback(self, interaction: discord.Interaction):
        if not self.session_data.pending_selected_cards:
            await interaction.response.send_message('您尚未選擇任何卡片。請先選卡再確認兌換。', ephemeral=True)
            return
            
        if len(self.session_data.pending_selected_cards) < self.session_data.total_quantity_to_redeem:
            needed = self.session_data.total_quantity_to_redeem - len(self.session_data.pending_selected_cards)
            await interaction.response.send_message(f'您尚未選擇足夠的卡片。還需選擇 {needed} 張才能兌換。', ephemeral=True)
            return
            
        await interaction.response.defer()
        try:
            result = await self.shop_service.finalize_specific_ticket_exchange(session_id=self.session_id)
            
            # 兌換成功，顯示結果
            exchanged_cards_data = result.get('exchanged_cards', [])
            
            # 將字典轉換為Card對象
            exchanged_cards = []
            for card_data in exchanged_cards_data:
                if isinstance(card_data, dict):
                    exchanged_cards.append(Card(**card_data))
                elif hasattr(card_data, 'card_id'):  # 已經是Card對象
                    exchanged_cards.append(card_data)
                else:
                    logger.warning("Unexpected card data format: %s", type(card_data))
            
            result_embed = build_specific_ticket_result_embed(
                interaction, 
                self.session_data.ticket_definition.display_name,
                exchanged_cards
            )
            
            # 正確提供所需的參數
            ticket_name = self.session_data.ticket_definition.display_name
            result_view = SpecificTicketResultView(
                original_interaction=interaction,
                ticket_name=ticket_name,
                exchanged_cards=exchanged_cards
            )
            
            await interaction.edit_original_response(content=None, embed=result_embed, view=result_view)
        except Exception as e:
            error_message = f'兌換失敗：{str(e)}'
            await interaction.followup.send(error_message, ephemeral=True)
            logger.error('Specific ticket exchange finalization error: %s', str(e))

    async def _cancel_button_callback(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)
        try:
            await self.shop_service.cancel_exchange_session(self.session_id)
            await interaction.followup.send('已取消兌換。', ephemeral=True)
            self.stop()
        except Exception as e:
            error_message = f'取消兌換失敗：{str(e)}'
            await interaction.followup.send(error_message, ephemeral=True)
            logger.error('Cancel exchange error: %s', str(e))

    async def disable_view(self, interaction: Optional[discord.Interaction], is_final_action: bool=False, message_on_disable: Optional[str]=None):
        for item in self.children:
            if hasattr(item, 'disabled'):
                item.disabled = True
        if self.message:
            try:
                content = message_on_disable if message_on_disable else self.message.content
                view_to_set = None if is_final_action else self
                if interaction and interaction.response.is_done():
                    logger.info('disable_view: Interaction %s already responded. Editing self.message.', interaction.id)
                    await self.message.edit(content=content, view=view_to_set)
                elif interaction:
                    logger.info('disable_view: Interaction %s not responded. Using it to edit.', interaction.id)
                    await interaction.edit_original_response(content=content, view=view_to_set)
                else:
                    logger.info('disable_view: No interaction provided. Editing self.message.')
                    await self.message.edit(content=content, view=view_to_set)
            except discord.HTTPException as e:
                logger.error('Failed to edit message on disable_view for session %s: %s', self.session_id, e, exc_info=True)
        if is_final_action:
            self.stop()

    async def on_timeout(self) -> None:
        logger.info('SpecificTicketSelectionView timed out for session %s (user: %s).', self.session_id, self.user_id)
        try:
            await self.shop_service.cancel_exchange_session(self.session_id)
            logger.info('Successfully cancelled shop session %s on view timeout.', self.session_id)
        except Exception as e:
            logger.error('Error cancelling shop session %s on view timeout: %s', self.session_id, e, exc_info=True)
        self.stop()

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        return await check_user_permission(interaction, self.session_data.user_id, '您無法操作此互動介面。')