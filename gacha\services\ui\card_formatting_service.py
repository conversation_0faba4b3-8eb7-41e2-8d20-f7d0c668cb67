# -*- coding: utf-8 -*-
"""
服務類，用於提供卡片相關的UI元素格式化功能。
"""
from typing import Optional, TYPE_CHECKING
from decimal import Decimal

from gacha.app_config import config_service
from gacha.views import utils as view_utils
from gacha.constants import RarityLevel
from gacha.views.utils import format_oil  # 假設存在，用於格式化油幣

if TYPE_CHECKING:
    from gacha.models.models import Card  # 避免循環導入


class CardFormattingService:
    """
    提供卡片UI元素格式化邏輯的服務。
    """

    def __init__(self):
        # 如果服務需要狀態或一次性加載的配置，可以在此初始化
        pass

    def format_draw_title(
        self,
        display_name: str,
        is_wish: bool,
        pool_name_or_prefix: str,  # 可以是卡池類型，服務內部獲取前綴，或者直接傳入前綴
        is_new_card: bool,
    ) -> str:
        """格式化抽卡結果的Embed標題。"""
        wish_prefix = "✨" if is_wish else ""
        # 假設 pool_name_or_prefix 若為卡池類型，config_service 能處理
        # 這裡簡化為直接使用傳入的 pool_name_or_prefix 作為顯示
        # 實際可能需要: pool_display = config_service.get_pool_display_name(pool_name_or_prefix)
        title_suffix = "新卡!" if is_new_card else "重複!"
        return (
            f"{display_name} 抽到了 {wish_prefix}{pool_name_or_prefix} {title_suffix}"
        )

    def format_card_name_and_star_line(
        self,
        card_name: str,
        is_favorite: bool,
        is_new_card: bool,
        is_wish: bool,
        pool_emoji_prefix: str,  # 通常從 config_service 獲取
        star_level: int = 0,
    ) -> str:
        """格式化卡片名稱、狀態 Emoji 和星級。"""
        ui_emoji = (
            view_utils.get_ui_emoji("heart")
            if is_favorite
            else (
                view_utils.get_ui_emoji("new_card")
                if is_new_card
                else view_utils.get_ui_emoji("old_card")
            )
        )
        wish_prefix = "✨ " if is_wish else ""
        star_display_string = ""
        if star_level > 0:
            star_display_string = view_utils.get_star_emoji_string(star_level)

        return f"{ui_emoji} **{pool_emoji_prefix}{wish_prefix}{card_name}**{(' ' + star_display_string) if star_display_string else ''}"

    def format_card_series_line(
        self,
        card_series: str,
        is_favorite: bool,  # is_favorite 和 is_new_card 用於決定 ui_emoji
        is_new_card: bool,
    ) -> str:
        """格式化卡片系列行。"""
        ui_emoji = (
            view_utils.get_ui_emoji("heart")
            if is_favorite
            else (
                view_utils.get_ui_emoji("new_card")
                if is_new_card
                else view_utils.get_ui_emoji("old_card")
            )
        )
        return f"{ui_emoji} *{card_series}*"

    def format_rarity_pool_line(
        self, rarity_enum: Optional[RarityLevel], pool_type: str
    ) -> str:
        """格式化稀有度和卡池資訊行。"""
        rarity_display = (
            view_utils.get_user_friendly_rarity_name(rarity_enum)
            if rarity_enum
            else "未知稀有度"
        )
        pool_display_text = config_service.get_config('gacha_core_settings.pool_type_prefixes').get(
            pool_type, pool_type
        )  # 使用前綴或原始類型
        return f"稀有度: {rarity_display} | {pool_display_text}"

    def format_owner_count_display_line(
        self,
        owner_count: int,
        # base_builder_format_owner_count_method: Callable[[int, str], str] #
        # 假設BaseEmbedBuilder的方法
    ) -> str:
        """
        格式化擁有者數量顯示行 (用於抽卡詳情)。
        注意: BaseEmbedBuilder._format_owner_count 已經能產生 "擁有者: X" 或 "持有人數: `X`"。
        此方法主要添加 emoji。
        """
        # owner_text_from_base = base_builder_format_owner_count_method(owner_count, "draw")
        # 這裡我們直接根據 DrawEmbedBuilder 的邏輯來組裝，因為 _format_owner_count 可能變化
        if owner_count > 0:
            owner_emoji = "👥"  # 或者 view_utils.get_ui_emoji('owner')
            return f"{owner_emoji} 擁有者: {owner_count}"
        return ""

    def format_market_price_display(
        self,
        current_market_sell_price: Optional[Decimal],
        card_rarity_for_fallback: Optional[RarityLevel],  # 用於價格回退
        pool_type_for_fallback: str,  # 用於價格回退
        trend_symbol: str = "",  # 趨勢符號，目前通常為空
    ) -> tuple[str, str]:
        """
        格式化市場價格顯示。
        返回 (價格標籤, 格式化後的價格字串)。
        例如 ("市場價", "💧 `12345`") 或 ("基礎賣價 (異常)", "💧 `100`")
        """
        price_text_label = "市場價"
        price_display_text = "價格計算中..."
        oil_emoji = config_service.get_oil_emoji()

        if current_market_sell_price is not None:
            if isinstance(current_market_sell_price, Decimal):
                price_display_text = f"`{int(current_market_sell_price)}` {oil_emoji} {trend_symbol}".strip(
                )
            else:
                try:
                    actual_price_decimal = Decimal(
                        str(current_market_sell_price))
                    price_display_text = f"`{int(actual_price_decimal)}` {oil_emoji} {trend_symbol}".strip(
                    )
                except Exception:
                    # logging.warning(f"Card ID {self.card.card_id}:
                    # current_market_sell_price ('{actual_price}') is not a
                    # valid Decimal. Falling back to base price.") # 需要卡片ID來日誌
                    rarity_value_for_fallback = (
                        card_rarity_for_fallback.value
                        if card_rarity_for_fallback
                        else None
                    )
                    base_sell_price = config_service.gacha_core.pool_rarity_prices.get(
                        pool_type_for_fallback, {}).get(rarity_value_for_fallback, 0)
                    price_display_text = f"`{base_sell_price}` {oil_emoji}"
                    price_text_label = "基礎賣價 (異常)"
        else:
            rarity_value_for_fallback = (
                card_rarity_for_fallback.value if card_rarity_for_fallback else None)
            base_sell_price = config_service.gacha_core.pool_rarity_prices.get(
                pool_type_for_fallback, {}
            ).get(rarity_value_for_fallback, 0)
            price_display_text = f"`{base_sell_price}` {oil_emoji}"
            price_text_label = "基礎賣價 (未更新)"
        return price_text_label, price_display_text

    def format_balance_display(self, balance: int) -> str:
        """格式化餘額顯示。"""
        # format_oil 預期會返回類似 "餘額: 💧 XXXX" 的字串
        return format_oil(balance, label="餘額")

    def combine_price_and_balance_field_value(
        self, price_label: str, price_display: str, balance_display: str
    ) -> str:
        """組合價格和餘額信息成為一個 Embed field value。"""
        return f"{price_label}: {price_display} | {balance_display}"

    def format_draw_footer_text_main_content(
        self,
        is_wish: bool,
        sell_command_text: str = "使用/sw 賣出卡片",  # 讓它可以被配置
    ) -> str:
        """
        格式化抽卡 Embed 頁腳的主要文字內容 (不包含卡片ID和機器人簽名，那些由 BaseEmbedBuilder._set_common_footer 添加)。
        """
        parts = [sell_command_text]
        if is_wish:
            parts.append("✨許願成功✨")
        return " • ".join(filter(None, parts))

    def format_multi_draw_page_card_event_title(
        self,
        display_name: str,
        # card_name: str, # REMOVED card_name from parameters
        is_wish: bool,
        pool_type: Optional[str],
        is_new_card: bool,
    ) -> str:
        """格式化十連抽翻頁檢視時，單張卡片的事件描述標題 (不含卡片名稱)。
        例如: '玩家A 抽到了 普通 新卡!' 或 '玩家B 抽到了 ✨限定池 重複!'
        """
        wish_indicator = "✨" if is_wish else ""

        pool_prefix_value = ""
        if pool_type:
            pool_prefix_value = config_service.get_config('gacha_core_settings.pool_type_prefixes').get(
                pool_type, ""
            )

        status_text = "新卡!" if is_new_card else "重複!"

        # Combine wish indicator and pool prefix.
        # Example: wish="✨", pool="普通" -> "✨普通"
        # Example: wish="", pool="普通" -> "普通"
        # Example: wish="✨", pool="" -> "✨"
        # Example: wish="", pool="" -> ""
        combined_wish_pool = f"{wish_indicator}{pool_prefix_value}"

        # If there's a wish indicator or a pool prefix (or both)
        if combined_wish_pool:
            return f"{display_name} 抽到了 {combined_wish_pool} {status_text}"
        else:  # Fallback if no wish and no pool prefix could be determined
            # This case implies the card might be from a pool type that has no prefix configured,
            # or pool_type was None.
            return f"{display_name} 抽到了 {status_text}"

    def format_multi_draw_card_summary_line(
        self,
        card_name: str,
        card_series: str,
        is_favorite: bool,
        is_new_card: bool,
        is_wish: bool,
        rarity_enum: Optional[RarityLevel],
        shorten_series: bool = False,
        max_series_len: int = 12,  # 可配置的系列最大長度
        series_suffix: str = "...",  # 可配置的系列縮短後綴
    ) -> str:
        """格式化十連抽總覽中單張卡片的摘要行。"""
        star_emoji = (
            view_utils.get_ui_emoji("heart")
            if is_favorite
            else (
                view_utils.get_ui_emoji("new_card")
                if is_new_card
                else view_utils.get_ui_emoji("old_card")
            )
        )

        rarity_emoji = (view_utils.get_rarity_display_code(
            rarity_enum) if rarity_enum else "<?> ")  # 未知稀有度時

        wish_prefix = "✨ " if is_wish else ""

        processed_series_name = card_series
        if shorten_series and len(card_series) > max_series_len:
            actual_max = max_series_len - len(series_suffix)
            if actual_max < 0:
                actual_max = 0
            processed_series_name = card_series[:actual_max] + series_suffix

        return f"{star_emoji} {rarity_emoji}{wish_prefix}**{card_name}** *({processed_series_name})*"


# Example instantiation (optional, for testing or if it's a singleton)
# card_formatter = CardFormattingService()
