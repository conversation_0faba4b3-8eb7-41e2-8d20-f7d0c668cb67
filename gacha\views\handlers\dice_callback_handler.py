"""
Gacha 系統骰子遊戲回調處理器
"""
from typing import TYPE_CHECKING, Optional, Any, Dict
import discord
import functools
from utils.logger import logger
from gacha.utils import interaction_utils
from gacha.utils.cog_error_handler import handle_gacha_error # 導入 handle_gacha_error
from .base_game_handler import BaseGameCallbackHandler
from gacha.services.games.dice_game_service import DiceGameService
from gacha.services.core.economy_service import EconomyService
from gacha.services.ui.game_display_service import GameDisplayService
from gacha.views.games.dice_game_view import DiceGameView
from gacha.views.embeds.games.dice_game_embed_builder import DiceGameEmbedBuilder
if TYPE_CHECKING:
    from gacha.views.collection.collection_view.interaction_manager import InteractionManager

class DiceCallbackHandler(BaseGameCallbackHandler):
    """處理骰子遊戲視圖回調的類"""

    def __init__(self, interaction_manager: 'InteractionManager', user_id: int, dice_game_service: DiceGameService, economy_service: EconomyService, game_display_service: GameDisplayService, view: 'DiceGameView'):
        """
        初始化骰子遊戲回調處理器

        參數:
            interaction_manager: InteractionManager 實例
            user_id: 觸發遊戲的原始用戶 ID
            dice_game_service: DiceGameService 實例
            economy_service: EconomyService 實例
            game_display_service: GameDisplayService 實例
            view: 關聯的 DiceGameView 實例
        """
        super().__init__(interaction_manager, user_id)
        self._dice_game_service = dice_game_service
        self._economy_service = economy_service
        self._game_display_service = game_display_service
        self.view = view

    async def handle_bet_selected(self, interaction: discord.Interaction, new_bet: int):
        """處理下注金額按鈕點擊"""
        if not await interaction_utils.check_user_permission(interaction, self.user_id):
            return
        await interaction_utils.safe_defer(interaction)
        try:
            current_view: Optional[DiceGameView] = self.view
            if current_view and isinstance(current_view, DiceGameView):
                current_view.bet = new_bet
                user_id = self.user_id
                latest_balance = 0
                try:
                    balance_info = await self._economy_service.get_balance(user_id)
                    latest_balance = balance_info.get('balance', 0)
                except Exception as e:
                    logger.error('Error fetching balance for embed update in handler for user %s: %s', user_id, e)
                    latest_balance = 0
                updated_embed = current_view.get_initial_embed(latest_balance)
                await interaction.edit_original_response(embed=updated_embed, view=current_view)
                logger.debug('Updated dice game bet to %s for user %s.', new_bet, self.user_id)
                await current_view.init_buttons()
                logger.debug('Re-initialized buttons on dice game view for user %s after bet update.', self.user_id)
            else:
                logger.error('Cannot find or cast view for interaction %s to update bet.', interaction.id)
                await interaction_utils.safe_send_message(interaction, '更新下注金額時發生錯誤。', ephemeral=True)
        except Exception as e:
            await handle_gacha_error(interaction, e, '處理骰子下注按鈕時')

    async def handle_choice_selected(self, interaction: discord.Interaction, choice: str):
        """處理 '大' 或 '小' 按鈕點擊並開始遊戲"""
        if not await interaction_utils.check_user_permission(interaction, self.user_id):
            return
        await interaction_utils.safe_defer(interaction)
        try:
            current_view: Optional[DiceGameView] = self.view
            if current_view:
                # 按鈕將通過 View 的 timeout 自動禁用，無需手動處理
                # 立即停止此 View 響應後續交互，無需 API 調用
                current_view.stop()
                logger.debug('Stopped dice game view for interaction %s to prevent further clicks.', interaction.id)
            game_id = str(interaction.id)
            actual_bet = self.view.bet if self.view else 10
            
            # 使用純異常模式
            await self._dice_game_service.start_dice_game(interaction, self.user_id, actual_bet, choice, game_id)
            
        except Exception as e:
            await handle_gacha_error(interaction, e, '處理骰子選擇並開始遊戲時')

    async def handle_replay_callback(self, interaction: discord.Interaction):
        """處理遊戲結束後的重玩按鈕點擊 (Same, Change, Double, Min)"""
        if not await interaction_utils.check_user_permission(interaction, self.user_id):
            return
        # No need to stop the view here, as start_dice_game will send a new message.
        # Stopping might cause "interaction failed" if start_dice_game needs the original interaction context.
            
        await interaction_utils.safe_defer(interaction, ephemeral=True, thinking=False)
        custom_id = interaction.data.get('custom_id', '')
        logger.debug('Handling Dice replay callback with custom_id: %s', custom_id)
        try:
            parts = custom_id.split(':')
            if len(parts) < 4 or parts[0] != 'dice' or parts[1] != 'replay':
                logger.warning('Invalid custom_id format for Dice replay: %s', custom_id)
                await interaction.followup.send('無效的操作請求。', ephemeral=True)
                return
            action = parts[2]
            try:
                original_bet_base = int(parts[3])
            except ValueError:
                logger.warning('Invalid original bet in custom_id for replay: %s', parts[3])
                await interaction.followup.send('無效的操作請求（賭注格式錯誤）。', ephemeral=True)
                return
            new_bet = original_bet_base
            new_choice = 'big'
            if action == 'change':
                if len(parts) < 5:
                    logger.warning("Invalid custom_id format for Dice replay 'change': %s", custom_id)
                    await interaction.followup.send('無效的操作請求（缺少新選擇）。', ephemeral=True)
                    return
                new_choice = parts[4]
                if new_choice not in ['big', 'small']:
                    logger.warning("Invalid new choice in custom_id for replay 'change': %s", new_choice)
                    await interaction.followup.send('無效的操作請求（選擇無效）。', ephemeral=True)
                    return
                new_bet = original_bet_base
            min_bet = self._dice_game_service.MIN_BET if hasattr(self._dice_game_service, 'MIN_BET') else 10
            if action == 'same':
                pass
            elif action == 'double':
                new_bet = original_bet_base * 2
            elif action == 'half':
                new_bet = max(min_bet, original_bet_base // 2)
            elif action == 'min':
                new_bet = min_bet
            elif action != 'change':
                logger.warning('Unknown replay action: %s', action)
                await interaction.followup.send('未知的重玩操作。', ephemeral=True)
                return
            
            # 檢查餘額 - 使用純異常模式
            balance_info = await self._economy_service.get_balance(self.user_id)
            latest_balance = balance_info.get('balance', 0)
            if latest_balance < new_bet:
                await interaction.followup.send(f'你的油幣不足以進行 {new_bet} 油幣的下注！(當前餘額: {latest_balance})', ephemeral=True)
                return
            # 按鈕將通過 View 的 timeout 自動禁用，無需手動處理
            
                
            # 開始新遊戲 - 使用純異常模式
            new_game_id = str(interaction.id)
            await self._dice_game_service.start_dice_game(interaction, self.user_id, new_bet, new_choice, new_game_id)
            
        except Exception as e:
            # 使用統一的錯誤處理
            await handle_gacha_error(interaction, e, '處理骰子重玩按鈕時')
            try:
                if not interaction.response.is_done():
                    await interaction.followup.send('處理重玩請求時發生未預期錯誤。', ephemeral=True)
                else:
                    logger.warning('Attempted to send followup error message after interaction already responded.')
            except Exception as followup_e:
                logger.error('Failed to send fallback followup message after error in handle_replay_callback: %s', followup_e)
    pass