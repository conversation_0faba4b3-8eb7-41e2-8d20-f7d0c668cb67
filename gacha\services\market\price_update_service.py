import asyncio
import logging
import time
from typing import List, Dict, Set, Optional
from decimal import Decimal
from gacha.repositories.card.master_card_repository import MasterCardRepository
from gacha.services.market.market_price_service import MarketPriceService
from utils.logger import logger
from gacha.app_config import config_service

class PriceUpdateService:

    def __init__(self, master_card_repo: MasterCardRepository, market_price_service: MarketPriceService):
        price_update_cfg = config_service.get_price_update_service_config()
        queue_max_size: int = price_update_cfg.queue_max_size
        batch_size: int = price_update_cfg.batch_size
        batch_interval: float = price_update_cfg.batch_interval_seconds
        if master_card_repo is None:
            raise ValueError('PriceUpdateService requires a MasterCardRepository instance.')
        if market_price_service is None:
            raise ValueError('PriceUpdateService requires a MarketPriceService instance.')
        self.master_card_repo = master_card_repo
        self.market_price_service = market_price_service
        self._update_queue: asyncio.Queue[int] = asyncio.Queue(maxsize=queue_max_size)
        self._worker_task: Optional[asyncio.Task] = None
        self._active = False
        self.batch_size = batch_size
        self.batch_interval = batch_interval
        self.logger = logger

    async def start(self):
        """Starts the price update worker task."""
        if self._worker_task is None or self._worker_task.done():
            self._active = True
            self._worker_task = asyncio.create_task(self._price_update_worker())
        else:
            self.logger.warning('PriceUpdateService worker already running.')

    async def stop(self):
        """Stops the price update worker task gracefully."""
        self._active = False
        if self._update_queue:
            try:
                self._update_queue.put_nowait(-1)
            except asyncio.QueueFull:
                self.logger.warning('Price update queue is full while trying to add sentinel for stop.')
        if self._worker_task:
            try:
                await asyncio.wait_for(self._worker_task, timeout=self.batch_interval + 5.0)
            except asyncio.TimeoutError:
                self.logger.warning('PriceUpdateService worker did not stop in time, cancelling.')
                self._worker_task.cancel()
            except Exception as e:
                self.logger.error(f'Error during PriceUpdateService stop: {e}', exc_info=True)

    async def schedule_price_update(self, card_ids: List[int]):
        """
        Schedules a list of card_ids for market price recalculation and update.
        """
        if not self._active and (self._worker_task is None or self._worker_task.done()):
            self.logger.warning('PriceUpdateService is not active. Consider starting it first.')
        count = 0
        for card_id in card_ids:
            try:
                self._update_queue.put_nowait(card_id)
                count += 1
            except asyncio.QueueFull:
                self.logger.warning(f'Price update queue is full. Card_id {card_id} and subsequent IDs were not added.')
                break
        if count > 0:
            pass

    async def schedule_full_price_recalculation(self):
        """
        Schedules all master card IDs for a full market price recalculation.
        This is intended for periodic fallback checks.
        """
        if not self._active and (self._worker_task is None or self._worker_task.done()):
            self.logger.warning('PriceUpdateService is not active for full recalculation. Consider starting it.')
        try:
            all_card_ids = await self.master_card_repo.get_all_master_card_ids()
            if not all_card_ids:
                return
            scheduled_count = 0
            PUT_TIMEOUT_SECONDS = 5.0
            for card_id in all_card_ids:
                try:
                    await asyncio.wait_for(self._update_queue.put(card_id), timeout=PUT_TIMEOUT_SECONDS)
                    scheduled_count += 1
                except asyncio.TimeoutError:
                    self.logger.error(f'Timeout ({PUT_TIMEOUT_SECONDS}s) putting card_id {card_id} into queue during full recalculation. Queue might be persistently full or worker is stalled. Aborting further scheduling.')
                    break
                except Exception as e:
                    self.logger.error(f'Error putting card_id {card_id} into queue: {e}', exc_info=True)
                    break
            if scheduled_count > 0:
                self.logger.info(f'Scheduled {scheduled_count}/{len(all_card_ids)} cards for full price recalculation.')
                if scheduled_count < len(all_card_ids):
                    self.logger.warning('Full price recalculation scheduling was incomplete due to issues (see prior errors).')
        except Exception as e:
            self.logger.error(f'Error during schedule_full_price_recalculation: {e}', exc_info=True)

    async def _process_batch(self, card_ids_to_process: Set[int]):
        """
        Processes a batch of card_ids: calculates new prices and updates them in the database.
        """
        if not card_ids_to_process:
            return
        batch_process_start_time = time.monotonic()
        num_ids = len(card_ids_to_process)
        try:
            calc_prices_start_time = time.monotonic()
            new_prices: Dict[int, Decimal] = await self.market_price_service.calculate_and_get_market_prices_for_ids(list(card_ids_to_process))
            calc_prices_duration = (time.monotonic() - calc_prices_start_time) * 1000
            if not new_prices:
                processing_time_total = (time.monotonic() - batch_process_start_time) * 1000
                return
            self.logger.debug(f'[_process_batch] Calculated new prices for {len(new_prices)} cards. Example: {dict(list(new_prices.items())[:3])}')
            db_update_start_time = time.monotonic()
            updated_rows = await self.master_card_repo.bulk_update_market_prices(new_prices)
            db_update_duration = (time.monotonic() - db_update_start_time) * 1000
            processing_time_total = (time.monotonic() - batch_process_start_time) * 1000
            if updated_rows != len(new_prices):
                self.logger.warning(f'[_process_batch] Mismatch in updated rows. Expected {len(new_prices)}, got {updated_rows}. Card IDs with new prices: {list(new_prices.keys())}. Initial batch size: {num_ids}.')
        except Exception as e:
            processing_time_total_error = (time.monotonic() - batch_process_start_time) * 1000
            self.logger.error(f'[_process_batch] Error processing price update batch for {num_ids} card_ids (Example: {list(card_ids_to_process)[:10]}): {e}. Total batch time before error: {processing_time_total_error:.2f} ms.', exc_info=True)

    async def _price_update_worker(self):
        """
        Worker task that continuously fetches card_ids from the queue,
        batches them, and processes them.
        """
        batch_card_ids: Set[int] = set()
        last_processed_time = asyncio.get_event_loop().time()
        while self._active or not self._update_queue.empty():
            try:
                timeout = None
                if batch_card_ids:
                    time_since_last_process = asyncio.get_event_loop().time() - last_processed_time
                    timeout = max(0, self.batch_interval - time_since_last_process)
                card_id = await asyncio.wait_for(self._update_queue.get(), timeout=timeout)
                if card_id == -1:
                    self._update_queue.task_done()
                    break
                batch_card_ids.add(card_id)
                self._update_queue.task_done()
                if len(batch_card_ids) >= self.batch_size:
                    await self._process_batch(batch_card_ids.copy())
                    batch_card_ids.clear()
                    last_processed_time = asyncio.get_event_loop().time()
            except asyncio.TimeoutError:
                if batch_card_ids:
                    self.logger.debug(f'Batch interval reached. Processing {len(batch_card_ids)} card_ids.')
                    await self._process_batch(batch_card_ids.copy())
                    batch_card_ids.clear()
                    last_processed_time = asyncio.get_event_loop().time()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f'Unexpected error in price update worker: {e}', exc_info=True)
                await asyncio.sleep(1)
        if batch_card_ids:
            await self._process_batch(batch_card_ids.copy())
            batch_card_ids.clear()