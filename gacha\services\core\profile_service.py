"""
Profile Service - 處理用戶檔案相關的業務邏輯
"""
import asyncpg
import asyncio
import os
import re
from typing import Optional, Dict, Any, List
from decimal import Decimal
import discord
from urllib.parse import urlparse

from utils.logger import logger
from gacha.models.profile_models import UserProfile, ProfileData, ProfileCardInfo
from gacha.repositories.profile.profile_repository import ProfileRepository
from gacha.services.core.user_service import UserService
from gacha.services.core.collection_service import CollectionService
from database.redis.manager import RedisManager
from gacha.exceptions import UserNotFoundError, RecordNotFoundError, ProfileCardSetError, ProfileBackgroundError, LikeProfileError, DatabaseOperationError, UserDoesNotOwnCardError
from gacha.app_config import config_service # Import global config_service

# 預下載圖片相關配置
# IMAGE_BASE_PATH = "downloaded_gacha_master_cards" # Removed hardcoded path
RARITY_MAP = {
    1: "C",
    2: "R",
    3: "SR",
    4: "SSR",
    5: "UR",
    6: "LR",
    7: "EX",
}
DEFAULT_RARITY_NAME = "UnknownRarity"

# 從卡片URL中提取擴展名的函數
def sanitize_filename_part(text: str, max_length: int = 50) -> str:
    """清理文本使其適合作為文件名的一部分"""
    if not text:
        return "unknown"
    # 移除或替換不安全字符
    text = re.sub(r'[\\/*?:"<>|]', "", text) # Windows and common unsafe chars
    text = text.replace(" ", "_")
    # 限制長度
    return text[:max_length]


class ProfileService:
    """用戶檔案服務"""

    # 添加 Redis 缓存的 TTL 设置（7天）
    PROFILE_CACHE_TTL = 604800  # 7天，以秒为单位

    def __init__(self, pool: asyncpg.Pool, user_service: UserService, collection_service: CollectionService, redis_service: RedisManager):
        self.pool = pool
        self.profile_repo = ProfileRepository(pool)
        self.user_service = user_service
        self.collection_service = collection_service
        self.redis_service = redis_service
        
        # Get image_base_path from config_service
        default_image_base_path = "downloaded_gacha_master_cards"
        if config_service:
            self.image_base_path = config_service.get_config(
                'gacha_core_settings.profile.image_local_base_path',
                default_image_base_path
            )
            if self.image_base_path != default_image_base_path:
                logger.info(f"[ProfileService] Using image_base_path from config: {self.image_base_path}")
            else:
                logger.info(f"[ProfileService] Using default image_base_path: {self.image_base_path} (either from default or config was same)")
        else:
            self.image_base_path = default_image_base_path
            logger.warning(f"[ProfileService] Global config_service not available. Using default image_base_path: {self.image_base_path}")

        # 設定
        self.like_cooldown_seconds = 3600  # 1小時冷卻時間

    async def get_profile_data(self, user_id: int, discord_display_name: str, avatar_url: Optional[str] = None) -> ProfileData:
        """獲取完整的檔案資料"""
        try:
            # 並行獲取用戶、檔案設定和統計數據
            user_data_task = self.user_service.get_user(user_id, create_if_missing=True)
            profile_settings_task = self.profile_repo.get_or_create_user_profile(user_id)
            user_stats_task = self.profile_repo.get_user_statistics(user_id)

            user, profile, stats = await asyncio.gather(
                user_data_task,
                profile_settings_task,
                user_stats_task
            )

            if not user: # user_service.get_user 在 create_if_missing=True 時應該總能返回用戶或引發異常
                logger.error(f"User service failed to return user for user_id: {user_id} despite create_if_missing=True")
                raise UserNotFoundError(f"無法獲取或創建用戶資料: {user_id}")


            # 收集需要獲取的卡片 collection_id
            collection_ids_to_fetch = []
            main_card_collection_id = profile.showcased_card_collection_id
            if main_card_collection_id:
                collection_ids_to_fetch.append(main_card_collection_id)

            sub_card_collection_id_map = {} # 用於稍後映射回 slot
            for slot in range(1, 5):
                collection_id = getattr(profile, f'sub_card_{slot}_collection_id')
                if collection_id:
                    collection_ids_to_fetch.append(collection_id)
                    sub_card_collection_id_map[collection_id] = slot
            
            # 去重
            unique_collection_ids = list(set(collection_ids_to_fetch))
            
            # 並行獲取所有卡片資訊
            card_infos_dict: Dict[int, Optional[ProfileCardInfo]] = {}
            if unique_collection_ids:
                card_info_tasks = [
                    self.profile_repo.get_card_info_by_collection_id(cid) for cid in unique_collection_ids
                ]
                card_info_results = await asyncio.gather(*card_info_tasks, return_exceptions=True)
                
                for i, result in enumerate(card_info_results):
                    cid = unique_collection_ids[i]
                    if isinstance(result, Exception):
                        logger.error(f"Error fetching card info for collection_id {cid}: {result}")
                        card_infos_dict[cid] = None # 或者根據需要處理錯誤
                    elif result: # 確保 result 不是 None
                        card_infos_dict[cid] = result
                        self._set_local_image_path(result) # 為有效卡片設置本地路徑
                    else:
                        card_infos_dict[cid] = None


            main_card: Optional[ProfileCardInfo] = None
            if main_card_collection_id:
                main_card = card_infos_dict.get(main_card_collection_id)
            
            sub_cards: Dict[int, ProfileCardInfo] = {}
            for collection_id, slot in sub_card_collection_id_map.items():
                card_info = card_infos_dict.get(collection_id)
                if card_info:
                    sub_cards[slot] = card_info
            
            return ProfileData(
                user_id=user_id,
                nickname=discord_display_name,
                oil_balance=user.oil_balance,
                oil_ticket_balance=user.oil_ticket_balance,
                total_draws=user.total_draws,
                like_count=profile.like_count,
                collection_completion_rate=stats['completion_rate'],
                total_owned_cards=stats['total_owned'],
                rarity_counts=stats['rarity_counts'],
                main_card=main_card,
                sub_cards=sub_cards,
                background_image_url=profile.background_image_url,
                user_status=profile.user_status,
                avatar_url=avatar_url
            )
            
        except Exception as e:
            logger.error(f"獲取檔案資料失敗: user_id={user_id}, error={e}")
            raise

    def _set_local_image_path(self, card_info: ProfileCardInfo) -> None:
        """設置卡片的本地圖片路徑"""
        try:
            if not card_info.image_url:
                return
            
            # 獲取稀有度對應的目錄名
            rarity_name = RARITY_MAP.get(card_info.rarity, DEFAULT_RARITY_NAME)
            
            # 處理文件名部分
            name_part = sanitize_filename_part(card_info.name if card_info.name else card_info.series)
            
            # 從原始 URL 推斷文件擴展名
            try:
                parsed_url = urlparse(card_info.image_url)
                original_filename = os.path.basename(parsed_url.path)
                _, extension = os.path.splitext(original_filename)
                
                valid_extensions = {".jpg", ".jpeg", ".png", ".gif", ".webp"}
                if not extension or extension.lower() not in valid_extensions or len(extension) > 5:
                    lower_url = card_info.image_url.lower()
                    found_ext = False
                    for ext_type in valid_extensions:
                        if lower_url.endswith(ext_type):
                            extension = ext_type
                            found_ext = True
                            break
                    if not found_ext:
                        extension = ".png"
            except Exception:
                # 如果解析失敗，使用默認擴展名
                extension = ".png"
            
            # 構建本地文件路徑
            image_filename = f"{card_info.card_id}_{name_part}{extension}"
            local_path = os.path.join(self.image_base_path, rarity_name, image_filename) # Use self.image_base_path
            
            # 檢查文件是否存在
            if os.path.exists(local_path) and os.path.getsize(local_path) > 0:
                # 使用絕對路徑以確保在任何情況下都能找到文件
                abs_path = os.path.abspath(local_path)
                card_info.local_image_path = abs_path
                logger.debug(f"卡片 {card_info.card_id} ({card_info.name}) 找到本地圖片: {abs_path}")
            else:
                logger.debug(f"卡片 {card_info.card_id} ({card_info.name}) 無本地圖片或文件大小為0: {local_path}")
                card_info.local_image_path = None
        except Exception as e:
            logger.error(f"設置本地圖片路徑失敗: card_id={card_info.card_id}, error={e}")
            card_info.local_image_path = None

    async def get_cached_profile_image_url(self, user_id: int) -> Optional[str]:
        """獲取快取的檔案圖片URL（从Redis），期望此URL为字符串。"""
        try:
            cache_key = f"profile_url:{user_id}"
            # 使用新的 get_string 方法，它会处理解码并直接返回字符串或None
            cached_url = await self.redis_service.get_string(cache_key)
            
            if cached_url:
                logger.debug(f"Retrieved cached profile URL for user {user_id} as string.")
            else:
                logger.debug(f"No cached profile URL found for user {user_id} or failed to retrieve as string.")
                
            return cached_url
        except Exception as e:
            # 一般来说，get_string 内部应该已经处理了大部分异常并返回 None 或 default
            # 但为了保险起见，仍然保留此处的异常捕获
            logger.error(f"從Redis獲取快取的檔案圖片URL時發生未預期錯誤: user_id={user_id}, error={e}", exc_info=True)
            return None

    async def update_cached_profile_image_url(self, user_id: int, image_url: str) -> bool:
        """更新快取的檔案圖片URL（存储到Redis）"""
        try:
            cache_key = f"profile_url:{user_id}"
            success = await self.redis_service.set(cache_key, image_url, ttl=self.PROFILE_CACHE_TTL)
            return success
        except Exception as e:
            logger.error(f"更新Redis快取圖片URL失敗: user_id={user_id}, error={e}")
            return False

    async def invalidate_profile_cache(self, user_id: int) -> bool:
        """使檔案快取失效（从Redis删除）"""
        try:
            cache_key = f"profile_url:{user_id}"
            success = await self.redis_service.delete(cache_key)
            return success
        except Exception as e:
            logger.error(f"使Redis快取失效失敗: user_id={user_id}, error={e}")
            return False

    async def set_main_card(self, user_id: int, card_id: int) -> Dict[str, Any]:
        """設定主展示卡片. Returns dict with card_id and name on success."""
        try:
            # 驗證用戶是否擁有此卡片
            user_card = await self._verify_user_owns_card(user_id, card_id)
            
            # 更新主展示卡片
            await self.profile_repo.update_showcased_card(user_id, user_card['collection_id'])
                
            return {'card_id': user_card['card_id'], 'name': user_card['name']}
                
        except ProfileCardSetError: # Covers UserDoesNotOwnCardError and others from _verify
            raise
        except UserNotFoundError: 
            raise ProfileCardSetError(f'設定主展示卡片失敗：找不到用戶 {user_id} 的檔案設定。', card_id=card_id)
        except DatabaseOperationError as db_err: 
            logger.error(f"設定主展示卡片時資料庫操作失敗: user_id={user_id}, card_id={card_id}, error={db_err}")
            raise ProfileCardSetError(f'設定主展示卡片時發生資料庫錯誤', card_id=card_id)
        except Exception as e: # 其他未預期錯誤
            logger.error(f"設定主展示卡片失敗: user_id={user_id}, card_id={card_id}, error={e}")
            raise ProfileCardSetError(f'設定主展示卡片時發生未預期錯誤: {e}', card_id=card_id)

    async def set_sub_card(self, user_id: int, slot_index: int, card_id: int) -> Dict[str, Any]:
        """設定副展示卡片. Returns dict with card_id, name, and slot on success."""
        try:
            if slot_index not in [1, 2, 3, 4]:
                raise ProfileCardSetError('無效的槽位')
            
            user_card = await self._verify_user_owns_card(user_id, card_id)
            
            await self.profile_repo.update_sub_card(user_id, slot_index, user_card['collection_id'])
                
            return {'card_id': user_card['card_id'], 'name': user_card['name'], 'slot': slot_index}
                
        except ProfileCardSetError: # Covers UserDoesNotOwnCardError and others from _verify
            raise 
        except UserNotFoundError:
            raise ProfileCardSetError(f'設定副卡片失敗：找不到用戶 {user_id} 的檔案設定。', card_id=card_id)
        except DatabaseOperationError as db_err:
            logger.error(f"設定副展示卡片時資料庫操作失敗: user_id={user_id}, slot={slot_index}, card_id={card_id}, error={db_err}")
            raise ProfileCardSetError(f'設定副展示卡片時發生資料庫錯誤', card_id=card_id)
        except Exception as e:
            logger.error(f"設定副展示卡片失敗: user_id={user_id}, slot={slot_index}, card_id={card_id}, error={e}")
            raise ProfileCardSetError(f'設定副展示卡片時發生未預期錯誤: {e}', card_id=card_id)

    async def clear_sub_card(self, user_id: int, slot_index: int) -> int:
        """清除指定位置的副展示卡片. Returns slot_index on success."""
        try:
            if slot_index not in [1, 2, 3, 4]:
                raise ProfileCardSetError(f'無效的副卡片位置: {slot_index}')
            
            await self.profile_repo.clear_sub_card_slot(user_id, slot_index)
            return slot_index
                
        except ProfileCardSetError: 
            raise
        except UserNotFoundError: 
             raise ProfileCardSetError(f'清除副卡片位置 {slot_index} 失敗：找不到用戶設定。')
        except DatabaseOperationError as db_err:
            logger.error(f"清除副展示卡片時資料庫操作失敗: user_id={user_id}, slot={slot_index}, error={db_err}")
            raise ProfileCardSetError(f'清除副卡片位置 {slot_index} 時發生資料庫錯誤')
        except Exception as e:
            logger.error(f"清除副展示卡片失敗: user_id={user_id}, slot={slot_index}, error={e}")
            raise ProfileCardSetError(f'清除副展示卡片位置 {slot_index} 時發生未預期錯誤: {e}')

    async def reset_background(self, user_id: int) -> None:
        """重置背景圖片"""
        try:
            await self.profile_repo.update_background_image(user_id, None)
            # No specific data to return for reset, None indicates success
                
        except UserNotFoundError:
            raise ProfileBackgroundError(f'重置背景圖片失敗：找不到用戶 {user_id} 的檔案設定。')
        except DatabaseOperationError as db_err:
            logger.error(f"重置背景圖片失敗 (DB): user_id={user_id}, error={db_err}")
            raise ProfileBackgroundError(f'重置背景圖片時發生資料庫錯誤')
        except Exception as e:
            logger.error(f"重置背景圖片失敗: user_id={user_id}, error={e}")
            raise ProfileBackgroundError(f'重置背景圖片時發生未預期錯誤: {e}')

    async def set_background_image(self, user_id: int, background_url: str) -> None:
        """設定背景圖片"""
        try:
            await self.profile_repo.update_background_image(user_id, background_url)
            # No specific data to return for set, None indicates success
                
        except UserNotFoundError:
            raise ProfileBackgroundError(f'設定背景圖片失敗：找不到用戶 {user_id} 的檔案設定。')
        except DatabaseOperationError as db_err:
            logger.error(f"設定背景圖片失敗 (DB): user_id={user_id}, error={db_err}")
            raise ProfileBackgroundError(f'設定背景圖片時發生資料庫錯誤')
        except Exception as e:
            logger.error(f"設定背景圖片失敗: user_id={user_id}, error={e}")
            raise ProfileBackgroundError(f'設定背景圖片時發生未預期錯誤: {e}')

    async def like_profile(self, liker_id: int, owner_id: int) -> None:
        """按讚檔案"""
        try:
            # 檢查冷卻時間
            cooldown_key = f"profile_like_cd:{liker_id}:{owner_id}"
            
            if await self.redis_service.exists(cooldown_key):
                raise LikeProfileError('您最近已經按過讚了，請稍後再試。', cooldown=True)
            
            # 增加按讚數
            await self.profile_repo.increment_like_count(owner_id)
            # 如果 increment_like_count 内部失败 (例如 UserNotFoundError 或 DatabaseOperationError)，
            # 异常会在这里被抛出并由下方的 except 块捕获。
            # 不再需要检查返回值。
                
            # 設定冷卻時間
            await self.redis_service.set(cooldown_key, "1", ttl=self.like_cooldown_seconds)
            # No specific data to return, None indicates success
                
        except LikeProfileError:
            # 直接向上傳遞特定異常
            raise
        except Exception as e:
            logger.error(f"按讚檔案失敗: liker_id={liker_id}, owner_id={owner_id}, error={e}")
            raise LikeProfileError(f'按讚時發生錯誤: {e}')

    async def _verify_user_owns_card(self, user_id: int, card_id: int) -> Dict[str, Any]:
        """驗證用戶是否擁有指定的卡片. Raises UserDoesNotOwnCardError if not owned."""
        try:
            # 使用現有的 collection_service 來查詢
            # 這裡我們需要直接查詢資料庫
            query = """
                SELECT 
                    uc.id as collection_id,
                    uc.card_id,
                    mc.name,
                    uc.quantity
                FROM gacha_user_collections uc
                JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
                WHERE uc.user_id = $1 AND uc.card_id = $2 AND mc.is_active = TRUE
                LIMIT 1
            """
            
            async with self.pool.acquire() as connection:
                result = await connection.fetchrow(query, user_id, card_id)
                
            if result:
                return {
                    'collection_id': result['collection_id'],
                    'card_id': result['card_id'],
                    'name': result['name'],
                    'quantity': result['quantity']
                }
            # If no result, card is not owned or not active
            raise UserDoesNotOwnCardError(f'您並未擁有ID為 {card_id} 的卡片，或該卡片不存在。', card_id=card_id, user_id=user_id)
            
        except UserDoesNotOwnCardError: # Re-raise the specific error
            raise
        except Exception as e:
            logger.error(f"驗證用戶卡片擁有權失敗: user_id={user_id}, card_id={card_id}, error={e}")
            # This path indicates an unexpected error during the query execution itself.
            raise DatabaseOperationError(f"驗證用戶 {user_id} 卡片 {card_id} 擁有權時發生資料庫錯誤: {e}", original_exception=e)

    async def set_user_status(self, user_id: int, status: str) -> Optional[str]:
        """設定用戶個性簽名. Returns cleaned_status or None if cleared."""
        try:
            # 验证签名长度
            if len(status) > 150:
                raise ValueError("個性簽名不能超過150個字符")
            
            # 清除过长的签名或为None的情况
            if not status or status.strip() == "":
                await self.profile_repo.update_user_status(user_id, None)
                await self.invalidate_profile_cache(user_id) # Ensure cache invalidation here too
                return None # Cleared status
            
            # 去除首尾空白
            cleaned_status = status.strip()
            
            # 更新签名
            await self.profile_repo.update_user_status(user_id, cleaned_status)
            
            # 清除缓存（从Redis删除）
            await self.invalidate_profile_cache(user_id)
            
            return cleaned_status # Return cleaned status
            
        except ValueError as e:
            # 参数验证错误
            raise ProfileBackgroundError(str(e))
        except UserNotFoundError:
            # 用户不存在
            raise ProfileBackgroundError(f"設定個性簽名失敗: 找不到用戶 {user_id} 的檔案")
        except DatabaseOperationError as db_err:
            # 数据库错误
            logger.error(f"設定個性簽名失敗 (DB): user_id={user_id}, error={db_err}")
            raise ProfileBackgroundError("設定個性簽名時發生資料庫錯誤")
        except Exception as e:
            # 其他未预期错误
            logger.error(f"設定個性簽名失敗: user_id={user_id}, error={e}")
            raise ProfileBackgroundError(f"設定個性簽名時發生未預期錯誤: {e}") 