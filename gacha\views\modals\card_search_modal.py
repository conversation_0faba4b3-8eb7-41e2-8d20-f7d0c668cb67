import discord
from discord.ui import Modal, TextInput
from typing import Optional
from utils.logger import logger
import traceback

class CardSearchModal(Modal):

    def __init__(self, parent_view: discord.ui.View, current_query: Optional[str]=None, title: str='搜尋卡片', *, timeout=None):
        super().__init__(title=title, timeout=timeout)
        self.parent_view = parent_view
        self.search_input = TextInput(label='卡片ID或名稱 (留空清除搜尋)', placeholder='請輸入要搜尋的卡片ID或名稱...', required=False, max_length=100, default=current_query if current_query else '')
        self.add_item(self.search_input)

    async def on_submit(self, interaction: discord.Interaction):
        search_query = self.search_input.value.strip() if self.search_input.value else None
        logger.info("Search query submitted: '%s' by user %s", search_query, interaction.user.id)
        if hasattr(self.parent_view, 'handle_search_submit'):
            await self.parent_view.handle_search_submit(interaction, search_query)
        else:
            logger.error('Parent view %s does not have handle_search_submit method.', type(self.parent_view).__name__)
            await interaction.response.send_message('處理搜尋請求時發生錯誤：父元件無法處理搜尋。', ephemeral=True)

    async def on_error(self, interaction: discord.Interaction, error: Exception) -> None:
        logger.error('在 CardSearchModal (%s) 中發生錯誤: %s', interaction.user.id, error, exc_info=True)
        if not interaction.response.is_done():
            try:
                await interaction.response.send_message('處理您的搜尋請求時發生錯誤。', ephemeral=True)
            except discord.InteractionResponded:
                logger.warning('Interaction already responded in CardSearchModal on_error, sending followup.')
                await interaction.followup.send('處理您的搜尋請求時發生錯誤。', ephemeral=True)
            except Exception as e_resp:
                logger.error('Failed to send error message in CardSearchModal on_error: %s', e_resp, exc_info=True)
        else:
            try:
                await interaction.followup.send('處理您的搜尋請求時發生錯誤。', ephemeral=True)
            except Exception as e_followup:
                logger.error('Failed to send followup error message in CardSearchModal on_error: %s', e_followup, exc_info=True)