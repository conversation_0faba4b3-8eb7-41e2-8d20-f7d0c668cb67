from utils.logger import logger
'\n確認視圖模塊\n提供統一的確認操作視圖組件\n'
from typing import Any, Callable, Dict, Optional, Union
import discord
from gacha.utils.interaction_utils import check_user_permission

class ConfirmationViewBase(discord.ui.View):
    """確認操作的基礎視圖類"""

    def __init__(self, user_id: int, callback: Optional[Callable]=None, timeout: int=60, confirm_label: str='確認', cancel_label: str='取消', confirm_style: discord.ButtonStyle=discord.ButtonStyle.success, cancel_style: discord.ButtonStyle=discord.ButtonStyle.danger):
        """初始化確認視圖

        參數:
            user_id: 用戶ID
            callback: 確認後回調函數，接收 (interaction, is_confirmed) 參數
            timeout: 超時時間(秒)
            confirm_label: 確認按鈕文字
            cancel_label: 取消按鈕文字
            confirm_style: 確認按鈕樣式
            cancel_style: 取消按鈕樣式
        """
        super().__init__(timeout=timeout)
        self.user_id = user_id
        self.callback = callback
        self.result = None
        self.confirm_button = discord.ui.Button(label=confirm_label, style=confirm_style, custom_id='confirmation_confirm')
        self.confirm_button.callback = self.on_confirm
        self.cancel_button = discord.ui.Button(label=cancel_label, style=cancel_style, custom_id='confirmation_cancel')
        self.cancel_button.callback = self.on_cancel
        self.add_item(self.confirm_button)
        self.add_item(self.cancel_button)

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """檢查用戶權限

        參數:
            interaction: Discord交互對象

        返回:
            bool: 是否有權限
        """
        return await check_user_permission(interaction, self.user_id, '你無法操作此按鈕。')

    async def on_confirm(self, interaction: discord.Interaction):
        """確認按鈕處理"""
        for child in self.children:
            child.disabled = True
        await interaction.response.edit_message(view=self)
        self.result = True
        self.stop()
        if self.callback:
            try:
                await self.callback(interaction, True)
            except Exception as e:
                logger.error('確認視圖回調出錯: %s', str(e))

    async def on_cancel(self, interaction: discord.Interaction):
        """取消按鈕處理"""
        for child in self.children:
            child.disabled = True
        await interaction.response.edit_message(view=self)
        self.result = False
        self.stop()
        if self.callback:
            try:
                await self.callback(interaction, False)
            except Exception as e:
                logger.error('確認視圖回調出錯: %s', str(e))

    async def on_timeout(self):
        """超時處理"""
        for child in self.children:
            child.disabled = True
        if hasattr(self, 'message') and self.message:
            try:
                await self.message.edit(view=self)
            except discord.NotFound:
                logger.warning('確認視圖超時：訊息 (%s) 已被刪除或無法找到。', self.message.id if self.message else 'N/A')
            except discord.HTTPException as e:
                logger.error('確認視圖超時更新出錯 (HTTPException): %s', str(e))
            except Exception as e:
                logger.error('確認視圖超時更新時發生未預期錯誤: %s', str(e))

class ConfirmationFactory:
    """確認視圖工廠類，用於快速創建各種確認視圖"""

    @staticmethod
    async def create_confirmation(interaction: discord.Interaction, title: str, description: str, user_id: int, callback: Optional[Callable]=None, field_name: Optional[str]=None, field_value: Optional[str]=None, color: discord.Color=discord.Color.red(), ephemeral: bool=True, confirm_label: str='確認', cancel_label: str='取消', footer_text: str='請仔細考慮後再確認。', confirm_style: discord.ButtonStyle=discord.ButtonStyle.success, cancel_style: discord.ButtonStyle=discord.ButtonStyle.danger) -> ConfirmationViewBase:
        """創建並發送確認視圖

        參數:
            interaction: Discord交互對象
            title: 標題
            description: 描述
            user_id: 用戶ID
            callback: 確認回調
            field_name: 附加字段名稱
            field_value: 附加字段值
            color: 嵌入顏色
            ephemeral: 是否僅用戶可見
            confirm_label: 確認按鈕文字
            cancel_label: 取消按鈕文字
            footer_text: 頁腳文字
            confirm_style: 確認按鈕樣式
            cancel_style: 取消按鈕樣式

        返回:
            ConfirmationViewBase: 創建的確認視圖
        """
        view = ConfirmationViewBase(user_id=user_id, callback=callback, confirm_label=confirm_label, cancel_label=cancel_label, confirm_style=confirm_style, cancel_style=cancel_style)
        embed = discord.Embed(title=title, description=description, color=color)
        if field_name and field_value:
            embed.add_field(name=field_name, value=field_value, inline=False)
        if footer_text:
            embed.set_footer(text=footer_text)
        if not interaction.response.is_done():
            await interaction.response.send_message(embed=embed, view=view, ephemeral=ephemeral)
        else:
            await interaction.followup.send(embed=embed, view=view, ephemeral=ephemeral)
        return view