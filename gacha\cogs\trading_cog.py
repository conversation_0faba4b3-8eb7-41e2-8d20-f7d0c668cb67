import discord
from discord import app_commands
from discord.ext import commands
import logging
from typing import Optional, Dict
from gacha.services.trading.card_trading_service import CardTradingService, InvalidTradeParametersError, InsufficientCardQuantityError, CardNotFoundError, TradeNotFoundError, UnauthorizedTradeAccessError
from gacha.repositories.trading.card_trade_history_repository import CardTradeHistoryRepository
from gacha.views.trading.trade_request_view import TradeRequestView
from gacha.views.embeds.trading.trade_embed_builder import TradeEmbedBuilder
from gacha.repositories.card.master_card_repository import MasterCardRepository
from gacha.services.core.collection_service import CollectionService
from gacha.exceptions import DatabaseOperationError
from types import SimpleNamespace
logger = logging.getLogger(__name__)

class TradingCog(commands.Cog):

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        try:
            pool = getattr(self.bot, 'pool', None)
            collection_service = getattr(self.bot, 'collection_service', None)
            user_service = getattr(self.bot, 'user_service', None)
            master_card_repo = getattr(self.bot, 'master_card_repo', None)
            if not all([pool, collection_service, user_service, master_card_repo]):
                missing_deps = [name for name, dep in [('pool', pool), ('collection_service', collection_service), ('user_service', user_service), ('master_card_repo', master_card_repo)] if dep is None]
                logger.critical('CRITICAL: TradingCog: Missing dependencies for CardTradingService: %s. CardTradingService will not be initialized.', ', '.join(missing_deps))
                self.trading_service = None
            else:
                trade_history_repo = CardTradeHistoryRepository(pool=pool)
                self.trading_service = CardTradingService(pool=pool, collection_service=collection_service, user_service=user_service, trade_history_repo=trade_history_repo, master_card_repo=master_card_repo)
                logger.info('TradingCog: CardTradingService initialized successfully.')
        except Exception as e:
            logger.critical('CRITICAL: TradingCog: Error initializing CardTradingService: %s. TradingCog may not function correctly.', e, exc_info=True)
            self.trading_service = None
        try:
            if hasattr(self.bot, 'master_card_repo'):
                self.master_card_repo: MasterCardRepository = self.bot.master_card_repo
                logger.info('TradingCog: MasterCardRepository successfully obtained from bot.master_card_repo.')
            else:
                self.master_card_repo = None
                logger.critical('CRITICAL: TradingCog: bot.master_card_repo not found. MasterCardRepository could not be initialized. Trade autocomplete for request_card_id may not function.')
        except Exception as e:
            logger.critical('CRITICAL: TradingCog: Error initializing MasterCardRepository: %s. Trade autocomplete may not function.', e, exc_info=True)
            self.master_card_repo = None
        try:
            if hasattr(self.bot, 'collection_service'):
                self.collection_service: CollectionService = self.bot.collection_service
                logger.info('TradingCog: CollectionService successfully obtained from bot.collection_service.')
            else:
                self.collection_service = None
                logger.critical('CRITICAL: TradingCog: bot.collection_service not found. CollectionService could not be initialized. Trade autocomplete for offer_card_id may not function.')
        except Exception as e:
            logger.critical('CRITICAL: TradingCog: Error initializing CollectionService: %s. Trade autocomplete for offer_card_id may not function.', e, exc_info=True)
            self.collection_service = None
        self.embed_builder = TradeEmbedBuilder(bot)

    @app_commands.command(name='trade', description='與另一位使用者發起卡片交易。')
    @app_commands.describe(user='您想與之交易的使用者', offer_card_id='您提供的卡片 ID (master_card_id)', offer_quantity='您提供的卡片數量 (預設 1)', request_card_id='您想要的對方卡片 ID (可選)', request_quantity='您想要的對方卡片數量 (預設 1, 僅當 request_card_id 提供時有效)', price='您向對方索要的油幣價格 (與 request_card_id 互斥)')
    async def trade(self, interaction: discord.Interaction, user: discord.User, offer_card_id: str, offer_quantity: Optional[int]=1, request_card_id: Optional[str]=None, request_quantity: Optional[int]=1, price: Optional[float]=None):
        """
        處理 /trade 命令的邏輯。
        """
        if offer_quantity <= 0:
            await interaction.response.send_message('提供的卡片數量必須大於 0。', ephemeral=True)
            return
        if request_card_id and request_quantity <= 0:
            await interaction.response.send_message('想要的卡片數量必須大於 0。', ephemeral=True)
            return
        if price is not None and price <= 0:
            await interaction.response.send_message('索要的油幣價格必須大於 0。', ephemeral=True)
            return
        if request_card_id and price is not None:
            await interaction.response.send_message('不能同時要求卡片和油幣。', ephemeral=True)
            return
        if interaction.user.id == user.id:
            await interaction.response.send_message('您不能與自己交易！', ephemeral=True)
            return
        initiator_id = interaction.user.id
        target_user_id = user.id
        if not self.trading_service:
            await interaction.response.send_message('交易服務目前不可用，請稍後再試。', ephemeral=True)
            logger.error('TradingCog: trading_service is not initialized. Cannot initiate trade.')
            return
        
        try:
            trade_details = await self.trading_service.initiate_trade(
                initiator_id=initiator_id, 
                receiver_id=target_user_id, 
                offered_card_id=offer_card_id, 
                offer_quantity=offer_quantity, 
                requested_card_id=request_card_id, 
                requested_quantity=request_quantity, 
                price=price
            )
            
            target_discord_user = self.bot.get_user(target_user_id)
            if not target_discord_user:
                try:
                    target_discord_user = await self.bot.fetch_user(target_user_id)
                except discord.NotFound:
                    await interaction.response.send_message('錯誤：找不到目標使用者。', ephemeral=True)
                    logger.error('Could not find target user %s for trade %s.', target_user_id, trade_details.get('trade_id'))
                    return
                
            trade_embed = await self.embed_builder.build_trade_request_embed(trade_details, interaction.user, target_discord_user)
            trade_view = TradeRequestView(self.bot, trade_details, self.trading_service, self.embed_builder)
            
            try:
                content_message = f'{interaction.user.mention} 向 {target_discord_user.mention} 發起了交易請求！\n請目標用戶點擊下方按鈕進行操作。'
                if not interaction.response.is_done():
                    await interaction.response.send_message(content=content_message, embed=trade_embed, view=trade_view)
                    logger.info('Trade request %s sent in channel by %s to %s.', trade_details.get('trade_id'), initiator_id, target_user_id)
                else:
                    await interaction.followup.send(content=content_message, embed=trade_embed, view=trade_view)
                    logger.info('Trade request %s sent as followup in channel by %s to %s.', trade_details.get('trade_id'), initiator_id, target_user_id)
            except discord.Forbidden:
                await interaction.followup.send('無法在頻道中發送交易請求。請檢查我的權限設定。', ephemeral=True)
                logger.warning('Failed to send trade request in channel for trade %s. Initiator: %s. Possible permission issue.', trade_details.get('trade_id'), initiator_id)
                try:
                    current_trade_info = await self.trading_service.get_trade_details(trade_details.get('trade_id'))
                    await self.trading_service.cancel_trade(
                        trade_details=current_trade_info, 
                        canceling_user_id=self.bot.user.id, 
                        reason='無法在頻道中發送交易請求'
                    )
                    logger.info('Trade %s auto-cancelled due to channel send failure.', trade_details.get('trade_id'))
                except TradeNotFoundError:
                    logger.error('Could not find trade details for %s to auto-cancel after channel send failure.', trade_details.get('trade_id'))
                except Exception as e:
                    logger.error('Failed to auto-cancel trade %s after channel send failure: %s', trade_details.get('trade_id'), str(e))
            except Exception as e:
                error_message_destination = interaction.followup if interaction.response.is_done() else interaction.response
                try:
                    await error_message_destination.send_message('發送交易請求時發生未知錯誤。', ephemeral=True)
                except discord.InteractionResponded:
                    logger.error('Failed to send error message for trade %s, interaction already responded or other issue.', trade_details.get('trade_id'))
                logger.error('Error sending trade request %s from %s to %s: %s', trade_details.get('trade_id'), initiator_id, target_user_id, e, exc_info=True)
                try:
                    current_trade_info = await self.trading_service.get_trade_details(trade_details.get('trade_id'))
                    await self.trading_service.cancel_trade(
                        trade_details=current_trade_info, 
                        canceling_user_id=self.bot.user.id, 
                        reason=f'發送交易請求時內部錯誤: {str(e)[:100]}'
                    )
                    logger.info('Trade %s auto-cancelled due to exception.', trade_details.get('trade_id'))
                except TradeNotFoundError:
                    logger.error('Could not find trade details for %s to auto-cancel after exception.', trade_details.get('trade_id'))
                except Exception as cancel_error:
                    logger.error('Failed to auto-cancel trade %s after exception: %s', trade_details.get('trade_id'), str(cancel_error))
        
        except InvalidTradeParametersError as e:
            await interaction.response.send_message(f'交易參數無效：{str(e)}', ephemeral=True)
            logger.warning('Invalid trade parameters for %s to %s: %s', initiator_id, target_user_id, str(e))
        
        except InsufficientCardQuantityError as e:
            await interaction.response.send_message(f'卡片數量不足：{str(e)}', ephemeral=True)
            logger.warning('Insufficient card quantity for %s: %s', initiator_id, str(e))
        
        except CardNotFoundError as e:
            await interaction.response.send_message(f'找不到卡片：{str(e)}', ephemeral=True)
            logger.warning('Card not found for trade from %s to %s: %s', initiator_id, target_user_id, str(e))
        
        except DatabaseOperationError as e:
            await interaction.response.send_message(f'資料庫操作錯誤：{str(e)}', ephemeral=True)
            logger.error('Database error during trade initiation from %s to %s: %s', initiator_id, target_user_id, str(e))
        
        except Exception as e:
            await interaction.response.send_message('發起交易時發生未知錯誤，請稍後再試。', ephemeral=True)
            logger.error('Unexpected error during trade initiation from %s to %s: %s', initiator_id, target_user_id, str(e), exc_info=True)

    @trade.autocomplete('offer_card_id')
    async def offer_card_id_autocomplete(self, interaction: discord.Interaction, current: str) -> list[app_commands.Choice[str]]:
        choices = []
        if not self.collection_service or not hasattr(self.collection_service, 'collection_repo'):
            logger.warning('TradingCog: collection_service or its collection_repo is not initialized. Autocomplete for offer_card_id will be empty.')
            return []
        try:
            from gacha.models.filters import CollectionFilters
            if not current:
                filters = CollectionFilters()
                logger.debug('TradingCog: offer_card_id_autocomplete - current is empty, fetching without card_name filter for user %s', interaction.user.id)
            else:
                try:
                    card_id_input = int(current)
                    filters = CollectionFilters(card_id=card_id_input)
                    logger.debug("TradingCog: offer_card_id_autocomplete - current is '%s' (parsed as ID), fetching with card_id filter for user %s", current, interaction.user.id)
                except ValueError:
                    filters = CollectionFilters(card_name=current)
                    logger.debug("TradingCog: offer_card_id_autocomplete - current is '%s' (parsed as name), fetching with card_name filter for user %s", current, interaction.user.id)
            user_cards = await self.collection_service.collection_repo.get_filtered_user_cards(user_id=interaction.user.id, filters=filters)
            count = 0
            for user_card_obj in user_cards:
                if count >= 25:
                    break
                if user_card_obj and user_card_obj.card:
                    display_name = f'{user_card_obj.card.name} (ID: {user_card_obj.card.card_id})'
                    if hasattr(user_card_obj, 'quantity') and user_card_obj.quantity is not None:
                        display_name += f' (擁有: {user_card_obj.quantity})'
                    if len(display_name) > 100:
                        display_name = display_name[:97] + '...'
                    choices.append(app_commands.Choice(name=display_name, value=str(user_card_obj.card.card_id)))
                    count += 1
        except Exception as e:
            logger.error('TradingCog: Error during offer_card_id_autocomplete query: %s', e, exc_info=True)
            return []
        logger.debug("Offer_card_id autocomplete for '%s' for user %s resulted in %s choices.", current, interaction.user.id, len(choices))
        return choices

    @trade.autocomplete('request_card_id')
    async def request_card_id_autocomplete(self, interaction: discord.Interaction, current: str) -> list[app_commands.Choice[str]]:
        choices = []
        target_user_discord_obj = interaction.namespace.user
        if not target_user_discord_obj:
            logger.debug('TradingCog: request_card_id_autocomplete - Target user not specified in the command yet. Returning empty list.')
            return []
        target_user_id = target_user_discord_obj.id
        if not self.collection_service or not hasattr(self.collection_service, 'collection_repo'):
            logger.warning('TradingCog: collection_service or its collection_repo is not initialized. Autocomplete for request_card_id for target user %s will be empty.', target_user_id)
            return []
        try:
            from gacha.models.filters import CollectionFilters
            if not current:
                filters = CollectionFilters()
                logger.debug('TradingCog: request_card_id_autocomplete - current is empty, fetching without card_name filter for target user %s', target_user_id)
            else:
                try:
                    card_id_input = int(current)
                    filters = CollectionFilters(card_id=card_id_input)
                    logger.debug("TradingCog: request_card_id_autocomplete - current is '%s' (parsed as ID), fetching with card_id filter for target user %s", current, target_user_id)
                except ValueError:
                    filters = CollectionFilters(card_name=current)
                    logger.debug("TradingCog: request_card_id_autocomplete - current is '%s' (parsed as name), fetching with card_name filter for target user %s", current, target_user_id)
            user_cards = await self.collection_service.collection_repo.get_filtered_user_cards(user_id=target_user_id, filters=filters)
            count = 0
            for user_card_obj in user_cards:
                if count >= 25:
                    break
                if user_card_obj and user_card_obj.card:
                    display_name = f'{user_card_obj.card.name} (ID: {user_card_obj.card.card_id})'
                    if hasattr(user_card_obj, 'quantity') and user_card_obj.quantity is not None:
                        display_name += f' (對方擁有: {user_card_obj.quantity})'
                    if len(display_name) > 100:
                        display_name = display_name[:97] + '...'
                    choices.append(app_commands.Choice(name=display_name, value=str(user_card_obj.card.card_id)))
                    count += 1
        except Exception as e:
            logger.error('TradingCog: Error during request_card_id_autocomplete query for target user %s: %s', target_user_id, e, exc_info=True)
            return []
        logger.debug("Request_card_id autocomplete for '%s' for target user %s resulted in %s choices.", current, target_user_id, len(choices))
        return choices

async def setup(bot: commands.Bot):
    await bot.add_cog(TradingCog(bot))
    logger.info('TradingCog has been loaded.')