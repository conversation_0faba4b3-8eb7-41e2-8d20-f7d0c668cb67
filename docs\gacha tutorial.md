--- :star2: Gacha 機器人玩法速覽 :star2: ---

**【基礎 & 資源】**
*   `/balance`：查看油幣:moneybag:與抽卡券。
*   `/daily`：每日簽到。
*   `/hourly`：每小時領油幣。

**【核心：抽卡】**
*   `/w`：**單抽**。`/w multi:True`：**十連抽**。
*   `/w pool_type:[卡池]`：可選不同卡池。

**【收藏管理】**
*   `/mw`：開啟 **卡冊** (翻頁/跳頁)。卡冊內可操作：標記最愛:heart:, 賣卡:heavy_dollar_sign:, 升星:sparkles:, 排序:bar_chart:。
*   `/mwr`：查看各稀有度收集。
*   `/mws [series:系列名]`：查看系列收集進度。

**【賣卡換錢】**
*   `/sw`：賣卡通用指令。可指定 `card_id_param`, `card_name`, `series`, `rarity`, `pool_type` 等條件。
*   `operation:[one/leave_one/all]`：選擇賣1張、賣到剩1張、或全賣。
*   :warning: 預設不賣最愛卡。可用 `include_favorites:True` 強制包含，請小心使用。

**【最愛標記:heart:】**
*   `/favorite card_id:[ID]`：標記/取消最愛。
*   最愛卡通常不會被批次賣出 (除非特別指定)。

**【強化 & 排序】** (在卡冊 `/mw` 內操作)
*   **升星:sparkles;**：消耗油幣和重複卡提升星級。
*   **排序模式:bar_chart;**：調整卡片在卡冊的順序。

**【玩家交易:left_right_arrow:】**
*   `/trade user:[玩家]`：與其他玩家交易卡片或油幣。
    *   `offer_card_id:[你的卡片ID]` 你出的卡。
    *   (可選) `request_card_id:[對方卡片ID]` 或 `price:[油幣]` 你想要的。

**【油票商店:ticket:】**
*   `/shop`：開啟油票商店，可用油票購買兌換券等特殊物品。

**【市場 & 投資:chart_with_upwards_trend:】**
*   `/cardinfo query:[ID或名稱]`：查卡片市場行情。
*   `/news`：看市場新聞。
*   `/stock [symbol:代碼]`：查股票列表或指定股票詳情。
*   `/portfolio`：看你的油幣和股票投資組合 (持股/盈虧)。

**【小遊戲:game_die::black_joker:】**
*   `/blackjack bet:[金額]`：玩21點。
*   `/dice bet:[金額] choice:[大/小]`：玩骰子比大小。
*   `/mines [mine_count:地雷數] [bet_amount:金額]`：玩尋寶礦區，躲地雷賺獎金。

**【排行榜:trophy:】**
*   `/lb [leaderboard_type:類型]`：查看各類排行榜。

--- :sparkles: 祝歐氣滿滿！:sparkles: ---