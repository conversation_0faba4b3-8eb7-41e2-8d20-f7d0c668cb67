import asyncpg
from typing import Dict, List, Optional, Tuple, Any
import asyncio
from database.base_repository import BaseRepository
from utils.logger import logger
from gacha.exceptions import DatabaseOperationError, RecordNotFoundError

class LeaderboardRepository(BaseRepository):
    """排行榜存儲庫 (Asyncpg 版本)，處理排行榜數據查詢
    使用純異常模式進行錯誤處理
    """
    LEADERBOARD_CONFIG_STRUCTURE = {'rarity': {'title': '稀有度收集排行榜', 'with_template': '\n             WITH RarityCount AS (\n                 SELECT\n                     uc.user_id,\n                     SUM(CASE WHEN mc.rarity = 7 THEN uc.quantity ELSE 0 END) AS rarity_7_count,\n                     SUM(CASE WHEN mc.rarity = 6 THEN uc.quantity ELSE 0 END) AS rarity_6_count,\n                     SUM(CASE WHEN mc.rarity = 5 THEN uc.quantity ELSE 0 END) AS rarity_5_count,\n                     SUM(CASE WHEN mc.rarity = 4 THEN uc.quantity ELSE 0 END) AS rarity_4_count,\n                     SUM(CASE WHEN mc.rarity = 3 THEN uc.quantity ELSE 0 END) AS rarity_3_count,\n                     SUM(CASE WHEN mc.rarity = 2 THEN uc.quantity ELSE 0 END) AS rarity_2_count,\n                     SUM(CASE WHEN mc.rarity = 1 THEN uc.quantity ELSE 0 END) AS rarity_1_count\n                 FROM {collection_table} uc JOIN {master_card_table} mc ON uc.card_id = mc.card_id\n                 WHERE mc.is_active = True GROUP BY uc.user_id\n             )\n             ', 'select_template': '\n                 gu.user_id, gu.oil_balance, gu.total_draws,\n                 COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,\n                 COALESCE(rc.rarity_7_count, 0) AS rarity_7_count, COALESCE(rc.rarity_6_count, 0) AS rarity_6_count,\n                 COALESCE(rc.rarity_5_count, 0) AS rarity_5_count, COALESCE(rc.rarity_4_count, 0) AS rarity_4_count,\n                 COALESCE(rc.rarity_3_count, 0) AS rarity_3_count, COALESCE(rc.rarity_2_count, 0) AS rarity_2_count,\n                 COALESCE(rc.rarity_1_count, 0) AS rarity_1_count\n             ', 'from_template': '{user_table} gu LEFT JOIN RarityCount rc ON gu.user_id = rc.user_id', 'order_by_template': '\n                 COALESCE(rc.rarity_7_count, 0) DESC, COALESCE(rc.rarity_6_count, 0) DESC,\n                 COALESCE(rc.rarity_5_count, 0) DESC, COALESCE(rc.rarity_4_count, 0) DESC,\n                 COALESCE(rc.rarity_3_count, 0) DESC, COALESCE(rc.rarity_2_count, 0) DESC,\n                 COALESCE(rc.rarity_1_count, 0) DESC, gu.user_id ASC\n             '}, 'completion': {'title': '圖鑑完成率排行榜', 'with_template': '\n                WITH UserCardCollection AS (\n                    SELECT uc.user_id, COUNT(uc.card_id) as collected_cards\n                    FROM {collection_table} uc JOIN {master_card_table} mc ON uc.card_id = mc.card_id\n                    WHERE mc.is_active = True GROUP BY uc.user_id\n                )\n            ', 'select_template': '\n                gu.user_id, COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,\n                COALESCE(ucc.collected_cards, 0) as collected_cards,\n                $1::integer as total_cards,\n                ROUND(COALESCE(ucc.collected_cards, 0) * 100.0 / $1::integer, 1) as completion_percentage\n            ', 'from_template': '{user_table} gu LEFT JOIN UserCardCollection ucc ON gu.user_id = ucc.user_id', 'order_by_template': '\n                ROUND(COALESCE(ucc.collected_cards, 0) * 100.0 / $1::integer, 1) DESC,\n                COALESCE(ucc.collected_cards, 0) DESC, gu.user_id ASC\n            '}, 'draws': {'title': '抽卡次數排行榜', 'with_template': '', 'select_template': 'user_id, COALESCE(nickname, CAST(user_id AS VARCHAR)) AS user_name, oil_balance, total_draws', 'from_template': '{user_table}', 'order_by_template': 'total_draws DESC, user_id ASC'}, 'oil': {'title': '油幣餘額排行榜', 'with_template': '', 'select_template': 'user_id, COALESCE(nickname, CAST(user_id AS VARCHAR)) AS user_name, oil_balance, total_draws', 'from_template': '{user_table}', 'order_by_template': 'oil_balance DESC, user_id ASC'}, 'collection_unique': {'title': '稀有度收集排行榜', 'with_template': "\n             WITH UserCollectionStats AS (\n                 SELECT uc.user_id, mc.pool_type, mc.rarity, COUNT(DISTINCT uc.card_id) AS unique_cards\n                 FROM {collection_table} uc JOIN {master_card_table} mc ON uc.card_id = mc.card_id\n                 WHERE mc.is_active = True GROUP BY uc.user_id, mc.pool_type, mc.rarity\n             ), TotalStats AS (\n                 SELECT 'pool' AS stat_type, pool_type AS type_value, COUNT(DISTINCT card_id) AS total_count\n                 FROM {master_card_table} WHERE is_active = True GROUP BY pool_type\n                 UNION ALL\n                 SELECT 'rarity' AS stat_type, CAST(rarity AS VARCHAR) AS type_value, COUNT(DISTINCT card_id) AS total_count\n                 FROM {master_card_table} WHERE is_active = True GROUP BY rarity\n             ), UserTotalUniqueCards AS (\n                 SELECT uc.user_id, COUNT(DISTINCT uc.card_id) AS total_unique_cards\n                 FROM {collection_table} uc JOIN {master_card_table} mc ON uc.card_id = mc.card_id\n                 WHERE mc.is_active = True GROUP BY uc.user_id\n             ), PoolTypeTotals AS (\n                 SELECT user_id, pool_type, SUM(unique_cards) AS unique_cards\n                 FROM UserCollectionStats GROUP BY user_id, pool_type\n             )\n             ", 'select_template': None, 'from_template': '{user_table} gu LEFT JOIN UserTotalUniqueCards utuc ON gu.user_id = utuc.user_id', 'order_by_template': '\n                 COALESCE((SELECT SUM(unique_cards) FROM UserCollectionStats ucs WHERE ucs.user_id = gu.user_id AND ucs.rarity = 7), 0)::INTEGER DESC,\n                 COALESCE((SELECT SUM(unique_cards) FROM UserCollectionStats ucs WHERE ucs.user_id = gu.user_id AND ucs.rarity = 6), 0)::INTEGER DESC,\n                 COALESCE((SELECT SUM(unique_cards) FROM UserCollectionStats ucs WHERE ucs.user_id = gu.user_id AND ucs.rarity = 5), 0)::INTEGER DESC,\n                 COALESCE((SELECT SUM(unique_cards) FROM UserCollectionStats ucs WHERE ucs.user_id = gu.user_id AND ucs.rarity = 4), 0)::INTEGER DESC,\n                 COALESCE((SELECT SUM(unique_cards) FROM UserCollectionStats ucs WHERE ucs.user_id = gu.user_id AND ucs.rarity = 3), 0)::INTEGER DESC,\n                 COALESCE((SELECT SUM(unique_cards) FROM UserCollectionStats ucs WHERE ucs.user_id = gu.user_id AND ucs.rarity = 2), 0)::INTEGER DESC,\n                 COALESCE((SELECT SUM(unique_cards) FROM UserCollectionStats ucs WHERE ucs.user_id = gu.user_id AND ucs.rarity = 1), 0)::INTEGER DESC,\n                 COALESCE(utuc.total_unique_cards, 0) DESC, gu.user_id ASC\n             '}, 'portfolio_value': {'title': '總資產價值排行', 'with_template': '', 'select_template': '\n               gu.user_id,\n               COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,\n               SUM(pp.quantity * va.current_price) AS total_portfolio_value\n           ', 'from_template': '\n               {user_table} gu\n               JOIN {player_portfolios_table} pp ON gu.user_id = pp.user_id\n               JOIN {virtual_assets_table} va ON pp.asset_id = va.asset_id\n           ', 'group_by_template': 'gu.user_id, COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR))', 'order_by_template': 'SUM(pp.quantity * va.current_price) DESC, gu.user_id ASC'}, 'stock_holding': {'title': '特定股票持有排行', 'with_template': '', 'select_template': '\n               gu.user_id,\n               COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,\n               pp.quantity AS stock_quantity,\n               va.asset_name\n           ', 'from_template': '\n               {user_table} gu\n               JOIN {player_portfolios_table} pp ON gu.user_id = pp.user_id\n               JOIN {virtual_assets_table} va ON pp.asset_id = va.asset_id\n           ', 'where_template': 'va.asset_symbol = $1', 'order_by_template': 'stock_quantity DESC, gu.user_id ASC'}, 'trade_volume': {'title': '股票交易總額排行', 'with_template': '', 'select_template': '\n               gu.user_id,\n               COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,\n               SUM(mt.total_amount) AS total_trade_volume\n           ', 'from_template': '\n               {user_table} gu\n               JOIN {market_transactions_table} mt ON gu.user_id = mt.user_id\n           ', 'group_by_template': 'gu.user_id, COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR))', 'order_by_template': 'SUM(mt.total_amount) DESC, gu.user_id ASC'}, 'trade_count': {'title': '股票交易次數排行', 'with_template': '', 'select_template': '\n               gu.user_id,\n               COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name,\n               COUNT(mt.id) AS total_trade_count\n           ', 'from_template': '\n               {user_table} gu\n               JOIN {market_transactions_table} mt ON gu.user_id = mt.user_id\n           ', 'group_by_template': 'gu.user_id, COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR))', 'order_by_template': 'COUNT(mt.id) DESC, gu.user_id ASC'}}

    def __init__(self, pool: asyncpg.Pool):
        """初始化排行榜存儲庫 (Asyncpg 版本)"""
        super().__init__(pool)
        self.user_table = 'gacha_users'
        self.collection_table = 'gacha_user_collections'
        self.master_card_table = 'gacha_master_cards'
        self.leaderboard_stats_table = 'gacha_leaderboard_stats'
        self.player_portfolios_table = 'player_portfolios'
        self.virtual_assets_table = 'virtual_assets'
        self.market_transactions_table = 'market_transactions'
        self._total_cards: Optional[int] = None
        self._pool_types: Optional[List[str]] = None
        self._rarities: Optional[List[int]] = None

    def _get_cache_key(self, leaderboard_type: str, limit: int, offset: int, asset_symbol: Optional[str]=None) -> str:
        """生成排行榜緩存鍵"""
        key = f'leaderboard:{leaderboard_type}:{limit}:{offset}'
        if asset_symbol:
            key += f':{asset_symbol}'
        return key

    def _get_player_rank_cache_key(self, leaderboard_type: str, player_name: str, asset_symbol: Optional[str]=None) -> str:
        """生成玩家排名緩存鍵"""
        key = f'player_rank:{leaderboard_type}:{player_name.lower()}'
        if asset_symbol:
            key += f':{asset_symbol}'
        return key

    async def _ensure_metadata_loaded(self):
        """非同步加載或刷新元數據 (總卡數, 卡池, 稀有度)"""
        if self._total_cards is None or self._pool_types is None or self._rarities is None:
            try:
                total_cards_query = f'SELECT COUNT(*)::integer as count FROM {self.master_card_table} WHERE is_active = True'
                pool_types_query = f'SELECT DISTINCT pool_type FROM {self.master_card_table} WHERE is_active = True AND pool_type IS NOT NULL ORDER BY pool_type'
                rarities_query = f'SELECT DISTINCT rarity FROM {self.master_card_table} WHERE is_active = True AND rarity IS NOT NULL ORDER BY rarity ASC'
                tasks = [self._fetchval(total_cards_query), self._fetch(pool_types_query), self._fetch(rarities_query)]
                total_cards_res, pool_types_res, rarities_res = await asyncio.gather(*tasks)
                self._total_cards = total_cards_res or 0
                self._pool_types = [row['pool_type'] for row in pool_types_res] if pool_types_res else []
                self._rarities = [int(row['rarity']) for row in rarities_res] if rarities_res else []
            except asyncpg.PostgresError as e:
                raise DatabaseOperationError(f"載入排行榜元數據失敗: {e}") from e

    async def get_total_cards(self) -> int:
        """(Async) 獲取活躍的總卡片數量"""
        await self._ensure_metadata_loaded()
        return self._total_cards if self._total_cards is not None else 0

    async def get_leaderboard(self, leaderboard_type: str, limit: Optional[int]=10, offset: int=0, asset_symbol: Optional[str]=None) -> Tuple[List[Dict[str, Any]], int]:
        """
        (Async) 通用排行榜獲取方法
        返回: Tuple[結果列表, 符合條件的總行數]
        """
        config_structure = self.LEADERBOARD_CONFIG_STRUCTURE.get(leaderboard_type)
        if not config_structure:
            raise ValueError(f'無效的排行榜類型: {leaderboard_type}')
            
        try:
            await self._ensure_metadata_loaded()
            params_main = []
            params_count = []
            table_format_args = {'collection_table': self.collection_table, 'master_card_table': self.master_card_table, 'player_portfolios_table': self.player_portfolios_table, 'virtual_assets_table': self.virtual_assets_table, 'market_transactions_table': self.market_transactions_table, 'user_table': self.user_table}
            with_clause = config_structure['with_template'].format(**table_format_args)
            select_fields = config_structure['select_template']
            order_by = config_structure['order_by_template']
            from_clause = config_structure['from_template'].format(**table_format_args)
            group_by_clause = config_structure.get('group_by_template', '')
            where_clause_template = config_structure.get('where_template', '')
            actual_where_clause = ''
            if leaderboard_type == 'completion':
                total_cards_val = int(self._total_cards)
                params_main.append(total_cards_val)
            elif leaderboard_type == 'collection_unique':
                select_fields = await self._generate_optimized_collection_fields()
            elif leaderboard_type == 'stock_holding':
                if not asset_symbol:
                    raise ValueError(f'股票代號為必填項目，排行榜類型: {leaderboard_type}')
                params_main.append(asset_symbol)
                params_count.append(asset_symbol)
                if where_clause_template:
                    actual_where_clause = f'WHERE {where_clause_template}'
            query_main = f'{with_clause} SELECT {select_fields} FROM {from_clause}'
            if actual_where_clause:
                query_main += f' {actual_where_clause}'
            if group_by_clause:
                query_main += f' GROUP BY {group_by_clause}'
            query_main += f' ORDER BY {order_by}'
            current_param_idx_main = len(params_main) + 1
            if limit is not None:
                query_main += f' LIMIT ${current_param_idx_main}'
                params_main.append(limit)
                current_param_idx_main += 1
            query_main += f' OFFSET ${current_param_idx_main}'
            params_main.append(offset)
            if group_by_clause:
                count_select = f'COUNT(*) FROM (SELECT 1 FROM {from_clause}'
                if actual_where_clause:
                    count_select += f' {actual_where_clause}'
                count_select += f' GROUP BY {group_by_clause}) AS subquery_for_count'
            else:
                count_select = f'COUNT(*) FROM {from_clause}'
                if actual_where_clause:
                    count_select += f' {actual_where_clause}'
            query_count = f'{with_clause} SELECT {count_select}'
            results_records_task = self._fetch(query_main, params_main)
            total_rows_task = self._fetchval(query_count, params_count)
            results_records, total_rows = await asyncio.gather(results_records_task, total_rows_task)
            results = [dict(r) for r in results_records]
            return (results, total_rows or 0)
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"獲取排行榜失敗: type={leaderboard_type}, error={e}") from e

    async def search_player_by_name(self, leaderboard_type: str, player_name: str, asset_symbol: Optional[str]=None) -> Optional[Dict[str, Any]]:
        """搜尋玩家在排行榜中的位置"""
        config_structure = self.LEADERBOARD_CONFIG_STRUCTURE.get(leaderboard_type)
        if not config_structure:
            raise ValueError(f'無效的排行榜類型: {leaderboard_type}')
            
        try:
            await self._ensure_metadata_loaded()
            base_params = []
            table_format_args = {'collection_table': self.collection_table, 'master_card_table': self.master_card_table, 'player_portfolios_table': self.player_portfolios_table, 'virtual_assets_table': self.virtual_assets_table, 'market_transactions_table': self.market_transactions_table, 'user_table': self.user_table}
            with_clause = config_structure['with_template'].format(**table_format_args)
            select_fields = config_structure['select_template']
            order_by = config_structure['order_by_template']
            from_clause = config_structure['from_template'].format(**table_format_args)
            group_by_clause = config_structure.get('group_by_template', '')
            where_clause_template = config_structure.get('where_template', '')
            if leaderboard_type == 'completion':
                base_params.append(int(self._total_cards if self._total_cards is not None else 0))
            elif leaderboard_type == 'collection_unique':
                select_fields = await self._generate_optimized_collection_fields()
            elif leaderboard_type == 'stock_holding':
                if not asset_symbol:
                    raise ValueError(f'股票代號為必填項目，排行榜類型: {leaderboard_type}')
                base_params.append(asset_symbol)
            inner_query_select_for_rank = f'SELECT {select_fields}, ROW_NUMBER() OVER (ORDER BY {order_by}) AS rank'
            inner_query_from = f'FROM {from_clause}'
            actual_where_clause_for_rank = ''
            if where_clause_template:
                actual_where_clause_for_rank = f'WHERE {where_clause_template}'
            inner_query_group_by_for_rank = ''
            if group_by_clause:
                inner_query_group_by_for_rank = f'GROUP BY {group_by_clause}'
            search_param_index = len(base_params) + 1
            final_query_params = base_params + [f'%{player_name}%']
            connector = ',' if with_clause.strip() else 'WITH'
            query = f'\n                {with_clause}\n                {connector} user_ranks AS (\n                    {inner_query_select_for_rank}\n                    {inner_query_from}\n                    {actual_where_clause_for_rank}\n                    {inner_query_group_by_for_rank}\n                )\n                SELECT * FROM user_ranks\n                WHERE LOWER(user_name) LIKE LOWER(${search_param_index})\n                ORDER BY rank LIMIT 1\n            '
            result_record = await self._fetchrow(query, final_query_params)
            return dict(result_record) if result_record else None
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"搜尋玩家排名失敗: type={leaderboard_type}, player={player_name}, error={e}") from e

    async def get_rarity_collection_leaderboard(self, limit: Optional[int]=10, offset: int=0) -> List[Dict[str, Any]]:
        results, _ = await self.get_leaderboard('rarity', limit, offset)
        return results

    async def get_completion_leaderboard(self, limit: Optional[int]=10, offset: int=0) -> List[Dict[str, Any]]:
        results, _ = await self.get_leaderboard('completion', limit, offset)
        return results

    async def get_draw_count_leaderboard(self, limit: Optional[int]=10, offset: int=0) -> List[Dict[str, Any]]:
        results, _ = await self.get_leaderboard('draws', limit, offset)
        return results

    async def get_oil_balance_leaderboard(self, limit: Optional[int]=10, offset: int=0) -> List[Dict[str, Any]]:
        results, _ = await self.get_leaderboard('oil', limit, offset)
        return results

    async def get_total_users_count(self) -> int:
        """(Async) 獲取 gacha_users 表中的總用戶數"""
        try:
            query = f'SELECT COUNT(*)::integer as count FROM {self.user_table}'
            count = await self._fetchval(query)
            return count or 0
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"獲取總用戶數失敗: {e}") from e

    async def update_user_leaderboard_stats(self, user_stats: List[Dict[str, Any]]) -> None:
        """(Async) 批量更新用戶排行榜統計數據 (使用 batch_upsert)"""
        if not user_stats:
            return
            
        for stat in user_stats:
            if 'user_id' not in stat:
                raise ValueError('每條統計數據記錄必須包含 user_id 字段')
                
        try:
            all_keys = set()
            for stat in user_stats:
                all_keys.update(stat.keys())
            normalized_stats = []
            for stat in user_stats:
                new_stat = {}
                for key in all_keys:
                    new_stat[key] = stat.get(key)
                normalized_stats.append(new_stat)
            inserted, updated = await self.batch_upsert(self.leaderboard_stats_table, normalized_stats, 'user_id')
            logger.debug('批量更新排行榜統計: 插入 %s, 更新 %s', inserted, updated)
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"批量更新排行榜統計失敗: {e}") from e

    async def _generate_optimized_collection_fields(self) -> str:
        """(Async) 生成優化版的收集排行榜字段"""
        await self._ensure_metadata_loaded()
        fields = ['gu.user_id', 'COALESCE(gu.nickname, CAST(gu.user_id AS VARCHAR)) AS user_name', 'COALESCE(utuc.total_unique_cards, 0)::INTEGER AS total_unique_cards']
        pool_fields = [f"\n            COALESCE((SELECT unique_cards FROM PoolTypeTotals pt WHERE pt.user_id = gu.user_id AND pt.pool_type = '{pool_type}'), 0)::INTEGER AS {pool_type.lower().replace('-', '_')}_pool_unique_cards,\n            COALESCE((SELECT total_count FROM TotalStats ts WHERE ts.stat_type = 'pool' AND ts.type_value = '{pool_type}'), 0)::INTEGER AS {pool_type.lower().replace('-', '_')}_pool_total_cards\n            " for pool_type in self._pool_types]
        rarity_fields = [f"\n            COALESCE((SELECT SUM(unique_cards) FROM UserCollectionStats ucs WHERE ucs.user_id = gu.user_id AND ucs.rarity = {rarity}), 0)::INTEGER AS rarity_{rarity}_unique_cards,\n            COALESCE((SELECT total_count FROM TotalStats ts WHERE ts.stat_type = 'rarity' AND ts.type_value = CAST({rarity} AS VARCHAR)), 0)::INTEGER AS rarity_{rarity}_total_cards\n            " for rarity in self._rarities]
        final_select_statement = ',\n'.join(fields + pool_fields + rarity_fields)
        return final_select_statement

    async def preheat_rankings(self):
        """(Async) 預熱排行榜數據"""
        try:
            leaderboard_types = ['rarity', 'completion', 'draws', 'oil', 'collection_unique']
            leaderboard_types.extend(['portfolio_value', 'trade_volume', 'trade_count'])
            tasks = []
            for lb_type in leaderboard_types:
                for page in range(3):
                    offset = page * 10
                    tasks.append(self.get_leaderboard(lb_type, limit=10, offset=offset))
            await asyncio.gather(*tasks)
        except Exception as e:
            # 預熱失敗不應該影響主要功能，只記錄警告
            logger.warning(f"預熱排行榜數據失敗: {e}")