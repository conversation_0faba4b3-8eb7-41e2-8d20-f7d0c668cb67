services:
  # Bot 測試環境配置（連接到主機現有服務）
  bot-test:
    build: .
    container_name: dickpk_bot_test
    env_file: []  # 禁止讀取 .env 文件，避免被本機設定覆蓋
    volumes:
      # 基本掛載
      - ./logs:/app/logs
      - ./database/gacha_waifu:/app/database/gacha_waifu
      - ./fonts:/app/fonts
      - ./image_processing:/app/image_processing
      
      # 測試環境：代碼熱更新掛載
      - ./bot.py:/app/bot.py
      - ./command_registry.py:/app/command_registry.py
      - ./reaction_utils.py:/app/reaction_utils.py
      - ./config:/app/config
      - ./core:/app/core
      - ./cogs:/app/cogs
      - ./gacha:/app/gacha
      - ./ai_assistant:/app/ai_assistant
      - ./di_system:/app/di_system
      - ./database/postgresql:/app/database/postgresql
      - ./database/redis:/app/database/redis
      - ./utils:/app/utils
      - ./scripts:/app/scripts
      - ./memory-bank:/app/memory-bank
      
      # 配置文件掛載
      - ./config.yaml:/app/config.yaml
      - ./gacha_settings.yaml:/app/gacha_settings.yaml
      - ./ui_settings.yaml:/app/ui_settings.yaml
    environment:
      # 從測試.bat轉移的完整環境變數設定
      - DISCORD_TOKEN=MTMzNzEzMDg4MTY4NDM0NDg0Mw.GGSw6F.HUty_hI6OW9htvuzX9nx3hq3YfEFwrOHBfEygY
      - GACHA_DB_NAME=TEST
      - LOG_LEVEL=DEBUG
      - DATABASE_URL=postgresql://postgres:<EMAIL>:5432/TEST
      - PG_HOST=host.docker.internal  # Docker標準主機訪問方式，不受VPN影響
      - PG_PORT=5432
      - PG_USER=postgres
      - PG_PASSWORD=********
      - PVE_DB_TYPE=postgresql
      - LADDER_DB_TYPE=postgresql
      - LADDER_DATABASE=ladder_database
      - DEV_MODE=true
      - PVE_DATABASE=pve_database
      - ENABLE_PVE_SYSTEM=false
      - ENABLE_LADDER_SYSTEM=true
      - ENABLE_GACHA_SYSTEM=true
      - AI_API_KEY=********
      - REDIS_URL=redis://host.docker.internal:6379/1
      - REDIS_HOST=host.docker.internal  # Docker標準主機訪問方式，不受VPN影響
      - REDIS_PORT=6379
      - REDIS_DB=1
      - REDIS_PASSWORD=
      - REDIS_ENABLED=True
    restart: unless-stopped

volumes:
  postgres_test_data:
  redis_test_data: 