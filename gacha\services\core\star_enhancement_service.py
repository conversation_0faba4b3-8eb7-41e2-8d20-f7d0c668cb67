"""
Gacha系統升星服務
實現卡片升星功能，包括計算消耗資源、成功率、升星操作等
"""
import asyncio
import asyncpg
import random
from typing import Dict, List, Optional, Tuple, Any
from utils.logger import logger
from gacha.constants import MarketStatsEventType
from gacha.utils.redis_publisher import RedisEventPublisher
from database.postgresql.async_manager import AsyncPgManager
from gacha.models.models import Card
from gacha.repositories.collection.user_collection_repository import UserCollectionRepository
from gacha.repositories.card.card_encyclopedia_repository import CardEncyclopediaRepository
from gacha.services.core.economy_service import EconomyService
from gacha.exceptions import InsufficientBalanceError, CardNotFoundError, DatabaseOperationError, GachaRuntimeError

class StarEnhancementService:
    """卡片升星服務，處理升星的核心邏輯"""
    BASE_OIL_COST = 50
    OIL_COST_PER_STAR = 50
    HIGH_STAR_COST_MULTIPLIER = 2.5
    DUPLICATE_COST_PER_ENHANCE = 1
    RARITY_COST_MODIFIER: Dict[str, Dict[int, float]] = {'main': {1: 1.0, 2: 1.5, 3: 2.0, 4: 3.0, 5: 4.0, 6: 6.0, 7: 10.0}, 'special': {1: 1.5, 2: 2.0, 3: 3.0, 4: 5.0, 5: 8.0}, 'summer': {1: 1.0, 2: 1.5, 3: 2.0, 4: 3.0, 5: 4.0, 6: 6.0}}
    SUCCESS_RATE_CONFIG = {(0, 5): 90.0, (6, 10): 80.0, (11, 15): 70.0, (16, 20): 60.0, (21, 24): 40.0, (25, 27): 25.0, (28, 30): 15.0, (31, 35): 8.0}

    def __init__(self, pool: asyncpg.Pool, collection_repo: UserCollectionRepository, encyclopedia_repo: CardEncyclopediaRepository, economy_service: EconomyService, redis_publisher: RedisEventPublisher):
        """初始化升星服務 (Asyncpg 版本) - 接收注入的依賴"""
        if pool is None or collection_repo is None or encyclopedia_repo is None or (economy_service is None) or (redis_publisher is None):
            err_msg = 'StarEnhancementService 初始化失敗：必須提供所有核心依賴 Repository, Service 和 RedisEventPublisher 實例。'
            logger.error(err_msg)
            raise ValueError(err_msg)
        self.pool = pool
        self.collection_repo = collection_repo
        self.encyclopedia_repo = encyclopedia_repo
        self.economy_service = economy_service
        self.redis_publisher = redis_publisher

    def _calculate_enhancement_cost(self, current_star_level: int, rarity: int, pool_type: str) -> Dict[str, int]:
        """計算升級成本 (使用數字稀有度和卡池類型)"""
        base_oil_cost = self.BASE_OIL_COST + current_star_level * self.OIL_COST_PER_STAR
        if current_star_level >= 25:
            base_oil_cost = int(base_oil_cost * self.HIGH_STAR_COST_MULTIPLIER)
        pool_modifiers = self.RARITY_COST_MODIFIER.get(pool_type, self.RARITY_COST_MODIFIER.get('main', {}))
        rarity_modifier = pool_modifiers.get(rarity, 1.0)
        oil_cost = int(base_oil_cost * rarity_modifier)
        duplicate_cost = self.DUPLICATE_COST_PER_ENHANCE
        return {'oil': oil_cost, 'duplicates': duplicate_cost}

    def _calculate_success_rate(self, current_star_level: int) -> float:
        """計算成功率"""
        for level_range, rate in self.SUCCESS_RATE_CONFIG.items():
            if level_range[0] <= current_star_level <= level_range[1]:
                return rate
        max_defined_level = max((level_range[1] for level_range in self.SUCCESS_RATE_CONFIG.keys()))
        if current_star_level > max_defined_level:
            for level_range, rate in self.SUCCESS_RATE_CONFIG.items():
                if level_range[1] == max_defined_level:
                    return rate
        return 5.0

    async def check_enhancement_possibility(self, user_id: int, card_id: int) -> Dict[str, Any]:
        """檢查卡片升星的可能性，如果不可能則拋出異常
        
        Returns:
            Dict[str, Any]: 包含卡片數據、當前星級、成本和成功率的字典
            
        Raises:
            CardNotFoundError: 找不到指定卡片
            GachaRuntimeError: 卡片已達到最高星級
            InsufficientBalanceError: 油幣不足
            GachaRuntimeError: 重複卡不足
        """
        try:
            card_data = await self.collection_repo.get_user_card(user_id, card_id)
            if not card_data:
                logger.warning('找不到用戶 %s 的卡片 %s', user_id, card_id)
                raise CardNotFoundError(f'找不到指定卡片 (ID: {card_id})', card_id=card_id)
                
            current_star_level = card_data.star_level
            max_star_level = getattr(Card, 'MAX_STAR_LEVEL', 35)
            if current_star_level >= max_star_level:
                raise GachaRuntimeError(f'此卡片已達到最高星級（{max_star_level}星）')
                
            costs = self._calculate_enhancement_cost(current_star_level, card_data.card.rarity, card_data.card.pool_type)
            balance_info = await self.economy_service.get_balance(user_id)
            oil_balance = balance_info.get('balance', 0)
            if oil_balance < costs['oil']:
                raise InsufficientBalanceError(
                    message=f"油幣不足，需要{costs['oil']}油幣，但你只有{oil_balance}油幣",
                    required=costs['oil'],
                    current=oil_balance
                )
                
            required_total_cards = costs['duplicates'] + 1
            if card_data.quantity < required_total_cards:
                raise GachaRuntimeError(f"重複卡不足，升星需要消耗 {costs['duplicates']} 張重複卡（共需 {required_total_cards} 張），但你只有 {card_data.quantity} 張")
                
            success_rate = self._calculate_success_rate(current_star_level)
            return {
                'current_star_level': current_star_level, 
                'costs': costs, 
                'success_rate': success_rate, 
                'card_data': card_data
            }
        except (CardNotFoundError, GachaRuntimeError, InsufficientBalanceError) as e:
            # 直接向上傳播這些已知異常
            raise
        except Exception as e:
            logger.error('檢查升星可能性時發生錯誤: %s', str(e), exc_info=True)
            raise GachaRuntimeError(f'檢查升星可能性時發生未知錯誤: {str(e)}')

    async def enhance_card(self, user_id: int, card_id: int) -> Dict[str, Any]:
        """執行卡片升星操作
        
        Returns:
            Dict[str, Any]: 包含升星結果的字典
            
        Raises:
            CardNotFoundError: 找不到指定卡片
            GachaRuntimeError: 卡片已達到最高星級或其他錯誤
            InsufficientBalanceError: 油幣不足
        """
        # 檢查升星可能性，如果不可能會拋出異常
        enhancement_info = await self.check_enhancement_possibility(user_id, card_id)
        
        card_data = enhancement_info['card_data']
        current_star_level = enhancement_info['current_star_level']
        costs = enhancement_info['costs']
        success_rate = enhancement_info['success_rate']
        new_star_level = current_star_level
        remaining_oil = 0
        
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    # 直接使用 award_oil 並獲取新餘額，不再檢查 success 標誌
                    remaining_oil = await self.economy_service.award_oil(user_id, -costs['oil'], '卡片升星', connection=conn)
                    
                    duplicates_to_remove = costs['duplicates']
                    remove_card_result = await self.collection_repo.remove_card(user_id, card_id, duplicates_to_remove, connection=conn)
                    actual_remaining_quantity = remove_card_result['remaining_quantity']
                    
                    if actual_remaining_quantity < 0:
                        logger.error('StarEnhancementService: Unexpected negative remaining quantity (%s) for card %s (user %s) after remove_card call.', actual_remaining_quantity, card_id, user_id)
                        raise GachaRuntimeError('扣除重複卡後，卡片剩餘數量變為非預期的負數。')
                    
                    if actual_remaining_quantity < 1:
                        logger.error('StarEnhancementService: Card %s (user %s) quantity became %s after removing duplicates for enhancement. It should be >= 1.', card_id, user_id, actual_remaining_quantity)
                        raise GachaRuntimeError(f'升星失敗：消耗素材後，目標卡片 {card_id} 數量不足 (剩餘 {actual_remaining_quantity})。')
                    
                    roll = random.random() * 100
                    is_star_enhanced = roll < success_rate
                    
                    if is_star_enhanced:
                        new_star_level = current_star_level + 1
                        await self.collection_repo.update_star_level(user_id, card_id, new_star_level, connection=conn)
                        await self.encyclopedia_repo.update_highest_star(card_id, user_id, new_star_level, connection=conn)
            
            if costs['duplicates'] > 0:
                updates_payload = [(card_id, -costs['duplicates'])]
                await self.redis_publisher.publish(event_type=MarketStatsEventType.TOTAL_OWNED_UPDATE, payload=updates_payload, batch_payload=True, user_id_for_log=user_id)
            
            # 返回結果，遵循純異常模式 - 不包含success字段
            return {
                'message': f"升星{'成功' if is_star_enhanced else '失敗'}！{card_data.card.name} {'從 ' + str(current_star_level) + ' 星升級到 ' + str(new_star_level) + ' 星！' if is_star_enhanced else '星級保持在 ' + str(current_star_level) + ' 星。'}消耗了 {costs['oil']} 油幣和 {costs['duplicates']} 張重複卡。",
                'star_enhanced': is_star_enhanced,
                'old_star_level': current_star_level,
                'new_star_level': new_star_level,
                'costs': costs,
                'remaining_oil': remaining_oil
            }
        
        except (InsufficientBalanceError, CardNotFoundError, GachaRuntimeError, ValueError) as e:
            # 直接向上傳播這些已知異常
            logger.warning('升星操作失敗 (%s): %s', type(e).__name__, str(e))
            raise
        except Exception as e:
            logger.error('升星操作失敗: %s', str(e), exc_info=True)
            raise GachaRuntimeError(f'升星過程中發生未知錯誤: {str(e)}')