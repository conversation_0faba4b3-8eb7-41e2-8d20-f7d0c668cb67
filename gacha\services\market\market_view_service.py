from __future__ import annotations
from dataclasses import dataclass, field
from typing import Optional, Union, Any, List, Dict, TYPE_CHECKING
from decimal import Decimal
import discord
from discord.ext import commands
import asyncpg
from gacha.models.market_models import StockLifecycleStatus
from gacha.constants import RarityLevel
from utils.logger import logger
from gacha.exceptions import (
    MarketCardNotFoundError, 
    MarketStockNotFoundError, 
    MarketDataUnavailableError,
    MarketSystemError
)
if TYPE_CHECKING:
    from gacha.services.market.stock_trading_service import StockTradingService
    from gacha.repositories.card.master_card_repository import MasterCardRepository
    from gacha.models.models import Card

@dataclass
class StockData:
    asset_id: int
    asset_symbol: str
    asset_name: str
    current_price: Decimal
    description: Optional[str] = None
    previous_price: Optional[Decimal] = None
    volume_24h: Optional[Decimal] = None
    lifecycle_status: Optional[StockLifecycleStatus] = None
    total_shares: Optional[int] = None
    base_volatility: Optional[Decimal] = None
    volatility_factor: Optional[Decimal] = None
    last_updated: Optional[Any] = None

    @staticmethod
    def from_record(record: Union[Dict[str, Any], asyncpg.Record]) -> 'StockData':
        status_val = record.get('lifecycle_status')
        lifecycle_status_enum = None
        if status_val:
            try:
                lifecycle_status_enum = StockLifecycleStatus(status_val)
            except ValueError:
                logger.warning("Invalid lifecycle_status value '%s' for asset_id %s. Defaulting to None.", status_val, record.get('asset_id'))
        return StockData(
            asset_id=record['asset_id'],
            asset_symbol=record['asset_symbol'],
            asset_name=record['asset_name'],
            current_price=Decimal(str(record['current_price'])),
            description=record.get('description'),
            previous_price=Decimal(str(record['previous_price'])) if record.get('previous_price') is not None else None,
            volume_24h=Decimal(str(record.get('volume_24h'))) if record.get('volume_24h') is not None else None,
            lifecycle_status=lifecycle_status_enum,
            total_shares=record.get('total_shares'),
            base_volatility=Decimal(str(record['base_volatility'])) if record.get('base_volatility') is not None else None,
            volatility_factor=Decimal(str(record['volatility_factor'])) if record.get('volatility_factor') is not None else None,
            last_updated=record.get('last_updated')
        )

@dataclass
class StockListPageData:
    stocks: List[StockData]
    current_page: int
    total_pages: int
    total_stocks: int

@dataclass
class AggregatedVolumeData:
    total_buy_volume: int
    total_sell_volume: int

@dataclass
class RecentTransactionData:
    user_nickname: str
    transaction_type: str
    quantity: int
    price_per_unit: Decimal
    timestamp: Any

    @staticmethod
    def from_record(record: Union[Dict[str, Any], asyncpg.Record]) -> 'RecentTransactionData':
        return RecentTransactionData(user_nickname=record['user_nickname'], transaction_type=record['transaction_type'], quantity=record['quantity'], price_per_unit=Decimal(str(record['price_per_unit'])), timestamp=record['timestamp'])

@dataclass
class StockDetailData:
    stock_data: StockData
    aggregated_volume_7d: Optional[AggregatedVolumeData] = None
    recent_transactions_5: List[RecentTransactionData] = field(default_factory=list)
    chart_image_bytes: Optional[bytes] = None
    user_oil_balance: Optional[Decimal] = None
    user_stock_quantity: Optional[int] = None

@dataclass
class NewsFilterOptions:
    filter_type: Any
    news_type_filter: Any

@dataclass
class NewsItemData:
    id: int
    headline: str
    content: str
    published_at: Any
    sentiment: Optional[str] = None
    source: Optional[str] = None
    news_type: Optional[str] = None
    character_archetype: Optional[str] = None
    character_name: Optional[str] = None
    impacts_price: Optional[bool] = None
    asset_symbol: Optional[str] = None
    asset_name: Optional[str] = None
    asset_lifecycle_status: Optional[StockLifecycleStatus] = None

    @staticmethod
    def from_record(record: Union[Dict[str, Any], asyncpg.Record]) -> 'NewsItemData':
        status_val = record.get('asset_lifecycle_status')
        lifecycle_status_enum = None
        if status_val:
            try:
                lifecycle_status_enum = StockLifecycleStatus(status_val)
            except ValueError:
                logger.warning("Invalid asset_lifecycle_status value '%s' for news_id %s. Defaulting to None.", status_val, record.get('id'))
        return NewsItemData(id=record['id'], headline=record['headline'], content=record['content'], published_at=record['published_at'], sentiment=record.get('sentiment'), source=record.get('source'), news_type=record.get('news_type'), character_archetype=record.get('character_archetype'), character_name=record.get('character_name'), impacts_price=record.get('impacts_price'), asset_symbol=record.get('asset_symbol'), asset_name=record.get('asset_name'), asset_lifecycle_status=lifecycle_status_enum)

@dataclass
class NewsPageData:
    news_items: List[NewsItemData]
    current_page: int
    total_items: int
    filters_applied: NewsFilterOptions

@dataclass
class CardMarketStatsData:
    total_owned_quantity: Optional[int] = None
    unique_owner_count: Optional[int] = None
    wishlist_count: Optional[int] = None
    favorite_count: Optional[int] = None

@dataclass
class LinkedStockData:
    asset_symbol: str
    asset_name: str
    current_price: Decimal
    lifecycle_status: Optional[StockLifecycleStatus] = None

    @staticmethod
    def from_record(record: Union[Dict[str, Any], asyncpg.Record]) -> 'LinkedStockData':
        status_val = record.get('lifecycle_status')
        lifecycle_status_enum = None
        if status_val:
            try:
                lifecycle_status_enum = StockLifecycleStatus(status_val)
            except ValueError:
                logger.warning("Invalid lifecycle_status value '%s' for linked stock %s. Defaulting to None.", status_val, record.get('asset_symbol'))
        return LinkedStockData(asset_symbol=record['asset_symbol'], asset_name=record['asset_name'], current_price=Decimal(str(record['current_price'])), lifecycle_status=lifecycle_status_enum)

@dataclass
class CardInfoData:
    card_id: int
    name: str
    series: Optional[str] = None
    rarity: Optional[int] = None
    pool_type: Optional[str] = None
    current_market_sell_price: Optional[Decimal] = None
    image_url: Optional[str] = None
    description: Optional[str] = None
    market_stats: Optional[CardMarketStatsData] = None
    linked_stocks: List[LinkedStockData] = field(default_factory=list)
    raw_card_data: Optional[Any] = None

class MarketViewService:

    def __init__(self, bot: commands.Bot, pool: asyncpg.Pool, stock_trading_service: 'StockTradingService', master_card_repo: 'MasterCardRepository'):
        self.bot = bot
        self.pool = pool
        self.stock_trading_service = stock_trading_service
        self.master_card_repo = master_card_repo

    async def get_stock_list_page_data(self, page: int, per_page: int = 10) -> StockListPageData:
        """Fetches data for the stock list view."""
        logger.info('MarketViewService: Fetching stock list page %s, per_page %s', page, per_page)
        try:
            raw_page_data = await self.stock_trading_service.get_all_stocks_paginated(page=page, per_page=per_page)
            if not raw_page_data:
                raise MarketDataUnavailableError("無法獲取股票列表數據")
                
            stocks_dto_list: List[StockData] = []
            if raw_page_data.get('stocks'):
                for stock_dict in raw_page_data['stocks']:
                    stocks_dto_list.append(StockData.from_record(stock_dict))
            
            return StockListPageData(
                stocks=stocks_dto_list,
                current_page=raw_page_data.get('page', page),
                total_pages=raw_page_data.get('total_pages', 1),
                total_stocks=raw_page_data.get('total_stocks', 0)
            )
        except MarketDataUnavailableError:
            raise
        except Exception as e:
            logger.error('Error in get_stock_list_page_data: %s', e, exc_info=True)
            raise MarketSystemError(f"獲取股票列表時發生錯誤: {str(e)}")

    async def get_stock_detail_data(self, asset_symbol_or_id: Union[str, int], user_id: int) -> StockDetailData:
        """Fetches comprehensive data for the stock detail view."""
        logger.info('MarketViewService: Fetching stock detail for %s, user %s', asset_symbol_or_id, user_id)
        try:
            raw_detail_data = await self.stock_trading_service.get_stock_details_for_view(asset_symbol_or_id, user_id=user_id)
            if not raw_detail_data or not raw_detail_data.get('stock_data'):
                raise MarketStockNotFoundError(f"找不到股票: {asset_symbol_or_id}", str(asset_symbol_or_id))
                
            sd = raw_detail_data['stock_data']
            stock_data_dto = StockData.from_record(sd)
            
            agg_vol_raw = raw_detail_data.get('aggregated_volume_7d')
            agg_vol_dto = AggregatedVolumeData(**agg_vol_raw) if agg_vol_raw else None
            
            recent_trans_dto_list: List[RecentTransactionData] = []
            if raw_detail_data.get('recent_transactions_5'):
                for rt_dict in raw_detail_data['recent_transactions_5']:
                    recent_trans_dto_list.append(RecentTransactionData.from_record(rt_dict))
            
            return StockDetailData(
                stock_data=stock_data_dto,
                aggregated_volume_7d=agg_vol_dto,
                recent_transactions_5=recent_trans_dto_list,
                chart_image_bytes=raw_detail_data.get('chart_image_bytes'),
                user_oil_balance=Decimal(str(raw_detail_data['user_oil_balance'])) if raw_detail_data.get('user_oil_balance') is not None else None,
                user_stock_quantity=raw_detail_data.get('user_stock_quantity')
            )
        except MarketStockNotFoundError:
            raise
        except Exception as e:
            logger.error('Error in get_stock_detail_data for %s: %s', asset_symbol_or_id, e, exc_info=True)
            raise MarketSystemError(f"獲取股票詳情時發生錯誤: {str(e)}")

    async def _build_news_query(self, user_id: int, filters: NewsFilterOptions) -> tuple[str, List[Any], str, List[Any]]:
        """
        Builds the SELECT and COUNT queries for news items based on filters.
        Returns: (select_query_template, select_params_template, count_query_template, count_params)
        The templates will have placeholders for LIMIT and OFFSET.
        """
        select_base = '\n            SELECT\n                mn.id, mn.headline, mn.content, mn.published_at, mn.sentiment, mn.source,\n                mn.news_type, mn.character_archetype, mn.character_name, mn.impacts_price,\n                va.asset_symbol, va.asset_name, va.lifecycle_status AS asset_lifecycle_status\n            FROM market_news mn\n            LEFT JOIN virtual_assets va ON mn.affected_asset_id = va.asset_id\n        '
        count_base = 'SELECT COUNT(mn.id) FROM market_news mn LEFT JOIN virtual_assets va ON mn.affected_asset_id = va.asset_id'
        where_conditions: List[str] = []
        query_params: List[Any] = []
        param_idx = 1
        if hasattr(filters, 'news_type_filter') and filters.news_type_filter and (filters.news_type_filter.value != 'all_types'):
            where_conditions.append(f'mn.news_type = ${param_idx}')
            query_params.append(filters.news_type_filter.value)
            param_idx += 1
        if filters.filter_type:
            if filters.filter_type.value == 'portfolio':
                user_portfolio_asset_ids = await self.stock_trading_service.get_user_portfolio_assets(user_id)
                if user_portfolio_asset_ids:
                    where_conditions.append(f'mn.affected_asset_id = ANY(${param_idx}::INT[])')
                    query_params.append(user_portfolio_asset_ids)
                    param_idx += 1
                else:
                    where_conditions.append('1 = 0')
            elif filters.filter_type.value == 'global':
                where_conditions.append('(mn.affected_asset_id IS NULL)')
        query_where_clause = ''
        if where_conditions:
            query_where_clause = 'WHERE ' + ' AND '.join(where_conditions)
        select_query_template = f'{select_base} {query_where_clause} ORDER BY mn.published_at DESC'
        count_query_full = f'{count_base} {query_where_clause}'
        return (select_query_template, list(query_params), count_query_full, list(query_params))

    async def get_news_page_data(self, user_id: int, filters: NewsFilterOptions, current_page_idx: int, items_per_page: int=1) -> NewsPageData:
        """Fetches news data based on filters and pagination. items_per_page=1 for single news view."""
        logger.info('MarketViewService: Fetching news page for user %s, filters: %s, page_idx: %s, items_per_page: %s', user_id, filters, current_page_idx, items_per_page)
        try:
            select_template, select_params_template, count_query, count_params = await self._build_news_query(user_id, filters)
            news_items_dto: List[NewsItemData] = []
            total_items = 0
            async with self.pool.acquire() as conn:
                total_items_record = await conn.fetchval(count_query, *count_params)
                total_items = total_items_record if total_items_record is not None else 0
                if total_items > 0:
                    limit_offset_params = [items_per_page, current_page_idx * items_per_page]
                    final_select_query = f'{select_template} LIMIT ${len(select_params_template) + 1} OFFSET ${len(select_params_template) + 2}'
                    final_select_params = select_params_template + limit_offset_params
                    raw_news_records = await conn.fetch(final_select_query, *final_select_params)
                    for rec in raw_news_records:
                        news_items_dto.append(NewsItemData.from_record(rec))
            return NewsPageData(news_items=news_items_dto, current_page=current_page_idx + 1, total_items=total_items, filters_applied=filters)
        except Exception as e:
            logger.error('Error in get_news_page_data: %s', e, exc_info=True)
            raise MarketSystemError(f"獲取新聞數據時發生錯誤: {str(e)}")

    async def get_card_info_data(self, card_query: str) -> CardInfoData:
        """Fetches card information, including market stats and linked stocks."""
        logger.info("MarketViewService: Fetching card info for query '%s'", card_query)
        
        # pylint: disable=possibly-used-before-assignment
        # 註：Card 類在 TYPE_CHECKING 塊中正確導入，這是避免循環導入的標準做法
        card_data_obj: Optional['Card'] = None
        card_id: Optional[int] = None
        
        try:
            if card_query.isdigit():
                try:
                    card_id = int(card_query)
                    card_data_obj = await self.master_card_repo.get_card(card_id)
                except ValueError:
                    logger.warning('Invalid card ID format: %s', card_query)
                    raise MarketCardNotFoundError(f"無效的卡片ID格式: {card_query}", card_query)
            else:
                logger.debug("[MarketViewService.get_card_info_data] Attempting to find card by name/original_id: '%s'", card_query)
                try:
                    raw_card_data = await self.master_card_repo._fetchrow(
                        f'SELECT * FROM {self.master_card_repo.table_name} WHERE name = $1 OR original_id = $1 LIMIT 1',
                        (card_query,)
                    )
                    if raw_card_data:
                        try:
                            # pylint: disable=possibly-used-before-assignment
                            # 註：Card 類在運行時通過 TYPE_CHECKING 正確解析
                            card_data_obj = Card.from_db_record(raw_card_data)
                            if card_data_obj:
                                card_id = card_data_obj.card_id
                                logger.debug('[MarketViewService.get_card_info_data] Found card by name/original_id: %s', card_data_obj)
                            else:
                                logger.warning("[MarketViewService.get_card_info_data] Card.from_db_record returned None for name query '%s', raw_data: %s", card_query, raw_card_data)
                                card_data_obj = None
                                card_id = None
                        except ValueError as e_map:
                            logger.error("[MarketViewService.get_card_info_data] Error mapping card data from name/original_id search for query '%s': %s", card_query, e_map, exc_info=True)
                            raise MarketSystemError(f"卡片數據映射失敗: {str(e_map)}")
                    else:
                        logger.debug("[MarketViewService.get_card_info_data] No card found with name/original_id: '%s' from DB query.", card_query)
                        card_data_obj = None
                        card_id = None
                except Exception as e:
                    logger.error("[MarketViewService.get_card_info_data] Unexpected error during name/original_id search for card '%s': %s", card_query, e, exc_info=True)
                    raise MarketSystemError(f"查詢卡片時發生錯誤: {str(e)}")
            
            if not card_data_obj or not card_id:
                logger.warning('Card not found for query: %s', card_query)
                raise MarketCardNotFoundError(f"找不到卡片: {card_query}", card_query)
            
            # 獲取市場統計和關聯股票
            market_stats_dto = await self.get_card_market_stats(card_id)
            linked_stocks_dto = await self.get_linked_stocks_for_card(card_id, card_data_obj)
            
            # 處理稀有度
            rarity_val: Optional[int] = None
            if card_data_obj and hasattr(card_data_obj, 'rarity') and (card_data_obj.rarity is not None):
                if isinstance(card_data_obj.rarity, RarityLevel):
                    rarity_val = card_data_obj.rarity.value
                else:
                    logger.warning('Card ID %s has rarity of type %s, expected RarityLevel enum. Value: %s', 
                                 card_data_obj.card_id, type(card_data_obj.rarity), card_data_obj.rarity)
                    if isinstance(card_data_obj.rarity, int):
                        rarity_val = card_data_obj.rarity
            
            # 處理卡池類型
            pool_type_val: Optional[str] = None
            if card_data_obj and hasattr(card_data_obj, 'pool_type'):
                pool_type_val = card_data_obj.pool_type
            
            return CardInfoData(
                card_id=card_data_obj.card_id,
                name=card_data_obj.name,
                series=getattr(card_data_obj, 'series', None),
                rarity=rarity_val,
                pool_type=pool_type_val,
                current_market_sell_price=Decimal(str(card_data_obj.current_market_sell_price)) if hasattr(card_data_obj, 'current_market_sell_price') and card_data_obj.current_market_sell_price is not None else None,
                image_url=getattr(card_data_obj, 'image_url', None),
                description=getattr(card_data_obj, 'description', None),
                market_stats=market_stats_dto,
                linked_stocks=linked_stocks_dto,
                raw_card_data=card_data_obj
            )
        except (MarketCardNotFoundError, MarketSystemError):
            raise
        except Exception as e:
            logger.error("Unexpected error in get_card_info_data for query '%s': %s", card_query, e, exc_info=True)
            raise MarketSystemError(f"獲取卡片信息時發生未預期錯誤: {str(e)}")

    async def get_card_market_stats(self, card_id: int) -> Optional[CardMarketStatsData]:
        """Fetches market statistics for a given card ID."""
        logger.info('MarketViewService: Fetching market stats for card_id %s', card_id)
        async with self.pool.acquire() as conn:
            try:
                stats_row = await conn.fetchrow(
                    'SELECT total_owned_quantity, unique_owner_count, wishlist_count, favorite_count FROM gacha_card_market_stats WHERE card_id = $1',
                    card_id
                )
                if stats_row:
                    return CardMarketStatsData(**dict(stats_row))
                return CardMarketStatsData()
            except Exception as e:
                logger.error('Error fetching card market stats for card_id %s: %s', card_id, e, exc_info=True)
                # 市場統計失敗不應該阻止整個操作，返回空統計
                return CardMarketStatsData()

    async def get_linked_stocks_for_card(self, card_id: int, card_model_instance: 'Card') -> List[LinkedStockData]:
        """Fetches stocks linked to a card based on its properties using clearer SQL construction."""
        linked_stocks_info_dto: List[LinkedStockData] = []
        
        # 構建查詢條件
        conditions = []
        query_params = []
        
        if hasattr(card_model_instance, 'series') and card_model_instance.series:
            conditions.append("asset_name ILIKE $" + str(len(query_params) + 1))
            query_params.append(f"%{card_model_instance.series}%")
        
        if hasattr(card_model_instance, 'name') and card_model_instance.name:
            conditions.append("asset_name ILIKE $" + str(len(query_params) + 1))
            query_params.append(f"%{card_model_instance.name}%")
        
        if not conditions:
            return linked_stocks_info_dto
        
        where_clause = " OR ".join(conditions)
        full_query_text = f"SELECT asset_symbol, asset_name, current_price, lifecycle_status FROM virtual_assets WHERE {where_clause} ORDER BY current_price DESC LIMIT 5"
        
        async with self.pool.acquire() as conn:
            try:
                stocks_data_raw = await conn.fetch(full_query_text, *query_params)
                for stock_row in stocks_data_raw:
                    linked_stocks_info_dto.append(LinkedStockData.from_record(stock_row))
            except Exception as e:
                logger.error('Error fetching linked stocks for card_id %s: %s', card_id, e, exc_info=True)
                # 關聯股票失敗不應該阻止整個操作，返回空列表
        
        return linked_stocks_info_dto