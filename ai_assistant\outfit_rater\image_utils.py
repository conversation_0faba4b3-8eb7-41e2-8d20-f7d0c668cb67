"""
圖像工具模組 - 提供圖像處理功能
"""

import io
import logging
from typing import Optional, Tuple
from PIL import Image

# 設置日誌
logger = logging.getLogger("ImageUtils")

class ImageUtils:
    """提供圖像處理功能的工具類"""
    
    @staticmethod
    async def download_image(url: str) -> Optional[bytes]:
        """從URL下載圖像"""
        import aiohttp  # 動態導入
        try:
            async with aiohttp.ClientSession() as session:
                logger.info(f"下載圖像: {url}")
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.read()
                        logger.info(f"下載成功: {len(data)} 字節")
                        return data
                    logger.error(f"下載失敗: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"下載錯誤: {str(e)}")
            return None
    
    @staticmethod
    def ensure_compatible_format(image_data: bytes) -> bytes:
        """確保圖像格式兼容，將webp等特殊格式轉換為標準格式"""
        try:
            with Image.open(io.BytesIO(image_data)) as img:
                img_format = img.format.lower() if img.format else "unknown"
                
                # 只處理需要轉換的格式
                if img_format in ["webp", "tiff", "bmp"]:
                    logger.info(f"轉換{img_format}格式")
                    output = io.BytesIO()
                    
                    # 根據圖像模式選擇適當的格式
                    if img.mode == "RGBA":
                        img.save(output, format="PNG")
                    else:
                        img.save(output, format="JPEG", quality=95)
                    
                    converted_data = output.getvalue()
                    logger.info(f"格式轉換完成: {len(converted_data)} 字節")
                    return converted_data
                
                logger.info(f"無需轉換: {img_format}")
                return image_data
        except Exception as e:
            logger.error(f"格式轉換失敗: {str(e)}")
            return image_data
    
    @staticmethod
    def resize_image(image_data: bytes, max_size: int = 2 * 1024 * 1024, 
                     max_dimensions: Tuple[int, int] = (1000, 1000)) -> bytes:
        """調整圖像大小以符合API限制"""
        try:
            # 確保格式兼容
            image_data = ImageUtils.ensure_compatible_format(image_data)
            
            # 已符合大小限制
            if len(image_data) <= max_size:
                logger.info(f"圖像大小已符合要求: {len(image_data)} 字節")
                return image_data
            
            # 打開圖像並調整尺寸
            img = Image.open(io.BytesIO(image_data))
            width, height = img.size
            
            # 需要調整尺寸
            if width > max_dimensions[0] or height > max_dimensions[1]:
                ratio = min(max_dimensions[0] / width, max_dimensions[1] / height)
                new_width, new_height = int(width * ratio), int(height * ratio)
                logger.info(f"調整尺寸: {width}x{height} → {new_width}x{new_height}")
                img = img.resize((new_width, new_height), Image.LANCZOS)
            
            # 調整品質
            for quality in [85, 80, 75, 70, 65, 60, 55, 50]:
                output = io.BytesIO()
                img.save(output, format="JPEG", quality=quality)
                resized_data = output.getvalue()
                
                if len(resized_data) <= max_size:
                    logger.info(f"調整完成: {len(resized_data)} 字節, 品質: {quality}")
                    return resized_data
                
                logger.debug(f"尺寸仍超過限制: {len(resized_data)} > {max_size}, 品質: {quality}")
            
            # 返回最後一次調整的結果
            logger.warning(f"無法達到目標大小: {len(resized_data)} > {max_size}")
            return resized_data
            
        except Exception as e:
            logger.error(f"調整圖像失敗: {str(e)}")
            return image_data 