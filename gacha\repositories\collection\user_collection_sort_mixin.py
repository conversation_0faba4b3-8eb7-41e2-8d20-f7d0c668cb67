"""
Gacha系統用戶收藏存儲庫 - 自定義排序 Mixin (Asyncpg 版本)
"""
import asyncpg
from typing import Dict, List, Optional, Tuple, Any
from utils.logger import logger
from gacha.models.filters import CollectionFilters
from gacha.repositories.sql_queries import GET_USER_CARD_POSITION_SQL
from gacha.exceptions import CardNotFoundError, DatabaseOperationError, RepositoryError

class UserCollectionSortMixin:
    """提供用戶收藏自定義排序相關方法的 Mixin (Asyncpg 版本)"""

    async def get_card_sort_indexes(self, user_id: int, connection: Optional[asyncpg.Connection]=None) -> Dict[int, Optional[int]]:
        """(Async) 獲取用戶所有卡片的自定義排序索引."""
        query = f'\n            SELECT card_id, custom_sort_index\n            FROM {self.table_name}\n            WHERE user_id = $1\n            ORDER BY custom_sort_index ASC NULLS LAST, card_id ASC\n        '
        results = await self._fetch(query, (user_id,), connection=connection)
        return {result['card_id']: result['custom_sort_index'] for result in results} if results else {}

    async def get_min_custom_sort_index(self, user_id: int, connection: Optional[asyncpg.Connection]=None) -> Optional[int]:
        """(Async) 獲取用戶已排序卡片中最小的排序索引值. 未找到則返回 None."""
        query = f'\n            SELECT MIN(custom_sort_index) as min_index\n            FROM {self.table_name}\n            WHERE user_id = $1 AND custom_sort_index IS NOT NULL\n        '
        min_index = await self._fetchval(query, (user_id,), connection=connection)
        return min_index

    async def get_max_custom_sort_index(self, user_id: int, connection: Optional[asyncpg.Connection]=None) -> Optional[int]:
        """(Async) 獲取用戶已排序卡片中最大的排序索引值. 未找到則返回 None."""
        query = f'\n            SELECT MAX(custom_sort_index) as max_index\n            FROM {self.table_name}\n            WHERE user_id = $1 AND custom_sort_index IS NOT NULL\n        '
        max_index = await self._fetchval(query, (user_id,), connection=connection)
        return max_index

    async def count_sorted_cards(self, user_id: int, connection: Optional[asyncpg.Connection]=None) -> int:
        """(Async) 計算用戶已排序卡片的數量."""
        query = f'\n            SELECT COUNT(*)::integer as count\n            FROM {self.table_name}\n            WHERE user_id = $1 AND custom_sort_index IS NOT NULL\n        '
        count = await self._fetchval(query, (user_id,), connection=connection)
        return count or 0

    async def get_card_position(self, user_id: int, card_id: int, connection: Optional[asyncpg.Connection]=None) -> int:
        """(Async) 獲取卡片的當前位置 (基於自定義排序). 未找到則拋出 CardNotFoundError."""
        query = f'\n            WITH ranked_cards AS (\n                SELECT card_id, custom_sort_index,\n                       ROW_NUMBER() OVER (ORDER BY custom_sort_index ASC NULLS LAST, card_id ASC) as rank\n                FROM {self.table_name}\n                WHERE user_id = $1\n            )\n            SELECT rank, custom_sort_index\n            FROM ranked_cards\n            WHERE card_id = $2\n        '
        result = await self._fetchrow(query, (user_id, card_id), connection=connection)
        if result and result['custom_sort_index'] is not None and ('rank' in result):
            return result['rank']
        raise CardNotFoundError(f'找不到卡片 {card_id} 的排序位置 for user {user_id}')

    async def get_card_sort_info(self, user_id: int, card_id: int, connection: Optional[asyncpg.Connection]=None) -> Dict[str, Any]:
        """(Async) 獲取卡片的排序信息 (索引值和排名). 未找到則拋出 CardNotFoundError."""
        query = f'\n            WITH ranked_cards AS (\n                SELECT card_id, custom_sort_index,\n                       ROW_NUMBER() OVER (ORDER BY custom_sort_index ASC NULLS LAST, card_id ASC) as rank\n                FROM {self.table_name}\n                WHERE user_id = $1\n            )\n            SELECT rc.custom_sort_index as sort_index, rc.rank\n            FROM ranked_cards rc\n            WHERE rc.card_id = $2\n        '
        result = await self._fetchrow(query, (user_id, card_id), connection=connection)
        if result:
            return {'sort_index': result.get('sort_index'), 'rank': result.get('rank')}
        raise CardNotFoundError(f'找不到卡片 {card_id} 的排序信息 for user {user_id}')

    async def get_card_at_position(self, user_id: int, position: int, connection: Optional[asyncpg.Connection]=None) -> Dict[str, Any]:
        """(Async) 根據位置獲取已排序卡片信息 (card_id, sort_index). 未找到則拋出 CardNotFoundError."""
        if position < 1:
            raise ValueError('位置參數必須大於等於1')
        query = f'\n            WITH ranked_cards AS (\n                SELECT card_id, custom_sort_index,\n                       ROW_NUMBER() OVER (ORDER BY custom_sort_index ASC NULLS LAST, card_id ASC) as rank\n                FROM {self.table_name}\n                WHERE user_id = $1 AND custom_sort_index IS NOT NULL\n            )\n            SELECT card_id, custom_sort_index as sort_index\n            FROM ranked_cards\n            WHERE rank = $2\n        '
        result = await self._fetchrow(query, (user_id, position), connection=connection)
        if not result:
            raise CardNotFoundError(f'在位置 {position} 找不到已排序的卡片 for user {user_id}')
        return dict(result)

    async def get_prev_sorted_card(self, user_id: int, current_sort_index: int, connection: Optional[asyncpg.Connection]=None) -> Optional[Dict[str, Any]]:
        """(Async) 獲取排序索引小於指定值的前一張已排序卡片. 未找到則返回 None."""
        query = f'\n            SELECT card_id, custom_sort_index as sort_index\n            FROM {self.table_name}\n            WHERE user_id = $1 AND custom_sort_index IS NOT NULL AND custom_sort_index < $2\n            ORDER BY custom_sort_index DESC\n            LIMIT 1\n        '
        result = await self._fetchrow(query, (user_id, current_sort_index), connection=connection)
        return dict(result) if result else None

    async def get_next_sorted_card(self, user_id: int, current_sort_index: int, connection: Optional[asyncpg.Connection]=None) -> Optional[Dict[str, Any]]:
        """(Async) 獲取排序索引大於指定值的下一張已排序卡片. 未找到則返回 None."""
        query = f'\n            SELECT card_id, custom_sort_index as sort_index\n            FROM {self.table_name}\n            WHERE user_id = $1 AND custom_sort_index IS NOT NULL AND custom_sort_index > $2\n            ORDER BY custom_sort_index ASC\n            LIMIT 1\n        '
        result = await self._fetchrow(query, (user_id, current_sort_index), connection=connection)
        return dict(result) if result else None

    async def get_all_sorted_cards(self, user_id: int, connection: Optional[asyncpg.Connection]=None) -> List[Dict[str, Any]]:
        """(Async) 獲取用戶所有已排序卡片信息 (card_id, sort_index, name, rarity)."""
        query = f'\n            SELECT uc.card_id, uc.custom_sort_index, c.name, c.rarity\n            FROM {self.table_name} uc\n            JOIN gacha_master_cards c ON uc.card_id = c.card_id\n            WHERE uc.user_id = $1 AND uc.custom_sort_index IS NOT NULL\n            ORDER BY uc.custom_sort_index ASC, uc.card_id ASC\n        '
        results = await self._fetch(query, (user_id,), connection=connection)
        return [dict(row) for row in results] if results else []

    async def get_card_with_sort_index(self, user_id: int, sort_index: int, exclude_card_id: Optional[int]=None, connection: Optional[asyncpg.Connection]=None) -> Optional[Dict[str, Any]]:
        """(Async) 查找具有特定排序索引值的卡片 (返回 card_id 和 sort_index). 未找到則返回 None."""
        params: List[Any] = [user_id, sort_index]
        exclude_clause = ''
        if exclude_card_id is not None:
            exclude_clause = f' AND card_id != ${len(params) + 1}'
            params.append(exclude_card_id)
        query = f'\n            SELECT card_id, custom_sort_index as sort_index\n            FROM {self.table_name}\n            WHERE user_id = $1 AND custom_sort_index = $2\n        '
        query += exclude_clause
        query += ' LIMIT 1'
        result = await self._fetchrow(query, params, connection=connection)
        return dict(result) if result else None

    def _build_sorting_clause(self, sort_by: str, sort_order: str) -> str:
        """構建通用的排序子句 (內部輔助) - 統一實現選項 B"""
        unified_cache_key = f'unified_optionB-{sort_by}-{sort_order}'
        if not hasattr(self, '_sorting_clause_cache'):
            self._sorting_clause_cache = {}
        if unified_cache_key in self._sorting_clause_cache:
            return self._sorting_clause_cache[unified_cache_key]
        default_sort_by = sort_by.lower() if sort_by else 'rarity'
        default_sort_order = sort_order.lower() if sort_order in ['asc', 'desc'] else 'desc'
        default_sort_dir = 'ASC' if default_sort_order == 'asc' else 'DESC'
        text_nulls_order = 'NULLS LAST'
        numeric_nulls_order = 'NULLS LAST'
        rarity_sort_dir = 'DESC' if default_sort_order == 'desc' else 'ASC'
        sort_field_map = {'rarity': 'mc.rarity', 'name': 'mc.name', 'series': 'mc.series', 'quantity': 'uc.quantity', 'star': 'uc.star_level', 'position': 'uc.custom_sort_index'}
        default_sort_field = sort_field_map.get(default_sort_by)
        order_clauses = []
        order_clauses.append('CASE WHEN uc.custom_sort_index IS NULL THEN 1 ELSE 0 END ASC')
        order_clauses.append('uc.custom_sort_index ASC NULLS LAST')
        if default_sort_field:
            if default_sort_field in [sort_field_map['name'], sort_field_map['series']]:
                order_clauses.append(f'{default_sort_field} {default_sort_dir} {text_nulls_order}')
            else:
                order_clauses.append(f"{default_sort_field} {(rarity_sort_dir if default_sort_by == 'rarity' else default_sort_dir)} {numeric_nulls_order}")
        else:
            logger.warning('Invalid default sort_by value: %s. Falling back to rarity sort for NULL group.', default_sort_by)
            order_clauses.append(f"{sort_field_map['rarity']} DESC {numeric_nulls_order}")
        if default_sort_by != 'name':
            order_clauses.append(f"{sort_field_map['name']} ASC {text_nulls_order}")
        order_clauses.append('uc.card_id ASC')
        final_clause_string = ', '.join(order_clauses)
        self._sorting_clause_cache[unified_cache_key] = final_clause_string
        logger.debug("[GACHA][SORT][CLAUSE] Built sorting clause for '%s' (Option B Unified): %s", unified_cache_key, final_clause_string)
        return final_clause_string

    async def get_user_card_position(self, user_id: int, card_id: int, sort_by: str='rarity', sort_order: str='desc', filters: Optional[CollectionFilters]=None, connection: Optional[asyncpg.Connection]=None) -> Dict[str, Any]:
        """(Async) 獲取用戶卡片在篩選和排序後的列表中的位置、總數以及卡片的 custom_sort_index.

        Args:
            user_id: 用戶 ID.
            card_id: 要查找位置的卡片 ID.
            sort_by: 排序欄位 (例如, 'rarity', 'name', 'id', 'quantity', 'series', 'first_acquired', 'last_acquired').
            sort_order: 排序順序 ('asc' 或 'desc').
            filters: CollectionFilters 對象，包含篩選條件.
            connection: 可選的 asyncpg 連線.

        Returns:
            一個字典，包含:
            - 'position': 卡片在排序列表中的位置 (1-based). 如果卡片未找到或不符合篩選條件，則為 None.
            - 'total': 符合篩選條件的總卡片數.
            - 'custom_sort_index': 卡片的 custom_sort_index 值 (如果存在).
            
        Raises:
            CardNotFoundError: 如果未找到卡片
            RepositoryError: 如果查詢配置錯誤
            DatabaseOperationError: 如果數據庫操作失敗
        """
        if filters is None:
            filters = CollectionFilters()
        where_clause, filter_params = self._build_filter_conditions(user_id, filters)
        order_clause = self._build_sorting_clause(sort_by, sort_order)
        card_id_placeholder_index = len(filter_params) + 1
        final_params = filter_params + [card_id]
        if not GET_USER_CARD_POSITION_SQL or not isinstance(GET_USER_CARD_POSITION_SQL, str):
            logger.error('[GACHA_SORT_MIXIN] GET_USER_CARD_POSITION_SQL is not a valid query string template.')
            raise RepositoryError('Query template for card position is not configured correctly.')
        query = GET_USER_CARD_POSITION_SQL.format(order_clause=order_clause, table_name=self.table_name, where_clause=where_clause, card_id_placeholder_index=f'${card_id_placeholder_index}')
        result = await self._fetchrow(query, final_params, connection=connection)
        if result and result.get('position') is not None:
            return {'position': result['position'], 'total': result.get('total', 0), 'custom_sort_index': result.get('custom_sort_index')}
        logger.info('[GACHA_SORT_MIXIN] Card ID %s for user %s not found or position is null with current filters/sort.', card_id, user_id)
        raise CardNotFoundError(f'Card {card_id} not found in the filtered and sorted list for user {user_id}.')