"""
數據庫工具模塊
提供共享的數據庫初始化和工具函數
"""

import logging
import os
from typing import Any, Callable, Dict, List, Optional

import psycopg2

logger = logging.getLogger(__name__)


def init_postgres_database(
    db_name: str,
    setup_tables_func: Callable,
    system_name: str = "系統",
    db_config: Optional[Dict[str, Any]] = None,
    force_refresh_views: bool = True,
    force_setup: bool = False,
) -> bool:
    """通用的PostgreSQL數據庫初始化函數

    參數:
        db_name: 數據庫名稱
        setup_tables_func: 初始化表結構的函數
        system_name: 系統名稱（用於日誌顯示）
        db_config: 可選的數據庫配置
        force_refresh_views: 強制刷新所有物化視圖
        force_setup: 即使數據庫已存在也執行setup_tables_func

    返回:
        bool: 是否成功初始化
    """
    # 設置環境變量
    env_var_name = f"{system_name.upper()}_DATABASE"
    os.environ[env_var_name] = os.environ.get(env_var_name, db_name)

    # 獲取數據庫配置
    if db_config is None:
        db_config = {
            "host": "127.0.0.1",
            "port": int(os.environ.get("PG_PORT", "5432")),
            "user": os.environ.get("PG_USER", "postgres"),
            "password": os.environ.get("PG_PASSWORD", "postgres"),
        }

    # 檢查並創建數據庫
    try:
        conn = psycopg2.connect(
            host=db_config["host"],
            port=db_config["port"],
            database="postgres",
            user=db_config["user"],
            password=db_config["password"],
        )
        conn.autocommit = True
        cursor = conn.cursor()

        # 檢查數據庫是否存在
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (db_name,))
        exists = cursor.fetchone() is not None

        if not exists:
            print(f"【{system_name}】創建新數據庫: {db_name}")
            cursor.execute(f"CREATE DATABASE {db_name}")
        else:
            print(f"【{system_name}】數據庫已存在: {db_name}")

        cursor.close()
        conn.close()
    except Exception as e:
        logger.error(f"{system_name} 檢查/創建數據庫失敗: {str(e)}")
        print(f"【{system_name}】檢查/創建數據庫時出錯: {str(e)}")
        return False

    # 初始化表結構（對於新建的數據庫或強制更新）
    if not exists or force_setup:
        try:
            conn = psycopg2.connect(
                host=db_config["host"],
                port=db_config["port"],
                database=db_name,
                user=db_config["user"],
                password=db_config["password"],
            )
            setup_tables_func(conn)
            conn.close()
            print(f"【{system_name}】數據庫表結構初始化成功: {db_name}")
        except Exception as e:
            logger.error(f"{system_name} 初始化表結構失敗: {str(e)}")
            print(f"【{system_name}】初始化表結構時出錯: {str(e)}")
            return False
    else:
        print(f"【{system_name}】使用現有數據庫結構，跳過表初始化: {db_name}")

    logger.info(f"{system_name} 數據庫驗證成功: {db_name}")
    return True


def setup_postgres_extensions(conn, extensions: Optional[List[str]] = None) -> bool:
    """安裝 PostgreSQL 必要的擴展

    參數:
        conn: 已開啟的數據庫連接
        extensions: 要安裝的擴展列表，默認包含全文搜索需要的擴展

    返回:
        bool: 安裝是否成功
    """
    if extensions is None:
        extensions = ["pg_trgm", "btree_gin", "btree_gist"]

    try:
        cursor = conn.cursor()
        for extension in extensions:
            cursor.execute(f"CREATE EXTENSION IF NOT EXISTS {extension}")

        conn.commit()
        logger.info(f"成功安裝 PostgreSQL 擴展: {', '.join(extensions)}")
        return True
    except Exception as e:
        conn.rollback()
        logger.error(f"安裝 PostgreSQL 擴展時出錯: {str(e)}")
        return False


def create_fulltext_search_index(
    conn, table: str, column: str, language: str = "chinese"
) -> bool:
    """為指定表的欄位創建全文搜索索引

    參數:
        conn: 已開啟的數據庫連接
        table: 表名
        column: 要建立全文索引的欄位名
        language: 使用的語言，默認為chinese

    返回:
        bool: 操作是否成功
    """
    try:
        cursor = conn.cursor()
        vector_column = f"{column}_vector"

        # 先確保安裝了必要的擴展
        setup_postgres_extensions(conn)

        # 檢查是否存在向量列
        cursor.execute(
            """
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = %s AND column_name = %s
        """,
            (table, vector_column),
        )

        vector_exists = cursor.fetchone() is not None

        if not vector_exists:
            # 添加tsvector列
            cursor.execute(
                f"""
            ALTER TABLE {table} 
            ADD COLUMN {vector_column} tsvector 
            GENERATED ALWAYS AS (to_tsvector('{language}', {column})) STORED
            """
            )

        # 創建GIN索引
        index_name = f"idx_{table}_{column}_fts"
        cursor.execute(
            f"""
        CREATE INDEX IF NOT EXISTS {index_name}
        ON {table} USING GIN ({vector_column})
        """
        )

        conn.commit()
        logger.info(f"成功為表 {table} 的 {column} 列創建全文搜索索引")
        return True
    except Exception as e:
        conn.rollback()
        logger.error(f"創建全文搜索索引時出錯: {str(e)}")
        return False

