"""
Profile Repository - 處理用戶檔案相關的資料庫操作
使用純異常模式進行錯誤處理
"""
import asyncpg
from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal

from database.base_repository import BaseRepository
from gacha.models.profile_models import UserProfile, ProfileCardInfo
from gacha.exceptions import RecordNotFoundError, DatabaseOperationError, UserNotFoundError
from utils.logger import logger


class ProfileRepository(BaseRepository):
    """用戶檔案存儲庫"""

    def __init__(self, pool: asyncpg.Pool):
        super().__init__(pool)
        self.table_name = 'user_profiles'

    async def get_user_profile(self, user_id: int, connection: Optional[asyncpg.Connection] = None) -> Optional[UserProfile]:
        """獲取用戶檔案"""
        query = f"""
            SELECT * FROM {self.table_name} WHERE user_id = $1
        """
        try:
            result = await self._fetchrow(query, (user_id,), connection=connection)
            
            if not result:
                return None
                
            return UserProfile(
                user_id=result['user_id'],
                showcased_card_collection_id=result.get('showcased_card_collection_id'),
                sub_card_1_collection_id=result.get('sub_card_1_collection_id'),
                sub_card_2_collection_id=result.get('sub_card_2_collection_id'),
                sub_card_3_collection_id=result.get('sub_card_3_collection_id'),
                sub_card_4_collection_id=result.get('sub_card_4_collection_id'),
                background_image_url=result.get('background_image_url'),
                like_count=result.get('like_count', 0),
                created_at=result.get('created_at'),
                updated_at=result.get('updated_at'),
                user_status=result.get('user_status')
            )
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"獲取用戶檔案失敗: user_id={user_id}, error={e}") from e

    async def create_user_profile(self, user_id: int, connection: Optional[asyncpg.Connection] = None) -> UserProfile:
        """建立新用戶檔案"""
        query = f"""
            INSERT INTO {self.table_name} (user_id, like_count, created_at, updated_at)
            VALUES ($1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING *
        """
        try:
            result = await self._fetchrow(query, (user_id,), connection=connection)
            
            if not result:
                raise DatabaseOperationError(f"建立用戶檔案失敗: user_id={user_id}")
                
            return UserProfile(
                user_id=result['user_id'],
                showcased_card_collection_id=result.get('showcased_card_collection_id'),
                sub_card_1_collection_id=result.get('sub_card_1_collection_id'),
                sub_card_2_collection_id=result.get('sub_card_2_collection_id'),
                sub_card_3_collection_id=result.get('sub_card_3_collection_id'),
                sub_card_4_collection_id=result.get('sub_card_4_collection_id'),
                background_image_url=result.get('background_image_url'),
                like_count=result.get('like_count', 0),
                created_at=result.get('created_at'),
                updated_at=result.get('updated_at')
            )
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"建立用戶檔案失敗: user_id={user_id}, error={e}") from e

    async def get_or_create_user_profile(self, user_id: int, connection: Optional[asyncpg.Connection] = None) -> UserProfile:
        """獲取或建立用戶檔案"""
        profile = await self.get_user_profile(user_id, connection)
        if profile is None:
            profile = await self.create_user_profile(user_id, connection)
        return profile

    async def update_showcased_card(self, user_id: int, collection_id: Optional[int], connection: Optional[asyncpg.Connection] = None) -> None:
        """更新主展示卡片"""
        query = f"""
            UPDATE {self.table_name} 
            SET showcased_card_collection_id = $1, 
                updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $2
        """
        try:
            status = await self._execute(query, (collection_id, user_id), connection=connection)
            if not status or not status.startswith("UPDATE 1"):
                raise UserNotFoundError(f"更新主展示卡片失敗，用戶可能不存在: user_id={user_id}", user_id=user_id)
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"更新主展示卡片失敗: user_id={user_id}, error={e}") from e

    async def update_sub_card(self, user_id: int, slot_index: int, collection_id: Optional[int], connection: Optional[asyncpg.Connection] = None) -> None:
        """更新副展示卡片"""
        if slot_index not in [1, 2, 3, 4]:
            raise ValueError(f"無效的槽位索引: {slot_index}")
            
        column_name = f"sub_card_{slot_index}_collection_id"
        query = f"""
            UPDATE {self.table_name} 
            SET {column_name} = $1, 
                updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $2
        """
        try:
            status = await self._execute(query, (collection_id, user_id), connection=connection)
            if not status or not status.startswith("UPDATE 1"):
                raise UserNotFoundError(f"更新副展示卡片失敗，用戶可能不存在: user_id={user_id}, slot={slot_index}", user_id=user_id)
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"更新副展示卡片失敗: user_id={user_id}, slot={slot_index}, error={e}") from e

    async def clear_sub_card_slot(self, user_id: int, slot_index: int, connection: Optional[asyncpg.Connection] = None) -> None:
        """清除指定位置的副展示卡片"""
        if slot_index not in [1, 2, 3, 4]:
            raise ValueError(f"無效的槽位索引: {slot_index}")
            
        column_name = f"sub_card_{slot_index}_collection_id"
        query = f"""
            UPDATE {self.table_name} 
            SET {column_name} = NULL, 
                updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $1
        """
        try:
            status = await self._execute(query, (user_id,), connection=connection)
            if not status or not status.startswith("UPDATE 1"):
                raise UserNotFoundError(f"清除副展示卡片失敗，用戶可能不存在: user_id={user_id}, slot={slot_index}", user_id=user_id)
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"清除副展示卡片失敗: user_id={user_id}, slot={slot_index}, error={e}") from e

    async def update_background_image(self, user_id: int, background_url: Optional[str], connection: Optional[asyncpg.Connection] = None) -> None:
        """更新背景圖片"""
        query = f"""
            UPDATE {self.table_name} 
            SET background_image_url = $1, 
                updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $2
        """
        try:
            status = await self._execute(query, (background_url, user_id), connection=connection)
            if not status or not status.startswith("UPDATE 1"):
                raise UserNotFoundError(f"更新背景圖片失敗，用戶可能不存在: user_id={user_id}", user_id=user_id)
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"更新背景圖片失敗: user_id={user_id}, error={e}") from e

    async def increment_like_count(self, user_id: int, connection: Optional[asyncpg.Connection] = None) -> None:
        """增加按讚數 (不再清除圖片快取)"""
        query = f"""
            UPDATE {self.table_name} 
            SET like_count = like_count + 1,
                updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $1
        """
        try:
            status = await self._execute(query, (user_id,), connection=connection)
            if not status or not status.startswith("UPDATE 1"):
                raise UserNotFoundError(f"增加按讚數失敗，用戶可能不存在: user_id={user_id}", user_id=user_id)
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"增加按讚數失敗: user_id={user_id}, error={e}") from e

    async def get_card_info_by_collection_id(self, collection_id: int, connection: Optional[asyncpg.Connection] = None) -> Optional[ProfileCardInfo]:
        """根據收藏ID獲取卡片資訊"""
        query = """
            SELECT 
                uc.id as collection_id,
                uc.card_id,
                mc.name,
                mc.series,
                mc.image_url,
                mc.rarity,
                uc.star_level,
                uc.quantity
            FROM gacha_user_collections uc
            JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
            WHERE uc.id = $1 AND mc.is_active = TRUE
        """
        try:
            result = await self._fetchrow(query, (collection_id,), connection=connection)
            
            if not result:
                return None
                
            return ProfileCardInfo(
                collection_id=result['collection_id'],
                card_id=result['card_id'],
                name=result['name'],
                series=result['series'],
                image_url=result['image_url'],
                rarity=result['rarity'],
                star_level=result['star_level'],
                quantity=result['quantity']
            )
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"獲取卡片資訊失敗: collection_id={collection_id}, error={e}") from e

    async def get_user_statistics(self, user_id: int, connection: Optional[asyncpg.Connection] = None) -> Dict[str, Any]:
        """獲取用戶統計資料"""
        # 計算圖鑑完成度
        completion_query = """
            WITH total_cards AS (
                SELECT COUNT(DISTINCT card_id) as total
                FROM gacha_master_cards
                WHERE is_active = TRUE
            ),
            owned_cards AS (
                SELECT COUNT(DISTINCT card_id) as owned
                FROM gacha_user_collections
                WHERE user_id = $1
            )
            SELECT 
                COALESCE(owned_cards.owned, 0) as owned,
                total_cards.total,
                CASE 
                    WHEN total_cards.total > 0 THEN 
                        ROUND((COALESCE(owned_cards.owned, 0)::numeric / total_cards.total::numeric) * 100, 2)
                    ELSE 0
                END as completion_rate
            FROM total_cards, owned_cards
        """
        
        # 計算總持有數和稀有度分佈
        stats_query = """
            SELECT 
                COALESCE(SUM(uc.quantity), 0) as total_owned,
                COALESCE(SUM(CASE WHEN mc.rarity = 1 THEN uc.quantity ELSE 0 END), 0) as rarity_1_count,
                COALESCE(SUM(CASE WHEN mc.rarity = 2 THEN uc.quantity ELSE 0 END), 0) as rarity_2_count,
                COALESCE(SUM(CASE WHEN mc.rarity = 3 THEN uc.quantity ELSE 0 END), 0) as rarity_3_count,
                COALESCE(SUM(CASE WHEN mc.rarity = 4 THEN uc.quantity ELSE 0 END), 0) as rarity_4_count,
                COALESCE(SUM(CASE WHEN mc.rarity = 5 THEN uc.quantity ELSE 0 END), 0) as rarity_5_count,
                COALESCE(SUM(CASE WHEN mc.rarity = 6 THEN uc.quantity ELSE 0 END), 0) as rarity_6_count,
                COALESCE(SUM(CASE WHEN mc.rarity = 7 THEN uc.quantity ELSE 0 END), 0) as rarity_7_count
            FROM gacha_user_collections uc
            JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
            WHERE uc.user_id = $1 AND mc.is_active = TRUE
        """
        
        try:
            completion_result = await self._fetchrow(completion_query, (user_id,), connection=connection)
            stats_result = await self._fetchrow(stats_query, (user_id,), connection=connection)
            
            return {
                'completion_rate': float(completion_result['completion_rate'] or 0),
                'total_owned': int(stats_result['total_owned'] or 0),
                'rarity_counts': {
                    1: int(stats_result['rarity_1_count'] or 0),
                    2: int(stats_result['rarity_2_count'] or 0),
                    3: int(stats_result['rarity_3_count'] or 0),
                    4: int(stats_result['rarity_4_count'] or 0),
                    5: int(stats_result['rarity_5_count'] or 0),
                    6: int(stats_result['rarity_6_count'] or 0),
                    7: int(stats_result['rarity_7_count'] or 0),
                }
            }
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"獲取用戶統計資料失敗: user_id={user_id}, error={e}") from e

    async def update_user_status(self, user_id: int, status: Optional[str], connection: Optional[asyncpg.Connection] = None) -> None:
        """更新用戶個性簽名"""
        query = f"""
            UPDATE {self.table_name} 
            SET user_status = $1, 
                updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $2
        """
        try:
            status_result = await self._execute(query, (status, user_id), connection=connection)
            if not status_result or not status_result.startswith("UPDATE 1"):
                raise UserNotFoundError(f"更新用戶簽名失敗，用戶可能不存在: user_id={user_id}", user_id=user_id)
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"更新用戶簽名失敗: user_id={user_id}, error={e}") from e 