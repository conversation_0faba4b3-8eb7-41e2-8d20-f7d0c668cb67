"""
Gacha系統系列收集視圖
提供系列收集情況的顯示和交互功能
"""
from datetime import datetime
from typing import Any, Dict, Optional
import discord
from gacha.models.models import Card, SeriesCollection
from gacha.services.core.collection_service import CollectionService
from gacha.views.embeds.collection.series_embed_builder import SeriesEmbedBuilder
from .base_pagination import BasePaginationJumpModal, BasePaginationView
from gacha.views.utils import get_completion_indicator
from .gacha_services import GachaServices
from utils.logger import logger

class SeriesListView(BasePaginationView):
    """系列列表分頁視圖"""

    def __init__(self, user: discord.User, series_data: Dict[str, Any], services: GachaServices, pool_type: Optional[str]=None):
        """初始化系列列表分頁視圖

        參數:
            user: Discord用戶對象
            series_data: 系列分頁數據
            services: 統一的服務容器
            pool_type: 卡池類型（可選）
        """
        super().__init__(user=user, current_page=series_data['current_page'], total_pages=series_data['total_pages'], timeout=120)
        self.series_list = series_data['series_list']
        self.total_series = series_data['total_series']
        self.services = services
        self.user_id = user.id
        self.pool_type = pool_type or series_data.get('pool_type')
        self.overall_completion_percentage = 0
        self.total_collected = 0
        self.total_all_cards = 0
        self.overall_indicator = None
        self.overall_color = None
        self.series_collections = {}
        self.overall_stats = {}

    @property
    def collection_service(self) -> CollectionService:
        """統一的收藏服務訪問接口

        遵循系統的服務容器模式，通過 services 訪問服務
        """
        return self.services.collection

    @classmethod
    async def create_view(cls, user: discord.User, series_data: Dict[str, Any], services: GachaServices, pool_type: Optional[str]=None) -> 'SeriesListView':
        """創建並異步初始化系列列表視圖的工廠方法

        參數:
            user: Discord用戶對象
            series_data: 系列分頁數據
            services: 統一的服務容器
            pool_type: 卡池類型（可選）

        返回:
            初始化後的SeriesListView實例
        """
        view = cls(user, series_data, services, pool_type)
        if view.pool_type:
            pool_stats = await view.collection_service.get_pool_specific_collection_stats(view.user_id, view.pool_type)
            view.overall_completion_percentage = pool_stats.get('completion_rate', 0)
            view.total_collected = pool_stats.get('total_collected', 0)
            view.total_all_cards = pool_stats.get('total_cards', 0)
        else:
            view.overall_stats = await view.collection_service.get_user_overall_collection_stats(view.user_id)
            view.overall_completion_percentage = view.overall_stats.get('completion_rate', 0)
            view.total_collected = view.overall_stats.get('total_collected', 0)
            view.total_all_cards = view.overall_stats.get('total_cards', 0)
        view.overall_indicator, view.overall_color = get_completion_indicator(view.overall_completion_percentage)
        view.series_collections = await view.collection_service.get_user_all_series_collections(view.user_id, view.series_list, view.pool_type)
        return view

    def build_embed(self) -> discord.Embed:
        """使用 SeriesEmbedBuilder 構建當前頁的Embed

        返回:
            格式化後的Discord Embed對象
        """
        builder = SeriesEmbedBuilder(user=self.user)
        return builder.build_embed(series_list=self.series_list, series_collections=self.series_collections, overall_completion_percentage=self.overall_completion_percentage, total_collected=self.total_collected, total_all_cards=self.total_all_cards, current_page=self.current_page, total_pages=self.total_pages, total_series=self.total_series, pool_type=self.pool_type)

    def get_current_page_embed(self) -> discord.Embed:
        """獲取當前頁的Embed（實現父類的抽象方法）"""
        return self.build_embed()

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """更新頁面內容

        參數:
            page: 目標頁碼
            interaction: Discord交互對象
        """
        series_data = await self.collection_service.get_all_series_paginated(page, self.pool_type)
        self.series_list = series_data['series_list']
        self.current_page = series_data['current_page']
        self.total_pages = series_data['total_pages']
        self.total_series = series_data['total_series']
        self.series_collections = await self.collection_service.get_user_all_series_collections(self.user_id, self.series_list, self.pool_type)
        self.clear_items()
        self._add_pagination_buttons()
        self._refresh_button_states()
        embed = self.build_embed()
        try:
            if self.message:
                await self.message.edit(embed=embed, view=self)
                return
            await interaction.response.edit_message(embed=embed, view=self)
        except Exception:
            try:
                await interaction.followup.send(embed=embed, view=self)
            except Exception as ex:
                logger.error('更新系列列表時出錯: %s', str(ex))

    def _create_jump_modal(self):
        """創建跳轉模態框"""
        return BasePaginationJumpModal(self, title='跳轉到指定系列頁')