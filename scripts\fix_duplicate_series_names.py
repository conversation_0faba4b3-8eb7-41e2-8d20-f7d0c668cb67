import psycopg2
import os
import re
from dotenv import load_dotenv

# 載入環境變數
dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
load_dotenv(dotenv_path=dotenv_path)

# 資料庫連接
DB_HOST = os.getenv("PG_HOST", "127.0.0.1")
DB_PORT = os.getenv("PG_PORT", "5432")
DB_NAME = os.getenv("GACHA_DB_NAME")
DB_USER = os.getenv("PG_USER")
DB_PASSWORD = os.getenv("PG_PASSWORD")

def get_connection():
    """建立並返回資料庫連接"""
    if not all([DB_NAME, DB_USER, DB_PASSWORD]):
        print("錯誤：資料庫連接資訊未在 .env 檔案中完整設定。")
        return None
    
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        return conn
    except psycopg2.Error as e:
        print(f"資料庫連接失敗: {e}")
        return None

def fix_duplicate_series():
    """檢查並修正卡片名稱中重複的系列名"""
    print("開始檢查卡片名稱中的重複系列名...")
    
    # 連接資料庫
    conn = get_connection()
    if not conn:
        return
    
    cursor = conn.cursor()
    
    try:
        # 查找所有卡片
        cursor.execute("SELECT card_id, name, series FROM public.gacha_master_cards")
        cards = cursor.fetchall()
        
        # 修正計數
        detected_count = 0
        fixed_count = 0
        
        # 開始處理
        for card_id, name, series in cards:
            if not name or not series:
                continue
            
            # 如果卡名末尾包含括號內的系列名
            pattern = r"(.*)\(" + re.escape(series) + r"\)$"
            match = re.match(pattern, name)
            
            if match:
                detected_count += 1
                
                # 獲取基本卡名（不含系列名部分）
                card_base_name = match.group(1).strip()
                
                # 如果基本卡名為空，可能需要特殊處理
                if not card_base_name:
                    print(f"警告：卡片ID {card_id} 的基本名稱為空，跳過修改")
                    continue
                
                print(f"檢測到重複系列名：卡片ID {card_id}")
                print(f"  - 原始卡名：{name}")
                print(f"  - 系列名：{series}")
                print(f"  - 基本卡名：{card_base_name}")
                
                # 這裡您可以選擇以下兩種處理方式之一：
                
                # 選項1：從卡名中移除系列名（取消下方註釋啟用）
                fixed_name = card_base_name
                cursor.execute(
                    "UPDATE public.gacha_master_cards SET name = %s WHERE card_id = %s",
                    (fixed_name, card_id)
                )
                fixed_count += 1
                print(f"  - 修正後卡名：{fixed_name}")
                
                # 選項2：維持原樣，不做修改（若選此項，請註釋上方選項1的代碼）
                # print(f"  - 未做修改，保持原樣")
        
        # 提交更改
        conn.commit()
        print(f"\n完成處理：")
        print(f"  - 檢測到 {detected_count} 張卡片名稱包含重複的系列名")
        print(f"  - 已修正 {fixed_count} 張卡片")
        
    except psycopg2.Error as e:
        conn.rollback()
        print(f"處理過程中發生錯誤: {e}")
    finally:
        # 關閉連接
        cursor.close()
        conn.close()

if __name__ == "__main__":
    fix_duplicate_series() 