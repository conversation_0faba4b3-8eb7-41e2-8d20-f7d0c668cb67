import asyncpg
from decimal import Decimal
from typing import List, Any, Dict, Tuple, Optional
from utils.logger import logger

class CardMarketStatsRepository:

    async def get_max_stat_value(self, conn: asyncpg.Connection, column_name: str, table_name: str='gacha_card_market_stats') -> Optional[Decimal]:
        """獲取指定表和列的最大值。"""
        query = f'SELECT MAX({column_name}) FROM {table_name}'
        db_max_value = await conn.fetchval(query)
        if db_max_value is not None:
            return Decimal(str(db_max_value))
        return None

    async def get_all_card_stats_for_supply_demand(self, conn: asyncpg.Connection) -> List[asyncpg.Record]:
        """獲取所有卡牌的市場統計數據以及相關的主卡牌數據，用於計算供需調節因子。"""
        query = '\n            SELECT\n                cs.card_id,\n                cs.total_owned_quantity,\n                cs.unique_owner_count,\n                cs.wishlist_count,\n                cs.favorite_count,\n                cs.supply_demand_modifier AS old_modifier,\n                mc.price_config\n            FROM gacha_card_market_stats cs\n            LEFT JOIN gacha_master_cards mc ON cs.card_id = mc.card_id;\n        '
        records = await conn.fetch(query)
        return records

    async def batch_update_supply_demand_modifiers(self, conn: asyncpg.Connection, updates: List[Tuple[Decimal, Decimal, int]]) -> None:
        """批量更新卡牌的供需調節因子。"""
        if not updates:
            return
        update_query = '\n            UPDATE gacha_card_market_stats\n            SET supply_demand_modifier = $1,\n                previous_sd_modifier = $2,\n                last_sd_calculated_at = CURRENT_TIMESTAMP\n            WHERE card_id = $3\n        '
        try:
            await conn.executemany(update_query, updates)
            logger.info('已成功為 %s 張卡片批量更新供需調節因子。', len(updates))
        except Exception as e:
            logger.error('批量更新供需調節因子時出錯: %s', e, exc_info=True)
            raise