# -*- coding: utf-8 -*-
"""
Gacha 系統的常量中心 - 清理後版本
僅包含核心 Enum 定義。
其他 UI 相關靜態數據（表情、顏色、名稱映射等）已遷移至 settings (來自 YAML 配置)。
"""
from enum import Enum, IntEnum  # 同時導入 Enum 和 IntEnum
import discord  # 保留以防某些 Enum 值是 discord 對象 (雖然在此版本中沒有)


class RarityLevel(IntEnum):
    """統一的卡片稀有度等級"""

    COMMON = 1  # 對應 "C"
    RARE = 2  # 對應 "R"
    SUPER_RARE = 3  # 對應 "SR"
    SSR = 4  # 對應 "SSR"
    ULTRA_RARE = 5  # 對應 "UR"
    LEGENDARY_RARE = 6  # 對應 "LR"
    EXCLUSIVE = 7  # 對應 "EX"

    # display_code 和 display_name 屬性已移除。
    # 獲取顯示名稱/代碼的邏輯應在需要時使用 settings 對象。


class MarketStatsEventType(str, Enum):
    """市場統計相關的事件類型"""

    # Gacha 事件
    TOTAL_OWNED_UPDATE = "total_owned_update"  # 卡片總擁有量更新
    UNIQUE_OWNER_UPDATE = "unique_owner_update"  # 卡片獨立擁有者數量更新

    # Favorite 事件
    FAVORITE_COUNT_UPDATE = "favorite_count_update"  # 卡片收藏數量更新

    # Wishlist 事件
    WISHLIST_COUNT_UPDATE = "wishlist_count_update"  # 卡片許願數量更新


class NewsTypeFilterEnum(Enum):
    """新聞類型過濾"""

    ALL = "all_types"
    PTT_ANALYSIS = "PTT_ANALYSIS"
    CITIZEN_STORY = "CITIZEN_STORY"
    INSIDER_TIP = "INSIDER_TIP"
    ANALYST_REPORT = "ANALYST_REPORT"
    CORPORATE_ANNOUNCEMENT = "CORPORATE_ANNOUNCEMENT"
    REGULATORY_CHANGE = "REGULATORY_CHANGE"
    MARKET_RUMOR = "MARKET_RUMOR"
    TECHNICAL_SIGNAL = "TECHNICAL_SIGNAL"
    GENERAL_MARKET_NEWS = "GENERAL_MARKET_NEWS"
    STOCK_DELISTED = "STOCK_DELISTED"
    STOCK_ST_WARNING = "STOCK_ST_WARNING"
    STOCK_ST_RECOVERY = "STOCK_ST_RECOVERY"
    STOCK_NEW_LISTING = "STOCK_NEW_LISTING"


class NewsFilterType(Enum):  # Scope Filter
    """新聞範圍過濾"""

    ALL = "all"  # All news regardless of asset linkage
    PORTFOLIO = "portfolio"  # News related to user's held assets
    GLOBAL = "global_news"  # News not linked to any specific asset


class CharacterArchetypeFilterEnum(Enum):  # Restoring Enum
    """新聞來源角色原型過濾"""

    ALL = "all_archetypes"
    PTT_USER = "PTT_USER"
    ORDINARY_CITIZEN = "ORDINARY_CITIZEN"
    MYSTERIOUS_INFORMANT = "MYSTERIOUS_INFORMANT"
    MARKET_ANALYST = "MARKET_ANALYST"
    CORPORATE_SPOKESPERSON = "CORPORATE_SPOKESPERSON"
    OFFICIAL_PRESS_RELEASE = "OFFICIAL_PRESS_RELEASE"
    REGULATORY_BODY_BULLETIN = "REGULATORY_BODY_BULLETIN"
    INDUSTRY_ANALYST_INTERPRETATION = "INDUSTRY_ANALYST_INTERPRETATION"
    GOSSIP_MONGER = "GOSSIP_MONGER"
    TA_BOT = "TA_BOT"
    AI_NEWSCASTER_STANDARD = "AI_NEWSCASTER_STANDARD"
    # ARCHIVED_SOURCE was in CHARACTER_ARCHETYPE_READABLE_NAMES but not in
    # Enum, decide if needed
