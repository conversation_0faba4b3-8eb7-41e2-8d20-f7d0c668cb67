"""
Gacha系統視圖通用工具函數
"""
import discord
from typing import Callable, Optional, Dict, Any, Tuple
from discord import app_commands
from typing import List
from gacha.app_config import config_service
from gacha.utils import interaction_utils
import logging
from gacha.services.core.collection_service import CollectionService
from ..models.models import Card
from ..constants import RarityLevel
from utils.logger import logger

def format_oil(amount: int, label: str='餘額') -> str:
    """格式化油幣數量顯示

    參數:
        amount: 油幣數量
        label: 顯示標籤 (例如 "餘額", "價格", "贏得", "輸掉")

    返回:
        str: 格式化後的油幣字符串
    """
    sign = ''
    if label in ['贏得', '淨收益']:
        sign = '+'
    elif label in ['輸掉', '淨損失']:
        sign = '-'
        amount = abs(amount)
    return f'{sign}{label}:`{amount}` {config_service.get_oil_emoji()}'

async def common_pool_type_autocomplete(interaction: discord.Interaction, current: str, show_individual_pools_only: bool=False) -> List[app_commands.Choice[str]]:
    """
    為卡池類型提供通用的自動完成選項。
    從 config_service 獲取 pool_configurations。
    假設 "main" 卡池已在 pool_configurations 中定義。

    Args:
        interaction: discord.Interaction 物件 (主要用於上下文，設定從 config_service 獲取)。
        current: 用戶當前輸入的字串。
        show_individual_pools_only: 控制卡池列表的篩選邏輯。
            - True (用於 /aw, /mw, /sw 等指令):
                - 包含所有在 pool_configurations 中定義的獨立卡池 (包括 "main")。
                - 排除 "混合池" (key "all")。
            - False (用於抽卡指令如 /w 等):
                - 包含所有在 pool_configurations 中定義的卡池，但特別排除 "主卡池" (key "main")。
                - 包含 "混合池" (key "all")。

    Returns:
        一個 app_commands.Choice 物件列表。
    """
    choices = []
    try:
        pool_configs = config_service.get_config('gacha_core_settings.pool_configurations')
        if pool_configs is None:
            return []
    except Exception as e:
        return []
    if not isinstance(pool_configs, dict):
        return []
    if show_individual_pools_only:
        for key, config_detail in pool_configs.items():
            if key == 'all':
                continue
            display_name = config_detail.name if hasattr(config_detail, 'name') else key
            choices.append(app_commands.Choice(name=display_name, value=key))
    else:
        for key, config_detail in pool_configs.items():
            if key == 'main':
                continue
            display_name = config_detail.name if hasattr(config_detail, 'name') else key
            choices.append(app_commands.Choice(name=display_name, value=key))
    filtered_choices = []
    if current:
        for choice in choices:
            if current.lower() in choice.name.lower():
                filtered_choices.append(choice)
    else:
        filtered_choices = choices
    return filtered_choices[:25]

async def common_series_autocomplete(interaction: discord.Interaction, current: str, collection_service: CollectionService) -> List[app_commands.Choice[str]]:
    """通用系列名稱自動完成函數 (基於 CollectionService)

    參數:
        interaction: 交互對象
        current: 當前輸入
        collection_service: CollectionService 實例

    返回:
        List[app_commands.Choice[str]]: 選項列表
    """
    try:
        all_series = await collection_service.get_all_series()
        filtered_series = [series for series in all_series if current.lower() in series.lower()][:25]
        return [app_commands.Choice(name=series, value=series) for series in filtered_series]
    except Exception:
        return []

async def common_rarity_autocomplete(interaction: discord.Interaction, current: str) -> List[app_commands.Choice[int]]:
    """通用稀有度自動補全函數 (使用 RarityLevel Enum)

    參數:
        interaction: 交互對象
        current: 當前輸入的文字

    返回:
        List[app_commands.Choice[int]]: 選項列表, value 為稀有度整數值 (int)
    """
    filtered_rarities = []
    for rarity_level in RarityLevel:
        display_code = config_service.get_rarity_display_codes().get(rarity_level.value)
        display_name = config_service.get_user_friendly_rarity_display_names().get(display_code, f"{rarity_level.name.replace('_', ' ').title()} ({display_code or rarity_level.name[:1]})")
        if display_code and current.upper() in display_code.upper() or (display_name and current.lower() in display_name.lower()):
            filtered_rarities.append(app_commands.Choice(name=display_name, value=rarity_level.value))
    return filtered_rarities[:25]

def get_rarity_display_code(rarity_level: Optional[RarityLevel], pool_type: Optional[str]=None) -> str:
    """
    獲取稀有度對應的顯示代碼 (通常是 emoji)。

    此函數會根據傳入的 RarityLevel Enum 和可選的 pool_type，
    從配置中查找對應的顯示代碼。
    優先級：特定卡池配置 (`config_service.get_pool_specific_rarity_emojis()`)
             -> 默認配置 (`config_service.get_default_rarity_emojis()`)。

    參數:
        rarity_level: RarityLevel Enum 的成員，或 None。
        pool_type: 可選的卡池類型字串。

    返回:
        str: 對應的顯示代碼 (emoji)。如果找不到或輸入為 None，則返回空字串。
    """
    if rarity_level is None:
        return ''
    rarity_value = rarity_level.value
    if pool_type:
        pool_specific_emojis_map = config_service.get_pool_specific_rarity_emojis().get(pool_type)
        if isinstance(pool_specific_emojis_map, dict):
            emoji = pool_specific_emojis_map.get(rarity_value)
            if emoji:
                return emoji
    default_emoji = config_service.get_default_rarity_emojis().get(rarity_value)
    if default_emoji:
        return default_emoji
    return ''

def get_encyclopedia_rarity_emoji(rarity_level: Optional[RarityLevel], pool_type: Optional[str]=None) -> str:
    """獲取全圖鑑專用的稀有度表情符號 (使用 RarityLevel Enum，考慮卡池)"""
    if rarity_level is None:
        return '❓'
    rarity_value = rarity_level.value
    if pool_type and pool_type in config_service.get_pool_specific_encyclopedia_rarity_emojis():
        pool_specific_emojis = config_service.get_pool_specific_encyclopedia_rarity_emojis()[pool_type]
        emoji = pool_specific_emojis.get(rarity_value)
        if emoji and (pool_type == 'main' or 'ID' not in emoji):
            return emoji
    default_emoji = config_service.get_default_encyclopedia_rarity_emojis().get(rarity_value)
    if default_emoji:
        return default_emoji
    return get_rarity_display_code(rarity_level, pool_type)

def get_rarity_color(rarity_level: Optional[RarityLevel], pool_type: Optional[str]=None) -> int:
    """根據稀有度返回對應的Discord Embed顏色值 (使用 RarityLevel Enum，考慮卡池)"""
    if rarity_level is None:
        return 8421504
    rarity_value = rarity_level.value
    actual_pool_key = pool_type if pool_type and pool_type in config_service.get_rarity_colors_int() else 'default'
    pool_colors = config_service.get_rarity_colors_int().get(actual_pool_key)
    if pool_colors:
        color = pool_colors.get(rarity_value)
        if color is not None:
            return color
    if actual_pool_key != 'default':
        default_pool_colors = config_service.get_rarity_colors_int().get('default')
        if default_pool_colors:
            color = default_pool_colors.get(rarity_value)
            if color is not None:
                return color
    return 8421504

def get_rarity_image(rarity_level: Optional[RarityLevel]) -> Optional[str]:
    """獲取對應稀有度的圖片URL (使用 RarityLevel Enum)"""
    if rarity_level is None:
        return None
    rarity_value = rarity_level.value
    all_pools_images = config_service.get_rarity_images_url().get('all_pools')
    if all_pools_images:
        return all_pools_images.get(rarity_value)
    return None

def get_user_friendly_rarity_name(rarity_level: Optional[RarityLevel]) -> str:
    """
    獲取用戶友好的稀有度顯示名稱。

    此函數會根據傳入的 RarityLevel Enum，從配置中查找對應的
    中文稀有度名稱。

    例如：
    - RarityLevel.COMMON (假設其 value 為 1, 對應的 display_code 為 "C")
      -> "普通 (C)"

    參數:
        rarity_level: RarityLevel Enum 的成員，或 None。

    返回:
        str: 對應的中文稀有度名稱。如果找不到或輸入為 None，則返回備用字串。
    """
    if rarity_level is None:
        return '未知稀有度'
    display_code = config_service.get_rarity_display_codes().get(rarity_level.value)
    if display_code:
        friendly_name = config_service.get_user_friendly_rarity_display_names().get(display_code)
        if friendly_name:
            return friendly_name
        return f"{rarity_level.name.replace('_', ' ').title()} ({display_code})"
    return f"{rarity_level.name.replace('_', ' ').title()} (代碼 {rarity_level.value})"

def get_star_emoji_string(star_level: int) -> str:
    """根據星級返回對應的表情符號字串

    參數:
        star_level: 星級數值

    返回:
        str: 表情符號字串
    """
    if star_level <= 0:
        return ''
    tier = min((star_level - 1) // config_service.get_config('gacha_core_settings.stars_per_tier') + 1, len(config_service.get_star_emojis()))
    stars_count = (star_level - 1) % config_service.get_config('gacha_core_settings.stars_per_tier') + 1
    emoji = config_service.get_star_emojis().get(tier, config_service.get_star_emojis().get(1, ''))
    return emoji * stars_count

def get_ui_emoji(name: str) -> str:
    """獲取通用的 UI 表情符號

    參數:
        name: 表情符號名稱 ('old_card', 'heart', 'new_card')

    返回:
        str: 表情符號字串，找不到時返回空字串
    """
    if name == 'old_card':
        return config_service.get_ui_button_emojis().old_card or ''
    elif name == 'heart':
        return config_service.get_ui_button_emojis().favorite or ''
    elif name == 'new_card':
        return config_service.get_ui_button_emojis().new_card or ''
    return ''

def get_pool_rarity_stats(cards_summary: List[Dict[str, Any]], unique_only: bool=False) -> Dict[str, Dict[int, int]]:
    """將卡片摘要按卡池類型和稀有度進行統計 (使用數字稀有度)

    參數:
        cards_summary: 卡片摘要列表，每個字典應包含 'pool_type', 'rarity'(int), 'count'(唯一卡片數), 'total_quantity'(總卡片數)
        unique_only: 是否僅計算不同卡片（使用 'count' 欄位）還是計算總張數（使用 'total_quantity' 欄位）

    返回:
        Dict[str, Dict[int, int]]: 按卡池類型和數字稀有度分組的統計結果
    """
    pool_rarity_counts: Dict[str, Dict[int, int]] = {}
    for item in cards_summary:
        pool_type = item.get('pool_type')
        rarity_val = item.get('rarity')
        if pool_type is None or rarity_val is None:
            logger.warning('[GACHA][VIEW_UTILS] Skipped item in get_pool_rarity_stats due to missing pool_type or rarity: %s', item)
            continue
        try:
            rarity_int = int(rarity_val)
        except (ValueError, TypeError):
            logger.warning('[GACHA][VIEW_UTILS] Skipped item in get_pool_rarity_stats due to invalid rarity value: %s in item %s', rarity_val, item)
            continue
        pool_data = pool_rarity_counts.setdefault(pool_type, {})
        if unique_only:
            value_to_store = item.get('count', 0)
        else:
            value_to_store = item.get('quantity_sold', item.get('total_quantity', 0))
        pool_data[rarity_int] = pool_data.get(rarity_int, 0) + value_to_store
    return pool_rarity_counts

def get_completion_indicator(completion_rate: float) -> Tuple[str, discord.Color]:
    """根據完成率獲取指示器圖標和顏色

    參數:
        completion_rate: 完成率百分比

    返回:
        包含指示器圖標和顏色的元組
    """
    if completion_rate == 100:
        indicator = config_service.get_completion_indicator_emojis()['100']
        color = discord.Color.gold()
    elif completion_rate >= 75:
        indicator = config_service.get_completion_indicator_emojis()['75+']
        color = discord.Color.green()
    elif completion_rate >= 50:
        indicator = config_service.get_completion_indicator_emojis()['50+']
        color = discord.Color.blue()
    elif completion_rate > 0:
        indicator = config_service.get_completion_indicator_emojis()['0+']
        color = discord.Color.light_gray()
    else:
        indicator = config_service.get_completion_indicator_emojis()['0']
        color = discord.Color.light_gray()
    return (indicator, color)