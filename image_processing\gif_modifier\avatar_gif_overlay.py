"""
GIF與頭像疊加模塊
將用戶頭像疊加到GIF圖像上，確保頭像被GIF正確疊加，並保留GIF透明背景。
"""
import os
import discord
import aiohttp
from PIL import Image, ImageSequence
import io
from .config import get_gif_config, list_available_gifs

async def fetch_avatar(user):
    """獲取用戶頭像"""
    avatar_url = user.display_avatar.url
    async with aiohttp.ClientSession() as session:
        async with session.get(str(avatar_url)) as resp:
            if resp.status == 200:
                avatar_data = await resp.read()
                return Image.open(io.BytesIO(avatar_data)).convert("RGBA")
    return None

def calculate_avatar_position(config, gif_width, gif_height, avatar_size, frame_index, num_frames):
    """計算頭像位置，包含動畫效果"""
    position = config["avatar_position"]
    offset_x = config["offset_x"]
    offset_y = config["offset_y"]
    
    # 基礎位置計算
    if position == "center_bottom":
        base_x = (gif_width - avatar_size[0]) // 2
        base_y = gif_height - avatar_size[1] + offset_y
    elif position == "left_bottom":
        base_x = offset_x  # 直接使用offset_x (從左側邊緣開始)
        base_y = gif_height - avatar_size[1] + offset_y
    elif position == "right_bottom":
        base_x = gif_width - avatar_size[0] - offset_x  # 從右側邊緣開始向左偏移
        base_y = gif_height - avatar_size[1] + offset_y
    elif position == "center":
        base_x = (gif_width - avatar_size[0]) // 2
        base_y = (gif_height - avatar_size[1]) // 2
    else:  # 默認為底部中央
        base_x = (gif_width - avatar_size[0]) // 2
        base_y = gif_height - avatar_size[1] + offset_y
    
    # 添加動畫效果
    animation = config["animation"]
    x_amp = animation["x_amplitude"]
    y_amp = animation["y_amplitude"]
    phase = animation["phase_shift"]
    frequency = animation.get("frequency", 1.0)  # 獲取頻率參數，默認為1.0
    
    # 防止除零錯誤
    safe_frames = max(1, num_frames - 1)
    
    # 計算動畫偏移 - 使用正弦和餘弦函數增強震動效果
    import math
    import random
    
    # 創建一個決定性的隨機源，使每次GIF生成時看起來一致
    # 使用幀索引作為種子使每幀的隨機值都不同但每次生成都一致
    random.seed(frame_index)
    
    # 隨機振動因子 (±15% 的變化)
    random_factor_x = 1.0 + (random.random() * 0.3 - 0.15)
    random_factor_y = 1.0 + (random.random() * 0.3 - 0.15)
    
    if x_amp > 0:
        # 使用正弦函數計算X軸震動，提供更自然的震動效果
        normalized_pos = (frame_index * frequency) / safe_frames
        # 應用隨機因子使震動幅度有細微變化
        anim_x = int(x_amp * random_factor_x * math.sin(normalized_pos * 2 * math.pi))
    else:
        anim_x = 0
        
    if y_amp > 0:
        # 使用餘弦函數計算Y軸震動，並應用相位差
        shifted_index = ((frame_index * frequency) + (num_frames * phase)) % num_frames
        normalized_pos = shifted_index / safe_frames
        # 應用隨機因子使震動幅度有細微變化
        anim_y = int(y_amp * random_factor_y * math.cos(normalized_pos * 2 * math.pi))
    else:
        anim_y = 0
    
    return (base_x + anim_x, base_y + anim_y)

async def overlay_avatar_on_gif(gif_path, user, output_path=None, fixed_size=(0, 0)):
    """
    將用戶頭像疊加到GIF上
    
    參數:
        gif_path (str): GIF文件路徑
        user (discord.User): Discord用戶對象
        output_path (str, 可選): 輸出文件路徑，默認為臨時文件
        fixed_size (tuple, 可選): 設置固定輸出尺寸，例如(350, 350)，默認(0, 0)表示保持原尺寸
        
    返回:
        str: 生成的GIF文件路徑
    """
    # 獲取GIF配置
    gif_config = get_gif_config(gif_path)
    
    # 獲取用戶頭像
    avatar = await fetch_avatar(user)
    if avatar is None:
        raise ValueError("無法獲取用戶頭像")
    
    # 調整頭像大小
    avatar_size = gif_config["avatar_size"]
    avatar = avatar.resize(avatar_size, Image.LANCZOS)
    
    try:
        # 使用PIL打開GIF
        original_gif = Image.open(gif_path)
        
        # 取得原始GIF的信息
        original_duration = original_gif.info.get('duration', 100)
        original_loop = original_gif.info.get('loop', 0)
        
        # 獲取GIF尺寸
        gif_width, gif_height = original_gif.size
        
        # 獲取GIF幀數
        num_frames = 0
        for frame in ImageSequence.Iterator(original_gif):
            num_frames += 1
        
        # 創建輸出文件路徑
        if output_path is None:
            output_dir = os.path.join("image_processing", "temp")
            os.makedirs(output_dir, exist_ok=True)
            gif_name = os.path.basename(gif_path)
            output_path = os.path.join(output_dir, f"{gif_name.split('.')[0]}_{user.id}.gif")
        
        # 處理GIF的每一幀並疊加頭像
        frames = []
        for i, frame in enumerate(ImageSequence.Iterator(original_gif)):
            # 創建新的空白幀
            new_frame = Image.new("RGBA", (gif_width, gif_height), (0, 0, 0, 0))
            
            # 計算頭像在當前幀的位置 - 基於配置添加動畫效果
            avatar_pos = calculate_avatar_position(gif_config, gif_width, gif_height, avatar_size, i, num_frames)
            
            # 先放置頭像（在底層）
            new_frame.paste(avatar, avatar_pos, avatar)
            
            # 取得轉換為RGBA的GIF幀
            frame_rgba = frame.convert("RGBA")
            
            # 再放置GIF幀（在頭像上方），保留GIF的透明度
            new_frame.alpha_composite(frame_rgba)
            
            # 添加到幀列表
            frames.append(new_frame)
        
        # 如果需要調整為固定尺寸
        if fixed_size[0] > 0 and fixed_size[1] > 0:
            resized_frames = []
            for frame in frames:
                # 使用高質量的LANCZOS算法調整大小
                resized_frame = frame.resize(fixed_size, Image.LANCZOS)
                resized_frames.append(resized_frame)
            frames = resized_frames
            # 更新寬高為新的尺寸
            gif_width, gif_height = fixed_size
        
        # 處理透明通道 - 使用單一統一方法
        transparent_color = (0, 0, 0)  # 黑色作為透明色
        converted_frames = []
        
        for frame in frames:
            # 創建一個RGB圖像，用特定的顏色填充
            background = Image.new("RGB", frame.size, transparent_color)
            # 合成頭像和GIF，透明部分將顯示為transparent_color
            background.paste(frame, (0, 0), frame)
            # 轉換為P模式，指定顏色範圍
            paletted = background.quantize(colors=255, method=2)
            
            # 獲取alpha通道作為遮罩
            alpha = frame.getchannel('A')
            # 創建透明區域的遮罩
            mask = Image.eval(alpha, lambda a: 0 if a > 0 else 255)
            # 設置透明區域
            paletted.paste(255, mask)
            
            # 設置透明索引
            paletted.info['transparency'] = 255
            converted_frames.append(paletted)
        
        # 保存為GIF
        output_buffer = io.BytesIO()
        converted_frames[0].save(
            output_buffer,
            format="GIF",
            save_all=True,
            append_images=converted_frames[1:],
            duration=original_duration,
            loop=original_loop,
            disposal=2,  # 確保每一幀都完全替換前一幀
            transparency=255
        )
        
        # 寫入文件
        with open(output_path, 'wb') as f:
            f.write(output_buffer.getvalue())
        
        return output_path
    
    except Exception as e:
        # 處理錯誤
        import traceback
        error_details = traceback.format_exc()
        raise ValueError(f"GIF處理失敗: {str(e)}\n{error_details}") 