import discord
from discord.ext import commands
from typing import Optional, Union, Any, List, Dict, TYPE_CHECKING, Callable
from decimal import Decimal
import math
import io
import dataclasses
from utils.logger import logger
from gacha.app_config import config_service
from gacha.utils.interaction_utils import check_user_permission, safe_defer, safe_send_message
from gacha.utils.cog_error_handler import handle_gacha_error
from gacha.services.market.market_view_service import MarketViewService, StockListPageData, StockDetailData, NewsPageData, NewsItemData, CardInfoData, NewsFilterOptions, CardMarketStatsData, LinkedStockData
from gacha.views.embeds.market.market_embed_builders import StockListEmbedBuilder, NewsEmbedBuilder, CardInfoEmbedBuilder, LinkedStocksEmbedBuilder
from gacha.views.embeds.market.stock_market_embed_builder import StockDetailEmbedBuilder
from gacha.models.market_models import StockLifecycleStatus
from gacha.views.modals import BuyStockModal, SellStockModal
from gacha.constants import NewsTypeFilterEnum, NewsFilterType
if TYPE_CHECKING:
    from gacha.services.market.stock_trading_service import StockTradingService
    from gacha.repositories.card.master_card_repository import MasterCardRepository
MAX_STOCKS_PER_PAGE = 10

class BaseMarketView(discord.ui.View):

    def __init__(self, initiating_interaction: discord.Interaction, bot_instance: commands.Bot, market_view_service: MarketViewService, message: Optional[discord.Message]=None, timeout: Optional[float]=180.0):
        super().__init__(timeout=timeout)
        self.initiating_interaction = initiating_interaction
        self.bot = bot_instance
        self.market_view_service = market_view_service
        self.message: Optional[discord.Message] = message
        self.user_id: int = initiating_interaction.user.id
        self.current_page: int = 1

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """
        檢查與此市場視圖互動的使用者是否為原始觸發者。
        """
        return await check_user_permission(interaction=interaction, expected_user_id=self.user_id, error_message_on_fail='您無法操作此市場介面！')

    async def _defer_if_needed(self, interaction: discord.Interaction, thinking: bool=False, ephemeral: bool=False):
        if not interaction.response.is_done():
            try:
                await interaction.response.defer(thinking=thinking, ephemeral=ephemeral)
            except discord.NotFound:
                logger.warning('Failed to defer interaction (ID: %s): Not found. It might have expired.', interaction.id)
            except discord.HTTPException as e:
                logger.error('HTTPException during deferral for interaction (ID: %s): %s', interaction.id, e)

    async def _get_data_for_display(self, **kwargs) -> Any:
        raise NotImplementedError('Subclasses must implement _get_data_for_display')

    def _build_embed(self, data: Any) -> discord.Embed:
        raise NotImplementedError('Subclasses must implement _build_embed')

    def _build_components(self, data: Any) -> List[discord.ui.Item]:
        raise NotImplementedError('Subclasses must implement _build_components')

    async def update_display(self, interaction_for_update: discord.Interaction, **kwargs):
        if not self.message:
            logger.error('%s: No message to update.', self.__class__.__name__)
            try:
                if not interaction_for_update.response.is_done():
                    await interaction_for_update.response.send_message('發生錯誤：無法找到要更新的訊息。', ephemeral=True)
                else:
                    await interaction_for_update.followup.send('發生錯誤：無法找到要更新的訊息。', ephemeral=True)
            except discord.HTTPException:
                logger.warning('Failed to send error followup for missing message during update_display.')
            return
        await self._defer_if_needed(interaction_for_update)
        try:
            data = await self._get_data_for_display(**kwargs)
            if data is None:
                logger.warning('%s: Failed to get data for display.', self.__class__.__name__)
                await interaction_for_update.followup.send('無法獲取最新數據，請稍後再試。', ephemeral=True)
                return
            new_embed = self._build_embed(data)
            self.clear_items()
            self._build_components(data)
            attachments_to_send = []
            if hasattr(data, 'chart_image_bytes') and data.chart_image_bytes:
                attachments_to_send.append(discord.File(io.BytesIO(data.chart_image_bytes), filename='price_chart_7d.png'))
            await self.message.edit(embed=new_embed, view=self, attachments=attachments_to_send or [])
            if interaction_for_update.response.is_done() and getattr(interaction_for_update.response, '_deferred_thinking', False) and (interaction_for_update.id != self.initiating_interaction.id):
                try:
                    await interaction_for_update.delete_original_response()
                except discord.HTTPException as e_del:
                    logger.warning('Could not delete original response of thinking interaction %s: %s', interaction_for_update.id, e_del)
        except NotImplementedError as nie:
            logger.error('Service method not implemented: %s', nie, exc_info=True)
            await interaction_for_update.followup.send('此功能正在開發中，部分資料可能無法顯示。', ephemeral=True)
        except Exception as e:
            logger.error('Error during %s.update_display: %s', self.__class__.__name__, e, exc_info=True)
            try:
                await interaction_for_update.followup.send('更新視圖時發生未預期錯誤。', ephemeral=True)
            except discord.HTTPException:
                logger.error('Failed to send followup error during update_display exception.', exc_info=True)

    async def send_initial_message(self, ephemeral: bool=False, **kwargs):
        if not self.initiating_interaction.response.is_done():
            await self._defer_if_needed(self.initiating_interaction, thinking=not ephemeral)
        try:
            data = await self._get_data_for_display(**kwargs)
            if data is None:
                logger.warning('%s: Failed to get data for initial message.', self.__class__.__name__)
                await self.initiating_interaction.followup.send('無法加載初始數據。', ephemeral=True)
                return
            embed = self._build_embed(data)
            self.clear_items()
            self._build_components(data)
            attachments_to_send = []
            if hasattr(data, 'chart_image_bytes') and data.chart_image_bytes:
                attachments_to_send.append(discord.File(io.BytesIO(data.chart_image_bytes), filename='price_chart_7d.png'))
            if getattr(self.initiating_interaction.response, '_deferred_thinking', False) and (not ephemeral):
                self.message = await self.initiating_interaction.edit_original_response(embed=embed, view=self, attachments=attachments_to_send or [])
            else:
                self.message = await self.initiating_interaction.followup.send(embed=embed, view=self, ephemeral=ephemeral, files=attachments_to_send or [], wait=True)
        except NotImplementedError as nie:
            logger.error('Service method not implemented for initial message: %s', nie, exc_info=True)
            await self.initiating_interaction.followup.send('此功能正在開發中，無法顯示初始內容。', ephemeral=True)
        except Exception as e:
            logger.error('Error during %s.send_initial_message: %s', self.__class__.__name__, e, exc_info=True)
            if not self.initiating_interaction.response.is_done():
                try:
                    await self.initiating_interaction.followup.send('初始化視圖時發生錯誤。', ephemeral=True)
                except discord.HTTPException:
                    pass

class StockListView(BaseMarketView):

    def __init__(self, initiating_interaction: discord.Interaction, bot_instance: commands.Bot, market_view_service: MarketViewService, message: Optional[discord.Message]=None):
        super().__init__(initiating_interaction, bot_instance, market_view_service, message, timeout=300.0)
        self.current_page = 1
        self.total_pages = 1

    async def _get_data_for_display(self, page: Optional[int]=None) -> Optional[StockListPageData]:
        page_to_fetch = page if page is not None else self.current_page
        data = await self.market_view_service.get_stock_list_page_data(page=page_to_fetch, per_page=MAX_STOCKS_PER_PAGE)
        if data:
            self.current_page = data.current_page
            self.total_pages = data.total_pages
        return data

    def _build_embed(self, data: StockListPageData) -> discord.Embed:
        return StockListEmbedBuilder(page_data=data).build()

    def _build_components(self, data: StockListPageData) -> List[discord.ui.Item]:
        prev_button = discord.ui.Button(label='⬅️ 上一頁', style=discord.ButtonStyle.secondary, custom_id='stocklist_prev', disabled=self.current_page <= 1, row=0)
        prev_button.callback = self.go_to_previous_page
        self.add_item(prev_button)
        page_indicator = discord.ui.Button(label=f'{self.current_page}/{self.total_pages}', style=discord.ButtonStyle.grey, disabled=True, row=0)
        self.add_item(page_indicator)
        next_button = discord.ui.Button(label='下一頁 ➡️', style=discord.ButtonStyle.secondary, custom_id='stocklist_next', disabled=self.current_page >= self.total_pages, row=0)
        next_button.callback = self.go_to_next_page
        self.add_item(next_button)
        tutorial_button = discord.ui.Button(label='📖 教學', style=discord.ButtonStyle.blurple, custom_id='stocklist_tutorial', row=0)
        tutorial_button.callback = self.tutorial_callback
        self.add_item(tutorial_button)
        if data.stocks:
            options = []
            for s in data.stocks:
                status_suffix = ''
                if s.lifecycle_status == StockLifecycleStatus.ST:
                    status_suffix = ' <a:Error:1371096622053724292>'
                elif s.lifecycle_status == StockLifecycleStatus.DELISTED:
                    status_suffix = ' (已退市)'
                label_text = f'{s.asset_symbol} - {s.asset_name}{status_suffix}'
                options.append(discord.SelectOption(label=label_text[:100], value=str(s.asset_id), description=(s.description or '暫無描述')[:100]))
            if options:
                details_select = discord.ui.Select(placeholder='選擇股票查看詳細信息...', options=options, custom_id='stocklist_view_details_select', row=1)
                details_select.callback = self.on_view_details_select
                self.add_item(details_select)
        return self.children

    async def go_to_page(self, interaction: discord.Interaction, page_num: int):
        await self.update_display(interaction_for_update=interaction, page=page_num)

    async def go_to_previous_page(self, interaction: discord.Interaction):
        await self.go_to_page(interaction, self.current_page - 1)

    async def go_to_next_page(self, interaction: discord.Interaction):
        await self.go_to_page(interaction, self.current_page + 1)

    async def on_view_details_select(self, interaction: discord.Interaction):
        await self._defer_if_needed(interaction)
        selected_asset_id_str = interaction.data['values'][0]
        try:
            selected_asset_id = int(selected_asset_id_str)
        except ValueError:
            logger.error('Invalid asset_id from select: %s', selected_asset_id_str)
            await interaction.followup.send('選擇的股票ID無效。', ephemeral=True)
            return
        detail_view = StockDetailView(initiating_interaction=self.initiating_interaction, bot_instance=self.bot, market_view_service=self.market_view_service, message=self.message, asset_symbol_or_id=selected_asset_id, came_from_list_view=True)
        await detail_view.update_display(interaction_for_update=interaction, asset_id_to_load=selected_asset_id)

    async def tutorial_callback(self, interaction: discord.Interaction):
        tutorial_message = '**股市機制教學**\n\n**📈 基本概念**\n- **股價**：代表公司一部分所有權的價值，會因市場供需、公司表現、新聞等因素波動。\n- **交易時間**：本市場為24/7模擬交易，但價格更新頻率依系統設定。\n\n**💸 手續費計算方式**\n交易手續費的計算方式如下：\n1. **百分比費用**：交易總金額 × 3%\n2. **每股費用**：每股 0.05 油幣 × 交易股數\n3. **比較取高**：取上述「百分比費用」和「每股費用」中較高者\n4. **最低手續費**：如果第3步算出的費用低於 5 油幣，則實際手續費為 5 油幣\n   *(買入時，手續費會加入總成本；賣出時，手續費會從總收益中扣除。)*\n\n**⚠️ ST (Special Treatment) 股票**\n- **定義**：被標記為 <a:Error:1371096622053724292> 的股票，通常表示該公司可能存在財務或其他營運風險，例如連續虧損。\n- **交易影響**：ST 股票的每日價格漲跌幅限制可能更嚴格（例如 `±5%`）。\n- **風險**：投資 ST 股票風險較高，請謹慎評估。\n\n**📉 退市 (Delisting)**\n- **定義**：股票從交易所下市，不再公開交易。原因可能包括：公司破產、長期不符合上市標準、私有化等。\n- **影響**：一旦股票退市，您持有的股票可能難以出售或價值歸零。\n- **處理**：系統可能會以特定方式處理退市股票的剩餘價值（例如，以最後價格結算或按比例退還）。\n\n**💡 投資小提示**\n- **分散風險**：不要將所有資金投入單一股票。\n- **研究公司**：了解您投資的對象。\n- **市場波動**：價格波動是正常的，保持理性。\n\n*(本教學僅供參考，不構成任何投資建議。市場有風險，投資需謹慎。)*'
        await interaction.response.send_message(tutorial_message, ephemeral=True)

class StockDetailView(BaseMarketView):

    def __init__(self, initiating_interaction: discord.Interaction, bot_instance: commands.Bot, market_view_service: MarketViewService, message: Optional[discord.Message]=None, asset_symbol_or_id: Optional[Union[str, int]]=None, came_from_list_view: bool=False):
        super().__init__(initiating_interaction, bot_instance, market_view_service, message, timeout=300.0)
        self.asset_symbol_or_id = asset_symbol_or_id
        self.stock_data: Optional[StockDetailData] = None
        self.came_from_list_view = came_from_list_view

    async def _get_data_for_display(self, asset_id_to_load: Optional[Union[str, int]]=None, **kwargs) -> Optional[StockDetailData]:
        target_asset = asset_id_to_load if asset_id_to_load is not None else self.asset_symbol_or_id
        if target_asset is None:
            logger.error('StockDetailView: No asset_symbol_or_id provided to fetch data.')
            return None
        self.stock_data = await self.market_view_service.get_stock_detail_data(asset_symbol_or_id=target_asset, user_id=self.user_id)
        return self.stock_data

    def _build_embed(self, data: StockDetailData) -> discord.Embed:
        stock_data_dict = dataclasses.asdict(data.stock_data) if data.stock_data else None
        aggregated_volume_dict = None
        if data.aggregated_volume_7d:
            if hasattr(data.aggregated_volume_7d, '__dataclass_fields__'):
                aggregated_volume_dict = dataclasses.asdict(data.aggregated_volume_7d)
            elif isinstance(data.aggregated_volume_7d, dict):
                aggregated_volume_dict = data.aggregated_volume_7d
        recent_transactions_list_of_dicts = None
        if data.recent_transactions_5:
            recent_transactions_list_of_dicts = []
            for rt in data.recent_transactions_5:
                if hasattr(rt, '__dataclass_fields__'):
                    recent_transactions_list_of_dicts.append(dataclasses.asdict(rt))
                elif isinstance(rt, dict):
                    recent_transactions_list_of_dicts.append(rt)
        asset_symbol_or_id_for_builder = self.asset_symbol_or_id
        if data.stock_data and data.stock_data.asset_symbol:
            asset_symbol_or_id_for_builder = data.stock_data.asset_symbol
        return StockDetailEmbedBuilder(stock_data=stock_data_dict, asset_symbol_or_id=asset_symbol_or_id_for_builder, aggregated_volume_7d=aggregated_volume_dict, recent_transactions_5=recent_transactions_list_of_dicts, chart_image_bytes=data.chart_image_bytes, user_oil_balance=data.user_oil_balance, user_stock_quantity=data.user_stock_quantity).build()

    def _build_components(self, data: StockDetailData) -> List[discord.ui.Item]:
        buy_button = discord.ui.Button(label='買入股票', style=discord.ButtonStyle.success, custom_id='stockdetail_buy', emoji='🛒', row=0)
        buy_button.callback = self.buy_stock_button_callback
        self.add_item(buy_button)
        sell_button = discord.ui.Button(label='賣出股票', style=discord.ButtonStyle.danger, custom_id='stockdetail_sell', emoji='💸', row=0)
        sell_button.callback = self.sell_stock_button_callback
        self.add_item(sell_button)
        if self.came_from_list_view:
            back_button = discord.ui.Button(label='返回列表', style=discord.ButtonStyle.grey, custom_id='stockdetail_back_to_list', emoji='↩️', row=0)
            back_button.callback = self.back_to_list_view_callback
            self.add_item(back_button)
        news_button = discord.ui.Button(label='近期新聞', style=discord.ButtonStyle.secondary, custom_id='stockdetail_news', emoji='📰', row=1)
        news_button.callback = self.related_news_button_callback
        self.add_item(news_button)
        refresh_button = discord.ui.Button(label='刷新數據', style=discord.ButtonStyle.blurple, custom_id='stockdetail_refresh', emoji='🔄', row=1)
        refresh_button.callback = self.refresh_button_callback
        self.add_item(refresh_button)
        return self.children

    async def refresh_button_callback(self, interaction: discord.Interaction):
        await self.update_display(interaction_for_update=interaction, asset_id_to_load=self.asset_symbol_or_id)

    async def buy_stock_button_callback(self, interaction: discord.Interaction):
        if not self.stock_data or not self.stock_data.stock_data:
            await interaction.response.send_message('股票數據未加載，無法買入。', ephemeral=True)
            return
        current_stock_status = self.stock_data.stock_data.lifecycle_status
        if current_stock_status == StockLifecycleStatus.DELISTED:
            await interaction.response.send_message(f'股票 {self.stock_data.stock_data.asset_symbol} ({self.stock_data.stock_data.asset_name}) 已退市，無法進行買入操作。', ephemeral=True)
            return
        stock_trading_svc = getattr(self.market_view_service, 'stock_trading_service', None)
        if not stock_trading_svc:
            await interaction.response.send_message('交易服務暫不可用。', ephemeral=True)
            return
        modal = BuyStockModal(stock_symbol=self.stock_data.stock_data.asset_symbol, current_price=self.stock_data.stock_data.current_price, stock_trading_service=stock_trading_svc, on_trade_complete_callback=lambda modal_interaction: self.trigger_internal_refresh(modal_interaction))
        await interaction.response.send_modal(modal)

    async def sell_stock_button_callback(self, interaction: discord.Interaction):
        if not self.stock_data or not self.stock_data.stock_data:
            await interaction.response.send_message('股票數據未加載，無法賣出。', ephemeral=True)
            return
        current_stock_status = self.stock_data.stock_data.lifecycle_status
        if current_stock_status == StockLifecycleStatus.DELISTED:
            await interaction.response.send_message(f'股票 {self.stock_data.stock_data.asset_symbol} ({self.stock_data.stock_data.asset_name}) 已退市，無法進行賣出操作。', ephemeral=True)
            return
        stock_trading_svc = getattr(self.market_view_service, 'stock_trading_service', None)
        if not stock_trading_svc:
            await interaction.response.send_message('交易服務暫不可用。', ephemeral=True)
            return
        user_quantity = 0
        user_quantity = await stock_trading_svc.get_user_stock_quantity(interaction.user.id, self.stock_data.stock_data.asset_id)
        modal = SellStockModal(stock_symbol=self.stock_data.stock_data.asset_symbol, current_price=self.stock_data.stock_data.current_price, user_id=interaction.user.id, stock_trading_service=stock_trading_svc, max_quantity=user_quantity, on_trade_complete_callback=lambda modal_interaction: self.trigger_internal_refresh(modal_interaction))
        await interaction.response.send_modal(modal)

    async def related_news_button_callback(self, interaction: discord.Interaction):
        await self._defer_if_needed(interaction, ephemeral=True)
        if not self.stock_data or not self.stock_data.stock_data or (not self.market_view_service.stock_trading_service):
            await interaction.followup.send('無法獲取相關新聞，數據或服務未就緒。', ephemeral=True)
            return
        asset_id = self.stock_data.stock_data.asset_id
        asset_symbol = self.stock_data.stock_data.asset_symbol
        news_records_raw = await self.market_view_service.stock_trading_service.get_related_news_for_stock(asset_id, limit=5)
        if news_records_raw:
            embed = discord.Embed(title=f'{asset_symbol} 相關新聞', color=discord.Color.blurple())
            description = ''
            for rec in news_records_raw:
                ts = discord.utils.format_dt(rec['published_at'], 'd')
                headline = rec.get('headline', '無標題')
                description += f'• ({ts}) {headline}\n'
            embed.description = description if description else '暫無新聞描述。'
            await interaction.followup.send(embed=embed, ephemeral=True)
        else:
            await interaction.followup.send(f'沒有找到與 {asset_symbol} 相關的新聞。', ephemeral=True)

    async def trigger_internal_refresh(self, modal_submit_interaction: discord.Interaction):
        logger.info('StockDetailView: Triggering internal refresh for %s due to modal action by %s', self.asset_symbol_or_id, modal_submit_interaction.user.id)
        await self.update_display(interaction_for_update=modal_submit_interaction, asset_id_to_load=self.asset_symbol_or_id)

    async def back_to_list_view_callback(self, interaction: discord.Interaction):
        """Callback to return to the stock list view."""
        await self._defer_if_needed(interaction)
        list_view = StockListView(initiating_interaction=self.initiating_interaction, bot_instance=self.bot, market_view_service=self.market_view_service, message=self.message)
        await list_view.update_display(interaction_for_update=interaction)

class SingleNewsView(BaseMarketView):

    def __init__(self, initiating_interaction: discord.Interaction, bot_instance: commands.Bot, market_view_service: MarketViewService, message: Optional[discord.Message]=None):
        super().__init__(initiating_interaction, bot_instance, market_view_service, message, timeout=300.0)
        self.current_filter_scope: NewsFilterType = NewsFilterType.ALL
        self.current_news_type_filter: NewsTypeFilterEnum = NewsTypeFilterEnum.ALL
        self.current_item_index: int = 0
        self.total_items: int = 0
        self.current_news_item_data: Optional[NewsItemData] = None

    async def _get_data_for_display(self, item_index: Optional[int]=None, **kwargs) -> Optional[NewsPageData]:
        idx_to_fetch = item_index if item_index is not None else self.current_item_index
        filter_options = NewsFilterOptions(filter_type=self.current_filter_scope, news_type_filter=self.current_news_type_filter)
        page_data = await self.market_view_service.get_news_page_data(user_id=self.user_id, filters=filter_options, current_page_idx=idx_to_fetch, items_per_page=1)
        if page_data:
            self.total_items = page_data.total_items
            if page_data.news_items:
                self.current_news_item_data = page_data.news_items[0]
                self.current_item_index = idx_to_fetch
            else:
                self.current_news_item_data = None
                if self.total_items == 0:
                    self.current_item_index = 0
        else:
            self.current_news_item_data = None
            self.total_items = 0
            self.current_item_index = 0
        return page_data

    def _build_embed(self, data: NewsPageData) -> discord.Embed:
        if self.current_news_item_data:
            return NewsEmbedBuilder(news_data=self.current_news_item_data, current_item_index=self.current_item_index, total_items=self.total_items).build()
        else:
            return NewsEmbedBuilder(news_data=None, current_item_index=0, total_items=0).build()

    def _build_components(self, data: NewsPageData) -> List[discord.ui.Item]:
        scope_options = [discord.SelectOption(label='所有新聞 (範圍)', value=NewsFilterType.ALL.value, default=self.current_filter_scope == NewsFilterType.ALL), discord.SelectOption(label='我的持倉相關', value=NewsFilterType.PORTFOLIO.value, default=self.current_filter_scope == NewsFilterType.PORTFOLIO), discord.SelectOption(label='全局新聞', value=NewsFilterType.GLOBAL.value, default=self.current_filter_scope == NewsFilterType.GLOBAL)]
        scope_select = discord.ui.Select(placeholder='篩選新聞範圍...', options=scope_options, custom_id='news_filter_scope_select', row=0)
        scope_select.callback = self.on_filter_scope_select
        self.add_item(scope_select)
        news_type_opts = [discord.SelectOption(label='所有類型', value=NewsTypeFilterEnum.ALL.value, default=self.current_news_type_filter == NewsTypeFilterEnum.ALL)]
        news_type_opts.extend([discord.SelectOption(label=config_service.get_news_type_readable_names().get(nt.value, nt.value), value=nt.value, default=self.current_news_type_filter == nt) for nt in NewsTypeFilterEnum if nt != NewsTypeFilterEnum.ALL])
        news_type_select = discord.ui.Select(placeholder='篩選新聞種類...', options=news_type_opts, custom_id='news_type_filter_select', row=1)
        news_type_select.callback = self.on_news_type_filter_select
        self.add_item(news_type_select)
        prev_button = discord.ui.Button(label='⬅️ 上一條', style=discord.ButtonStyle.secondary, custom_id='news_prev', disabled=self.current_item_index <= 0, row=2)
        prev_button.callback = self.go_to_previous_item
        self.add_item(prev_button)
        page_indicator_label = f'{self.current_item_index + 1}/{self.total_items}' if self.total_items > 0 else '0/0'
        page_indicator = discord.ui.Button(label=page_indicator_label, style=discord.ButtonStyle.grey, disabled=True, custom_id='news_page_indicator', row=2)
        self.add_item(page_indicator)
        next_button = discord.ui.Button(label='下一條 ➡️', style=discord.ButtonStyle.secondary, custom_id='news_next', disabled=self.current_item_index >= self.total_items - 1 or self.total_items == 0, row=2)
        next_button.callback = self.go_to_next_item
        self.add_item(next_button)
        return self.children

    async def _handle_filter_change(self, interaction: discord.Interaction):
        self.current_item_index = 0
        await self.update_display(interaction_for_update=interaction)

    async def on_filter_scope_select(self, interaction: discord.Interaction):
        self.current_filter_scope = NewsFilterType(interaction.data['values'][0])
        await self._handle_filter_change(interaction)

    async def on_news_type_filter_select(self, interaction: discord.Interaction):
        self.current_news_type_filter = NewsTypeFilterEnum(interaction.data['values'][0])
        await self._handle_filter_change(interaction)

    async def go_to_previous_item(self, interaction: discord.Interaction):
        if self.current_item_index > 0:
            new_idx = self.current_item_index - 1
            await self.update_display(interaction_for_update=interaction, item_index=new_idx)
        else:
            await self._defer_if_needed(interaction)

    async def go_to_next_item(self, interaction: discord.Interaction):
        if self.current_item_index < self.total_items - 1:
            new_idx = self.current_item_index + 1
            await self.update_display(interaction_for_update=interaction, item_index=new_idx)
        else:
            await self._defer_if_needed(interaction)

class CardInfoDetailView(BaseMarketView):

    def __init__(self, initiating_interaction: discord.Interaction, bot_instance: commands.Bot, market_view_service: MarketViewService, card_query: Union[str, int], message: Optional[discord.Message]=None):
        super().__init__(initiating_interaction, bot_instance, market_view_service, message, timeout=180.0)
        self.card_query = card_query
        self.card_data: Optional[CardInfoData] = None

    async def _get_data_for_display(self, **kwargs) -> Optional[CardInfoData]:
        self.card_data = await self.market_view_service.get_card_info_data(self.card_query)
        return self.card_data

    def _build_embed(self, data: CardInfoData) -> discord.Embed:
        return CardInfoEmbedBuilder(card_info_data=data).build()

    def _build_components(self, data: CardInfoData) -> List[discord.ui.Item]:
        stocks_button = discord.ui.Button(label='關聯股票', style=discord.ButtonStyle.secondary, custom_id='cardinfo_linked_stocks')
        stocks_button.callback = self.linked_stocks_button_callback
        self.add_item(stocks_button)
        return self.children

    async def linked_stocks_button_callback(self, interaction: discord.Interaction):
        await safe_defer(interaction, ephemeral=True, thinking=True)
        
        if not self.card_data:
            await safe_send_message(interaction, '無法獲取關聯股票，卡片數據不完整。', ephemeral=True)
            return
            
        if self.card_data.linked_stocks is not None:
            embed = LinkedStocksEmbedBuilder(card_name=self.card_data.name, linked_stocks_data=self.card_data.linked_stocks).build()
            await safe_send_message(interaction, embed=embed, ephemeral=True)
        else:
            await safe_send_message(interaction, f'目前沒有找到與 {self.card_data.name} 明確關聯的股票信息。', ephemeral=True)