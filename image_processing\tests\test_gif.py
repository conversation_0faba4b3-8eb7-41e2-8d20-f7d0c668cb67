import asyncio
import os
from PIL import Image

from gif_modifier.avatar_gif_overlay import overlay_avatar_on_gif
from gif_modifier.config import list_available_gifs, get_gif_config

# GIF尺寸設定 - 與commands.py保持一致
GIF_SIZES = {
    "青蛙.gif": (150, 150),
    "阿北.gif": (480, 320)
}

# 默認GIF尺寸
DEFAULT_GIF_SIZE = (150, 150)

# 模擬 config.yaml 中的設定
# GIF尺寸設定
GIF_SETTINGS = {
    "DEFAULT_DURATION": 100,  # ms
}

# 創建模擬用戶頭像
def create_test_avatar():
    avatar_dir = os.path.join("temp")
    os.makedirs(avatar_dir, exist_ok=True)
    
    # 創建一個紅色測試頭像
    test_avatar = Image.new('RGBA', (80, 80), (255, 0, 0, 128))
    avatar_path = os.path.join(avatar_dir, "test_avatar.png")
    test_avatar.save(avatar_path)
    return avatar_path

# 修改fetch_avatar函數以使用本地文件
async def mock_fetch_avatar(user):
    return Image.open(create_test_avatar()).convert("RGBA")

async def test_gif(gif_name):
    try:
        # 顯示GIF配置
        gif_path = os.path.join("gifs", gif_name)
        print(f"\n測試GIF: {gif_name}")
        print(f"GIF文件是否存在: {os.path.exists(gif_path)}")
        
        # 顯示GIF配置
        gif_config = get_gif_config(gif_path)
        print(f"GIF配置: {gif_config}")
        
        # 創建模擬用戶
        class MockUser:
            def __init__(self):
                self.id = 123456
        
        # 獲取GIF的輸出尺寸配置
        output_size = GIF_SIZES.get(gif_name, DEFAULT_GIF_SIZE)
        print(f"使用輸出尺寸: {output_size}")
        
        # 測試GIF處理
        mock_user = MockUser()
        result = await overlay_avatar_on_gif(gif_path, mock_user, fixed_size=output_size)
        print(f"成功處理GIF: {result}")
        print(f"輸出文件是否存在: {os.path.exists(result)}")
        
        # 顯示一些統計信息
        if os.path.exists(result):
            print(f"輸出文件大小: {os.path.getsize(result)} 字節")
            # 驗證輸出GIF的尺寸是否正確
            with Image.open(result) as img:
                size = img.size
                print(f"輸出GIF尺寸: {size}")
                assert size == output_size, f"輸出GIF尺寸應為{output_size}，實際為{size}"
                print("尺寸驗證成功!")
        
        return result
    
    except Exception as e:
        import traceback
        print(f"處理GIF時出錯: {e}")
        traceback.print_exc()
        return None

async def main():
    try:
        # 顯示可用的GIF配置
        available_gifs = list_available_gifs()
        print(f"系統中可用的GIF配置: {available_gifs}")
        
        # 修改fetch_avatar函數
        import gif_modifier.avatar_gif_overlay
        original_fetch_avatar = gif_modifier.avatar_gif_overlay.fetch_avatar
        gif_modifier.avatar_gif_overlay.fetch_avatar = mock_fetch_avatar
        
        try:
            # 測試青蛙GIF
            await test_gif("青蛙.gif")
            
            # 測試阿北GIF
            await test_gif("阿北.gif")
        finally:
            # 恢復原始函數
            gif_modifier.avatar_gif_overlay.fetch_avatar = original_fetch_avatar
            
    except Exception as e:
        import traceback
        print(f"整體測試出錯: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main()) 