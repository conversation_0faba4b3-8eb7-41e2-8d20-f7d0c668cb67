"""
Gacha系統用戶收藏存儲庫 - 批量操作 Mixin (Asyncpg 版本 - 還原舊邏輯)
"""
import asyncpg
from typing import Dict, List, Any, Optional, TypedDict, Tuple
from datetime import datetime
import json
from gacha.models.models import UserCard
from utils.logger import logger
from database.redis.service import RedisService
from gacha.exceptions import DatabaseOperationError, RepositoryError

class DeletedCardInfo(TypedDict):
    card_id: int
    quantity: int
    rarity: int
    pool_type: str
    name: str
    is_favorite: bool

class AffectedCardSummary(TypedDict):
    card_id: int
    name: str
    rarity: int
    pool_type: str
    quantity_sold: int
    is_favorite: bool

class UserCollectionBulkMixin:
    """提供用戶收藏批量操作相關方法的 Mixin (Asyncpg 版本)"""

    async def delete_cards_by_ids_raw(self, user_id: int, card_ids: List[int], connection: Optional[asyncpg.Connection]=None) -> Dict[str, Any]:
        """
        根據 ID 列表批量刪除用戶的卡片。
        返回包含刪除結果的字典，包括刪除行數和被刪除的卡片詳細信息列表。
        被刪除的卡片信息包括：ID, 數量, 稀有度, 卡池類型, 名稱, 以及是否為最愛。

        參數:
            user_id: 用戶 ID
            card_ids: 要刪除的卡片 ID 列表
            connection: 可選的資料庫連接

        返回:
            包含刪除結果的字典：
            {
                "deleted_rows": 刪除的行數,
                "deleted_cards_info": 被刪除卡片的詳細信息列表
            }
            
        Raises:
            DatabaseOperationError: 如果數據庫操作失敗
        """
        if not card_ids:
            return {'deleted_rows': 0, 'deleted_cards_info': []}
            
        deleted_cards_details = []
        conn_to_use = connection
        
        # 獲取卡片詳細信息
        details_query = f'''
            SELECT uc.card_id, uc.quantity, mc.rarity, mc.pool_type, mc.name, uc.is_favorite
            FROM {self.table_name} uc
            JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
            WHERE uc.user_id = $1 AND uc.card_id = ANY($2::integer[])
        '''
        
        try:
            results = await self._fetch(details_query, [user_id, card_ids], connection=conn_to_use)
            
            if results:
                deleted_cards_details = [
                    {
                        'id': row['card_id'], 
                        'quantity': row['quantity'], 
                        'rarity': row['rarity'], 
                        'pool_type': row['pool_type'], 
                        'name': row['name'], 
                        'is_favorite': row['is_favorite'] if 'is_favorite' in row else False
                    } 
                    for row in results
                ]
            
            if not deleted_cards_details:
                return {'deleted_rows': 0, 'deleted_cards_info': []}
                
            actual_card_ids_to_delete = [details['id'] for details in deleted_cards_details]
            
            # 執行刪除操作
            delete_query = f'''
                DELETE FROM {self.table_name}
                WHERE user_id = $1 AND card_id = ANY($2::integer[])
            '''
            
            status = await self._execute(delete_query, [user_id, actual_card_ids_to_delete], connection=conn_to_use)
            
            deleted_rows = 0
            if status and isinstance(status, str) and ('DELETE' in status):
                try:
                    deleted_rows = int(status.split(' ')[1])
                except (IndexError, ValueError):
                    deleted_rows = len(deleted_cards_details)
            
            return {'deleted_rows': deleted_rows, 'deleted_cards_info': deleted_cards_details}
            
        except Exception as e:
            logger.error(f"批量刪除卡片失敗，用戶ID: {user_id}，卡片IDs: {card_ids}, 錯誤: {str(e)}")
            raise DatabaseOperationError(f"批量刪除卡片時資料庫操作失敗: {str(e)}", original_exception=e)

    async def bulk_update_sort_indexes(self, user_id: int, updates: List[Dict[str, Any]], connection: Optional[asyncpg.Connection]=None) -> bool:
        """(Async) 批量更新多張卡片的排序索引 - 使用 executemany 以提高性能
        
        Raises:
            DatabaseOperationError: 如果數據庫操作失敗
        """
        if not updates:
            return True
        update_params = []
        for update in updates:
            card_id = update.get('card_id')
            sort_index = update.get('sort_index')
            if card_id is None:
                logger.warning('[GACHA] bulk_update_sort: 缺少 card_id: %s', update)
                continue
            update_params.append((sort_index, user_id, card_id))
        if not update_params:
            return True
        query = f'UPDATE {self.table_name} SET custom_sort_index = $1 WHERE user_id = $2 AND card_id = $3'
        if connection:
            await connection.executemany(query, update_params)
        else:
            async with self.pool.acquire() as conn_from_pool:
                await conn_from_pool.executemany(query, update_params)
        return True

    async def batch_set_favorite_status_raw(self, user_id: int, card_ids: List[int], status: bool, sort_indexes_map: Optional[Dict[int, int]]=None, connection: Optional[asyncpg.Connection]=None) -> Dict[str, Any]:
        """
        (Async) 批量設置卡片的最愛狀態 (原子操作)。
        如果 status 為 True 且提供了 sort_indexes_map，則會更新 custom_sort_index。
        否則，如果 status 為 False，custom_sort_index 將被設為 NULL。
        此方法會將操作分批以避免超過資料庫參數限制。

        Args:
            user_id: 用戶 ID。
            card_ids: 要更新的卡片 ID 列表。
            status: True 表示設為最愛，False 表示取消最愛。
            sort_indexes_map: 可選字典，{card_id: sort_index}，僅在 status 為 True 時使用。
            connection: 可選的 asyncpg 連線。

        Returns:
            一個字典，包含:
            - updated_count (int): 受影響的總行數。
            - changed_favorites (List[Tuple[int, int]]): 成功變更最愛狀態的 (card_id, delta) 列表。
            
        Raises:
            DatabaseOperationError: 如果數據庫操作失敗
        """
        if not card_ids:
            return {'updated_count': 0, 'changed_favorites': []}
            
        try:
            conn_to_use = connection
            total_updated_count = 0
            BATCH_SIZE = 1000
            changed_favorites_details: List[Tuple[int, int]] = []
            delta = 1 if status else -1
            
            for i in range(0, len(card_ids), BATCH_SIZE):
                card_ids_batch = card_ids[i:i + BATCH_SIZE]
                current_batch_sort_indexes_map = None
                if sort_indexes_map:
                    current_batch_sort_indexes_map = {cid: idx for cid, idx in sort_indexes_map.items() if cid in card_ids_batch}
                batch_update_query = ''
                params: List[Any] = []
                returning_clause = 'RETURNING card_id'
                
                if status:
                    if current_batch_sort_indexes_map:
                        update_values = []
                        card_ids_with_indexes_batch = [cid for cid in card_ids_batch if cid in current_batch_sort_indexes_map]
                        if not card_ids_with_indexes_batch:
                            batch_update_query = f'''
                                UPDATE {self.table_name}
                                SET is_favorite = TRUE
                                WHERE user_id = $1 AND card_id = ANY($2::integer[])
                                  AND (is_favorite = FALSE OR is_favorite IS NULL)
                                {returning_clause};
                            '''
                            params = [user_id, card_ids_batch]
                        else:
                            for card_id_val in card_ids_with_indexes_batch:
                                update_values.append((card_id_val, current_batch_sort_indexes_map[card_id_val]))
                            value_placeholders = []
                            flat_params_batch = []
                            param_counter = 1
                            for c_id, s_idx in update_values:
                                value_placeholders.append(f'(${param_counter}::integer, ${param_counter + 1}::integer)')
                                flat_params_batch.extend([c_id, s_idx])
                                param_counter += 2
                            user_id_param_idx = param_counter
                            flat_params_batch.append(user_id)
                            any_clause_param_idx = param_counter + 1
                            flat_params_batch.append(card_ids_with_indexes_batch)
                            batch_update_query = f"""
                                WITH updates (card_id, new_sort_index) AS (
                                    VALUES {', '.join(value_placeholders)}
                                )
                                UPDATE {self.table_name} uc
                                SET is_favorite = TRUE, custom_sort_index = u.new_sort_index
                                FROM updates u
                                WHERE uc.user_id = ${user_id_param_idx} AND uc.card_id = u.card_id
                                  AND uc.card_id = ANY(${any_clause_param_idx}::integer[])
                                  AND (uc.is_favorite = FALSE OR uc.is_favorite IS NULL)
                                RETURNING uc.card_id;
                            """
                            params = flat_params_batch
                    else:
                        batch_update_query = f'''
                            UPDATE {self.table_name}
                            SET is_favorite = TRUE
                            WHERE user_id = $1 AND card_id = ANY($2::integer[])
                              AND (is_favorite = FALSE OR is_favorite IS NULL)
                            {returning_clause};
                        '''
                        params = [user_id, card_ids_batch]
                else:
                    batch_update_query = f'''
                        UPDATE {self.table_name}
                        SET is_favorite = FALSE, custom_sort_index = NULL
                        WHERE user_id = $1 AND card_id = ANY($2::integer[]) AND is_favorite = TRUE
                        {returning_clause};
                    '''
                    params = [user_id, card_ids_batch]
                
                if batch_update_query and params and card_ids_batch:
                    returned_rows = await self._fetch(batch_update_query, params, connection=conn_to_use)
                    if returned_rows:
                        batch_updated_count_from_rows = len(returned_rows)
                        total_updated_count += batch_updated_count_from_rows
                        for row in returned_rows:
                            changed_favorites_details.append((row['card_id'], delta))
                
                logger.info('Updated favorite status for %s cards in batch for user %s. Total updated: %s', len(card_ids_batch), user_id, total_updated_count)
            
            return {'updated_count': total_updated_count, 'changed_favorites': changed_favorites_details}
            
        except Exception as e:
            logger.error(f"批量設置最愛狀態失敗，用戶ID: {user_id}，卡片IDs: {len(card_ids)}, 錯誤: {str(e)}")
            raise DatabaseOperationError(f"批量設置最愛狀態時資料庫操作失敗: {str(e)}", original_exception=e)

    async def get_cards_info_for_selling_raw(self, user_id: int, favorite_condition_sql: str, quantity_condition_sql: str, sell_quantity_expr_sql: str, connection: asyncpg.Connection) -> List[Dict[str, Any]]:
        """
        (Async) 根據提供的 SQL 片段獲取準備賣出的卡片信息列表 (原子操作)。
        此方法假定在一個已啟動的事務中被調用。

        Args:
            user_id: 用戶 ID。
            favorite_condition_sql: 用於篩選最愛狀態的 SQL 條件字符串 (例如 "AND (uc.is_favorite = FALSE OR uc.is_favorite IS NULL)")。
            quantity_condition_sql: 用於篩選數量的 SQL 條件字符串 (例如 "uc.quantity > 1")。
            sell_quantity_expr_sql: 用於計算賣出數量的 SQL 表達式字符串 (例如 "(uc.quantity - 1)")。
            connection: 必須提供的 asyncpg 連線。

        Returns:
            一個包含卡片信息的字典列表，例如:
            [{'card_id': ..., 'quantity': ..., 'name': ..., 'rarity': ..., 'pool_type': ..., 'is_favorite': ..., 'sell_quantity': ...}, ...]
            如果沒有可賣出的卡片，返回空列表。
            
        Raises:
            ValueError: 如果未提供連接
            RuntimeError: 如果處理過程中發生錯誤
        """
        if connection is None:
            logger.error('[GACHA_REPO] get_cards_info_for_selling_raw: A valid database connection must be provided.')
            raise ValueError('A valid database connection must be provided for get_cards_info_for_selling_raw.')
        try:
            stats_query = f"\n            WITH card_stats AS (\n                SELECT\n                    uc.card_id,\n                    uc.quantity,\n                    mc.name,\n                    mc.rarity,\n                    mc.pool_type,\n                    uc.is_favorite,\n                    {sell_quantity_expr_sql} as sell_quantity\n                FROM {self.table_name} uc\n                JOIN gacha_master_cards mc ON uc.card_id = mc.card_id\n                WHERE uc.user_id = $1 AND {quantity_condition_sql} {favorite_condition_sql}\n            )\n            SELECT\n                jsonb_agg(jsonb_build_object(\n                    'card_id', card_id,\n                    'quantity', quantity, -- 原始數量\n                    'name', name,\n                    'rarity', rarity,\n                    'pool_type', pool_type,\n                    'is_favorite', is_favorite,\n                    'sell_quantity', sell_quantity -- 要賣出的數量\n                )) FILTER (WHERE sell_quantity > 0) as cards_info\n            FROM card_stats\n            "
            result = await connection.fetchrow(stats_query, user_id)
            if not result or not result['cards_info']:
                return []
            cards_info_str = result['cards_info']
            cards_info = json.loads(cards_info_str) if isinstance(cards_info_str, str) else []
            return cards_info
        except json.JSONDecodeError as e:
            logger.error('[GACHA] get_cards_info_for_selling_raw: 解析售賣卡片信息時JSON解碼失敗: %s', e, exc_info=True)
            raise RuntimeError(f'解析售賣卡片信息時JSON解碼失敗: {e}') from e
        except asyncpg.PostgresError as e:
            logger.error('[GACHA] get_cards_info_for_selling_raw: 獲取售賣卡片信息時資料庫操作出錯: %s', e, exc_info=True)
            raise RuntimeError(f'獲取售賣卡片信息時資料庫操作出錯: {e}') from e
        except Exception as e:
            logger.error('[GACHA] get_cards_info_for_selling_raw: 獲取售賣卡片信息時發生未知錯誤: %s', e, exc_info=True)
            raise RuntimeError(f'獲取售賣卡片信息時發生未知錯誤: {e}') from e