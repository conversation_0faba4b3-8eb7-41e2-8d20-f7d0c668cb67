import discord
from discord import app_commands
from discord.ext import commands
import logging
from typing import Optional, TYPE_CHECKING
from discord.ui import View, Button
from gacha.services.ui.game_display_service import GameDisplayService
from gacha.utils import interaction_utils
from gacha.views.games.mines_view import MinesModeSelectView
from gacha.views.games.mines_game_view import MinesGameView
from gacha.services.games.mines_service import MinesService
from gacha.services.core.economy_service import EconomyService
from gacha.views.embeds.games.mines_mode_select_embed_builder import (
    build_mode_selection_embed,
)
from gacha.views.embeds.games.mines_embed_builder import build_mines_game_embed
from gacha.views.handlers.mines_callback_handler import MinesCallbackHandler
from gacha.views.collection.collection_view.interaction_manager import (
    InteractionManager,
)
from utils.logger import logger
from gacha.utils.mines_constants import MIN_BET, MAX_BET
from gacha.utils.cog_error_handler import handle_gacha_error
from gacha.exceptions import (
    MinBetNotMetError, 
    InsufficientFundsError, 
    UserNotFoundError,
    GameSettlementError
)

if TYPE_CHECKING:
    from gacha.gacha_system import GachaBot


class MinesCog(commands.Cog):
    def __init__(
        self,
        bot: "GachaBot",
        mines_service: MinesService,
        economy_service: EconomyService,
        game_display_service: GameDisplayService,
    ):  # Inject services
        self.bot = bot
        self.mines_service = mines_service  # Store injected service
        self.economy_service = economy_service  # Store injected service
        self.game_display_service = game_display_service  # Store injected service
        logger.info("MinesCog initialized with services.")

    @app_commands.command(name="mines", description="開始一局尋寶礦區遊戲或查看說明")
    @app_commands.describe(
        mine_count="選擇地雷數量 (難度，可選)",
        bet_amount=f"您想下注的油幣金額 (可選，預設 {MIN_BET}，範圍 {MIN_BET}-{MAX_BET})",
    )
    @app_commands.choices(
        mine_count=[
            app_commands.Choice(name="🟢 簡單 (3 雷)", value=3),
            app_commands.Choice(name="🟡 中等 (7 雷)", value=7),
            app_commands.Choice(name="🔴 困難 (12 雷)", value=12),
        ]
    )
    async def mines_command(
        self,
        interaction: discord.Interaction,
        mine_count: Optional[app_commands.Choice[int]] = None,
        bet_amount: app_commands.Range[int, MIN_BET, MAX_BET] = MIN_BET,
    ):
        """處理 /mines 指令"""
        user_id = interaction.user.id
        final_bet = bet_amount
        player = interaction.user

        if mine_count is not None:
            selected_mine_count = mine_count.value
            try:
                if not interaction.response.is_done():
                    await interaction.response.defer(ephemeral=False, thinking=True)

                # MinesService.create_game 現在返回 MinesGame 實例或拋出異常
                new_game_instance = await self.mines_service.create_game(
                    user_id=user_id,
                    bet_amount=final_bet,
                    mine_count=selected_mine_count,
                )

                # 從 MinesGame 實例獲取 game_state
                game_state = new_game_instance.get_state_summary()
                # 如果 new_game_instance 為 None (理論上不應該發生，因為會拋異常)，
                # 或者 game_state 為 None，下面的邏輯會處理。
                # 但主要依賴異常捕獲來處理創建失敗。

                # 餘額獲取邏輯保持不變，已處理字典返回
                balance_data = await self.economy_service.get_balance(user_id)
                current_balance = balance_data.get("balance", 0)

                game_embed = build_mines_game_embed(
                    game_state, player, current_balance=current_balance
                )

                interaction_manager = InteractionManager()
                handler = MinesCallbackHandler(
                    interaction_manager=interaction_manager,
                    user_id=user_id,
                    mines_service=self.mines_service,
                    economy_service=self.economy_service,
                    game_display_service=self.game_display_service,
                    game_view=None, # game_view 將在下面設置
                )
                game_view = MinesGameView(
                    game_state=game_state, handler=handler, player=player
                )
                handler.game_view = game_view # 確保 handler 有 game_view 的引用

                message = await interaction.followup.send(
                    embed=game_embed, view=game_view, wait=True
                )
                game_view.interaction_message = message

            except (InsufficientFundsError, MinBetNotMetError, UserNotFoundError, GameSettlementError, ValueError) as specific_error:
                # 添加 ValueError 以匹配 MinesCallbackHandler 和服務層可能拋出的異常
                await handle_gacha_error(interaction, specific_error, '啟動尋寶礦區遊戲時')
            except Exception as e:
                logging.error(
                    f"Error during Mines game start process for user {user_id}: {e}",
                    exc_info=True,
                )
                # 通用錯誤處理保持不變
                error_message = "開始遊戲時發生意外錯誤，請稍後再試。"
                if interaction.response.is_done():
                    await interaction.followup.send(error_message, ephemeral=True)
                else:
                    await interaction_utils.safe_send_message(
                        interaction, error_message, ephemeral=True
                    )
        else:
            try:
                balance_data = await self.economy_service.get_balance(user_id)
                current_balance = balance_data.get("balance", 0)
            except UserNotFoundError as user_error:
                # 處理用戶不存在的情況
                await handle_gacha_error(interaction, user_error, '獲取用戶餘額時')
                return
            except Exception as e:
                logging.error(
                    f"Error getting balance for user {user_id}: {e}",
                    exc_info=True)
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        "無法獲取您的餘額，請稍後再試。", ephemeral=True
                    )
                else:
                    await interaction.followup.send(
                        "無法獲取您的餘額，請稍後再試。", ephemeral=True
                    )
                return

            try:
                embed = build_mode_selection_embed(
                    current_bet=final_bet,
                    current_balance=current_balance,
                    player=interaction.user,
                )
                view = MinesModeSelectView(
                    user_id=user_id,
                    current_bet=final_bet,
                    mines_service=self.mines_service,
                    economy_service=self.economy_service,
                    game_display_service=self.game_display_service,
                )
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        embed=embed, view=view, ephemeral=False
                    )
                    view.interaction_message = await interaction.original_response()
                else:
                    message = await interaction.followup.send(
                        embed=embed, view=view, wait=True
                    )
                    view.interaction_message = message

            except Exception as e:
                logging.error(
                    f"Error sending mode selection interface for user {user_id}: {e}",
                    exc_info=True,
                )
                await interaction_utils.safe_send_message(
                    interaction, "顯示模式選擇界面時發生錯誤。", ephemeral=True
                )


async def setup(bot: "GachaBot"):
    # 檢查 MinesService, EconomyService 和 GameDisplayService 是否已在 bot 上註冊
    mines_service = getattr(bot, "mines_service", None)
    economy_service = getattr(bot, "economy_service", None)
    game_display_service = getattr(bot, "game_display_service", None)

    if not mines_service:
        logger.error(
            "MinesService not found on bot. MinesCog will not be loaded.")
        return
    if not economy_service:
        logger.error(
            "EconomyService not found on bot. MinesCog will not be loaded.")
        return
    if not game_display_service:
        logger.error(
            "GameDisplayService not found on bot. MinesCog will not be loaded."
        )
        return

    # 傳遞服務實例給 MinesCog 構造函數
    await bot.add_cog(
        MinesCog(bot, mines_service, economy_service, game_display_service)
    )
    logger.info("MinesCog has been loaded with injected services.")
