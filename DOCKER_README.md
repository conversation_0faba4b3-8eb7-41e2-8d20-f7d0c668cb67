# DICKPK Discord Bot - Docker 部署指南

## 概述

此專案已經 Docker 化，可以避免受到 VPN 環境影響，並支持測試和正式環境的分離部署。

**重要說明：此配置使用你主機上現有的 PostgreSQL 和 Redis，無需遷移任何資料！**

## 環境要求

- Docker Desktop (Windows/Mac) 或 Docker Engine (Linux)
- Docker Compose v3.8+
- **主機上運行的 PostgreSQL 和 Redis 服務**

## 架構說明

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Docker 容器   │    │   主機 PostgreSQL │    │   主機 Redis    │
│                 │    │                 │    │                 │
│  Discord Bot    ├────┤  gacha_database │    │   localhost     │
│                 │    │  TEST (測試)     │    │   DB 0/1        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 檔案結構

```
├── Dockerfile                    # Docker 鏡像定義
├── docker-compose.yml           # 基礎 Bot 配置
├── docker-compose.prod.yml      # 正式環境覆蓋配置
├── docker-compose.test.yml      # 測試環境覆蓋配置
├── .dockerignore                # Docker 忽略檔案
├── docker-manage.bat            # 完整管理腳本 (推薦)
├── docker-start-prod.bat        # 快速啟動正式環境
├── docker-start-test.bat        # 快速啟動測試環境
└── docker-stop.bat              # 停止服務腳本
```

## 快速開始

### 前提條件
確保你的主機上 PostgreSQL 和 Redis 正在運行：
- PostgreSQL 運行在 `localhost:5432`
- Redis 運行在 `localhost:6379`

### 方法一：使用管理腳本 (推薦)

雙擊執行 `docker-manage.bat`，會出現選單：

```
======================================
     DICKPK Discord Bot - Docker 管理
======================================

請選擇操作:
1. 啟動正式環境
2. 啟動測試環境
3. 停止正式環境
4. 停止測試環境
5. 重新建置並啟動正式環境
6. 重新建置並啟動測試環境
7. 查看容器狀態
8. 查看日誌
9. 清理未使用的資源
0. 退出
```

### 方法二：使用快速啟動腳本

- **正式環境**: 雙擊 `docker-start-prod.bat`
- **測試環境**: 雙擊 `docker-start-test.bat`

### 方法三：手動命令

```bash
# 啟動正式環境
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 啟動測試環境
docker-compose -f docker-compose.yml -f docker-compose.test.yml up -d

# 停止服務
docker-compose -f docker-compose.yml -f docker-compose.prod.yml down
```

## 環境差異

### 正式環境
- **容器名稱**: `dickpk_bot_prod`
- **Discord Token**: 正式機器人 Token
- **資料庫名稱**: `gacha_database` (主機上的)
- **日誌級別**: `INFO`
- **Redis DB**: `0` (主機上的)
- **開發模式**: `false`

### 測試環境
- **容器名稱**: `dickpk_bot_test`
- **Discord Token**: 測試機器人 Token
- **資料庫名稱**: `TEST` (主機上的)
- **日誌級別**: `DEBUG`
- **Redis DB**: `1` (主機上的)
- **開發模式**: `true`

## 優勢

✅ **無需資料遷移** - 直接使用你現有的資料庫和 Redis  
✅ **VPN 隔離** - Bot 在容器內運行，避免 VPN 影響  
✅ **環境分離** - 測試和正式版本使用不同配置  
✅ **簡單部署** - 只需要容器化 Bot，資料庫保持不變  

## 常用操作

### 查看日誌
```bash
# 正式環境日誌
docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs -f bot

# 測試環境日誌
docker-compose -f docker-compose.yml -f docker-compose.test.yml logs -f bot
```

### 重新建置
```bash
# 重新建置正式環境
docker-compose -f docker-compose.yml -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### 進入容器
```bash
# 進入 Bot 容器
docker exec -it dickpk_bot_prod bash
docker exec -it dickpk_bot_test bash
```

## 故障排除

### 常見問題

1. **無法連接資料庫**: 確保主機上的 PostgreSQL 正在運行且可訪問
2. **無法連接 Redis**: 確保主機上的 Redis 正在運行
3. **權限問題**: 確保 Docker 有足夠權限訪問專案目錄

### 檢查主機服務狀態
```bash
# 檢查 PostgreSQL
netstat -an | findstr :5432

# 檢查 Redis
netstat -an | findstr :6379
```

### 清理資源
```bash
# 清理未使用的容器、網路、鏡像
docker system prune -f
```

## 從舊方式遷移

原來的啟動方式 → Docker 啟動方式：
- `運行.bat` → `docker-start-prod.bat` 或管理選單選項1
- `測試.bat` → `docker-start-test.bat` 或管理選單選項2

**完全無痛遷移，資料庫和 Redis 資料保持不變！**

## 注意事項

1. **確保主機服務運行**: PostgreSQL 和 Redis 必須在主機上正常運行
2. **防火牆設定**: 確保 Docker 可以訪問主機的 5432 和 6379 端口
3. **資料備份**: 雖然不需要遷移，但建議定期備份重要資料
4. **日誌管理**: 定期清理日誌檔案避免磁碟空間不足

## 支援

如有問題，請檢查：
1. Docker 服務是否正常運行
2. 網路連接是否正常
3. 資源使用情況
4. 容器日誌輸出 