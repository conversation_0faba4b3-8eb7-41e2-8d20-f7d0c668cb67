import discord
from discord import app_commands
from discord.ext import commands
from typing import Optional, Union, Any, TYPE_CHECKING, Callable, Coroutine, List, Dict
import functools
from decimal import Decimal, ROUND_HALF_UP
from gacha.utils import interaction_utils
import math
from utils.logger import logger
from gacha.services.market.market_view_service import MarketViewService
from gacha.views.market.market_views import StockListView, StockDetailView, SingleNewsView, CardInfoDetailView
from gacha.models.market_models import StockLifecycleStatus
from gacha.views.modals import BuyStockModal, SellStockModal
from gacha.exceptions import (
    MarketCardNotFoundError,
    MarketStockNotFoundError,
    MarketDataUnavailableError,
    MarketServiceUnavailableError,
    MarketSystemError,
    InsufficientStockQuantityError,
    StockTradingError
)
if TYPE_CHECKING:
    pass

def get_market_view_service(interaction: discord.Interaction) -> Optional[MarketViewService]:
    return getattr(interaction.client, 'market_view_service', None)

def get_stock_trading_service(interaction: discord.Interaction) -> Optional[Any]:
    return getattr(interaction.client, 'stock_trading_service', None)

def get_economy_service(interaction: discord.Interaction) -> Optional[Any]:
    return getattr(interaction.client, 'economy_service', None)

def ensure_market_service(error_message: str = '錯誤：相關市場服務目前不可用。'):
    def decorator(func: Callable[..., Coroutine[Any, Any, None]]):
        @functools.wraps(func)
        async def wrapper(self: 'MarketCog', interaction: discord.Interaction, *args, **kwargs):
            market_view_service_instance = get_market_view_service(interaction)
            if not market_view_service_instance and func.__name__ in ['cardinfo', 'news', 'stock']:
                raise MarketServiceUnavailableError(error_message)
            
            temp_attr_name = '_temp_mvs_instance'
            original_service_attr = getattr(self, temp_attr_name, None)
            if market_view_service_instance:
                setattr(self, temp_attr_name, market_view_service_instance)
            try:
                return await func(self, interaction, *args, **kwargs)
            finally:
                if market_view_service_instance:
                    if original_service_attr is not None:
                        setattr(self, temp_attr_name, original_service_attr)
                    elif hasattr(self, temp_attr_name):
                        delattr(self, temp_attr_name)
        return wrapper
    return decorator

class StockPortfolioSelect(discord.ui.Select):

    def __init__(self, holdings: List[Dict[str, Any]], view_instance: Any):
        self.parent_view = view_instance
        options = []
        if not holdings:
            options.append(discord.SelectOption(label='您沒有持倉', value='_no_holdings', default=True))
        else:
            for stock in holdings:
                status_suffix = ''
                stock_status_str = stock.get('lifecycle_status')
                if stock_status_str == StockLifecycleStatus.ST.value:
                    status_suffix = ' <a:Error:1371096622053724292>'
                elif stock_status_str == StockLifecycleStatus.DELISTED.value:
                    status_suffix = ' (已退市)'
                label = f"{stock['asset_symbol']} ({stock['asset_name']}{status_suffix})"
                description = f"持有: {stock['quantity']}股, 現價: {Decimal(str(stock['current_market_price'])):.2f}"
                options.append(discord.SelectOption(label=label, value=str(stock['asset_id']), description=description[:100]))
        super().__init__(placeholder='選擇要操作的股票...', min_values=1, max_values=1, options=options, custom_id='market_cog_portfolio_stock_select', disabled=not holdings)

    async def callback(self, interaction: discord.Interaction):
        if self.values[0] == '_no_holdings':
            await interaction.response.defer()
            return
        await interaction.response.defer()
        selected_asset_id = int(self.values[0])
        selected_stock = next((s for s in self.parent_view.all_holdings if s['asset_id'] == selected_asset_id), None)
        self.parent_view.selected_stock = selected_stock
        try:
            await self.parent_view._refresh_view_and_embed(interaction)
        except discord.NotFound:
            logger.warning('[StockPortfolioSelect] Original interaction message (ID: %s) not found for updating view.', self.parent_view.original_interaction.id)
            if not interaction.response.is_done():
                try:
                    await interaction.followup.send('更新視圖時發生錯誤：原始訊息未找到。', ephemeral=True)
                except discord.HTTPException:
                    pass
        except discord.HTTPException as e:
            logger.error('[StockPortfolioSelect] HTTPException updating view (Original Interaction ID: %s): %s %s', self.parent_view.original_interaction.id, e.status, e.text, exc_info=True)
            if not interaction.response.is_done():
                try:
                    await interaction.followup.send(f'更新視圖時發生HTTP錯誤: {e.status}', ephemeral=True)
                except discord.HTTPException:
                    pass
        except Exception as e:
            logger.error('[StockPortfolioSelect] Generic error updating view (Original Interaction ID: %s): %s', self.parent_view.original_interaction.id, e, exc_info=True)
            if not interaction.response.is_done():
                try:
                    await interaction.followup.send('更新視圖時發生未預期錯誤。', ephemeral=True)
                except discord.HTTPException:
                    pass

class PortfolioPaginatedView(discord.ui.View):
    ITEMS_PER_PAGE = 10

    def __init__(self, trading_service: Any, economy_service: Any, original_interaction: discord.Interaction, user_id: int, all_holdings: List[Dict[str, Any]], oil_balance: Decimal, total_portfolio_value: Decimal, overall_pnl: Decimal):
        super().__init__(timeout=300)
        self.trading_service = trading_service
        self.economy_service = economy_service
        self.original_interaction = original_interaction
        self.user_id = user_id
        self.all_holdings = all_holdings
        self.oil_balance = oil_balance
        self.total_portfolio_value = total_portfolio_value
        self.overall_pnl = overall_pnl
        self.current_page = 1
        self.total_pages = math.ceil(len(self.all_holdings) / self.ITEMS_PER_PAGE) if self.all_holdings else 1
        self.selected_stock: Optional[Dict[str, Any]] = None
        self.select_menu: Optional[StockPortfolioSelect] = None
        self.buy_button: Optional[discord.ui.Button] = None
        self.sell_button: Optional[discord.ui.Button] = None
        self.back_button: Optional[discord.ui.Button] = None
        self._build_view_components()

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """
        Checks if the user interacting with the view is the original user who invoked the command.
        """
        is_allowed = await interaction_utils.check_user_permission(interaction=interaction, expected_user_id=self.user_id, error_message_on_fail='您無法操作他人發起的投資組合介面。')
        return is_allowed

    def _build_view_components(self):
        self.clear_items()
        if self.total_pages > 1:
            first_page_btn = discord.ui.Button(label='⏪', style=discord.ButtonStyle.blurple, custom_id='m_pf_first', disabled=self.current_page == 1)
            first_page_btn.callback = self.go_to_first_page
            self.add_item(first_page_btn)
            prev_page_btn = discord.ui.Button(label='⬅️', style=discord.ButtonStyle.secondary, custom_id='m_pf_prev', disabled=self.current_page == 1)
            prev_page_btn.callback = self.go_to_previous_page
            self.add_item(prev_page_btn)
            page_indicator_btn = discord.ui.Button(label=f'{self.current_page}/{self.total_pages}', style=discord.ButtonStyle.grey, disabled=True, custom_id='m_pf_page_indicator')
            self.add_item(page_indicator_btn)
            next_page_btn = discord.ui.Button(label='➡️', style=discord.ButtonStyle.secondary, custom_id='m_pf_next', disabled=self.current_page >= self.total_pages)
            next_page_btn.callback = self.go_to_next_page
            self.add_item(next_page_btn)
            last_page_btn = discord.ui.Button(label='⏩', style=discord.ButtonStyle.blurple, custom_id='m_pf_last', disabled=self.current_page >= self.total_pages)
            last_page_btn.callback = self.go_to_last_page
            self.add_item(last_page_btn)
        self.select_menu = StockPortfolioSelect(self.all_holdings, self)
        self.add_item(self.select_menu)
        if self.selected_stock:
            asset_id = self.selected_stock['asset_id']
            asset_symbol = self.selected_stock['asset_symbol']
            self.buy_button = discord.ui.Button(label=f'買入更多 {asset_symbol}', style=discord.ButtonStyle.success, custom_id=f'm_pf_action_buy_{asset_id}')
            self.buy_button.callback = self.buy_more_callback
            self.add_item(self.buy_button)
            self.sell_button = discord.ui.Button(label=f'賣出部分 {asset_symbol}', style=discord.ButtonStyle.danger, custom_id=f'm_pf_action_sell_{asset_id}')
            self.sell_button.callback = self.sell_partial_callback
            self.add_item(self.sell_button)
            self.back_button = discord.ui.Button(label='清除選擇', style=discord.ButtonStyle.grey, custom_id='m_pf_action_clear_select')
            self.back_button.callback = self.clear_selection_callback
            self.add_item(self.back_button)

    async def _refresh_view_and_embed(self, interaction: Optional[discord.Interaction]=None):
        if interaction and (not interaction.response.is_done()):
            await interaction.response.defer()
        balance_data = await self.economy_service.get_balance(self.user_id)
        self.oil_balance = Decimal(str(balance_data.get('balance', 0))) if isinstance(balance_data, dict) else Decimal('0')
        portfolio_data_full = await self.trading_service.get_user_portfolio(self.user_id)
        self.all_holdings = portfolio_data_full.get('holdings', [])
        self.total_portfolio_value = Decimal(str(portfolio_data_full.get('total_portfolio_value_at_current_market_price', '0')))
        self.overall_pnl = Decimal(str(portfolio_data_full.get('overall_unrealized_pnl', '0')))
        self.total_pages = math.ceil(len(self.all_holdings) / self.ITEMS_PER_PAGE) if self.all_holdings else 1
        if self.current_page > self.total_pages:
            self.current_page = self.total_pages
        if self.current_page < 1:
            self.current_page = 1
        if self.selected_stock:
            selected_asset_id = self.selected_stock['asset_id']
            self.selected_stock = next((s for s in self.all_holdings if s['asset_id'] == selected_asset_id), None)
        start_index = (self.current_page - 1) * self.ITEMS_PER_PAGE
        end_index = start_index + self.ITEMS_PER_PAGE
        current_page_holdings_data = self.all_holdings[start_index:end_index]
        embed = MarketCog._build_portfolio_embed_static(user_display_name=self.original_interaction.user.display_name, oil_balance=self.oil_balance, current_page_holdings=current_page_holdings_data, total_portfolio_value=self.total_portfolio_value, overall_pnl=self.overall_pnl, current_page=self.current_page, total_pages=self.total_pages, total_holdings_count=len(self.all_holdings))
        self._build_view_components()
        await self.original_interaction.edit_original_response(embed=embed, view=self)

    async def buy_more_callback(self, interaction: discord.Interaction):
        if self.selected_stock and self.trading_service:
            stock_status_str = self.selected_stock.get('lifecycle_status')
            if stock_status_str == StockLifecycleStatus.DELISTED.value:
                await interaction.response.send_message(f"股票 {self.selected_stock['asset_symbol']} ({self.selected_stock['asset_name']}) 已退市，無法進行買入操作。", ephemeral=True)
                return
            modal = BuyStockModal(stock_symbol=self.selected_stock['asset_symbol'], current_price=Decimal(str(self.selected_stock['current_market_price'])), stock_trading_service=self.trading_service, on_trade_complete_callback=self.handle_trade_completion)
            await interaction.response.send_modal(modal)
        else:
            await interaction.response.send_message('請先選擇股票或交易服務不可用。', ephemeral=True)

    async def sell_partial_callback(self, interaction: discord.Interaction):
        if self.selected_stock and self.trading_service:
            stock_status_str = self.selected_stock.get('lifecycle_status')
            if stock_status_str == StockLifecycleStatus.DELISTED.value:
                await interaction.response.send_message(f"股票 {self.selected_stock['asset_symbol']} ({self.selected_stock['asset_name']}) 已退市，無法進行賣出操作。", ephemeral=True)
                return
            user_quantity = self.selected_stock.get('quantity', 0)
            modal = SellStockModal(stock_symbol=self.selected_stock['asset_symbol'], current_price=Decimal(str(self.selected_stock['current_market_price'])), user_id=self.user_id, stock_trading_service=self.trading_service, max_quantity=user_quantity, on_trade_complete_callback=self.handle_trade_completion)
            await interaction.response.send_modal(modal)
        else:
            await interaction.response.send_message('請先選擇股票或交易服務不可用。', ephemeral=True)

    async def clear_selection_callback(self, interaction: discord.Interaction):
        self.selected_stock = None
        await self._refresh_view_and_embed(interaction)

    async def handle_trade_completion(self, modal_interaction: discord.Interaction):
        if not modal_interaction.response.is_done():
            await modal_interaction.response.defer(ephemeral=True)
        await self._refresh_view_and_embed()

    async def go_to_first_page(self, interaction: discord.Interaction):
        if self.current_page != 1:
            self.current_page = 1
            await self._refresh_view_and_embed(interaction)
        elif not interaction.response.is_done():
            await interaction.response.defer()

    async def go_to_previous_page(self, interaction: discord.Interaction):
        if self.current_page > 1:
            self.current_page -= 1
            await self._refresh_view_and_embed(interaction)
        elif not interaction.response.is_done():
            await interaction.response.defer()

    async def go_to_next_page(self, interaction: discord.Interaction):
        if self.current_page < self.total_pages:
            self.current_page += 1
            await self._refresh_view_and_embed(interaction)
        elif not interaction.response.is_done():
            await interaction.response.defer()

    async def go_to_last_page(self, interaction: discord.Interaction):
        if self.current_page != self.total_pages:
            self.current_page = self.total_pages
            await self._refresh_view_and_embed(interaction)
        elif not interaction.response.is_done():
            await interaction.response.defer()

class MarketCog(commands.Cog, name='市場'):
    _temp_mvs_instance: Optional[MarketViewService] = None

    def __init__(self, bot: commands.Bot):
        self.bot = bot

    async def _handle_market_error(self, interaction: discord.Interaction, error: Exception, operation: str = "市場操作"):
        """統一處理市場系統錯誤"""
        if isinstance(error, MarketCardNotFoundError):
            error_embed = discord.Embed(
                title="❌ 找不到卡片",
                description=f"找不到查詢的卡片：`{error.card_query}`",
                color=discord.Color.red()
            )
            error_embed.add_field(
                name="💡 建議",
                value="• 請確認卡片ID是否正確\n• 嘗試使用卡片的完整名稱\n• 檢查是否有拼寫錯誤",
                inline=False
            )
            error_embed.set_footer(text="如果問題持續存在，請聯繫管理員")
            await interaction.followup.send(embed=error_embed, ephemeral=True)
            
        elif isinstance(error, MarketStockNotFoundError):
            error_embed = discord.Embed(
                title="❌ 找不到股票",
                description=f"找不到查詢的股票：`{error.stock_symbol}`",
                color=discord.Color.red()
            )
            error_embed.add_field(
                name="💡 建議",
                value="• 請確認股票代碼是否正確\n• 檢查股票是否已退市\n• 嘗試使用完整的股票名稱",
                inline=False
            )
            await interaction.followup.send(embed=error_embed, ephemeral=True)
            
        elif isinstance(error, MarketDataUnavailableError):
            await interaction.followup.send('📊 市場數據暫時無法獲取，請稍後再試', ephemeral=True)
            
        elif isinstance(error, MarketServiceUnavailableError):
            await interaction.followup.send('🔧 市場服務目前不可用，請稍後再試', ephemeral=True)
            
        elif isinstance(error, InsufficientStockQuantityError):
            await interaction.followup.send(f'📈 股票數量不足：需要 {error.required}，但您只有 {error.current}', ephemeral=True)
            
        elif isinstance(error, StockTradingError):
            await interaction.followup.send(f'💼 股票交易失敗：{str(error)}', ephemeral=True)
            
        elif isinstance(error, MarketSystemError):
            logger.error('Market system error in %s: %s', operation, error, exc_info=True)
            await interaction.followup.send(f'🔧 {operation}時發生系統錯誤，請稍後再試', ephemeral=True)
            
        else:
            # 未預期的錯誤
            logger.error('Unexpected error in %s: %s', operation, error, exc_info=True)
            await interaction.followup.send(f'❌ {operation}時發生未預期錯誤，請稍後再試', ephemeral=True)

    @app_commands.command(name='cardinfo', description='查看卡片的詳細市場信息。')
    @app_commands.describe(query='輸入卡片ID或卡片確切名稱')
    @ensure_market_service(error_message='錯誤：市場信息服務目前不可用。')
    async def cardinfo(self, interaction: discord.Interaction, query: str):
        await interaction.response.defer(thinking=True)
        
        try:
            market_view_service: MarketViewService = getattr(self, '_temp_mvs_instance')
            
            # 直接調用服務，讓異常往上拋
            card_data = await market_view_service.get_card_info_data(query)
            
            # 成功獲取數據，創建視圖
            view = CardInfoDetailView(
                initiating_interaction=interaction,
                bot_instance=self.bot,
                market_view_service=market_view_service,
                card_query=query
            )
            await view.send_initial_message(ephemeral=False)
            
        except (MarketCardNotFoundError, MarketStockNotFoundError, MarketDataUnavailableError, 
                MarketServiceUnavailableError, MarketSystemError) as e:
            await self._handle_market_error(interaction, e, "查看卡片信息")
        except Exception as e:
            await self._handle_market_error(interaction, e, "查看卡片信息")

    @app_commands.command(name='news', description='查看最新的市場新聞 (一頁一條，可篩選)。')
    @ensure_market_service(error_message='錯誤：新聞服務目前不可用。')
    async def news(self, interaction: discord.Interaction):
        await interaction.response.defer(thinking=True)
        
        try:
            market_view_service: MarketViewService = getattr(self, '_temp_mvs_instance')
            view = SingleNewsView(
                initiating_interaction=interaction,
                bot_instance=self.bot,
                market_view_service=market_view_service
            )
            await view.send_initial_message(ephemeral=False)
            
        except (MarketDataUnavailableError, MarketServiceUnavailableError, MarketSystemError) as e:
            await self._handle_market_error(interaction, e, "查看市場新聞")
        except Exception as e:
            await self._handle_market_error(interaction, e, "查看市場新聞")

    @app_commands.command(name='stock', description='查看股票市場行情或特定股票詳細信息。')
    @app_commands.describe(symbol='要查詢的股票代碼 (可選，留空則顯示列表)')
    @ensure_market_service(error_message='錯誤：股票市場服務目前不可用。')
    async def stock(self, interaction: discord.Interaction, symbol: Optional[str] = None):
        await interaction.response.defer(thinking=True)
        
        try:
            market_view_service: MarketViewService = getattr(self, '_temp_mvs_instance')
            
            if symbol:
                view = StockDetailView(
                    initiating_interaction=interaction,
                    bot_instance=self.bot,
                    market_view_service=market_view_service,
                    asset_symbol_or_id=symbol
                )
            else:
                view = StockListView(
                    initiating_interaction=interaction,
                    bot_instance=self.bot,
                    market_view_service=market_view_service
                )
            await view.send_initial_message(ephemeral=False)
            
        except (MarketStockNotFoundError, MarketDataUnavailableError, 
                MarketServiceUnavailableError, MarketSystemError) as e:
            await self._handle_market_error(interaction, e, "查看股票信息")
        except Exception as e:
            await self._handle_market_error(interaction, e, "查看股票信息")

    async def stock_symbol_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        choices = []
        trading_service = get_stock_trading_service(interaction)
        if not trading_service:
            logger.error('stock_symbol_autocomplete: StockTradingService not available.')
            return []
        try:
            asset_symbols = await trading_service.search_asset_symbols(current)
            for symbol_str in asset_symbols:
                choices.append(app_commands.Choice(name=symbol_str, value=symbol_str))
        except Exception as e:
            logger.error("Error in stock_symbol_autocomplete (current: '%s'): %s", current, e, exc_info=True)
            return []
        return choices

    @staticmethod
    def _build_portfolio_embed_static(user_display_name: str, oil_balance: Decimal, current_page_holdings: List[Dict[str, Any]], total_portfolio_value: Decimal, overall_pnl: Decimal, title_suffix: str='', current_page: int=1, total_pages: int=1, total_holdings_count: int=0) -> discord.Embed:
        title = f'{user_display_name} 的投資組合'
        if total_pages > 1:
            title += f' (第 {current_page}/{total_pages} 頁)'
        title += title_suffix
        embed = discord.Embed(title=title, color=discord.Color.purple())
        if total_holdings_count == 0:
            embed.add_field(name='股票持倉', value='您目前未持有任何股票。', inline=False)
        elif not current_page_holdings:
            embed.add_field(name='股票持倉', value='此頁沒有更多持倉股票。', inline=False)
        else:
            for stock_item in current_page_holdings:
                symbol = stock_item['asset_symbol']
                name = stock_item['asset_name']
                qty = stock_item['quantity']
                avg_buy = Decimal(str(stock_item['average_buy_price']))
                curr_price = Decimal(str(stock_item['current_market_price']))
                curr_value = Decimal(str(stock_item['current_value']))
                pnl_val = Decimal(str(stock_item['unrealized_pnl']))
                status_suffix = ''
                stock_status_str = stock_item.get('lifecycle_status')
                if stock_status_str == StockLifecycleStatus.ST.value:
                    status_suffix = ' <a:Error:1371096622053724292>'
                elif stock_status_str == StockLifecycleStatus.DELISTED.value:
                    status_suffix = ' (已退市)'
                pnl_str = f'{pnl_val:+.2f}'
                pnl_indicator = '🟢' if pnl_val > 0 else '🔴' if pnl_val < 0 else '⚪'
                emoji_to_use = '<a:Error:1371096622053724292>' if stock_status_str == StockLifecycleStatus.ST.value else '<a:my2:1370641774950875146>'
                field_name_stock = f'{emoji_to_use} **{symbol}** ({name}{status_suffix})'
                field_value_stock = f'<:ReplyCont:1357534065841930290> 持有: `{qty}` 股\n<:ReplyCont:1357534065841930290> 均買價: `{avg_buy:.2f}`\n<:ReplyCont:1357534065841930290> 現價: `{curr_price:.2f}`\n<:ReplyCont:1357534065841930290> 市值: `{curr_value:.2f}`\n<:Reply:1357534074830590143> 盈虧: {pnl_indicator} `{pnl_str}`'
                embed.add_field(name=field_name_stock, value=field_value_stock, inline=True)
        if total_holdings_count > 0 and current_page_holdings:
            embed.add_field(name='\u200b', value='\u200b', inline=False)
        total_assets = oil_balance + total_portfolio_value
        embed.add_field(name='市值/總資產', value=f'股票總市值: `{total_portfolio_value:.2f}`\n總資產估值: `{total_assets:.2f}`', inline=True)
        embed.add_field(name='可用油幣', value=f'`{int(oil_balance)}`', inline=True)
        pnl_color_indicator = '🟢' if overall_pnl > 0 else '🔴' if overall_pnl < 0 else '⚪'
        embed.add_field(name='總未實現盈虧', value=f'{pnl_color_indicator} `{overall_pnl:+.2f}`', inline=True)
        footer_text = '股票價格和市值會隨市場波動。'
        if title_suffix:
            footer_text += ' 數據已刷新。'
        embed.set_footer(text=footer_text)
        return embed

    @app_commands.command(name='portfolio', description='查看您的油幣和股票投資組合。')
    async def portfolio(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=False)
        user_id = interaction.user.id
        
        try:
            trading_service = get_stock_trading_service(interaction)
            economy_service = get_economy_service(interaction)
            
            if not trading_service or not economy_service:
                raise MarketServiceUnavailableError('投資組合服務目前不可用')
            
            balance_data = await economy_service.get_balance(user_id)
            oil_balance = Decimal(str(balance_data.get('balance', 0))) if isinstance(balance_data, dict) else Decimal('0')
            
            portfolio_data = await trading_service.get_user_portfolio(user_id)
            all_holdings = portfolio_data.get('holdings', [])
            total_portfolio_value = Decimal(str(portfolio_data.get('total_portfolio_value_at_current_market_price', '0')))
            overall_pnl = Decimal(str(portfolio_data.get('overall_unrealized_pnl', '0')))
            
            view = PortfolioPaginatedView(
                trading_service=trading_service,
                economy_service=economy_service,
                original_interaction=interaction,
                user_id=user_id,
                all_holdings=all_holdings,
                oil_balance=oil_balance,
                total_portfolio_value=total_portfolio_value,
                overall_pnl=overall_pnl
            )
            
            start_index = (view.current_page - 1) * view.ITEMS_PER_PAGE
            end_index = start_index + view.ITEMS_PER_PAGE
            current_page_holdings_data = all_holdings[start_index:end_index]
            
            embed = self._build_portfolio_embed_static(
                user_display_name=interaction.user.display_name,
                oil_balance=oil_balance,
                current_page_holdings=current_page_holdings_data,
                total_portfolio_value=total_portfolio_value,
                overall_pnl=overall_pnl,
                current_page=view.current_page,
                total_pages=view.total_pages,
                total_holdings_count=len(all_holdings)
            )
            
            await interaction.followup.send(embed=embed, view=view)
            
        except (MarketServiceUnavailableError, MarketSystemError) as e:
            await self._handle_market_error(interaction, e, "查看投資組合")
        except Exception as e:
            await self._handle_market_error(interaction, e, "查看投資組合")

async def setup(bot: commands.Bot):
    if not hasattr(bot, 'market_view_service'):
        logger.error('MarketCog: MarketViewService not found on bot. Cog will not be added.')
        return
    if not hasattr(bot, 'stock_trading_service'):
        logger.error('MarketCog: StockTradingService not found on bot. Cog will not be added.')
        return
    if not hasattr(bot, 'economy_service'):
        logger.error('MarketCog: EconomyService not found on bot. Cog will not be added.')
        return
    await bot.add_cog(MarketCog(bot))
    logger.info('MarketCog added successfully.')