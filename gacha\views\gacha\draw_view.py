"""
Gacha系統抽卡視圖
創建並格式化用於展示抽卡結果的Discord Embed
"""

from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Union, NamedTuple

import discord

from utils.operation_logger import OperationLogger
from utils.logger import logger

from gacha.models.models import Card, CardWithStatus
from gacha.services.core.wish_service import WishService
from .base_draw_view import BaseDrawView
from gacha.views.embeds.gacha.draw_embed_builder import DrawEmbedBuilder
from gacha.views.collection.favorite_component import (
    FavoriteComponent,
    FavoriteStateUpdatable,
)

# 定義一個簡單的包裝器，統一 current_card 的結構
class CurrentCardWrapper(NamedTuple):
    card: Optional[Card]
    is_favorite: bool

class DrawView(BaseDrawView, FavoriteStateUpdatable):
    """抽卡結果視圖，包含繼續抽卡和加入最愛的按鈕"""

    def __init__(
        self,
        user: discord.User,
        card: Union[Card, CardWithStatus],
        balance: int,
        is_new_card: bool = False,
        star_level: int = 0,
        is_wish: bool = False,
        is_favorite: bool = False,
        owner_count: int = 0,
        on_draw_callback=None,
        pool_types=None,
        pool_config_key=None,
    ):
        """初始化抽卡視圖

        參數:
            user: Discord用戶對象
            card: 抽到的卡片(可以是Card對象或CardWithStatus對象)
            balance: 用戶剩餘油幣
            is_new_card: 是否為新卡 (如果card是CardWithStatus對象，此參數會被覆蓋)
            star_level: 卡片星級 (如果card是CardWithStatus對象，此參數會被覆蓋)
            is_wish: 是否為許願卡 (如果card是CardWithStatus對象，此參數會被覆蓋)
            is_favorite: 是否為最愛卡 (如果card是CardWithStatus對象，此參數會被覆蓋)
            owner_count: 該卡片的擁有者數量
            on_draw_callback: 繼續抽卡的回調函數
            pool_types: 卡池類型列表
            pool_config_key: 卡池配置鍵
        """
        super().__init__(
            user=user,
            balance=balance,
            timeout=180,
            on_draw_callback=on_draw_callback,
            pool_types=pool_types,
            pool_config_key=pool_config_key,
        )

        card_info = self.extract_card_info(card)
        self.card: Card = card_info[0]
        self.is_new_card = card_info[1] if card_info[1] is not None else is_new_card
        self.star_level = card_info[2] if card_info[2] is not None else star_level
        self.is_wish = card_info[3] if card_info[3] is not None else is_wish
        self.is_favorite: bool = card_info[4] if card_info[4] is not None else is_favorite
        self.pool_type = card_info[5]
        self.card_id = self.card.card_id
        self.owner_count = owner_count

        self._add_buttons()

    @property
    def current_card(self) -> CurrentCardWrapper:
        """提供統一結構的當前卡片信息。"""
        return CurrentCardWrapper(card=self.card, is_favorite=self.is_favorite)

    def _add_buttons(self):
        """添加視圖按鈕"""
        draw_button = discord.ui.Button(
            label="繼續抽卡",
            style=discord.ButtonStyle.primary,
            custom_id="draw_again")
        draw_button.callback = self._draw_again_callback
        self.add_item(draw_button)

        multi_draw_button = discord.ui.Button(
            label="十連抽",
            style=discord.ButtonStyle.secondary,
            custom_id="multi_draw")
        multi_draw_button.callback = self._multi_draw_callback
        self.add_item(multi_draw_button)

        favorite_button = FavoriteComponent.create_initial_favorite_button(
            view=self,
            user_id=self.user.id,
            card_id=self.card_id,
            is_favorite=self.is_favorite,
            row=0,
            custom_id="toggle_favorite",
        )
        self.add_item(favorite_button)

    async def _draw_again_callback(self, interaction: discord.Interaction):
        """繼續抽卡按鈕回調"""
        await self._process_draw_callback(interaction, is_multi_draw=False)

    async def _multi_draw_callback(self, interaction: discord.Interaction):
        """十連抽按鈕回調"""
        await self._process_draw_callback(interaction, is_multi_draw=True)

    async def get_current_page_embed(
        self, interaction: Optional[discord.Interaction] = None
    ) -> discord.Embed:
        """獲取當前卡片的嵌入，與FavoriteComponent兼容

        參數:
            interaction: 可選的 Discord interaction 物件。

        返回:
            discord.Embed: 當前卡片的嵌入
        """
        # 如果 self.interaction 存在 (例如在 View 初始化時傳入)，則使用它
        # 否則，使用方法參數傳入的 interaction (通常來自回呼)
        current_interaction = (
            interaction
            if interaction is not None
            else getattr(self, "interaction", None)
        )

        builder = DrawEmbedBuilder(
            user=self.user,
            card=self.card,
            balance=self.balance,
            is_new_card=self.is_new_card,
            pool_type=self.pool_type,
            is_wish=self.is_wish,
            star_level=self.star_level,
            is_favorite=self.is_favorite,
            owner_count=self.owner_count,
            interaction=current_interaction,  # 傳遞 interaction
        )
        return builder.build_embed()

    def update_favorite_state(self, card_id: int, is_favorite: bool) -> bool:
        """實現FavoriteStateUpdatable介面，更新最愛狀態

        參數:
            card_id: 卡片ID
            is_favorite: 新的最愛狀態

        返回:
            bool: 是否成功更新
        """
        if self.card_id == card_id:
            self.is_favorite = is_favorite
            return True
        return False
