import time
import traceback
from typing import List, Optional
import discord
from utils.logger import logger
from gacha.models.filters import CollectionFilters
from gacha.models.models import UserCard
from gacha.views.collection.favorite_component import FavoriteStateUpdatable
from gacha.utils.interaction_utils import check_user_permission
from .base_pagination import BasePaginationJumpModal, BasePaginationView
from .gacha_services import GachaServices
from .view_state import CollectionViewState
from .improved_button_manager import ImprovedButtonManager
from .button_factory import ButtonFactory
from .handlers.card_sell_handler import CardSellHandler
from .handlers.card_sort_handler import CardSortHandler
from .handlers.card_favorite_handler import CardFavoriteHandler
from .handlers.card_enhance_handler import CardEnhanceHandler
from .handlers.card_description_handler import CardDescriptionHandler
from .collection_embed_builder import CollectionEmbedBuilder
from .collection_data_manager import CollectionDataManager
import functools
from .utils import get_card_state as get_view_card_state_util

class CollectionView(BasePaginationView, FavoriteStateUpdatable):
    """卡冊分頁視圖 (主類 - Asyncpg 版本)"""

    def __init__(self, user: discord.User, cards: List[UserCard], current_page: int, total_pages: int, total_cards: int, unique_cards: int, sort_by: str, sort_order: str, services: GachaServices, filters: Optional[CollectionFilters]=None, interaction: Optional[discord.Interaction]=None):
        """初始化卡冊分頁視圖 (重構版本)"""
        super().__init__(user=user, current_page=current_page, total_pages=total_pages, timeout=120)
        self.interaction = interaction
        self.services = services
        self.cards = cards
        self.total_cards = total_cards
        self.unique_cards = unique_cards
        self.sort_by = sort_by
        self.sort_order = sort_order
        self.CARDS_PER_PAGE = services.collection.CARDS_PER_PAGE
        self.filters = filters if filters is not None else CollectionFilters()
        self.state = CollectionViewState()
        self._sync_current_card()
        self.state.user_nickname = self.user.display_name
        self.state.user_avatar_url = self.user.display_avatar.url if hasattr(self.user, 'display_avatar') and self.user.display_avatar else None
        self.state.has_duplicates = self.total_cards > self.unique_cards
        self.data_manager = CollectionDataManager(services, user.id)
        self.sell_handler = CardSellHandler(self)
        self.sort_handler = CardSortHandler(self)
        self.favorite_handler = CardFavoriteHandler(self)
        self.enhance_handler = CardEnhanceHandler(self)
        self.description_handler = CardDescriptionHandler(self)
        self.embed_builder = CollectionEmbedBuilder(self)
        
        # --- 預先創建所有按鈕實例 --- (移到 ButtonManager 初始化之前)
        self._create_all_buttons()

        # --- 然後初始化依賴這些按鈕的 ButtonManager ---
        self.button_manager = ImprovedButtonManager(self)

        # --- 異步初始化中添加初始按鈕 ---

    async def async_init(self):
        """異步初始化方法，添加初始狀態的按鈕"""
        # 初始狀態下顯示主模式按鈕
        self.clear_items() # 清空以防萬一
        self._add_precreated_pagination_buttons()
        await self.button_manager.add_buttons() # 添加主模式功能按鈕
        await self._update_buttons() # 刷新所有按鈕的初始狀態

    async def _update_buttons(self):
        """僅刷新現有按鈕的狀態 (用於同模式翻頁)"""
        # 1. 刷新功能按鈕狀態 (調用 ButtonManager 的新方法)
        await self.button_manager.refresh_functional_buttons()

        # 2. 直接更新分頁按鈕狀態
        self._refresh_button_states() # 使用基類的邏輯更新分頁按鈕 disable 狀態

        # 注意：不再 clear_items 或 add_item

    async def get_current_page_embed(self) -> discord.Embed:
        """獲取當前頁的Embed"""
        self.state.user_nickname = self.user.display_name
        if self.state.current_card and self.state.current_card.card_id:
            self.state.current_card_dynamic_price_details = None
        else:
            self.state.current_card_dynamic_price_details = None
        return self.embed_builder.build_embed()

    async def _fetch_and_apply_page_data(self, page: int) -> bool:
        """獲取指定頁面的數據並更新視圖的核心狀態。"""
        try:
            effective_sort_by = self.sort_by
            effective_sort_order = self.sort_order
            
            data_fetch_start = time.time()
            cards, total_cards, unique_cards, total_pages = await self.data_manager.get_page_data(
                page=page, 
                sort_by=effective_sort_by, 
                sort_order=effective_sort_order, 
                filters=self.filters
            )
            data_fetch_time = time.time() - data_fetch_start

            self.cards = cards
            self.total_cards = total_cards
            self.unique_cards = unique_cards
            self.current_page = page
            self.total_pages = total_pages
            self.state.has_duplicates = total_cards > unique_cards
            self._sync_current_card()

            cards_count = len(cards) if cards else 0
            logger.debug(
                '[GACHA][PAGE_DATA] Fetched for page %s: time=%.3fs, cards_count=%s, total_pages=%s, sort:%s %s',
                page, data_fetch_time, cards_count, total_pages, effective_sort_by, effective_sort_order
            )
            try:
                card_ids_and_indices = [(c.card_id, getattr(c, 'custom_sort_index', 'N/A')) for c in cards] if cards else []
                logger.debug('[GACHA][PAGE_DATA_DETAILS] Fetched cards for page %s: %s', page, card_ids_and_indices)
            except Exception as log_err:
                logger.error('[GACHA][PAGE_DATA_DETAILS] Error preparing/logging fetched cards data: %s', log_err, exc_info=True)
            return True
        except Exception as e:
            logger.error('[GACHA][PAGE_DATA] Failed to fetch/apply data for page %s: %s', page, e, exc_info=True)
            return False

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """(Async) 更新頁面內容 - 使用數據管理器獲取頁面數據"""
        overall_start_time = time.time()
        try:
            # 1. 獲取並應用頁面數據和狀態
            if not await self._fetch_and_apply_page_data(page):
                await self._handle_page_error(interaction, "獲取卡冊數據時發生錯誤。")
                return False

            # 2. 更新UI組件 (按鈕和Embed)
            await self._update_buttons()
            embed = await self.get_current_page_embed()

            # 3. 更新Discord消息
            message_update_start_time = time.time()
            update_success = await self._try_update_message(interaction, embed)
            message_update_duration = time.time() - message_update_start_time
            logger.debug(
                '[GACHA][PAGE_MSG_UPDATE] Attempt for page %s: success=%s, time=%.3fs',
                page, update_success, message_update_duration
            )
            
            total_duration = time.time() - overall_start_time
            logger.info(
                '[GACHA][PAGE_UPDATE] Completed for page %s: success=%s, total_time=%.3fs',
                page, update_success, total_duration
            )
            return update_success

        except Exception as e: # 捕獲 _update_buttons, get_current_page_embed 等的未知異常
            error_traceback = traceback.format_exc()
            logger.error(
                '[GACHA][PAGE] Unhandled error during page update for page %s: %s\\n%s',
                page, e, error_traceback
            )
            await self._handle_page_error(interaction, f"更新卡冊頁面時發生意外錯誤: {e}")
            return False

    async def _handle_page_error(self, interaction: discord.Interaction, error_message: str):
        """(Async) 處理頁面更新錯誤"""
        error_embed = discord.Embed(title='載入卡冊失敗', description=f'出現錯誤: {error_message}', color=discord.Color.red())
        error_embed.set_footer(text='請聯絡管理員或重試')
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=error_embed, ephemeral=True)
            else:
                await interaction.followup.send(embed=error_embed, ephemeral=True)
        except Exception as send_error:
            logger.error('Failed to send page error message: %s', send_error, exc_info=True)

    async def _send_error_followup(self, interaction: discord.Interaction, error_content: str):
        """嘗試向用戶發送一個臨時的錯誤回應。"""
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(error_content, ephemeral=True)
            else:
                await interaction.followup.send(error_content, ephemeral=True)
        except Exception as send_error:
            logger.error('[GACHA][UPDATE] Failed to send error followup message: %s', send_error, exc_info=True)

    async def _send_initial_message(self, interaction: discord.Interaction, embed: discord.Embed) -> bool:
        """當 self.message 不存在時，發送初始消息。"""
        logger.debug('[GACHA][UPDATE] self.message not set. Attempting to send initial message.')
        try:
            if not interaction.response.is_done():
                # 如果交互尚未響應，嘗試 edit_message
                # 這通常意味著這是一個按鈕回調，我們正在編輯原始消息存根
                await interaction.response.edit_message(embed=embed, view=self)
                self.message = await interaction.original_response()
            else:
                # 如果交互已響應 (例如，在 defer 之後，或者是一個命令的後續操作)
                followup_message = await interaction.followup.send(embed=embed, view=self, wait=True)
                self.message = followup_message
            logger.debug('[GACHA][UPDATE] Successfully sent initial message and set self.message.')
            return True
        except Exception as e:
            logger.error('[GACHA][UPDATE] Failed to send initial message: %s', e, exc_info=True)
            # 使用之前定義的 _send_error_followup 來避免重複代碼
            await self._send_error_followup(interaction, f"嘗試顯示卡冊時發生錯誤: {e}")
            return False

    async def _handle_edit_not_found(self, interaction: discord.Interaction, embed: discord.Embed):
        """處理 self.message.edit() 拋出 discord.NotFound 的情況。"""
        message_id_for_log = self.message.id if self.message else "Unknown"
        logger.warning('[GACHA][UPDATE] Message to edit (ID: %s) not found. Sending new message as followup.', message_id_for_log)
        try:
            # 消息未找到，嘗試通過 followup 發送新消息作為替代
            # 提示用戶原始消息可能已被刪除
            followup_message = await interaction.followup.send(
                content='無法更新原始卡冊消息，它可能已被刪除。將顯示一個新的卡冊消息。',
                embed=embed, 
                view=self, 
                ephemeral=True, # 保持ephemeral以避免打擾
                wait=True
            )
            self.message = followup_message # 更新 self.message 為新的 followup 消息
            logger.debug('[GACHA][UPDATE] Successfully sent replacement message after NotFound.')
        except Exception as send_error:
            logger.error('[GACHA][UPDATE] Failed to send replacement message after NotFound: %s', send_error, exc_info=True)
            await self._send_error_followup(interaction, "嘗試更新卡冊時，原始消息未找到且無法發送替代消息。")

    async def _try_update_message(self, interaction: discord.Interaction, embed: discord.Embed) -> bool:
        """(Async) 嘗試使用儲存的 message 物件更新卡冊訊息"""
        if not self.message:
            # 如果沒有現有消息，嘗試發送初始消息
            return await self._send_initial_message(interaction, embed)

        # 如果 self.message 存在，嘗試編輯它
        try:
            edit_start_time = time.time()
            await self.message.edit(embed=embed, view=self)
            edit_duration = time.time() - edit_start_time
            logger.debug('[GACHA][UPDATE] Successfully edited message ID: %s, time: %ss', self.message.id, edit_duration)
            return True
        except discord.NotFound:
            # 處理消息未找到的情況，嘗試發送一個新的替換消息
            await self._handle_edit_not_found(interaction, embed)
            return False # 原始編輯操作失敗
        except discord.HTTPException as http_e:
            logger.error('[GACHA][UPDATE] Failed to edit message due to HTTPException: %s', http_e, exc_info=True)
            error_text = f'更新卡冊時發生 Discord API 錯誤: {http_e.status}'
            if hasattr(http_e, 'code') and http_e.code: # code 可能不存在
                error_text += f' (code: {http_e.code})'
            if hasattr(http_e, 'text') and http_e.text: # text 可能為空
                 error_text += f' - {http_e.text}'
            await self._send_error_followup(interaction, error_text)
            return False
        except Exception as e:
            logger.error('[GACHA][UPDATE] Failed to edit message due to general error: %s', e, exc_info=True)
            await self._send_error_followup(interaction, f"更新卡冊時發生內部錯誤: {e}")
            return False

    def _clear_page_cache(self, reason: str=''):
        """清除與此視圖關聯的數據管理器緩存"""
        if hasattr(self, 'data_manager') and self.data_manager:
            self.data_manager.clear_cache(reason)
        else:
            logger.warning('[GACHA][CACHE] Attempted to clear cache, but data_manager does not exist.')

    def _create_jump_modal(self):
        """創建跳頁輸入框"""
        return BasePaginationJumpModal(self, title='跳轉到指定卡片頁')

    async def _apply_mode_specific_buttons(self):
        """清除現有功能按鈕，並根據當前視圖狀態應用新的按鈕佈局和分頁按鈕。"""
        self.clear_items()  # 清除所有按鈕，包括分頁按鈕
        self._add_precreated_pagination_buttons()  # 重新添加分頁按鈕
        await self.button_manager.add_buttons()  # 根據當前模式添加功能按鈕
        await self._update_buttons()  # 刷新所有按鈕的狀態（包括分頁和功能按鈕）

    async def function_button_callback(self, interaction: discord.Interaction):
        """功能按鈕回調，切換到功能模式"""
        self.state.enter_function_mode()
        await self._apply_mode_specific_buttons()
        # 只需要 edit view 即可，不需要重新 update_page
        await interaction.response.edit_message(view=self)

    async def back_to_main_callback(self, interaction: discord.Interaction):
        """返回主視圖按鈕回調，切換回主模式"""
        self.state.exit_to_main_mode()
        await self._apply_mode_specific_buttons()

        # 檢查是否需要重置排序並刷新頁面 (如果排序邏輯只在主模式外，可能需要刷新)
        needs_page_update = False
        if self.sort_by != 'rarity' or self.sort_order != 'desc':
             self.sort_by = 'rarity'
             self.sort_order = 'desc'
             self._clear_page_cache('返回主模式重置排序')
             needs_page_update = True

        if needs_page_update:
             if not interaction.response.is_done():
                 await interaction.response.defer()
             await self._update_page(self.current_page, interaction) # 更新頁面以應用排序
        else:
             # 如果不需要更新頁面，只需編輯視圖
             await interaction.response.edit_message(view=self)

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Checks if the interacting user is the owner of the view."""
        error_message = '這不是你的收藏視圖，無法操作。'
        is_owner = await check_user_permission(interaction, self.user.id, error_message)
        if not is_owner:
            logger.warning('User %s attempted to interact with a collection view owned by %s.', interaction.user.id, self.user.id)
        return is_owner

    async def _check_cards_available(self, interaction: discord.Interaction, error_message: str='當前頁面沒有卡片') -> bool:
        """檢查當前頁面是否有卡片 - 統一調用utils中的方法"""
        from .utils import check_cards_available
        return await check_cards_available(self, interaction, error_message)

    async def on_timeout(self):
        """超時處理"""
        try:
            await super().on_timeout()
        except Exception as e:
            logger.error('[TIMEOUT] 視圖超時處理時出錯: %s', e, exc_info=True)

    def update_favorite_state(self, card_id: int, is_favorite: bool) -> bool:
        """實現FavoriteStateUpdatable介面，更新最愛狀態

        參數:
            card_id: 卡片ID
            is_favorite: 新的最愛狀態

        返回:
            bool: 是否成功更新
        """
        if not hasattr(self, 'cards') or not self.cards:
            return False
        updated = False
        for user_card in self.cards:
            if hasattr(user_card, 'card') and hasattr(user_card.card, 'card_id') and (user_card.card.card_id == card_id):
                if hasattr(user_card, 'is_favorite'):
                    user_card.is_favorite = is_favorite
                    updated = True
        return updated

    @property
    def current_card(self) -> Optional[UserCard]:
        """統一的當前卡片訪問接口

        這是系統中所有組件獲取當前卡片的標準方式。
        所有處理器都應使用此屬性來訪問當前卡片。

        Returns:
            Optional[UserCard]: 當前卡片，若無卡片則返回 None
        """
        return self.state.current_card

    def _sync_current_card(self) -> None:
        """同步當前卡片狀態

        在頁面數據變更時調用此方法，確保當前卡片狀態正確。
        這是維護狀態一致性的關鍵方法。
        """
        self.state.current_card = self.cards[0] if self.cards else None
        self.state.user_nickname = self.user.display_name
        self.state.user_avatar_url = self.user.display_avatar.url if hasattr(self.user, 'display_avatar') and self.user.display_avatar else None
        self.state.has_duplicates = self.total_cards > self.unique_cards

    def _create_all_buttons(self):
        """預先創建視圖中所有可能用到的按鈕實例及其回調。"""
        
        # --- 分頁按鈕 (這些按鈕的回調通常在 BasePaginationView 中處理或通過 super() 調用) ---
        # self.first_page_button = ... (假設已在基類或他處處理)
        # self.prev_page_button = ...
        # self.next_page_button = ...
        # self.last_page_button = ...
        # self.jump_button = ...

        # --- 主模式按鈕 ---
        initial_card_state = get_view_card_state_util(self)

        self.favorite_button_main = ButtonFactory.create_favorite_button(
            is_favorite=initial_card_state.is_favorite if initial_card_state.is_available else False,
            row=1 
        )
        # 修改回调设置
        self.favorite_button_main.callback = functools.partial(
            self.favorite_handler.handle_single_favorite_toggle,
            button=self.favorite_button_main
        )

        self.sell_one_button_main = ButtonFactory.create_sell_button(has_cards=initial_card_state.is_available, row=1)
        self.sell_one_button_main.callback = self.sell_handler.sell_one_card_callback

        self.sell_all_button_main = ButtonFactory.create_sell_all_button(has_cards=initial_card_state.is_available, row=1)
        self.sell_all_button_main.callback = self.sell_handler.sell_all_current_card_callback
        
        self.function_button = ButtonFactory.create_function_button(has_cards=initial_card_state.is_available, row=1)
        self.function_button.callback = self.function_button_callback

        # --- 功能模式按鈕 (非排序模式) ---
        self.batch_favorite_button = ButtonFactory.create_batch_favorite_button(has_cards=initial_card_state.is_available, row=1)
        self.batch_favorite_button.callback = self.favorite_handler.batch_favorite_callback

        self.batch_unfavorite_button = ButtonFactory.create_batch_unfavorite_button(has_cards=initial_card_state.is_available, row=1)
        self.batch_unfavorite_button.callback = self.favorite_handler.batch_unfavorite_callback
        
        self.back_to_main_button = ButtonFactory.create_back_button(row=1) 
        self.back_to_main_button.callback = self.back_to_main_callback

        self.enhance_button = ButtonFactory.create_enhance_button(
            has_cards=initial_card_state.is_available, 
            has_duplicates=initial_card_state.has_duplicates, 
            row=2
        )
        self.enhance_button.callback = self.enhance_handler.enhance_option_callback

        self.sort_button = ButtonFactory.create_sort_mode_button(
            has_cards=initial_card_state.is_available, 
            is_favorite=initial_card_state.is_favorite, 
            row=2
        )
        self.sort_button.callback = self.sort_handler.sort_mode_callback
        
        self.description_button = ButtonFactory.create_description_button(has_cards=initial_card_state.is_available, row=2)
        self.description_button.callback = self.description_handler.handle_set_description_button

        # --- 排序模式按鈕 ---
        self.move_top_button = ButtonFactory.create_move_to_top_button(has_cards=initial_card_state.is_available, row=2)
        self.move_top_button.callback = self.sort_handler.move_to_top_callback

        self.move_up_button = ButtonFactory.create_move_up_button(has_cards=initial_card_state.is_available, row=2)
        self.move_up_button.callback = self.sort_handler.move_up_callback

        self.move_down_button = ButtonFactory.create_move_down_button(has_cards=initial_card_state.is_available, row=2)
        self.move_down_button.callback = self.sort_handler.move_down_callback

        self.move_bottom_button = ButtonFactory.create_move_to_bottom_button(has_cards=initial_card_state.is_available, row=2)
        self.move_bottom_button.callback = self.sort_handler.move_to_bottom_callback

        self.move_position_button = ButtonFactory.create_move_to_position_button(has_cards=initial_card_state.is_available, row=3)
        self.move_position_button.callback = self.sort_handler.move_to_position_callback

        self.back_to_function_button = ButtonFactory.create_back_to_function_button(row=3)
        self.back_to_function_button.callback = self.sort_handler.back_to_function_callback
        
    def _add_precreated_pagination_buttons(self):
        """添加預創建的分頁按鈕 (如果它们在self中定义)"""
        self.add_item(self.btn_first)
        self.add_item(self.btn_prev)
        self.add_item(self.btn_jump) # 這個是頁數指示器和跳轉按鈕
        self.add_item(self.btn_next)
        self.add_item(self.btn_last)

__all__ = ['CollectionView']