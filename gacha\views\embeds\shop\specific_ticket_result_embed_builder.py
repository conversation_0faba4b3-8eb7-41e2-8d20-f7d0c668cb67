from __future__ import annotations
import discord
from typing import List, Dict, Any, TYPE_CHECKING, Optional
from collections import Counter

from gacha.models.models import Card
from gacha.constants import RarityLevel
from gacha.views import utils as view_utils
from gacha.app_config import config_service
from utils.logger import logger

if TYPE_CHECKING:
    from gacha.models.models import Card

RESULT_EMBED_COLOR = discord.Color.green()


def build_specific_ticket_result_embed(
        interaction: discord.Interaction,
        ticket_name: str,
        exchanged_cards: List[Card]) -> discord.Embed:
    """
    構建指定券兌換結果的 Embed。

    Args:
        interaction: Discord 交互對象。
        ticket_name: 券的顯示名稱。
        exchanged_cards: 實際獲得的 Card 物件列表。

    Returns:
        discord.Embed: 構建好的 Embed 對象。
    """
    user_display_name = interaction.user.display_name
    # 使用原始列表長度作為總數
    num_granted = len(exchanged_cards)

    title = f"{user_display_name} 的【{ticket_name}】兌換結果"
    description_lines = [f"您成功兌換並獲得了以下 **{num_granted}** 張卡片:"]

    if not exchanged_cards:
        description_lines.append("本次兌換沒有選擇任何卡片。")
    else:
        # 統計每張卡片的數量
        card_count = {}
        for card in exchanged_cards:
            card_id = card.card_id
            if card_id in card_count:
                card_count[card_id]["count"] += 1
            else:
                card_count[card_id] = {"card": card, "count": 1}

        card_lines = []
        for card_data in card_count.values():
            card = card_data["card"]
            count = card_data["count"]
            card_name = card.name
            card_series = card.series
            rarity_enum = card.rarity
            pool_type = card.pool_type or "main"

            rarity_emoji = view_utils.get_encyclopedia_rarity_emoji(
                rarity_enum, pool_type
            )

            # 添加數量顯示，只有當數量大於1時
            count_display = f"  x{count}" if count > 1 else ""
            line1 = f"{rarity_emoji} **{card_name}{count_display}**"
            line2 = f"<:ReplyCont:1357534065841930290> *{card_series}*"
            card_lines.append(f"{line1}\n{line2}")

        description_lines.append("\n\n".join(card_lines))

    description = "\n".join(description_lines)

    embed = discord.Embed(
        title=title, description=description, color=RESULT_EMBED_COLOR
    )

    embed.set_footer(text=f"由 {interaction.user.display_name} 完成兌換")

    return embed
