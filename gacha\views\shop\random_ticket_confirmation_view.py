import discord
from discord.ext import commands
from utils.logger import logger
from gacha.utils.interaction_utils import check_user_permission
from gacha.services.shop.shop_service import ShopService
from gacha.views.embeds.shop.random_ticket_confirmation_embed_builder import build_random_ticket_confirmation_embed
from gacha.views.shop.random_ticket_result_view import RandomTicketResultView
from gacha.views.embeds.shop.random_ticket_result_embed_builder import build_random_ticket_result_embed
from gacha.exceptions import InvalidSessionError, ShopSystemError, InsufficientBalanceError

class RandomTicketConfirmationView(discord.ui.View):

    def __init__(self, cog: commands.Cog, session_id: str, timeout=180):
        super().__init__(timeout=timeout)
        self.cog = cog
        self.session_id = session_id
        self.shop_service: ShopService = cog.shop_service

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        try:
            session_data = self.shop_service.get_session_data(self.session_id)
            return await check_user_permission(interaction, session_data.user_id, '您無法操作此確認介面。')
        except InvalidSessionError:
            logger.warning('RandomTicketConfirmationView: No session data found for session %s during interaction check.', self.session_id)
            await interaction.response.send_message('此兌換會話已失效或不存在。', ephemeral=True)
            return False

    @discord.ui.button(label='✅ 確認兌換', style=discord.ButtonStyle.green)
    async def confirm_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.defer(ephemeral=True)
        
        try:
            # 獲取會話數據
            initial_session_data = self.shop_service.get_session_data(self.session_id)
            ticket_name = initial_session_data.ticket_definition.display_name
            quantity_used = initial_session_data.total_quantity_to_redeem
            ticket_def_pool_type = getattr(initial_session_data.ticket_definition, 'pool_type', None)
            
            # 處理兌換確認
            result = await self.shop_service.process_random_ticket_confirmation(self.session_id)
            drawn_cards_objects = result.get('drawn_cards', [])
            success_message = result.get('message', '兌換處理完畢。')
            num_drawn_cards = len(drawn_cards_objects)
            
            # 根據抽到的卡片數量顯示不同的結果
            if num_drawn_cards == 0:
                # 沒有抽到卡片
                result_embed = build_random_ticket_result_embed(
                    interaction=interaction, 
                    ticket_name=ticket_name, 
                    quantity_used=quantity_used, 
                    displayed_card=None, 
                    current_page=0, 
                    total_pages=0, 
                    total_cards_drawn=0, 
                    ticket_definition_pool_type=ticket_def_pool_type
                )
                
                try:
                    if interaction.message:
                        await interaction.message.edit(embed=result_embed, view=None)
                    else:
                        logger.warning('ConfirmButton: interaction.message was None for 0 cards. Sending followup.')
                        await interaction.followup.send(embed=result_embed, ephemeral=False)
                except discord.HTTPException as e:
                    logger.error('ConfirmButton: Failed to edit message for 0 cards. Error: %s', e, exc_info=True)
                    await interaction.followup.send(embed=result_embed, ephemeral=False)
                    
            elif num_drawn_cards == 1:
                # 只抽到一張卡片
                result_embed = build_random_ticket_result_embed(
                    interaction=interaction, 
                    ticket_name=ticket_name, 
                    quantity_used=quantity_used, 
                    displayed_card=drawn_cards_objects[0], 
                    current_page=1, 
                    total_pages=1, 
                    total_cards_drawn=1, 
                    ticket_definition_pool_type=ticket_def_pool_type
                )
                
                try:
                    if interaction.message:
                        await interaction.message.edit(embed=result_embed, view=None)
                    else:
                        logger.warning('ConfirmButton: interaction.message was None for 1 card. Sending followup.')
                        await interaction.followup.send(embed=result_embed, ephemeral=False)
                except discord.HTTPException as e:
                    logger.error('ConfirmButton: Failed to edit message for 1 card. Error: %s', e, exc_info=True)
                    await interaction.followup.send(embed=result_embed, ephemeral=False)
                    
            else:
                # 抽到多張卡片
                result_pagination_view = RandomTicketResultView(
                    original_interaction=interaction, 
                    ticket_name=ticket_name, 
                    quantity_used=quantity_used, 
                    drawn_cards=drawn_cards_objects, 
                    ticket_definition_pool_type=ticket_def_pool_type
                )
                
                initial_embed, configured_view = await result_pagination_view.prepare_initial_message_payload(interaction)
                
                if initial_embed and configured_view:
                    try:
                        if interaction.message:
                            await interaction.message.edit(embed=initial_embed, view=configured_view)
                        else:
                            logger.warning('ConfirmButton: interaction.message was None for multi-card. Sending followup.')
                            await interaction.followup.send(embed=initial_embed, view=configured_view, ephemeral=False)
                    except discord.HTTPException as e:
                        logger.error('ConfirmButton: Failed to edit message for multi-card view. Error: %s', e, exc_info=True)
                        await interaction.followup.send(embed=initial_embed, view=configured_view, ephemeral=False)
                else:
                    logger.error('ConfirmButton: Failed to get initial embed/view from RandomTicketResultView. User: %s', interaction.user.id)
                    await interaction.followup.send('處理您的多卡片兌換結果時發生錯誤，請稍後重試或聯繫管理員。', ephemeral=True)
                    
        except InvalidSessionError:
            # 會話已失效
            logger.warning('RandomTicketConfirmationView: Session %s disappeared before confirm_button for user %s.', self.session_id, interaction.user.id)
            try:
                if interaction.message:
                    await interaction.message.edit(content='抱歉，您的兌換會話已失效，無法處理。', view=None, embed=None)
                else:
                    await interaction.followup.send('抱歉，您的兌換會話已失效，無法處理。', ephemeral=True)
            except discord.HTTPException as e:
                logger.error('ConfirmButton: Failed to edit message for expired session. Error: %s', e)
                await interaction.followup.send('抱歉，您的兌換會話已失效，無法處理。', ephemeral=True)
        except InsufficientBalanceError as e:
            # 餘額不足錯誤
            logger.error('RandomTicketConfirmationView: Insufficient balance for user %s during confirmation for session %s: %s', 
                        interaction.user.id, self.session_id, e, exc_info=True)
            await interaction.followup.send(f'兌換失敗: {str(e)}', ephemeral=True)
        except ShopSystemError as e:
            # 其他商店系統錯誤
            logger.error('RandomTicketConfirmationView: Shop system error during confirmation for session %s: %s', self.session_id, e, exc_info=True)
            await interaction.followup.send(f'兌換處理時發生錯誤: {str(e)}', ephemeral=True)
        except Exception as e:
            # 其他未預期的錯誤
            logger.error('RandomTicketConfirmationView: Unexpected error during confirmation for session %s: %s', self.session_id, e, exc_info=True)
            await interaction.followup.send('處理兌換時發生意外錯誤，請稍後重試或聯繫管理員。', ephemeral=True)
            
        self.stop()

    @discord.ui.button(label='❌ 取消', style=discord.ButtonStyle.grey)
    async def cancel_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.defer(ephemeral=True)
        
        # 禁用所有按鈕
        for item in self.children:
            if isinstance(item, discord.ui.Button):
                item.disabled = True
                
        # 取消兌換會話
        await self.cancel_exchange_session()
        
        try:
            if interaction.message:
                await interaction.message.edit(content='兌換已取消。', embed=None, view=self)
            else:
                await interaction.followup.send('兌換已取消。', ephemeral=True)
        except discord.HTTPException as e:
            logger.warning('CancelButton: Failed to edit message for session %s: %s', self.session_id, e)
            await interaction.followup.send('兌換已取消 (訊息更新失敗)。', ephemeral=True)
            
        self.stop()

    async def cancel_exchange_session(self):
        """調用 ShopService 取消對應的 session"""
        try:
            await self.shop_service.cancel_exchange_session(session_id=self.session_id)
            logger.info('成功取消兌換會話: %s', self.session_id)
        except InvalidSessionError:
            logger.info('取消兌換會話 %s 時，該會話已不存在', self.session_id)
        except Exception as e:
            logger.error('取消兌換會話 %s 時發生錯誤: %s', self.session_id, e, exc_info=True)

    async def on_timeout(self):
        # 禁用所有按鈕
        for item in self.children:
            if isinstance(item, discord.ui.Button):
                item.disabled = True
                
        # 取消會話
        await self.cancel_exchange_session()
        logger.info('隨機券兌換確認 View (session: %s) 超時，會話已嘗試取消。', self.session_id)
        self.stop()