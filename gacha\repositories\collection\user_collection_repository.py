"""
Gacha系統用戶收藏存儲庫 (Asyncpg 版本)
負責管理用戶卡片收藏的基本操作和查詢
"""
import asyncpg
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
import time
import redis.asyncio as redis
from database.base_repository import BaseRepository
from gacha.models.models import UserCard, Card, SeriesCollection
from utils.logger import logger
from gacha.models.filters import CollectionFilters
from gacha.constants import RarityLevel
from gacha.repositories.sql_queries import GET_USER_CARD_POSITION_SQL
from gacha.exceptions import RecordNotFoundError, DatabaseOperationError, CacheError, DataMappingError
from .user_collection_stats_mixin import UserCollectionStatsMixin
from .user_collection_features_mixin import UserCollectionFeaturesMixin
from .user_collection_sort_mixin import UserCollectionSortMixin
from gacha.repositories.collection.user_collection_bulk_mixin import UserCollectionBulkMixin
from typing import TypedDict, Literal
SellOperationDetailType = Literal['sell_specific_quantity_of_card', 'sell_all_for_card', 'sell_leaving_one_for_card']

class CardSellOperation(TypedDict):
    card_id: int
    operation_type: SellOperationDetailType
    current_quantity_owned: int
    quantity_to_sell: Optional[int]

class UserCollectionRepository(UserCollectionStatsMixin, UserCollectionFeaturesMixin, UserCollectionSortMixin, UserCollectionBulkMixin, BaseRepository):
    """用戶收藏存儲庫 (Asyncpg 版本)，管理 gacha_user_collections 表"""
    OWNER_COUNT_CACHE_PREFIX = 'gacha:owner_count:'
    OWNER_COUNT_CACHE_TTL = timedelta(hours=1)

    def __init__(self, pool: asyncpg.Pool, redis_client: redis.Redis):
        """初始化用戶收藏存儲庫 (Asyncpg 版本)

        參數:
            pool: asyncpg 連接池實例
            redis_client: Redis 客戶端實例
        """
        super().__init__(pool)
        self.redis = redis_client
        self.table_name = 'gacha_user_collections'
    _BASE_USER_CARD_FIELDS = '\n        uc.id, uc.user_id, uc.card_id, uc.quantity, uc.first_acquired,\n        uc.last_acquired, uc.is_favorite, uc.star_level, uc.custom_sort_index,\n        mc.name, mc.series, mc.rarity, mc.image_url, mc.description,\n        mc.current_market_sell_price, mc.creation_date, mc.pool_type, mc.is_active,\n        mc.original_id,\n        cd.description AS custom_description, cd.user_id AS description_user_id\n    '

    def _get_base_user_card_select_query(self) -> str:
        """返回獲取用戶卡片基礎信息的 SELECT 和 FROM/JOIN 子句"""
        return f'\n            SELECT {self._BASE_USER_CARD_FIELDS}\n            FROM {self.table_name} uc\n            JOIN gacha_master_cards mc ON uc.card_id = mc.card_id\n            LEFT JOIN gacha_card_descriptions cd ON uc.card_id = cd.card_id AND cd.user_id = uc.user_id\n        '

    def _create_user_card_from_result(self, result: asyncpg.Record) -> UserCard:
        """從查詢結果創建UserCard對象 (內部輔助) - 失敗時拋出異常"""
        try:
            card = Card.from_db_record(result)
            user_card = UserCard(id=result['id'], user_id=result['user_id'], card_id=result['card_id'], card=card, quantity=result['quantity'], first_acquired=result['first_acquired'], last_acquired=result['last_acquired'], is_favorite=result.get('is_favorite', False), star_level=result.get('star_level', 0), custom_sort_index=result.get('custom_sort_index'))
            user_card.custom_description = result.get('custom_description')
            user_card.description_user_id = result.get('description_user_id')
            return user_card
        except KeyError as e:
            logger.error('[GACHA] _create_user_card: 創建UserCard時數據中缺少鍵 %s: %s', e, result, exc_info=True)
            raise DataMappingError(f'創建UserCard對象時數據欄位缺失: {e}') from e
        except ValueError as e:
            logger.error('[GACHA] _create_user_card: 創建Card實例時出錯: %s', e, exc_info=True)
            raise DataMappingError(f'創建UserCard依賴的Card對象時出錯: {e}') from e
        except Exception as e:
            logger.error('[GACHA] _create_user_card: 創建UserCard對象時發生未知錯誤: %s', e, exc_info=True)
            raise DataMappingError(f'創建UserCard對象時發生未知錯誤: {e}') from e

    async def _set_custom_sort_index_field(self, user_id: int, card_id: int, sort_index: Optional[int], connection: Optional[asyncpg.Connection]=None) -> bool:
        """(Async) 設置卡片的 custom_sort_index 欄位 - DatabaseOperationError on failure"""
        query = f'\n            UPDATE {self.table_name}\n            SET custom_sort_index = $1\n            WHERE user_id = $2 AND card_id = $3\n        '
        status = await self._execute(query, (sort_index, user_id, card_id), connection=connection)
        if status != 'UPDATE 1':
            raise DatabaseOperationError(f'設置 custom_sort_index 操作未影響任何行: user_id={user_id}, card_id={card_id}, sort_index={sort_index}. Status: {status}')
        return True

    async def set_custom_sort_index(self, user_id: int, card_id: int, sort_index: Optional[int], connection: Optional[asyncpg.Connection]=None) -> bool:
        """(Async) 公共方法：設置卡片的 custom_sort_index 欄位。"""
        return await self._set_custom_sort_index_field(user_id, card_id, sort_index, connection)

    async def add_card_and_check_new(self, user_id: int, card_id: int, connection: Optional[asyncpg.Connection]=None) -> Dict[str, Any]:
        """(Async) 向用戶收藏中添加卡片，同時檢查該卡片是否為新卡，並在需要時清除快取."""
        now = datetime.now()
        query = f'\n            INSERT INTO {self.table_name} (user_id, card_id, quantity, first_acquired, last_acquired, star_level)\n            VALUES ($1, $2, 1, $3, $4, 0)\n            ON CONFLICT (user_id, card_id) DO UPDATE\n            SET quantity = {self.table_name}.quantity + 1,\n                last_acquired = EXCLUDED.last_acquired\n            RETURNING (xmax = 0) AS is_new, star_level, quantity\n        '
        result = await self._fetchrow(query, (user_id, card_id, now, now), connection=connection)
        if not result:
            raise RecordNotFoundError(f'添加/更新卡片操作失敗，資料庫未返回結果: user_id={user_id}, card_id={card_id}')
        is_new = result.get('is_new')
        quantity = result.get('quantity')
        star_level = result.get('star_level')
        if is_new is None:
            logger.error("[GACHA_INTEGRITY_ERROR] add_card_and_check_new: 'is_new' field was None in RETURNING clause for user_id=%s, card_id=%s. Result: %s", user_id, card_id, result)
            raise DatabaseOperationError(f"添加/更新卡片時 'is_new' 欄位返回結果異常 for user_id={user_id}, card_id={card_id}")
        if quantity is None:
            logger.error("[GACHA_INTEGRITY_ERROR] add_card_and_check_new: 'quantity' field was None in RETURNING clause for user_id=%s, card_id=%s. Result: %s", user_id, card_id, result)
            raise DatabaseOperationError(f"添加/更新卡片時 'quantity' 欄位返回結果異常 for user_id={user_id}, card_id={card_id}")
        if star_level is None:
            logger.error("[GACHA_INTEGRITY_ERROR] add_card_and_check_new: 'star_level' field was None in RETURNING clause for user_id=%s, card_id=%s. Result: %s", user_id, card_id, result)
            raise DatabaseOperationError(f"添加/更新卡片時 'star_level' 欄位返回結果異常 for user_id={user_id}, card_id={card_id}")
        if is_new:
            try:
                await self._invalidate_owner_count_cache(card_id)
            except CacheError as ce:
                logger.warning('Cache invalidation failed for new card %s for user %s but continuing: %s', card_id, user_id, ce)
            except Exception as e_cache:
                logger.warning('Generic cache invalidation error for new card %s for user %s but continuing: %s', card_id, user_id, e_cache)
        return {'is_new': is_new, 'star_level': star_level, 'quantity': quantity}

    async def bulk_add_cards_and_check_new(self, user_id: int, card_ids: List[int], connection: Optional[asyncpg.Connection]=None) -> Dict[int, Dict[str, Any]]:
        """(Async) 批量向用戶收藏中添加卡片，同時檢查是否為新卡，並在需要時清除快取."""
        if not card_ids:
            return {}
        card_id_counts = {}
        for card_id_val in card_ids:
            card_id_counts[card_id_val] = card_id_counts.get(card_id_val, 0) + 1
        now = datetime.now()
        results_dict: Dict[int, Dict[str, Any]] = {}
        newly_added_card_ids: List[int] = []
        input_data_for_query = [(cid, qty) for cid, qty in card_id_counts.items()]
        card_id_list = [item[0] for item in input_data_for_query]
        quantity_to_add_list = [item[1] for item in input_data_for_query]
        query = f'\n            WITH input_cards AS (\n                SELECT\n                    u.card_id,\n                    u.quantity_to_add\n                FROM unnest($2::integer[], $3::integer[]) AS u(card_id, quantity_to_add)\n            ),\n            upserted AS (\n                INSERT INTO {self.table_name} (user_id, card_id, quantity, first_acquired, last_acquired, star_level)\n                SELECT $1, ic.card_id, ic.quantity_to_add, $4, $4, 0\n                FROM input_cards ic\n                ON CONFLICT (user_id, card_id) DO UPDATE\n                SET quantity = {self.table_name}.quantity + EXCLUDED.quantity,\n                    last_acquired = EXCLUDED.last_acquired\n                RETURNING card_id, (xmax = 0) AS is_new, star_level, quantity\n            )\n            SELECT card_id, is_new, star_level, quantity FROM upserted;\n        '
        rows = await self._fetch(query, (user_id, card_id_list, quantity_to_add_list, now), connection=connection)
        if not rows and card_ids:
            raise RecordNotFoundError(f'批量添加/更新卡片操作未返回任何行 for user_id={user_id}')
        for row in rows:
            op_card_id = row['card_id']
            is_new = row['is_new']
            results_dict[op_card_id] = {'is_new': is_new, 'star_level': row['star_level'], 'quantity': row['quantity']}
            if is_new:
                newly_added_card_ids.append(op_card_id)
        if newly_added_card_ids:
            try:
                await self._invalidate_owner_count_cache_batch(newly_added_card_ids)
            except CacheError as ce:
                logger.warning('Batch cache invalidation failed for new cards user %s but continuing: %s', user_id, ce)
            except Exception as e_cache:
                logger.warning('Generic batch cache invalidation error for new cards user %s but continuing: %s', user_id, e_cache)
        return results_dict

    async def _remove_logic(self, conn: asyncpg.Connection, user_id: int, card_id: int, quantity_to_remove: int) -> Dict[str, Any]:
        check_query = f'\n            SELECT quantity FROM {self.table_name}\n            WHERE user_id = $1 AND card_id = $2\n            FOR UPDATE\n        '
        check_result = await conn.fetchrow(check_query, user_id, card_id)
        if not check_result:
            raise RecordNotFoundError(f'找不到要移除的卡片: user_id={user_id}, card_id={card_id}')
        current_quantity = check_result['quantity']
        if current_quantity < quantity_to_remove:
            raise ValueError(f'卡片數量不足: user_id={user_id}, card_id={card_id}, need={quantity_to_remove}, have={current_quantity}')
        remaining_quantity = current_quantity - quantity_to_remove
        was_fully_removed = False
        if remaining_quantity > 0:
            update_query = f'\n                UPDATE {self.table_name}\n                SET quantity = $1\n                WHERE user_id = $2 AND card_id = $3\n            '
            status = await conn.execute(update_query, remaining_quantity, user_id, card_id)
            if status != 'UPDATE 1':
                raise DatabaseOperationError(f'更新卡片數量失敗 for user={user_id}, card={card_id}, status={status}')
        else:
            delete_query = f'\n                DELETE FROM {self.table_name}\n                WHERE user_id = $1 AND card_id = $2\n            '
            status = await conn.execute(delete_query, user_id, card_id)
            if status == 'DELETE 1':
                was_fully_removed = True
                try:
                    await self._invalidate_owner_count_cache(card_id)
                except CacheError as ce:
                    logger.warning('Cache invalidation failed for removed card %s for user %s but continuing: %s', card_id, user_id, ce)
                except Exception as e_cache:
                    logger.warning('Generic cache invalidation error for removed card %s for user %s but continuing: %s', card_id, user_id, e_cache)
            else:
                raise DatabaseOperationError(f'刪除卡片記錄失敗 for user={user_id}, card={card_id}, status={status}')
        return {'remaining_quantity': remaining_quantity, 'was_fully_removed': was_fully_removed}

    async def remove_card(self, user_id: int, card_id: int, quantity_to_remove: int=1, connection: Optional[asyncpg.Connection]=None) -> Dict[str, Any]:
        """(Async) 從用戶收藏中移除指定數量的卡片。返回包含剩餘數量和是否完全移除標記的字典，或在失敗時拋出異常。
        如果提供了 connection，則在該連接上執行，否則創建新事務。
        """
        if quantity_to_remove <= 0:
            raise ValueError(f'無效的移除數量: {quantity_to_remove}')
        if connection:
            return await self._remove_logic(connection, user_id, card_id, quantity_to_remove)
        else:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    return await self._remove_logic(conn, user_id, card_id, quantity_to_remove)

    async def get_user_card(self, user_id: int, card_id: int, connection: Optional[asyncpg.Connection]=None) -> UserCard:
        """(Async) 根據用戶ID和卡片ID獲取單個用戶卡片 - 未找到則拋出 RecordNotFoundError"""
        base_query = self._get_base_user_card_select_query()
        query = f'\n            {base_query}\n            WHERE uc.user_id = $1 AND uc.card_id = $2\n        '
        result = await self._fetchrow(query, (user_id, card_id), connection=connection)
        if not result:
            raise RecordNotFoundError(f'找不到用戶卡片: user_id={user_id}, card_id={card_id}')
        return self._create_user_card_from_result(result)

    async def get_user_card_for_update(self, user_id: int, card_id: int, connection: asyncpg.Connection) -> Optional[UserCard]:
        """
        (Async) 根據用戶ID和卡片ID獲取單個用戶卡片，並鎖定該行以供更新。
        必須在一個事務中調用。如果未找到卡片，則返回 None。
        """
        if connection is None:
            raise ValueError('get_user_card_for_update 必須在事務中並提供一個有效的 asyncpg Connection。')
        base_query = self._get_base_user_card_select_query()
        query = f'\n            {base_query}\n            WHERE uc.user_id = $1 AND uc.card_id = $2\n            FOR UPDATE OF uc\n        '
        result = await connection.fetchrow(query, user_id, card_id)
        if not result:
            return None
        return self._create_user_card_from_result(result)

    def _process_paginated_results(self, results: List[asyncpg.Record]) -> List[UserCard]:
        """處理分頁查詢結果 (內部輔助) - 失敗時內部會拋出 DataMappingError"""
        user_cards = []
        if not results:
            return user_cards
        for result_row in results:
            user_cards.append(self._create_user_card_from_result(result_row))
        return user_cards

    def _build_filter_conditions(self, user_id: int, filters: Optional[CollectionFilters]=None) -> Tuple[str, List[Any]]:
        """(Asyncpg) 構建篩選條件和參數，使用 $N 佔位符"""
        clauses = []
        params = []
        param_index = 1
        clauses.append(f'uc.user_id = ${param_index}')
        params.append(user_id)
        param_index += 1
        if filters is None:
            return (' AND '.join(clauses), params)
        if filters.pool_type:
            clauses.append(f'mc.pool_type = ${param_index}')
            params.append(filters.pool_type)
            param_index += 1
        if filters.rarity_in:
            clauses.append(f'mc.rarity = ANY(${param_index}::integer[])')
            params.append(filters.rarity_in)
            param_index += 1
        if filters.rarity_not_in:
            clauses.append(f'mc.rarity <> ALL(${param_index}::integer[])')
            params.append(filters.rarity_not_in)
            param_index += 1
        if filters.series:
            clauses.append(f'mc.series = ${param_index}')
            params.append(filters.series)
            param_index += 1
        if filters.series_not_in:
            clauses.append(f'mc.series <> ALL(${param_index}::text[])')
            params.append(filters.series_not_in)
            param_index += 1
        if filters.quantity_greater_than is not None and filters.quantity_greater_than > 0:
            clauses.append(f'uc.quantity > ${param_index}')
            params.append(filters.quantity_greater_than)
            param_index += 1
        if filters.only_duplicates:
            clauses.append('uc.quantity > 1')
        if filters.card_id is not None:
            clauses.append(f'uc.card_id = ${param_index}')
            params.append(filters.card_id)
            param_index += 1
        if filters.card_name is not None:
            clauses.append(f'mc.name ILIKE ${param_index}')
            params.append(f'%{filters.card_name}%')
            param_index += 1
        if filters.extra_filters and 'exclude_favorites' in filters.extra_filters:
            if filters.extra_filters['exclude_favorites']:
                clauses.append('(uc.is_favorite = FALSE OR uc.is_favorite IS NULL)')
        return (' AND '.join(clauses), params)

    async def get_user_cards_paginated(self, user_id: int, page: int = 1, page_size: int = 9,
                                     sort_by: str = 'rarity', sort_order: str = 'desc',
                                     filters: Optional[CollectionFilters] = None,
                                     connection: Optional[asyncpg.Connection] = None, *,
                                     pre_fetched_unique_cards: Optional[int] = None,
                                     pre_fetched_total_cards: Optional[int] = None) -> Dict[str, Any]:
        """(Async) 獲取用戶卡片分頁數據. Can accept pre-fetched stats to avoid re-querying them."""
        if filters is None:
            filters = CollectionFilters()
        built_where_clause, built_params = self._build_filter_conditions(user_id, filters)
        unique_cards_stat: int
        total_cards_stat: int
        if pre_fetched_unique_cards is not None and pre_fetched_total_cards is not None:
            logger.debug('[GACHA_REPO_PAGINATED] Using pre-fetched stats: unique=%s, total=%s', pre_fetched_unique_cards, pre_fetched_total_cards)
            unique_cards_stat = pre_fetched_unique_cards
            total_cards_stat = pre_fetched_total_cards
        elif pre_fetched_unique_cards is not None:
            logger.debug('[GACHA_REPO_PAGINATED] Using pre-fetched unique_cards: %s. Total quantity will be queried.', pre_fetched_unique_cards)
            unique_cards_stat = pre_fetched_unique_cards
            stats = await self.get_user_collection_stats(user_id, filters, where_clause=built_where_clause, params=built_params)
            total_cards_stat = stats.get('total_quantity', 0)
        else:
            logger.debug('[GACHA_REPO_PAGINATED] No/incomplete pre-fetched stats. Querying all stats.')
            stats = await self.get_user_collection_stats(user_id, filters, where_clause=built_where_clause, params=built_params)
            total_cards_stat = stats.get('total_quantity', 0)
            unique_cards_stat = stats.get('unique_cards', 0)
        if unique_cards_stat == 0:
            return {'cards': [], 'total_pages': 0, 'current_page': 1, 'total_cards': 0, 'unique_cards': 0}
        total_pages = max(1, (unique_cards_stat + page_size - 1) // page_size)
        current_page = max(1, min(page, total_pages))
        cards = await self._fetch_user_cards_for_page(user_id, current_page, page_size, sort_by, sort_order, filters, where_clause=built_where_clause, params_list=built_params, connection=connection)
        return {'cards': cards, 'total_pages': total_pages, 'current_page': current_page, 'total_cards': total_cards_stat, 'unique_cards': unique_cards_stat}

    async def _fetch_user_cards_for_page(self, user_id: int, page: int, page_size: int, sort_by: str, sort_order: str, filters: CollectionFilters, *, where_clause: str, params_list: List[Any], connection: Optional[asyncpg.Connection]=None) -> List[UserCard]:
        """(Internal Async) Fetches a single page of user cards based on criteria."""
        offset = (page - 1) * page_size
        query_params = list(params_list)
        limit_placeholder = f'${len(query_params) + 1}'
        offset_placeholder = f'${len(query_params) + 2}'
        base_query = self._get_base_user_card_select_query()
        query = f'\n            {base_query}\n            WHERE {where_clause}\n            ORDER BY {self._build_sorting_clause(sort_by, sort_order)}\n            LIMIT {limit_placeholder} OFFSET {offset_placeholder}\n        '
        query_params.extend([page_size, offset])
        results = await self._fetch(query, query_params, connection=connection)
        user_cards = self._process_paginated_results(results)
        await self._attach_owner_counts_to_cards(user_cards)
        return user_cards

    async def get_filtered_card_ids(self, user_id: int, filters: Optional[CollectionFilters]=None) -> List[int]:
        """(Async) 獲取經過過濾的用戶卡片ID列表."""
        if filters is None:
            filters = CollectionFilters()
        where_clause, params = self._build_filter_conditions(user_id, filters)
        query = f'\n            SELECT uc.card_id\n            FROM {self.table_name} uc\n            JOIN gacha_master_cards mc ON uc.card_id = mc.card_id\n            WHERE {where_clause}\n        '
        results = await self._fetch(query, params)
        return [row['card_id'] for row in results]

    async def get_filtered_user_cards(self, user_id: int, filters: Optional[CollectionFilters]=None, connection: Optional[asyncpg.Connection]=None) -> List[UserCard]:
        """(Async) 獲取經過過濾的完整用戶卡片對象列表 (包含 Card 信息)."""
        if filters is None:
            filters = CollectionFilters()
        where_clause, params = self._build_filter_conditions(user_id, filters)
        base_query = self._get_base_user_card_select_query()
        query = f'\n            {base_query}\n            WHERE {where_clause}\n        '
        results = await self._fetch(query, params, connection=connection)
        user_cards = self._process_paginated_results(results)
        return user_cards

    async def find_user_card_by_name(self, user_id: int, filters: CollectionFilters) -> Dict[str, Any]:
        """(Async) 根據名稱模糊搜索用戶擁有的卡片."""
        if not filters.card_name:
            raise ValueError('缺少卡片名稱進行搜索')
        where_clause, params = self._build_filter_conditions(user_id, filters)
        query = f'\n            SELECT mc.card_id, mc.name as card_name\n            FROM {self.table_name} uc\n            JOIN gacha_master_cards mc ON uc.card_id = mc.card_id\n            WHERE {where_clause}\n            ORDER BY mc.name\n            LIMIT 1\n        '
        result = await self._fetchrow(query, params)
        if not result:
            raise RecordNotFoundError(f"找不到名為 '{filters.card_name}' 的卡片 for user {user_id}")
        return dict(result)

    async def get_user_cards_page_only(self, user_id: int, page: int=1, page_size: int=9, sort_by: str='rarity', sort_order: str='desc', filters: Optional[CollectionFilters]=None, connection: Optional[asyncpg.Connection]=None) -> Dict[str, Any]:
        """(Async) 僅獲取特定頁面的卡片列表 - 異常將從 _fetch_user_cards_for_page 傳播."""
        if filters is None:
            filters = CollectionFilters()
        where_clause, params_list = self._build_filter_conditions(user_id, filters)
        user_cards = await self._fetch_user_cards_for_page(user_id, page, page_size, sort_by, sort_order, filters, where_clause=where_clause, params_list=params_list, connection=connection)
        logger.debug("[GACHA_DIAGNOSIS] UserCollectionRepository.get_user_cards_page_only: Returning data: {'cards': len(user_cards) if user_cards else 0, 'current_page': %s, ...}", page)
        return {'cards': user_cards, 'current_page': page, 'total_pages': 1}

    async def get_star_levels_batch(self, user_id: int, card_ids: List[int]) -> Dict[int, int]:
        """(Async) 批量獲取多張卡片的星級."""
        if not card_ids:
            return {}
        query = f'\n            SELECT card_id, star_level\n            FROM {self.table_name}\n            WHERE user_id = $1 AND card_id = ANY($2::integer[])\n        '
        results = await self._fetch(query, [user_id, card_ids])
        return {row['card_id']: row['star_level'] for row in results}

    async def get_favorite_status_batch(self, user_id: int, card_ids: List[int]) -> Dict[int, bool]:
        """(Async) 批量獲取多張卡片的最愛狀態."""
        if not card_ids:
            return {}
        query = f'\n            SELECT card_id, is_favorite\n            FROM {self.table_name}\n            WHERE user_id = $1 AND card_id = ANY($2::integer[])\n        '
        results = await self._fetch(query, [user_id, card_ids])
        return {row['card_id']: row.get('is_favorite', False) for row in results}

    async def check_cards_owned_batch(self, user_id: int, card_ids: List[int]) -> Dict[int, bool]:
        """(Async) 批量檢查用戶是否擁有多張卡片."""
        if not card_ids:
            return {}
        query = f'\n            SELECT card_id\n            FROM {self.table_name}\n            WHERE user_id = $1 AND card_id = ANY($2::integer[])\n        '
        results = await self._fetch(query, [user_id, card_ids])
        owned_card_ids = {row['card_id'] for row in results}
        return {card_id: card_id in owned_card_ids for card_id in card_ids}

    async def update_star_level(self, user_id: int, card_id: int, new_star_level: int, connection: Optional[asyncpg.Connection]=None) -> None:
        """(Async) 更新用戶卡片的星級.
        
        Raises:
            ValueError: 當星級無效時
            DatabaseOperationError: 當數據庫操作失敗時
        """
        if new_star_level < 0:
            raise ValueError(f'無效星級: {new_star_level}')
        query = f'\n            UPDATE {self.table_name}\n            SET star_level = $1\n            WHERE user_id = $2 AND card_id = $3\n        '
        status = await self._execute(query, (new_star_level, user_id, card_id), connection=connection)
        if status != 'UPDATE 1':
            raise DatabaseOperationError(f'更新星級操作未影響任何行或找不到記錄: user_id={user_id}, card_id={card_id}')

    async def _attach_owner_counts_to_cards(self, user_cards: List[UserCard]) -> None:
        """
        (Async) 批量獲取並附加 owner_count 到 UserCard 列表。
        直接修改傳入的 user_cards 列表中的物件。
        """
        if not user_cards:
            return
        card_ids = [uc.card_id for uc in user_cards if uc]
        if not card_ids:
            return
        owner_counts = await self.get_card_owner_counts_batch(card_ids)
        for uc in user_cards:
            if uc:
                uc.owner_count = owner_counts.get(uc.card_id, 0)

    async def get_card_owner_count(self, card_id: int) -> int:
        """(Async) 獲取指定卡片的獨立擁有者數量 (使用快取). Returns 0 on DB/general error."""
        start_time = time.monotonic()
        cache_key = f'{self.OWNER_COUNT_CACHE_PREFIX}{card_id}'
        owner_count = 0
        cache_hit = False
        try:
            cached_count_str = await self.redis.get(cache_key)
            if cached_count_str is not None:
                try:
                    owner_count = int(cached_count_str)
                    cache_hit = True
                    logger.debug('[GACHA][PERF] get_card_owner_count for card %s (cache_hit=True) took %sms. Count: %s', card_id, (time.monotonic() - start_time) * 1000, owner_count)
                    return owner_count
                except ValueError:
                    logger.warning('[GACHA][CACHE] Invalid owner count value in cache for %s: %s. Will query DB.', cache_key, cached_count_str)
        except redis.RedisError as cache_read_err:
            logger.error('[GACHA][CACHE] Redis error reading owner count from cache for card %s: %s', card_id, cache_read_err, exc_info=True)
        query = f'SELECT COUNT(DISTINCT user_id) FROM {self.table_name} WHERE card_id = $1'
        db_count = await self._fetchval(query, (card_id,))
        owner_count = db_count if db_count is not None else 0
        try:
            await self.redis.set(cache_key, owner_count, ex=self.OWNER_COUNT_CACHE_TTL)
        except redis.RedisError as cache_write_err:
            logger.error('[GACHA][CACHE] Redis error writing owner count to cache for card %s: %s', card_id, cache_write_err, exc_info=True)
        duration_ms = (time.monotonic() - start_time) * 1000
        logger.debug('[GACHA][PERF] get_card_owner_count for card %s (cache_hit=%s) took %sms. Count: %s', card_id, cache_hit, duration_ms, owner_count)
        return owner_count

    async def get_card_owner_counts_batch(self, card_ids: List[int]) -> Dict[int, int]:
        """(Async) 批量獲取多張卡片的獨立擁有者數量 (使用快取). Returns {} on DB/general error."""
        if not card_ids:
            return {}
        unique_card_ids = sorted(list(set(card_ids)))
        if not unique_card_ids:
            return {}
        results: Dict[int, int] = {}
        cache_keys_map = {cid: f'{self.OWNER_COUNT_CACHE_PREFIX}{cid}' for cid in unique_card_ids}
        ids_to_fetch_from_db: List[int] = list(unique_card_ids)
        try:
            cached_values_list = await self.redis.mget(list(cache_keys_map.values()))
            temp_ids_still_missing = []
            for i, card_id_from_unique_list in enumerate(unique_card_ids):
                if i < len(cached_values_list) and cached_values_list[i] is not None:
                    try:
                        results[card_id_from_unique_list] = int(cached_values_list[i])
                    except ValueError:
                        logger.warning('[GACHA][CACHE] Invalid batch owner count value in cache for card %s: %s. Will query DB.', card_id_from_unique_list, cached_values_list[i])
                        temp_ids_still_missing.append(card_id_from_unique_list)
                else:
                    temp_ids_still_missing.append(card_id_from_unique_list)
            ids_to_fetch_from_db = temp_ids_still_missing
        except redis.RedisError as cache_read_err:
            logger.error('[GACHA][CACHE] Redis error during batch read of owner counts. Will attempt to fetch all unique IDs from DB. Error: %s', cache_read_err, exc_info=True)
            ids_to_fetch_from_db = list(unique_card_ids)
        if ids_to_fetch_from_db:
            query = f'\n                SELECT card_id, COUNT(DISTINCT user_id)::int as count\n                FROM {self.table_name}\n                WHERE card_id = ANY($1::integer[])\n                GROUP BY card_id\n            '
            db_results_map: Dict[int, int] = {}
            rows = await self._fetch(query, (ids_to_fetch_from_db,))
            for row in rows:
                if row:
                    db_results_map[row['card_id']] = row['count']
            for id_to_check in ids_to_fetch_from_db:
                results[id_to_check] = db_results_map.get(id_to_check, 0)
            if db_results_map:
                try:
                    async with self.redis.pipeline(transaction=False) as pipe:
                        for card_id_db, count_db in db_results_map.items():
                            if card_id_db in cache_keys_map:
                                pipe.set(cache_keys_map[card_id_db], count_db, ex=self.OWNER_COUNT_CACHE_TTL)
                        await pipe.execute()
                except redis.RedisError as cache_write_err:
                    logger.error('[GACHA][CACHE] Redis error during batch write of owner counts to cache: %s', cache_write_err, exc_info=True)
        return results

    async def _invalidate_owner_count_cache(self, card_id: int) -> None:
        """(Async) 失效單個卡片的擁有者數量快取"""
        try:
            cache_key = f'{self.OWNER_COUNT_CACHE_PREFIX}{card_id}'
            await self.redis.delete(cache_key)
            logger.debug('[GACHA][CACHE] Invalidated owner count cache for card_id: %s', card_id)
        except Exception as cache_err:
            logger.error('[GACHA][CACHE] Failed to invalidate owner count cache for card %s: %s', card_id, cache_err, exc_info=True)

    async def _invalidate_owner_count_cache_batch(self, card_ids: List[int]) -> None:
        """(Async) 批量失效多個卡片的擁有者數量快取"""
        if not card_ids:
            return
        start_time = time.monotonic()
        num_card_ids = len(card_ids)
        logger.debug('[CACHE_INVALIDATE_BATCH] Starting for %s card_ids.', num_card_ids)
        try:
            keys_to_delete = [f'{self.OWNER_COUNT_CACHE_PREFIX}{card_id}' for card_id in card_ids]
            if keys_to_delete:
                deleted_count = await self.redis.delete(*keys_to_delete)
                duration = (time.monotonic() - start_time) * 1000
            else:
                duration = (time.monotonic() - start_time) * 1000
                logger.debug('[CACHE_INVALIDATE_BATCH] No keys to delete for %s card_ids. Duration: %sms', num_card_ids, duration)
        except Exception as cache_err:
            duration = (time.monotonic() - start_time) * 1000
            logger.error('[CACHE_INVALIDATE_BATCH] Failed for %s card_ids after %sms. Error: %s', num_card_ids, duration, cache_err, exc_info=True)

    async def process_sell_operations_batch(self, user_id: int, operations: List[CardSellOperation], connection: asyncpg.Connection) -> Dict[str, Any]:
        """
        (Async) 通用的批量處理用戶卡片售賣操作。
        此方法應在一個事務中被調用 (由調用方保證)。
        使用純異常模式處理錯誤，失敗時直接拋出異常。

        參數:
            user_id (int): 用戶 ID。
            operations (List[CardSellOperation]): 卡片售賣操作列表。
            connection (asyncpg.Connection): 必須提供的數據庫連接，用於事務控制。

        返回:
            一個字典，包含操作結果，例如:
            {
                "message": "操作成功完成" / "部分操作失敗...",
                "updated_cards": [{"card_id": int, "remaining_quantity": int}, ...],
                "deleted_cards": [{"card_id": int}, ...],
                "failed_operations": [{"card_id": int, "reason": str}, ...],
                "card_ids_for_cache_invalidation": [int, ...]
            }
            
        拋出:
            ValueError: 如果未提供連接或參數無效
            DatabaseOperationError: 如果數據庫操作失敗
            RecordNotFoundError: 如果找不到記錄
        """
        if not connection:
            logger.error('[PROCESS_SELL_BATCH] User %s: Connection is required for batch processing.', user_id)
            raise ValueError('process_sell_operations_batch 必須在一個事務中並提供一個有效的 asyncpg Connection。')
        if not operations:
            return {'message': '沒有提供任何操作。', 'updated_cards': [], 'deleted_cards': [], 'failed_operations': [], 'card_ids_for_cache_invalidation': []}
        updated_cards_summary: List[Dict[str, Any]] = []
        deleted_cards_summary: List[Dict[str, Any]] = []
        failed_operations_summary: List[Dict[str, Any]] = []
        card_ids_to_invalidate_cache: List[int] = []
        updates_to_execute_values: List[Tuple[int, int, int]] = []
        deletes_to_execute_values: List[Tuple[int, int]] = []
        for op in operations:
            card_id = op['card_id']
            op_type = op['operation_type']
            current_owned = op['current_quantity_owned']
            quantity_to_sell_this_op = 0
            if op_type == 'sell_specific_quantity_of_card':
                if op.get('quantity_to_sell') is None or op['quantity_to_sell'] <= 0:
                    failed_operations_summary.append({'card_id': card_id, 'reason': '指定售賣數量無效。'})
                    continue
                quantity_to_sell_this_op = op['quantity_to_sell']
                if quantity_to_sell_this_op > current_owned:
                    failed_operations_summary.append({'card_id': card_id, 'reason': f'請求售賣數量 ({quantity_to_sell_this_op}) 超過擁有數量 ({current_owned})。'})
                    continue
            elif op_type == 'sell_all_for_card':
                quantity_to_sell_this_op = current_owned
            elif op_type == 'sell_leaving_one_for_card':
                if current_owned > 1:
                    quantity_to_sell_this_op = current_owned - 1
                else:
                    quantity_to_sell_this_op = 0
            else:
                failed_operations_summary.append({'card_id': card_id, 'reason': f'未知的操作類型: {op_type}'})
                continue
            if quantity_to_sell_this_op <= 0 and (not (op_type == 'sell_all_for_card' and current_owned == 0)):
                logger.debug('[PROCESS_SELL_BATCH] User %s, Card %s: No quantity to sell for operation type %s, owned %s.', user_id, card_id, op_type, current_owned)
                continue
            if current_owned == 0 and op_type == 'sell_all_for_card':
                logger.debug('[PROCESS_SELL_BATCH] User %s, Card %s: Special case - deleting card with quantity 0.', user_id, card_id)
                deletes_to_execute_values.append((user_id, card_id))
                continue
            remaining_quantity = current_owned - quantity_to_sell_this_op
            if remaining_quantity > 0:
                updates_to_execute_values.append((remaining_quantity, user_id, card_id))
            else:
                deletes_to_execute_values.append((user_id, card_id))
        if not updates_to_execute_values and (not deletes_to_execute_values) and failed_operations_summary:
            logger.warning('[PROCESS_SELL_BATCH] User %s: All operations failed pre-check or resulted in no DB action.', user_id)
            raise DatabaseOperationError('所有提供的操作均未能執行或無需執行。')
        if not updates_to_execute_values and (not deletes_to_execute_values) and (not failed_operations_summary):
            return {'message': '沒有需要執行的數據庫操作。', 'updated_cards': [], 'deleted_cards': [], 'failed_operations': [], 'card_ids_for_cache_invalidation': []}
        try:
            BATCH_SIZE_UPDATES = 800
            BATCH_SIZE_DELETES = 1000
            processed_update_card_ids = set()
            processed_delete_card_ids = set()
            if updates_to_execute_values:
                for i in range(0, len(updates_to_execute_values), BATCH_SIZE_UPDATES):
                    batch_updates = updates_to_execute_values[i:i + BATCH_SIZE_UPDATES]
                    if not batch_updates:
                        continue
                    update_query_batch = f"\n                        UPDATE {self.table_name} AS uc\n                        SET quantity = val.new_quantity\n                        FROM (VALUES {', '.join((f'(${j * 3 + 1}::integer, ${j * 3 + 2}::bigint, ${j * 3 + 3}::integer)' for j in range(len(batch_updates))))})\n                            AS val(new_quantity, user_id_val, card_id_val)\n                        WHERE uc.user_id = val.user_id_val AND uc.card_id = val.card_id_val\n                        RETURNING uc.card_id, uc.quantity;\n                    "
                    flat_update_params_batch = [item for sublist in batch_updates for item in sublist]
                    logger.debug('[PROCESS_SELL_BATCH] User %s: Executing UPDATE batch %s, size: %s, params: %s', user_id, i // BATCH_SIZE_UPDATES + 1, len(batch_updates), len(flat_update_params_batch))
                    updated_rows_batch = await connection.fetch(update_query_batch, *flat_update_params_batch)
                    current_batch_processed_ids = set()
                    for row in updated_rows_batch:
                        updated_cards_summary.append({'card_id': row['card_id'], 'remaining_quantity': row['quantity']})
                        current_batch_processed_ids.add(row['card_id'])
                        processed_update_card_ids.add(row['card_id'])
                    expected_batch_update_ids = {cid for _, _, cid in batch_updates}
                    failed_batch_update_ids = expected_batch_update_ids - current_batch_processed_ids
                    for failed_cid in failed_batch_update_ids:
                        failed_operations_summary.append({'card_id': failed_cid, 'reason': '更新卡片數量時(批次)，記錄未找到或更新未生效。'})
                        logger.warning('[PROCESS_SELL_BATCH] User %s: Failed to update card_id %s in batch %s.', user_id, failed_cid, i // BATCH_SIZE_UPDATES + 1)
            if deletes_to_execute_values:
                for i in range(0, len(deletes_to_execute_values), BATCH_SIZE_DELETES):
                    batch_deletes = deletes_to_execute_values[i:i + BATCH_SIZE_DELETES]
                    if not batch_deletes:
                        continue
                    values_clause_delete_batch = ', '.join((f'(${j * 2 + 1}::bigint, ${j * 2 + 2}::integer)' for j in range(len(batch_deletes))))
                    delete_query_batch = f'\n                        DELETE FROM {self.table_name}\n                        WHERE (user_id, card_id) IN (VALUES {values_clause_delete_batch})\n                        RETURNING card_id;\n                    '
                    flat_delete_params_batch = [item for sublist in batch_deletes for item in sublist]
                    logger.debug('[PROCESS_SELL_BATCH] User %s: Executing DELETE batch %s, size: %s, params: %s', user_id, i // BATCH_SIZE_DELETES + 1, len(batch_deletes), len(flat_delete_params_batch))
                    deleted_rows_batch = await connection.fetch(delete_query_batch, *flat_delete_params_batch)
                    current_batch_processed_ids = set()
                    for row in deleted_rows_batch:
                        deleted_card_id = row['card_id']
                        deleted_cards_summary.append({'card_id': deleted_card_id})
                        card_ids_to_invalidate_cache.append(deleted_card_id)
                        current_batch_processed_ids.add(deleted_card_id)
                        processed_delete_card_ids.add(deleted_card_id)
                    expected_batch_delete_ids = {cid for _, cid in batch_deletes}
                    failed_batch_delete_ids = expected_batch_delete_ids - current_batch_processed_ids
                    for failed_cid in failed_batch_delete_ids:
                        failed_operations_summary.append({'card_id': failed_cid, 'reason': '刪除卡片時(批次)，記錄未找到或刪除未生效。'})
                        logger.warning('[PROCESS_SELL_BATCH] User %s: Failed to delete card_id %s in batch %s.', user_id, failed_cid, i // BATCH_SIZE_DELETES + 1)
            if failed_operations_summary:
                logger.warning('[PROCESS_SELL_BATCH] User %s: Some operations failed across batches. Details: %s', user_id, failed_operations_summary)
                return {'message': f'部分售賣操作處理完成，但有 {len(failed_operations_summary)} 個操作失敗或未執行。', 'updated_cards': updated_cards_summary, 'deleted_cards': deleted_cards_summary, 'failed_operations': failed_operations_summary, 'card_ids_for_cache_invalidation': card_ids_to_invalidate_cache}
            return {'message': '批量售賣操作成功完成。', 'updated_cards': updated_cards_summary, 'deleted_cards': deleted_cards_summary, 'failed_operations': [], 'card_ids_for_cache_invalidation': card_ids_to_invalidate_cache}
        except asyncpg.PostgresError as e:
            logger.error('[PROCESS_SELL_BATCH] User %s: Database error during batch sell: %s', user_id, e, exc_info=True)
            raise DatabaseOperationError(f'批量售賣數據庫操作失敗: {e}') from e
        except Exception as e:
            logger.error('[PROCESS_SELL_BATCH] User %s: Unexpected error during batch sell: %s', user_id, e, exc_info=True)
            raise DatabaseOperationError(f'批量售賣時發生未知錯誤: {e}') from e

    async def get_sellable_card_snapshots_with_stored_price(self, user_id: int, filters: CollectionFilters, connection: Optional[asyncpg.Connection]=None) -> List[Dict[str, Any]]:
        """
        (Async) 獲取符合篩選條件的用戶卡片快照，包含預存的市場價格。
        用於 EconomyService 的售賣流程。
        """
        logger.debug('[SNAPSHOT_STORED_PRICE] User %s: Getting sellable snapshots with stored price. Filters: %s', user_id, filters)
        where_clause, params = self._build_filter_conditions(user_id, filters)
        query = f'\n            SELECT\n                uc.card_id,\n                uc.quantity,\n                uc.is_favorite,\n                mc.name AS card_name,\n                mc.rarity AS master_rarity,\n                mc.pool_type AS master_pool_type,\n                mc.current_market_sell_price\n            FROM {self.table_name} uc\n            JOIN gacha_master_cards mc ON uc.card_id = mc.card_id\n            WHERE {where_clause}\n            ORDER BY uc.card_id;\n        '
        rows = await self._fetch(query, params, connection=connection)
        snapshots = []
        if rows:
            for row_data in rows:
                try:
                    snapshots.append({'card_id': row_data['card_id'], 'quantity': row_data['quantity'], 'is_favorite': row_data['is_favorite'], 'name': row_data['card_name'], 'rarity': RarityLevel(row_data['master_rarity']), 'pool_type': row_data['master_pool_type'], 'current_market_sell_price': row_data['current_market_sell_price'] if row_data['current_market_sell_price'] is not None else None})
                except Exception as e_map:
                    logger.error('Data mapping error for snapshot: %s, row: %s', e_map, row_data, exc_info=True)
                    raise DataMappingError(f"Error mapping snapshot data for card_id {row_data.get('card_id', 'UNKNOWN')}") from e_map
        logger.debug('[SNAPSHOT_STORED_PRICE] User %s: Found %s snapshots.', user_id, len(snapshots))
        return snapshots

    async def get_card_quantity(self, user_id: int, card_id: int, connection: Optional[asyncpg.Connection]=None) -> int:
        """(Async) 查詢指定用戶特定卡片的數量。如果未找到或發生錯誤，返回 0。"""
        query = f'SELECT quantity FROM {self.table_name} WHERE user_id = $1 AND card_id = $2'
        quantity = await self._fetchval(query, (user_id, card_id), connection=connection)
        return quantity if quantity is not None else 0

    async def increment_card_quantity(self, user_id: int, card_id: int, quantity_to_add: int, connection: asyncpg.Connection) -> int:
        """
        (Async) 增加指定用戶特定卡片的數量。如果用戶之前沒有該卡片，則創建新記錄。
        返回操作後該卡片的總數量。此方法必須在事務中執行，並使用傳入的 connection。
        如果 quantity_to_add 小於等於0，則拋出 ValueError。
        """
        if quantity_to_add <= 0:
            raise ValueError('quantity_to_add 必須為正整數')
        now = datetime.now()
        query = f'\n            INSERT INTO {self.table_name} (user_id, card_id, quantity, first_acquired, last_acquired, star_level)\n            VALUES ($1, $2, $3, $4, $4, 0)\n            ON CONFLICT (user_id, card_id) DO UPDATE\n            SET quantity = {self.table_name}.quantity + EXCLUDED.quantity,\n                last_acquired = EXCLUDED.last_acquired\n            RETURNING quantity, (xmax = 0) AS is_new;\n        '
        result = await connection.fetchrow(query, user_id, card_id, quantity_to_add, now)
        if not result:
            raise RecordNotFoundError(f'增加卡片數量操作失敗，資料庫未返回結果: user_id={user_id}, card_id={card_id}')
        new_total_quantity = result['quantity']
        is_new_entry = result['is_new']
        if is_new_entry:
            try:
                await self._invalidate_owner_count_cache(card_id)
            except CacheError as ce:
                logger.warning('Cache invalidation failed for new card %s (increment_card_quantity): %s', card_id, ce)
            except Exception as e_generic_cache:
                logger.warning('Generic cache invalidation error for new card %s (increment_card_quantity): %s', card_id, e_generic_cache)
        return new_total_quantity

    async def get_card_favorite_status(self, user_id: int, card_id: int, connection: Optional[asyncpg.Connection]=None) -> Optional[bool]:
        """
        獲取指定用戶的特定卡片是否被標記為最愛。
        Returns True/False if found, None if not found or on error.
        """
        query = f'SELECT is_favorite FROM {self.table_name} WHERE user_id = $1 AND card_id = $2'
        result_val = await self._fetchval(query, [user_id, card_id], connection=connection)
        return result_val

    async def decrement_card_quantity(self, user_id: int, card_id: int, quantity_to_remove: int, connection: asyncpg.Connection) -> Optional[Tuple[int, bool]]:
        """
        減少指定用戶特定卡片的數量。
        如果卡片數量不足或未找到，則返回 None。
        如果操作成功，返回一個元組，包含操作後該卡片的總數量以及卡片是否被完全移除。
        此方法必須在事務中執行，並使用傳入的 connection。
        如果 quantity_to_remove 小於等於0，則拋出 ValueError。
        """
        if quantity_to_remove <= 0:
            raise ValueError('quantity_to_remove 必須為正整數')
        get_current_query = f'SELECT quantity, is_favorite FROM {self.table_name} WHERE user_id = $1 AND card_id = $2 FOR UPDATE'
        current_record = await connection.fetchrow(get_current_query, user_id, card_id)
        if not current_record:
            logger.info('decrement_card_quantity: User %s does not own card %s.', user_id, card_id)
            return None
        current_quantity = current_record['quantity']
        if current_quantity < quantity_to_remove:
            logger.info('decrement_card_quantity: User %s has insufficient quantity of card %s (%s < %s).', user_id, card_id, current_quantity, quantity_to_remove)
            return None
        new_quantity = current_quantity - quantity_to_remove
        was_deleted = False
        if new_quantity == 0:
            delete_query = f'DELETE FROM {self.table_name} WHERE user_id = $1 AND card_id = $2'
            status = await connection.execute(delete_query, user_id, card_id)
            if status != 'DELETE 1':
                raise DatabaseOperationError(f'刪除卡片記錄失敗 for user={user_id}, card={card_id}, status={status}')
            try:
                await self._invalidate_owner_count_cache(card_id)
            except CacheError as ce:
                logger.warning('Cache invalidation failed for deleted card %s (decrement_card_quantity): %s', card_id, ce)
            except Exception as e_generic_cache:
                logger.warning('Generic cache invalidation error for deleted card %s (decrement_card_quantity): %s', card_id, e_generic_cache)
            was_deleted = True
            return (0, was_deleted)
        else:
            update_query = f'UPDATE {self.table_name} SET quantity = $1 WHERE user_id = $2 AND card_id = $3 RETURNING quantity'
            updated_record = await connection.fetchrow(update_query, new_quantity, user_id, card_id)
            if not updated_record or updated_record['quantity'] != new_quantity:
                raise DatabaseOperationError(f'更新卡片數量失敗或返回數量不一致 for user={user_id}, card={card_id}')
            return (new_quantity, was_deleted)

    async def get_user_favorite_card_count(self, user_id: int, connection: Optional[asyncpg.Connection]=None) -> int:
        """(Async) 獲取用戶標記為最愛的卡片總數。"""
        query = f'\n            SELECT COUNT(*)::integer\n            FROM {self.table_name}\n            WHERE user_id = $1 AND is_favorite = TRUE\n        '
        count = await self._fetchval(query, (user_id,), connection=connection)
        return count or 0

    async def get_favorite_user_cards(self, user_id: int, connection: Optional[asyncpg.Connection]=None) -> List[UserCard]:
        """(Async) 獲取用戶所有標記為最愛的卡片，並按推薦順序排序。"""
        base_select_query = self._get_base_user_card_select_query()
        query = f'\n            {base_select_query}\n            WHERE uc.user_id = $1 AND uc.is_favorite = TRUE\n            ORDER BY uc.custom_sort_index ASC NULLS LAST, mc.rarity DESC, mc.name ASC\n        '
        results = await self._fetch(query, (user_id,), connection=connection)
        user_cards = []
        if results:
            for r in results:
                try:
                    user_cards.append(self._create_user_card_from_result(r))
                except DataMappingError as e:
                    logger.error('Error mapping favorite card data for user %s, record: %s, error: %s', user_id, r, e)
                    raise
        return user_cards