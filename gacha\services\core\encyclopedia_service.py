import asyncpg
import time
from typing import Dict, List, Optional, Any, Union, Tuple
from utils.logger import logger
from database.postgresql.async_manager import AsyncPgManager
from gacha.repositories.card.card_encyclopedia_repository import CardEncyclopediaRepository
from gacha.repositories.card.master_card_repository import MasterCardRepository
from gacha.repositories.collection.user_collection_repository import UserCollectionRepository
from gacha.models.models import Card
from gacha.exceptions import (
    CardPageNotFoundException,
    EncyclopediaError,
    CardDescriptionTooShortError,
    CardDescriptionTooLongError,
    UpdateCardDescriptionError,
    DatabaseOperationError,
    RecordNotFoundError
)

class EncyclopediaService:
    """全圖鑑服務 (Asyncpg 版本)，處理全圖鑑相關邏輯"""

    def __init__(self, pool: asyncpg.Pool, encyclopedia_repo: CardEncyclopediaRepository, master_card_repo: MasterCardRepository, user_collection_repo: UserCollectionRepository):
        """初始化全圖鑑服務 (Asyncpg 版本)"""
        if pool is None:
            err_msg = 'EncyclopediaService 初始化失敗：必須提供 asyncpg 連接池。'
            logger.error(err_msg)
            raise ValueError(err_msg)
        if encyclopedia_repo is None or master_card_repo is None or user_collection_repo is None:
            err_msg = 'EncyclopediaService 初始化失敗：必須提供所有 Repository 實例。'
            logger.error(err_msg)
            raise ValueError(err_msg)
        self.pool = pool
        self.encyclopedia_repo = encyclopedia_repo
        self.master_card_repo = master_card_repo
        self.user_collection_repo = user_collection_repo

    async def get_card_for_page(self, page: int=1, sort_by: str='rarity', sort_order: str='desc', pool_type: Optional[str]=None, rarity: Optional[Union[int, List[int]]]=None, series_name: Optional[str]=None, card_id: Optional[int]=None, card_name: Optional[str]=None) -> Dict[str, Any]:
        """獲取當前頁面的卡片圖鑑數據

        Args:
            page: 頁碼
            sort_by: 排序欄位
            sort_order: 排序順序
            pool_type: 卡池類型
            rarity: 稀有度 (數字)，可以是單一數字或數字列表
            series_name: 系列名稱
            card_id: 卡片ID (用於直接跳轉到該卡片頁面)
            card_name: 卡片名稱 (用於篩選)

        Returns:
            包含卡片數據、擁有者數量以及分頁信息的字典。
            如果找不到符合條件的卡片，返回的字典中 'has_card' 為 False。

        Raises:
            DatabaseOperationError: 當數據庫操作失敗時
        """
        try:
            page_data = await self.encyclopedia_repo.get_paginated_card_id(
                page=page, sort_by=sort_by, sort_order=sort_order, 
                pool_type=pool_type, rarity=rarity, series_name=series_name, 
                card_id=card_id, card_name=card_name
            )
            
            card_id_from_page = page_data.get('card_id') # This will exist if repo didn't raise RecordNotFound
            total_cards = page_data.get('total_cards', 0)
            total_pages = page_data.get('total_pages', 1)
            current_page_from_repo = page_data.get('current_page', 1)

            # card_id_from_page should always be present if get_paginated_card_id succeeded.
            # The repo now raises RecordNotFoundError if no card is found for the page/filters.
            
            card_data = await self.encyclopedia_repo.get_card_encyclopedia_data(card_id_from_page)
            # get_card_encyclopedia_data also raises RecordNotFoundError if card_id_from_page is invalid (should not happen here)
            
            owner_count = 0
            try:
                owner_counts_dict = await self.user_collection_repo.get_card_owner_counts_batch([card_id_from_page])
                owner_count = owner_counts_dict.get(card_id_from_page, 0)
            except Exception as e_owner_count: # Catch broadly for owner count, log and continue
                logger.error(f'[GACHA][ENCYCLOPEDIA] Failed to fetch owner count for card {card_id_from_page}: {e_owner_count}', exc_info=True)
                # owner_count remains 0, which is acceptable as a fallback.
                
            return {
                'has_card': True, 
                'card_data': card_data, 
                'owner_count': owner_count, 
                'total_cards': total_cards, 
                'total_pages': total_pages, 
                'current_page': current_page_from_repo
            }

        except RecordNotFoundError: # Catch from encyclopedia_repo.get_paginated_card_id or get_card_encyclopedia_data
            # This means no card matches the filters or the specific page/card_id doesn't exist.
            # We need to return a structure indicating no card, but also total_cards/pages if they were found before the error.
            # Let's try to get total_cards/pages again with broader filters if card_id was specified
            # to give some context to the UI.
            
            # If the initial call to get_paginated_card_id failed with RecordNotFound, 
            # it means no cards matched ANY filters. Total cards is 0.
            # If card_id was given and get_paginated_card_id for THAT card_id failed, total_cards might still be non-zero for the general filters.

            # Simplified: if RecordNotFoundError, it means no specific card for the current view. UI expects this.
            # The `page_data` might not be fully populated if RecordNotFoundError occurred early in `get_paginated_card_id`.
            # We can try to get a general count if `card_id` was specified, to show total pages for base filters.
            
            # Default to 0/1 if we can't get a better count after RecordNotFoundError
            final_total_cards = 0
            final_total_pages = 1
            final_current_page = page # Use the requested page

            if card_id is not None: # If search was for a specific card ID, try getting total counts for base filters
                try:
                    # Get total counts without the specific card_id that wasn't found
                    base_page_data = await self.encyclopedia_repo.get_paginated_card_id(
                        page=1, sort_by=sort_by, sort_order=sort_order,
                        pool_type=pool_type, rarity=rarity, series_name=series_name,
                        card_id=None, card_name=card_name # No specific card_id here
                    )
                    final_total_cards = base_page_data.get('total_cards', 0)
                    final_total_pages = base_page_data.get('total_pages', 1)
                    # current_page remains the initially requested page, or 1 if out of new bounds
                    final_current_page = max(1, min(page, final_total_pages if final_total_pages > 0 else 1))
                except RecordNotFoundError: # No cards even with base filters
                    pass # final_total_cards/pages remain 0/1
                except DatabaseOperationError as db_err_fallback:
                    logger.warning(f"Failed to get fallback total counts for encyclopedia: {db_err_fallback}")
                    # Let it propagate if severe, or keep 0/1 if just for UI display.
                    # For now, keep 0/1 and the main error is already logged.
                    pass 

            return {
                'has_card': False, 
                'card_data': None, # Explicitly None
                'owner_count': 0,
                'total_cards': final_total_cards, 
                'total_pages': final_total_pages, 
                'current_page': final_current_page 
            }
            
        except DatabaseOperationError: # Re-raise if it's a DB error not handled above
            raise
        except Exception as e:
            logger.error('[GACHA][SERVICE] 獲取圖鑑卡片數據時發生未知錯誤: %s', str(e), exc_info=True)
            # Convert unexpected errors to DatabaseOperationError for consistent service-level error type
            raise DatabaseOperationError(f"獲取圖鑑卡片數據時發生服務層未知錯誤: {str(e)}")

    async def get_card_series_list(self) -> List[str]:
        """獲取全部系列列表

        Returns:
            系列名稱列表

        Raises:
            DatabaseOperationError: 當獲取系列列表失敗時
        """
        try:
            return await self.master_card_repo.get_all_series()
        except Exception as e:
            logger.error('[GACHA] 獲取系列列表失敗: %s', str(e), exc_info=True)
            raise DatabaseOperationError(f"獲取系列列表失敗: {str(e)}")

    async def get_total_card_count(self) -> int:
        """獲取總卡片數量

        Returns:
            總卡片數量

        Raises:
            DatabaseOperationError: 當獲取總卡片數量失敗時
        """
        try:
            return await self.encyclopedia_repo.get_total_card_count()
        except Exception as e:
            logger.error('[GACHA] 獲取卡片總數失敗: %s', str(e), exc_info=True)
            raise DatabaseOperationError(f"獲取卡片總數失敗: {str(e)}")

    async def update_card_description(self, user_id: int, card_id: int, description: str) -> None:
        """更新卡片描述

        Args:
            user_id: 用戶ID
            card_id: 卡片ID
            description: 卡片描述

        Raises:
            CardDescriptionTooShortError: 當描述太短時
            CardDescriptionTooLongError: 當描述太長時
            UpdateCardDescriptionError: 當更新描述失敗時
        """
        if not description or len(description.strip()) < 5:
            raise CardDescriptionTooShortError()
            
        if len(description.strip()) > 200:
            raise CardDescriptionTooLongError()
            
        try:
            # 纯异常模式：仓库层不返回状态值，只有成功或抛出异常
            await self.encyclopedia_repo.update_card_description(
                user_id=user_id, 
                card_id=card_id, 
                description=description.strip()
            )
            # 没有异常即表示成功，不需要检查返回值
            
        except UpdateCardDescriptionError:
            # 直接向上傳遞特定異常
            raise
        except Exception as e:
            logger.error('[GACHA] 更新卡片描述失敗 (Service): %s', str(e), exc_info=True)
            raise UpdateCardDescriptionError(f"更新卡片描述時發生錯誤: {str(e)}")

    async def find_card_page_info_by_id(self, target_card_id: int, sort_by: str='rarity', sort_order: str='desc', pool_type: Optional[str]=None, rarity: Optional[Union[int, List[int]]]=None, series_name: Optional[str]=None) -> int:
        """找出指定卡片在圖鑑中的頁碼

        Args:
            target_card_id: 要查找的卡片ID
            sort_by: 排序欄位
            sort_order: 排序順序
            pool_type: 卡池類型過濾
            rarity: 稀有度過濾
            series_name: 系列名稱過濾

        Returns:
            卡片所在的頁碼
            
        Raises:
            CardPageNotFoundException: 如果卡片不存在或不符合過濾條件 (可能包含更詳細的原因)
            DatabaseOperationError: 如果底層數據庫操作失敗
        """
        try:
            # get_paginated_card_id will raise RecordNotFoundError if card is not found under current filters
            # or if the card_id itself implies a non-match to other active filters.
            page_info_from_repo = await self.encyclopedia_repo.get_paginated_card_id(
                page=1, # Not used when card_id is present, but required by repo method signature
                sort_by=sort_by, 
                sort_order=sort_order, 
                pool_type=pool_type, 
                rarity=rarity, 
                series_name=series_name, 
                card_id=target_card_id, # This is the key: repo will find this card's page
                card_name=None
            )
            
            # If get_paginated_card_id did not raise an error, it found the card and its page.
            found_page = page_info_from_repo.get('current_page')
            returned_card_id_check = page_info_from_repo.get('card_id')

            # Double check, though repo logic should ensure this if no error was raised
            if found_page is None or returned_card_id_check != target_card_id:
                # This case should ideally be covered by RecordNotFoundError from the repo
                logger.warning(f"[GACHA][SERVICE] find_card_page_info_by_id: Repo returned page data for card {target_card_id} but data seems inconsistent.")
                filters_desc = self._build_filters_description(sort_by, sort_order, pool_type, rarity, series_name)
                raise CardPageNotFoundException(
                    card_id=target_card_id, 
                    message=f"無法精確定位ID為 {target_card_id} 的卡片頁面（{filters_desc}）。資料可能不一致。"
                )
                
            return found_page
            
        except RecordNotFoundError as e: 
            # RecordNotFoundError from repo means the card was not found with the given filters/ID.
            # Convert to a service-level exception CardPageNotFoundException, preserving the original more detailed message if possible.
            logger.info(f"[GACHA][SERVICE] find_card_page_info_by_id: Card {target_card_id} not found by repository. Original error: {str(e)}")
            filters_desc = self._build_filters_description(sort_by, sort_order, pool_type, rarity, series_name)
            # Use the message from RecordNotFoundError as it's more specific
            raise CardPageNotFoundException(card_id=target_card_id, message=f"{str(e)} (篩選: {filters_desc})")
            
        except DatabaseOperationError: # Re-raise DatabaseOperationError directly
            raise
        except Exception as e: # Catch any other unexpected error from the repo or this service method
            logger.error(f"[GACHA][SERVICE] Unexpected error in find_card_page_info_by_id for card_id {target_card_id}: {str(e)}", exc_info=True)
            filters_desc = self._build_filters_description(sort_by, sort_order, pool_type, rarity, series_name)
            raise CardPageNotFoundException(card_id=target_card_id, message=f"查詢卡片 {target_card_id} 頁面時發生未知服務錯誤（{filters_desc}）。")

    def _build_filters_description(self, sort_by: str, sort_order: str, pool_type: Optional[str], rarity: Optional[Union[int, List[int]]], series_name: Optional[str]) -> str:
        """Helper to build a string representation of filters for error messages."""
        filters_parts = [f"排序={sort_by}", f"順序={sort_order}"]
        if pool_type:
            filters_parts.append(f"卡池={pool_type}")
        if rarity:
            rarity_str = ",".join(map(str, rarity)) if isinstance(rarity, list) else str(rarity)
            filters_parts.append(f"稀有度={rarity_str}")
        if series_name:
            filters_parts.append(f"系列={series_name}")
        return ", ".join(filters_parts)