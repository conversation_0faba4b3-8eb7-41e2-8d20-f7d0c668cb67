import discord
from typing import Optional

def build_mode_selection_embed(current_bet: int, current_balance: int, player: discord.User | discord.Member) -> discord.Embed:
    """構建尋寶礦區模式選擇的 Embed"""
    embed = discord.Embed(title='⛏️ 尋寶礦區 - 模式選擇', description='歡迎來到尋寶礦區！選擇一個難度開始遊戲。\n\n**遊戲規則：**\n1. 點擊格子來揭開它。\n2. 每揭開一個 **💎安全格**，您的獎金乘數會增加。\n3. 如果揭開 **💥地雷**，遊戲結束，您將失去賭注。\n4. 您可以隨時點擊 **💰提現** 來領取當前乘數對應的獎金。\n5. 特殊格：\n   - **💰金幣格**：立即獲得少量額外油幣。\n   - **⭐星星格**：立即獲得較多額外油幣。\n6. 達到最大贏利上限時會自動提現。\n\n**請選擇難度：**', color=discord.Color.gold())
    embed.set_author(name=f'{player.display_name}', icon_url=player.display_avatar.url if player.display_avatar else None)
    embed.add_field(name='💰 當前賭注', value=f'{current_bet:,} 油', inline=True)
    embed.add_field(name='🏦 您的餘額', value=f'{current_balance:,} 油', inline=True)
    embed.set_footer(text='提示：您也可以使用 /mines 指令直接指定難度和賭注金額。')
    return embed