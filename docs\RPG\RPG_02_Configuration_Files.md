**II. 配置文件結構與內容 (JSON格式)**

**配置管理與驗證策略：**

為了確保JSON配置的正確性和一致性，特別是在AI輔助生成和維護配置的過程中，我們採用以 Python 的 `pydantic` 模型作為配置結構的「源頭真相」策略。這意味著：

1.  **模型即文檔**：為每個核心JSON配置文件定義一個對應的 `pydantic` 模型。這些模型清晰地描述了數據的預期結構、字段類型以及驗證規則。
2.  **加載時驗證**：在服務器啟動時，`ConfigLoader` 會讀取JSON文件，並嘗試將其內容解析/反序列化為相應的 `pydantic` 模型實例。
3.  **錯誤處理**：如果JSON數據不符合 `pydantic` 模型的定義（例如，類型不匹配、缺少必需字段、違反自定義驗證規則），`pydantic` 會拋出 `ValidationError`。`ConfigLoader` 會捕獲此異常，記錄詳細錯誤，並阻止服務器啟動或加載該部分有問題的配置。

這種方法不僅提供了強大的數據驗證能力，也使得配置的結構更易於程序化處理和理解。

**以下將詳細描述各個JSON配置文件的結構。在實際項目中，應為每個文件創建對應的 `pydantic` 模型。**

---

**附錄：使用 Pydantic 定義 `active_skills.json` 的模型示例**

以下展示了如何使用 `pydantic` 為 `active_skills.json` 文件定義詳細的數據模型。這可以作為定義其他配置文件模型的參考。

```python
from typing import List, Optional, Dict, Any, Union, Literal
from pydantic import BaseModel, Field, validator

# -------------------- Modifier Model --------------------
# (來自 RPG_02_Configuration_Files.md 5.3. 傷害修正器 (modifiers) 詳細說明)
class ModifierCondition(BaseModel): # 用於 CONDITIONAL_BOOST 的內部條件
    source_combatant: Literal["caster", "target"]
    check: str # e.g., "has_status_effect", "hp_below_percent"
    value: Optional[Any] = None
    status_effect_id: Optional[str] = None

class ModifierConditionGroup(BaseModel): # 用於 CONDITIONAL_BOOST 的條件組
    type: Optional[Literal["AND", "OR"]] = "AND" # 假設默認為 AND
    conditions: List[ModifierCondition]

class Modifier(BaseModel):
    modifier_type: str # e.g., "SCALING_MODIFIER", "CONDITIONAL_BOOST"
    
    # SCALING_MODIFIER 參數
    scaling_source: Optional[str] = None 
    source_combatant: Optional[Literal["caster", "target"]] = None
    scaling_type: Optional[str] = None 
    scaling_factor: Optional[float] = None
    max_bonus_value: Optional[float] = None
    apply_as_damage_reduction: Optional[bool] = False
    
    # CONDITIONAL_BOOST 參數
    condition_group: Optional[ModifierConditionGroup] = None
    bonus_type: Optional[str] = None 
    bonus_value: Optional[float] = None

    @validator('source_combatant', always=True)
    def check_source_combatant_for_scaling_modifier(cls, v, values):
        if values.get('modifier_type') == "SCALING_MODIFIER" and v is None:
            raise ValueError('source_combatant is required for modifier_type "SCALING_MODIFIER"')
        return v

    @validator('scaling_source', 'scaling_type', 'scaling_factor', always=True)
    def check_scaling_modifier_params(cls, v, values):
        if values.get('modifier_type') == "SCALING_MODIFIER":
            # 粗略檢查，實際應更精確
            if values.get('scaling_source') is None or values.get('scaling_type') is None or values.get('scaling_factor') is None:
                raise ValueError('scaling_source, scaling_type, and scaling_factor are required for SCALING_MODIFIER')
        return v

    @validator('condition_group', 'bonus_type', 'bonus_value', always=True)
    def check_conditional_boost_params(cls, v, values):
        if values.get('modifier_type') == "CONDITIONAL_BOOST":
            if values.get('condition_group') is None or values.get('bonus_type') is None or values.get('bonus_value') is None:
                raise ValueError('condition_group, bonus_type, and bonus_value are required for CONDITIONAL_BOOST')
        return v

# -------------------- EffectDefinition Model --------------------
class StatModificationItem(BaseModel):
    stat_name: str
    modification_type: str # e.g., "PERCENTAGE_ADD", "FLAT_ADD"
    value_formula: str 

class EffectDefinition(BaseModel):
    effect_template: Optional[str] = None 
    
    multiplier: Optional[float] = None 
    can_crit: Optional[bool] = None
    duration_turns: Optional[int] = Field(None, ge=0)
    chance: Optional[float] = Field(None, ge=0, le=1)
    stack_count: Optional[int] = Field(None, ge=1)
    
    effect_type: Optional[str] = None 
    
    damage_type: Optional[Literal["PHYSICAL", "MAGICAL", "TRUE_DAMAGE"]] = None
    base_power_multiplier: Optional[float] = Field(None, ge=0) 
    flat_damage_add: Optional[float] = Field(None, ge=0)
    
    heal_type: Optional[Literal["FLAT", "PERCENT_MAX_HP", "PERCENT_CASTER_MATK"]] = None
    value: Optional[float] = None 
    
    status_effect_id: Optional[str] = None 
    
    modifications: Optional[List[StatModificationItem]] = None
    
    modifiers: Optional[List[Modifier]] = None

    @validator('effect_type', always=True)
    def check_definition_method(cls, v, values):
        effect_template_defined = values.get('effect_template') is not None
        effect_type_defined = v is not None

        if effect_template_defined and effect_type_defined:
            raise ValueError('Cannot define both effect_template and effect_type simultaneously for an effect.')
        if not effect_template_defined and not effect_type_defined:
            raise ValueError('Either effect_template or effect_type must be defined for an effect.')
        return v
    
    @validator('damage_type', always=True)
    def check_damage_params_damage_type(cls, v, values):
        if values.get('effect_type') == "DAMAGE" and values.get('effect_template') is None and v is None:
            raise ValueError('damage_type is required for DAMAGE effect_type without a template.')
        return v

    @validator('base_power_multiplier', always=True)
    def check_damage_params_power(cls, v, values):
        if values.get('effect_type') == "DAMAGE" and values.get('effect_template') is None and \
           v is None and values.get('flat_damage_add') is None:
            raise ValueError('base_power_multiplier or flat_damage_add is required for DAMAGE effect_type without a template.')
        return v

# -------------------- TargetLogicDetails Models --------------------
class TargetLogicParams(BaseModel):
    value: Optional[Union[float, int, str]] = None
    status_effect_id: Optional[str] = None

class TargetLogicCondition(BaseModel):
    type: str 
    params: TargetLogicParams

class TargetLogicDetail(BaseModel):
    priority_score: int
    condition: Optional[TargetLogicCondition] = None
    selector_type: str 
    max_targets: int = Field(..., ge=1)

# -------------------- ActiveSkillEffectLevel Model --------------------
class ActiveSkillEffectLevel(BaseModel):
    mp_cost: int = Field(..., ge=0)
    cooldown_turns: int = Field(..., ge=0)
    effect_definitions: List[EffectDefinition]

# -------------------- ActiveSkillConfig Model (Main) --------------------
class ActiveSkillConfig(BaseModel):
    name: str
    description_template: str
    skill_rarity: int = Field(..., ge=1, le=7)
    max_level: int = Field(..., ge=1)
    target_type: str 
    target_logic_details: Optional[List[TargetLogicDetail]] = None
    effects_by_level: Dict[str, ActiveSkillEffectLevel]
    xp_gain_on_sacrifice: Optional[int] = Field(None, ge=0)
    xp_to_next_level_config: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None

    @validator('effects_by_level')
    def check_level_keys_are_positive_numeric_strings(cls, v):
        if not v: 
            raise ValueError("effects_by_level cannot be empty.")
        for key in v.keys():
            if not key.isdigit() or int(key) <= 0:
                raise ValueError(f"Level key '{key}' in effects_by_level must be a positive numeric string (e.g., \\"1\\", \\"10\\").")
        return v

    @validator('target_logic_details', pre=True, always=True) # pre=True to sort before other validations if needed
    def sort_target_logic_details_by_priority(cls, v):
        if v:
            # Ensure it's a list of dicts or TargetLogicDetail objects before sorting
            if all(isinstance(item, dict) for item in v):
                v.sort(key=lambda x: x.get('priority_score', 0), reverse=True)
            elif all(isinstance(item, TargetLogicDetail) for item in v):
                 v.sort(key=lambda x: x.priority_score, reverse=True)
            # else: # mixed types or other types, could raise error or handle as per requirements
        return v

---

1.  **`cards.json` (卡牌RPG定義)**
    *   主鍵：`card_id` (對應 `gacha_master_cards.card_id`)
    *   **核心定位**：此文件為 `gacha_master_cards` 表中已存在的每一張卡牌，主要根據其在 `gacha_master_cards` 表中記錄的**稀有度 (`rarity`)** 和**卡池類型 (`pool_type`)**，通過預設的規則或生成腳本，為其分配在RPG系統中所需的初始基礎屬性 (`base_stats`)、成長值 (`growth_per_rpg_level`)、天賦技能 (`innate_passive_skill_id`)、普攻技能 (`primary_attack_skill_id`)、以及其他RPG相關配置。它作為連接現有卡牌數據與RPG新系統的橋樑，確保每張老卡都能擁有一套与其稀有度和卡池定位相符的RPG化能力。
    *   **初始化注意**：首次生成 `cards.json` 時，需要編寫一個腳本來完成數據填充。該腳本的關鍵第一步是從 `gacha_master_cards` 數據庫表中查詢出所有現存卡牌的 `card_id`，以及與每個 `card_id` 對應的 `rarity` (稀有度，例如用戶定義的1-7級，級別越高越稀有) 和 `pool_type` (卡池類型)。獲取這些基礎信息後，腳本才能根據預設的規則（例如，不同稀有度和卡池類型的卡牌對應不同的基礎屬性範圍、成長潛力等）或更複雜的生成邏輯，為每一張卡牌準確地填充 `cards.json` 文件中定義的各項RPG屬性。其中，對於天賦被動技能 (`innate_passive_skill_id`)、普攻技能 (`primary_attack_skill_id`) 以及可能的預設主動技能槽 (`default_active_skill_slot_X_id`) 的分配，腳本應考慮這些技能本身的稀有度。由於 `active_skills.json`, `passive_skills.json` 及 `innate_passive_skills.json` 中均已定義 `skill_rarity`，卡牌的稀有度將直接影響其能獲取到的技能稀有度層級。腳本應設計為從與卡牌稀有度相匹配的技能稀有度池中，為卡牌隨機挑選合適的技能，目標是讓高稀有度的卡牌更有機會獲得同等或更高稀有度的強力技能。
    *   包含 (RPG特有屬性，`gacha_master_cards` 中已有的基礎信息如 `name`, `rarity`, `pool_type`, `image_url` 則直接從數據庫獲取)：
        *   `innate_passive_skill_id` (字符串, 指向 `innate_passive_skills.json`): 卡牌的天賦被動技能ID。
        *   `primary_attack_skill_id` (字符串, 指向 `active_skills.json`): 卡牌的普攻技能ID。
        *   `default_active_skill_slot_1_id` (字符串, 可選, 指向 `active_skills.json`): 1號主動技能槽預裝的技能ID。
        *   `default_active_skill_slot_2_id` (字符串, 可選, 指向 `active_skills.json`): 2號主動技能槽預裝的技能ID。
        *   `default_active_skill_slot_3_id` (字符串, 可選, 指向 `active_skills.json`): 3號主動技能槽預裝的技能ID。
        *   `passive_skill_slots` (整數): 該卡牌擁有的通用被動技能槽位數量 (例如 0-4)。
        *   `default_passives_on_acquire` (數組, 可選): 卡牌首次獲取時預裝的被動技能。
            *   每個元素為對象：`{"slot_index": <integer>, "skill_id": "<string_or_null>"}`。
            *   `slot_index` (整數): 被動技能槽的索引 (從0開始)。
            *   `skill_id` (字符串或null): 指向 `passive_skills.json` 的技能ID，若為 `null` 或未指定，則表示該槽位初始為空。
        *   `base_stats` (對象): 卡牌1級時的基礎RPG屬性。
            *   `hp` (整數): 生命值。
            *   `max_mp` (整數): 最大法力值。
            *   `mp_regen_per_turn` (整數): 每回合基礎MP恢復量。
            *   `patk` (整數): 物理攻擊力。
            *   `pdef` (整數): 物理防禦力。
            *   `matk` (整數): 魔法攻擊力。
            *   `mdef` (整數): 魔法防禦力。
            *   `spd` (整數): 速度。
            *   `crit_rate` (浮點數, 0.0-1.0): 基礎暴擊率。
            *   `crit_dmg_multiplier` (浮點數, >=1.0): 基礎暴擊傷害倍率。
            *   `accuracy` (浮點數, 0.0-1.0): 基礎命中率。
            *   `evasion` (浮點數, 0.0-1.0): 基礎閃避率。
        *   `growth_per_rpg_level` (對象): 卡牌每提升1級RPG等級所增加的屬性值。鍵名同 `base_stats` 下的屬性 (如 `hp`, `patk` 等)，值為對應屬性的成長量。
        *   `star_level_effects_key` (字符串, 可選, 指向 `star_level_effects.json`): 該卡牌的升星效果定義的鍵。

2.  **`innate_passive_skills.json` (天賦被動技能定義)**
    *   主鍵：`skill_id`
    *   包含：`name`, `description_template` (或 `description_template_by_star_level`), `skill_rarity` (天賦技能本身的稀有度, 1-7), `tags`。
    *   核心結構：`effects_by_star_level` (鍵為「卡牌培養星級」的特定數值 (字符串格式，例如 "0", "5", "12", "35")，值為該培養星級檔位的**被動效果塊列表 `PassiveEffectBlock[]`**)。卡牌的當前培養星級 (0-35) 將決定其適用哪個鍵的效果 (取不大於當前培養星級的最大鍵值)。此處的 `star_level` 變量即指卡牌的培養星級。
    *   **單個被動效果塊 (`PassiveEffectBlock`) 結構：**
        ```json
        {
          "trigger_condition": {
            "type": "ON_DAMAGE_TAKEN", // 核心觸發類型 (枚舉或字符串): ON_BATTLE_START, ON_TURN_START, ON_TURN_END, ON_ACTION_EXECUTED, ON_DAMAGE_DEALT, ON_DAMAGE_TAKEN, ON_HEAL_RECEIVED, ON_STATUS_EFFECT_APPLIED, ON_ALLY_DEATH, ON_ENEMY_DEATH, ON_HP_THRESHOLD_REACHED, ON_SUCCESSFUL_RETALIATION (自定義) 等
            "sub_type": "ANY_DAMAGE", // 可選的子類型，用於進一步細化主類型，如 PHYSICAL_DAMAGE, SKILL_DAMAGE
            "chance_formula": "0.3 + (star_level * 0.01)", // 觸發機率公式，可使用 star_level (指卡牌培養星級 0-35), caster_stat_xxx 等變量
            "trigger_once_per_battle": false, // 可選，布爾值，是否戰鬥中僅觸發一次
            "params": { // 特定於觸發條件類型的參數
              "threshold_percent": 0.3, // 例如用於 ON_HP_THRESHOLD_REACHED
              "check_direction": "BELOW", // 例如用於 ON_HP_THRESHOLD_REACHED，"BELOW" 或 "ABOVE"
              "status_effect_id": "BERSERK_01", // 例如用於 ON_STATUS_EFFECT_APPLIED
              "target_of_status_effect": "SELF" // 例如用於 ON_STATUS_EFFECT_APPLIED，指明是誰被施加了狀態 (SELF, TARGET_OF_ACTION, EVENT_TARGET)
            },
            "additional_conditions": [ // 可選的附加條件列表，全部滿足才會觸發
              {
                "source_combatant": "self", // 條件檢查的對象: self, attacker, target_of_action, event_source, event_target
                "check": "hp_above_percent", // 檢查類型: hp_above_percent, hp_below_percent, has_status_effect, status_effect_stacks_gte, is_buffed, is_debuffed, target_is_boss 等
                "value": 0.2, // 檢查值，例如 HP 百分比
                "status_effect_id": "POISON_STATUS_ID" // 例如用於 has_status_effect
              }
            ]
          },
          "target_override": { // 效果應用目標，相對於觸發事件
            "selector_type": "ATTACKER", // 目標選擇器類型: SELF, ATTACKER, TARGET_OF_ACTION, EVENT_SOURCE (觸發事件的發起者), EVENT_TARGET (觸發事件的承受者), RANDOM_ENEMY, ALL_ALLIES, ALL_ENEMIES 等
            "max_targets": 1 // 最大目標數
          },
          "effect_definitions": [ // 效果定義列表，同主動技能中的 effect_definitions
            // ... 參考 5.1. 效果定義 (`effect_definitions`) 的結構 ...
            // 例如：反擊傷害
            // {
            //   "effect_template": "BASIC_PHYSICAL_DAMAGE",
            //   "base_power_multiplier_formula": "0.2 + (star_level * 0.005)", // star_level 指卡牌培養星級 0-35
            //   "scaling_attribute_override": "pdef", // 傷害基於防禦而非攻擊
            //   "can_crit": false
            // }
          ]
        }
        ```
    *   **示例 (天賦 - 血量低於30%時增加攻擊力)：**
        ```json
        "INNATE_LOW_HP_ATK_BOOST": {
          "name": "絕境逢生 (天賦)",
          "description_template_by_star_level": {
            "0": "當自身生命值首次低於30%時，永久提升 {effects_by_star_level['0'].effect_definitions[0].modifications[0].value_formula * 100}% 的物理攻擊力。",
            "15": "當自身生命值首次低於30%時，永久提升 {effects_by_star_level['15'].effect_definitions[0].modifications[0].value_formula * 100}% 的物理攻擊力，並獲得短暫無敵。"
          },
          "skill_rarity": "SSR", // 天賦技能本身的稀有度
          "effects_by_star_level": {
            "0": [
              {
                "trigger_condition": {
                  "type": "ON_HP_THRESHOLD_REACHED",
                  "chance_formula": "1.0",
                  "trigger_once_per_battle": true,
                  "params": {
                    "threshold_percent": 0.3,
                    "check_direction": "BELOW"
                  }
                },
                "target_override": { "selector_type": "SELF" },
                "effect_definitions": [
                  {
                    "effect_type": "STAT_MODIFICATION",
                    "modifications": [
                      {
                        "stat_name": "patk",
                        "modification_type": "PERCENTAGE_ADD_BASE",
                        "value_formula": "0.2 + (star_level * 0.005)" // star_level 指卡牌培養星級 (0-35)
                      }
                    ]
                  }
                ]
              }
            ],
            "15": [
              {
                "trigger_condition": {
                  "type": "ON_HP_THRESHOLD_REACHED",
                  "chance_formula": "1.0",
                  "trigger_once_per_battle": true,
                  "params": {
                    "threshold_percent": 0.3,
                    "check_direction": "BELOW"
                  }
                },
                "target_override": { "selector_type": "SELF" },
                "effect_definitions": [
                  {
                    "effect_type": "STAT_MODIFICATION",
                    "modifications": [
                      {
                        "stat_name": "patk",
                        "modification_type": "PERCENTAGE_ADD_BASE",
                        "value_formula": "0.35 + (star_level * 0.005)" // 培養星級達到15時效果更強
                      }
                    ]
                  },
                  {
                    "effect_template": "APPLY_INVINCIBLE_STATUS_SHORT" // 假設有此效果模板
                  }
                ]
              }
            ]
            // ...更高培養星級的效果...
          }
        }
        ```

3.  **`active_skills.json` (通用主動技能定義)**
    *   主鍵：`skill_id`
    *   包含：`name`, `description_template`, `skill_rarity` (技能稀有度, 1-7), `max_level` (全局技能等級上限), `target_type` (ENEMY_SINGLE, SELF, etc.), `target_logic_details` (數組, 可選, 詳細說明如下), `effects_by_level` (鍵為全局技能等級, 值包含 `mp_cost`, `cooldown_turns`, `effect_definitions` (效果定義列表)), `xp_gain_on_sacrifice`, `upgrade_cost_modifier`, `tags` (數組 of 字符串, 可選, 例如 `["DAMAGE", "FIRE", "AOE"]`)。
    *   **`target_logic_details` 結構 (目標選擇邏輯細節)：**
        *   一個數組，每個元素代表一條目標選擇規則。列表會首先根據 `priority_score` 從高到低排序，然後按此順序評估，第一條滿足條件的規則生效。
        *   如果數組為空或未提供，則使用 `target_type` 的默認行為。
        *   **單條規則結構:**
            ```json
            {
              "priority_score": 100, // 整數，執行優先級，數值越高越優先。可以用來區分多條可能同時滿足的規則。
              "condition": { // 該規則生效的條件 (可選，若無則始終滿足此部分條件)
                "type": "TARGET_HP_BELOW_PERCENT", // 條件類型，例如 TARGET_HP_BELOW_PERCENT, TARGET_HAS_STATUS_EFFECT, CASTER_MP_ABOVE_ABSOLUTE 等
                "params": { // 特定於條件類型的參數
                  "value": 0.5, // 例如血量百分比
                  "status_effect_id": "DEBUFF_SLOW"
                }
              },
              "selector_type": "ENEMY_LOWEST_HP_ABSOLUTE", // 滿足條件時使用的目標選擇器，如 ENEMY_SINGLE, ALL_ALLIES, ENEMY_LOWEST_HP_PERCENT, ENEMY_HIGHEST_ATK, ALL_ALLIES_EXCEPT_SELF 等
              "max_targets": 1 // 該規則下的最大目標數
            }
            ```
    *   **示例 (主動技能 - 優先攻擊血量最低的敵人，如果自身MP高於50則改為群攻)：**
        ```json
        "SKILL_ADVANCED_STRIKE": {
          "name": "進階打擊",
          "description_template": "對敵方單體造成傷害，若自身MP充足則轉為範圍攻擊。",
          "skill_rarity": "R",
          "max_level": 10,
          "target_type": "ENEMY_SINGLE", // 基礎目標類型，當 target_logic_details 不匹配或為空時使用
          "target_logic_details": [
            {
              "priority_score": 200,
              "condition": {
                "type": "CASTER_MP_ABOVE_ABSOLUTE",
                "params": { "value": 50 }
              },
              "selector_type": "ALL_ENEMIES",
              "max_targets": 3 // 假設群攻最多3個目標
            },
            {
              "priority_score": 100, // 默認情況或上一條不滿足
              "selector_type": "ENEMY_LOWEST_HP_PERCENT",
              "max_targets": 1
            }
          ],
          "effects_by_level": {
            "1": {
              "mp_cost": 15,
              "cooldown_turns": 2,
              "effect_definitions": [
                {
                  "effect_template": "BASIC_PHYSICAL_DAMAGE",
                  "multiplier": 1.2
                }
              ]
            }
            // ... 更高等級的效果
          }
        }
        ```

4.  **`passive_skills.json` (通用被動技能定義)**
    *   與 `innate_passive_skills.json` 結構非常相似，主要區別在於效果是按 `effects_by_level` (通用被動技能的全局熟練度等級) 而非 `effects_by_star_level` (卡牌培養星級) 組織。
    *   主鍵：`skill_id`
    *   包含：`name`, `description_template` (或 `description_template_by_level`), `skill_rarity` (技能稀有度, 1-7), `max_level` (該技能的全局熟練度等級上限), `xp_to_next_level_config` (類似 `active_skills.json` 中的定義), `tags`。
    *   `description_template_by_level` (對象, 可選): 類似天賦被動的 `description_template_by_star_level`，鍵為通用被動技能的全局熟練度等級 (字符串格式，例如 "1", "5", "10")，值為該等級下的描述模板。如果技能在所有等級描述一致，可只使用 `description_template`。
    *   核心結構：`effects_by_level` (鍵為全局熟練度等級, 值為該等級的**被動效果塊列表 `PassiveEffectBlock[]`**)。公式中的 `skill_level` 變量指玩家對此 `skill_id` 的全局熟練度等級。
    *   **單個被動效果塊 (`PassiveEffectBlock`) 結構：** 與 `innate_passive_skills.json` 中定義的結構相同 (包括 `trigger_condition` 下的 `params` 化)，但公式中的 `star_level` 應替換為 `skill_level` (指玩家對此 `skill_id` 的全局熟練度等級)。
        ```json
        {
          "trigger_condition": {
            "type": "ON_DAMAGE_DEALT",
            "chance_formula": "0.3 + (skill_level * 0.05)", // skill_level 指玩家對此 skill_id 的全局熟練度等級
            "params": { "damage_type_filter": "MAGICAL" }, // 例如，僅在造成魔法傷害時
            ...
          },
          "effect_definitions": [
            {
              "effect_template": "LEECH_HP_ON_DAMAGE",
              "leech_percentage_formula": "0.1 + (skill_level -1) * 0.02", // skill_level 指全局熟練度等級
              ...
            }
          ]
        }
        ```

5.  **效果定義 (`effect_definitions`) 與效果模板**
    *   本節詳細說明技能或狀態效果中 `effects_by_level` 或 `effects_by_star_level` 內每個"效果定義"對象的結構。
    
5.1. **效果定義 (`effect_definitions`) 的結構**
    *   一個效果定義 (Effect Definition) 是一個對象，可以通過以下兩種方式之一進行配置：
    
    1.  **使用模板 (推薦，更簡潔):**
        *   `effect_template` (字符串, 指向 `5.2. 效果模板 (effect_templates.json)` 中定義的鍵名, 例如: `"BASIC_PHYSICAL_DAMAGE"`)
        *   可選參數以覆蓋模板中的默認值 (例如: `multiplier`, `can_crit`, `duration_turns` 等，具體取決於模板本身定義了哪些可覆蓋字段)。
        *   `modifiers` (數組 of 傷害修正器定義, 可選, 參考 `5.3. 傷害修正器 (modifiers) 詳細說明`. 用於在模板基礎上追加或替換修正器)。
        *   **示例 (使用模板的傷害效果):**
            ```json
            {
              "effect_template": "BASIC_PHYSICAL_DAMAGE", // 引用模板
              "multiplier": 1.2,                        // 覆蓋模板中的 base_power_multiplier
              "can_crit": true,                         // 覆蓋模板中的 can_crit (假設模板允許)
              "modifiers": [                            // 在模板基礎上添加修正器
                {
                  "modifier_type": "SCALING_MODIFIER",
                  "scaling_source": "stat:pdef",
                  "source_combatant": "target",         // 明確修正器計算時的數據來源對象
                  "scaling_type": "linear",
                  "scaling_factor": -0.0005,          // pdef 越高，傷害越低
                  "max_bonus_value": -0.3             // 最大"加成"為-0.3，即最多減傷30% (此處用 max_bonus_value)
                }
              ]
            }
            ```
    
    2.  **直接定義 (不使用模板，更靈活):**
        *   `effect_type` (字符串: `"DAMAGE"`, `"HEAL"`, `"APPLY_STATUS_EFFECT"`, `"STAT_MODIFICATION"`, `"DISPEL_BUFF"`, `"DISPEL_DEBUFF"`, `"SUMMON"`, etc.)
        *   **根據 `effect_type` 的不同，包含不同的參數，例如：**
            *   **若 `effect_type` == `"DAMAGE"`:**
                *   `damage_type` (字符串: `"PHYSICAL"`, `"MAGICAL"`, `"TRUE_DAMAGE"`)
                *   `base_power_multiplier` (浮點數, 基於施法者主攻擊屬性)
                *   `flat_damage_add` (浮點數, 可選, 固定傷害加值)
                *   `can_crit` (布爾值, 可選)
                *   `modifiers` (數組 of 傷害修正器定義, 可選, 參考 `5.3. 傷害修正器 (modifiers) 詳細說明`)
            *   **若 `effect_type` == `"HEAL"`:**
                *   `heal_type` (`"FLAT"`, `"PERCENT_MAX_HP"`, `"PERCENT_CASTER_MATK"`)
                *   `value` (浮點數)
            *   **若 `effect_type` == `"APPLY_STATUS_EFFECT"`:**
                *   `status_effect_id` (字符串, 指向 `status_effects.json`)
                *   `duration_turns` (整數)
                *   `chance` (浮點數, 0.0-1.0, 施加機率)
                *   `stack_count` (整數, 可選, 施加層數)
            *   **若 `effect_type` == `"STAT_MODIFICATION"` (用於被動或Buff/Debuff的持續效果):**
                *   `trigger_condition` (字符串: `"ALWAYS"`, `"ON_HIT"`, `"ON_BATTLE_START"`, etc.)
                *   `modifications` (數組 of `{"stat_name": "patk", "modification_type": "PERCENTAGE_ADD", "value_formula": "0.10"}`)
                
5.2. **效果模板 (`effect_templates.json`)**
    *   此文件定義了一組可複用的效果模板，以簡化在技能或狀態效果中配置 `effect_definitions`。
    *   主鍵：模板名稱 (字符串)
    *   每個模板的值是一個完整的 `effect_definition` 對象 (不包含 `effect_template` 字段自身)。
    *   **示例結構：**
        ```json
        {
          "BASIC_PHYSICAL_DAMAGE": {
            "effect_type": "DAMAGE",
            "damage_type": "PHYSICAL",
            "base_power_multiplier": 1.0,
            "can_crit": false,
            "modifiers": []
          },
          "BASIC_HEAL_PERCENT_CASTER_MATK": {
            "effect_type": "HEAL",
            "heal_type": "PERCENT_CASTER_MATK",
            "value": 1.0
          },
          "APPLY_POISON_STATUS": {
            "effect_type": "APPLY_STATUS_EFFECT",
            "status_effect_id": "POISON_STATUS_ID", // 指向 status_effects.json
            "duration_turns": 3,
            "chance": 0.8,
            "stack_count": 1
          }
        }
        ```
    *   **使用方式：** 在 `effect_definitions` 中通過 `effect_template: "TEMPLATE_NAME"` 來引用，並可選擇性覆蓋模板中的字段。
    
5.3. **傷害修正器 (`modifiers`) 詳細說明**
    *   傷害修正器 (`modifiers`) 是 `effect_definitions` (尤其是 `effect_type: "DAMAGE"`) 中的一個可選數組，用於對基礎傷害進行調整。
    *   每個元素是一個修正器對象，其核心字段是 `modifier_type`。
    
    *   **單個傷害修正器對象的結構：**
        ```json
        {
          "modifier_type": "SCALING_MODIFIER", // 主要類型。其他特定類型如 "CONDITIONAL_BOOST" 也可存在。
                                              // 下方參數主要針對 SCALING_MODIFIER，其他類型有其專有參數。
    
          // --- SCALING_MODIFIER 參數 (當 modifier_type 為 "SCALING_MODIFIER") ---
          "scaling_source": "stat:pdef",     // 必需。指定縮放的數據來源。
                                            // 示例: "stat:<stat_name>" (如 "stat:patk", "stat:pdef"),
                                            //       "missing_hp", "current_hp_percent", "max_hp",
                                            //       "speed_diff", "buff_stacks:<buff_id>" 等。
          "source_combatant": "target",      // 必需。指定 `scaling_source` 的數據是從哪個戰鬥單位獲取。
                                            // 可選值: "caster", "target"。
          "scaling_type": "linear",          // 必需。縮放方式。
                                            // 示例: "linear" (每點value * factor),
                                            //       "percentage_add" (傷害增加 value * factor 百分比),
                                            //       "multiplier_add" (傷害倍率增加 value * factor)。
          "scaling_factor": 0.001,         // 必需。縮放因子。
          "max_bonus_value": 0.5,          // 可選。基於 `scaling_type` 的最大加成/減成值。
                                            // 例如，若 scaling_type 是 "percentage_add"，此值0.5代表最多增加50%傷害。
                                            // 若是 "multiplier_add"，此值0.5代表傷害倍率最多增加0.5。
          "apply_as_damage_reduction": false, // 可選 (默認 false)。若為 true，則計算出的 bonus 將作為傷害減免比例。
                                             // 例如，若計算出0.3，則傷害變為原來的70%。通常與正的 scaling_factor 配合使用。
    
          // --- CONDITIONAL_BOOST 參數示例 (當 modifier_type 為 "CONDITIONAL_BOOST") ---
          // "condition_group": { ... },     // 必需。定義觸發此修正的條件 (例如：目標有某buff，自身血量低於X%)。
                                            // (條件組的具體結構需進一步定義，可參考被動技能的 `trigger_condition.additional_conditions` 結構。)
          // "bonus_type": "percentage_increase", // 必需。例如 "percentage_increase", "flat_add", "multiplier_add"。
          // "bonus_value": 0.25               // 必需。獎勵的數值。
        }
        ```
    *   **`modifiers` 數組示例：**
        ```json
        "modifiers": [
          {
            "modifier_type": "SCALING_MODIFIER",
            "scaling_source": "missing_hp",
            "source_combatant": "target",
            "scaling_type": "percentage_add", // 每損失1%生命值，傷害增加 scaling_factor %
            "scaling_factor": 0.005,          // 例如，損失50% HP，則 value=50, 傷害增加 50 * 0.005 = 25%
            "max_bonus_value": 0.75           // 最大增傷75%
          },
          {
            "modifier_type": "CONDITIONAL_BOOST",
            "condition_group": {
                "type": "AND",
                "conditions": [
                    {"source_combatant": "caster", "check": "has_status_effect", "status_effect_id": "BERSERK_BUFF"},
                    {"source_combatant": "target", "check": "hp_below_percent", "value": 0.3}
                ]
            },
            "bonus_type": "multiplier_add",
            "bonus_value": 0.5                // 滿足條件時，最終傷害倍率額外增加0.5
          }
        ]
        ```
    
6.  **`status_effects.json` (Buff/Debuff定義)**
    *   主鍵：`status_effect_id`
    *   包含：`name`, `icon_key`, `is_buff`, `max_stacks`, `duration_type` ("TURNS", "INFINITE", "UNTIL_TRIGGERED"), `default_duration`, `tick_at_turn_start` (布爾值), `tick_at_turn_end` (布爾值), `effect_definitions_on_apply` (列表, 施加時的瞬時效果), `effect_definitions_per_tick` (列表, 每跳持續效果), `effect_definitions_on_expire` (列表, 過期時效果), `effect_definitions_triggered` (列表, 特定條件觸發的效果, 如受擊時反傷), `special_flags` (如 "CANNOT_ACT", "DISPELLABLE", "UNDISPELLABLE", "STACKABLE_DURATION", "STACKABLE_INTENSITY")。

    *   **`special_flags` 詳細說明與疊加規則:**
        *   `CANNOT_ACT`: (布爾型) 擁有此狀態效果的單位不能執行主要行動。
        *   `DISPELLABLE`: (布爾型, 默認為 true) 此效果可以被驅散類技能移除。
        *   `UNDISPELLABLE`: (布爾型) 此效果不能被驅散。若與 `DISPELLABLE` 同時存在，以 `UNDISPELLABLE` 為準。
        *   `STACKABLE_DURATION`: (布爾型)
            *   **行為:** 當已擁有此效果的目標再次被施加同一 `status_effect_id` 的效果時：
                *   如果當前層數未達到 `max_stacks`，則增加一層，並且效果的總持續時間刷新為 `default_duration` (或取兩者中較長者，需明確設計)。效果的強度（如每跳傷害、屬性修正值）不隨層數增加而線性疊加，每層的效果強度由其 `effect_definitions` 在對應技能/星級/狀態等級下計算得出（通常是固定的，除非效果本身定義了基於層數的公式）。
                *   如果已達到 `max_stacks`，則僅刷新總持續時間。
            *   **主要用途:** 用於那些效果強度固定，但希望通過多次施加來延長其影響時間的Buff/Debuff。
        *   `STACKABLE_INTENSITY`: (布爾型)
            *   **行為:** 當已擁有此效果的目標再次被施加同一 `status_effect_id` 的效果時：
                *   如果當前層數未達到 `max_stacks`，則增加一層。效果的強度會隨層數疊加。例如，如果一個效果定義為每層提供 "+10 物理攻擊"，那麼3層時就會提供 "+30 物理攻擊"。具體的疊加方式（是簡單的線性疊加基礎效果，還是每層效果都獨立計算後求和）需要在 `EffectApplier` 中明確。**建議默認：每層的效果值獨立計算（基於技能等級等），然後將所有層的效果值加總。**
                *   持續時間通常會刷新為 `default_duration`。
                *   如果已達到 `max_stacks`，則僅刷新持續時間，強度不再增加。
            *   **主要用途:** 用於效果強度隨層數增長的Buff/Debuff，如持續傷害疊加、逐漸增強的屬性Buff等。
        *   `NON_STACKABLE_REFRESH_DURATION`: (布爾型)
            *   **行為:** 即使 `max_stacks` 通常為1 (或未定義 `STACKABLE_INTENSITY` / `STACKABLE_DURATION`)，當目標再次被施加同一 `status_effect_id` 的效果時，此效果的持續時間會被刷新為 `default_duration`。效果強度不疊加，直接使用新施加的效果強度（即覆蓋）。
            *   **主要用途:** 適用於那些不允許疊加層數但希望後續施加能刷新其存在時間的單層Buff/Debuff。
        *   `NON_STACKABLE_IGNORE`: (布爾型)
            *   **行為:** 如果目標身上已經存在此 `status_effect_id` 的效果（無論來源），則任何後續嘗試施加同ID的效果都將完全無效（不疊加層數、不刷新強度、不刷新持續時間）。
            *   **主要用途:** 用於一次性、獨占性的狀態，防止重複施加。
        *   `PERSISTS_THROUGH_DEATH`: (布爾型, 可選) 狀態效果在戰鬥單位死亡後是否保留（例如，用於某些特殊的死後觸發效果或統計）。默認為否。
        *   `REMOVE_ON_DEATH`: (布爾型, 可選, 默認為 true) 狀態效果在戰鬥單位死亡時是否自動移除。
        *   `LINKED_TO_CASTER`: (布爾型, 可選) 如果為true，當施法者死亡或離開戰場時，此狀態效果會從目標身上移除。

    *   **狀態效果疊加與覆蓋總體規則 (General Stacking and Overriding Rules):**
        1.  **ID唯一性：** `status_effect_id` 是判斷是否為"同種"狀態效果的唯一標識。
        2.  **默認疊加行為 (無特定 `STACKABLE_*` flags 或 `NON_STACKABLE_*` flags 時)：**
            *   如果 `max_stacks` > 1 (或者未定義但效果設計上允許多層)：表現類似於 `STACKABLE_INTENSITY`。即，後續施加會增加層數（不超過 `max_stacks`），每層獨立計算其效果值並匯總生效，同時刷新總持續時間為 `default_duration`。
            *   如果 `max_stacks` == 1 (或效果設計上為單層)：後來的同ID效果會完全覆蓋前者，包括強度和持續時間（刷新為 `default_duration`）。
        3.  **`special_flags` 優先級：** 如果配置了 `STACKABLE_DURATION`, `STACKABLE_INTENSITY`, `NON_STACKABLE_REFRESH_DURATION`, 或 `NON_STACKABLE_IGNORE`，則它們的行為優先於上述默認行為。如果多個衝突的stacking flags被設置，系統應定義一個明確的解析順序或報錯（例如，`STACKABLE_INTENSITY` 和 `NON_STACKABLE_IGNORE` 同時存在是不合理的）。
        4.  **不同來源的同ID狀態效果：** 默認情況下，只要 `status_effect_id` 相同，就被視為同種效果，遵循上述疊加/覆蓋規則，不區分其施法來源。如果遊戲設計需要來自不同技能的同名效果（例如，技能A的"燃燒"和技能B的"燃燒"）能夠獨立共存並分別計算，那麼它們必須使用不同的 `status_effect_id` (例如，`SKILL_A_BURN_EFFECT` 和 `SKILL_B_BURN_EFFECT`)。
        5.  **屬性修改類Buff/Debuff的疊加方式：**
            *   由狀態效果提供的、直接作用於戰鬥單位基礎屬性的修改（例如，在 `effect_definitions` 中 `effect_type: "STAT_MODIFICATION"`，其 `modification_type` 為 `PERCENTAGE_ADD` 或 `FLAT_ADD`），在進行最終屬性計算時，應遵循 **加算 (Additive Summation)** 原則。
            *   **計算流程：**
                1.  首先計算出戰鬥單位不受任何Buff/Debuff影響的基礎戰鬥屬性 (BaseCombatStats)。
                2.  然後，遍歷單位身上所有激活的狀態效果，收集所有同類型（如 `patk` 的 `PERCENTAGE_ADD`）的屬性修改值。
                3.  將所有 `FLAT_ADD` 類型的修改值直接相加。
                4.  將所有 `PERCENTAGE_ADD` 類型的修改值（百分比）相加。
                5.  最終屬性計算示例 (以物理攻擊 `patk` 為例):
                    `FinalPatk = (BaseCombatPatk + SumOfAll_Patk_FlatAdd) * (1 + SumOfAll_Patk_PercentageAdd)`
                    (注意：是先加固定值，再乘以百分比總和，還是反之，需要統一設計。更常見的是 `FinalPatk = BaseCombatPatk * (1 + SumOfAll_Patk_PercentageAdd_From_Buffs) + SumOfAll_Patk_FlatAdd_From_Buffs`，即百分比作用於基礎值，然後再加上固定值。)
                    **修正和明確：** 業界更常見且易於平衡的疊加方式是：各類百分比增益之間通常是加算，並作用於基礎值；各類固定值增益也是加算。一個推薦的明確公式是：
                    `FinalStat = (BaseStat + Sum_FlatBonuses_TypeA + Sum_FlatBonuses_TypeB...) * (1 + Sum_PercentageBonuses_TypeA + Sum_PercentageBonuses_TypeB...) + Sum_FinalFlatBonuses`
                    對於我們的系統，可以簡化並明確為：
                    `FinalStat = (BaseOriginalStat + Sum_All_Flat_Modifications_For_Stat) * (1 + Sum_All_Percentage_Modifications_For_Stat)`
                    其中 `BaseOriginalStat` 是指卡牌自身屬性、等級成長、星級加成、天賦靜態加成等計算後的，未計入當前戰鬥中動態Buff/Debuff時的屬性。
            *   **最終傷害修正倍率：** 與直接屬性修改不同，那些描述為"最終傷害提升X%"或"受到傷害降低Y%"的修正，在 `DamageHandler` 中應用時，通常是 **乘算 (Multiplicative)** 的，除非明確指定為加算到某個修正因子上。

7.  **`star_level_effects.json` (卡牌升星額外效果)**
    *   主鍵：`star_level_effects_key` (由 `cards.json` 中的 `star_level_effects_key` 字段引用)
    *   文件內容是一個對象，其鍵為 `star_level_effects_key`，值是另一個對象。這個內部對象的鍵是「卡牌培養星級」的特定數值 (字符串格式，例如 "0", "5", "10", "35")，值為該培養星級檔位觸發的額外效果。
    *   **示例 (`my_card_star_effects` Key對應的內容):**
        ```json
        {
          "5": { // 當卡牌培養星級達到 5 時
            "additional_stats_flat": { "patk": 50, "hp": 200 },
            "unlock_passive_skill_slot": true // 假設解鎖一個被動槽
          },
          "15": { // 當卡牌培養星級達到 15 時
            "additional_stats_percent": { "matk": 0.10 }, // 增加10%魔攻
            "apply_self_effect_on_battle_start": [ // 戰鬥開始時對自身施加效果
              {
                "effect_template": "APPLY_REGEN_STATUS_WEAK"
              }
            ]
          }
          // ... 其他培養星級檔位的效果
        }
        ```
    *   **包含效果：** `additional_stats_flat` (對象, 固定屬性增加), `additional_stats_percent` (對象, 百分比屬性增加, 基於戰鬥屬性計算前的屬性), `unlock_passive_skill_slot` (布爾), 或一個 `effect_definitions` 列表 (例如 `apply_self_effect_on_battle_start`, 其效果會由 `EffectApplier` 處理)。

8.  **`monsters.json` (怪物定義)**
    *   主鍵：`monster_id`
    *   包含：`name`, RPG屬性 (固定值), `primary_attack_skill_id`, `active_skill_order` (主動技能ID列表), `equipped_passives` (列表 of `{"skill_id": "passive_X", "level": fixed_level}`，包含怪物的"天賦"和"通用"被動)。

9.  **`floors.json` (樓層/關卡定義)**
    *   主鍵：`floor_number`
    *   包含：`name`, `wins_required_to_advance`, `entry_cost_oil`, `first_clear_rewards_key`, `repeatable_rewards_per_win_key`, `possible_encounters` (指向 `monster_groups.json`)。

10. **`monster_groups.json` (怪物組定義)**
    *   主鍵：`monster_group_key`
    *   包含：怪物ID列表，可擴展支持站位 `[{"monster_id": "goblin_A", "position": 0}, ...]`。

11. **`reward_packages.json` (獎勵包定義)**
    *   主鍵：`