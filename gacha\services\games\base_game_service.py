import asyncpg
from typing import Dict, Any, Optional
from database.postgresql.async_manager import AsyncPgManager
from gacha.services.core.economy_service import EconomyService
from gacha.exceptions import (
    MinBetNotMetError, 
    InsufficientFundsError, 
    GameSettlementError,
    DatabaseOperationError,
    UserNotFoundError
)
from utils.logger import logger

class BaseGameService:
    """提供遊戲服務通用功能的基底類別，如下注處理和獎勵結算。使用純異常模式。"""
    DEFAULT_MIN_BET = 10

    def __init__(self, economy_service: EconomyService, pool: Optional[asyncpg.Pool]=None):
        """
        初始化 BaseGameService。

        Args:
            economy_service: EconomyService 的實例。
            pool: asyncpg 連接池。如果未提供，將嘗試從 AsyncPgManager 獲取。
            
        Raises:
            ValueError: 當 economy_service 為 None 時拋出
            RuntimeError: 當無法獲取資料庫連接池時拋出
        """
        if economy_service is None:
            err_msg = f'{self.__class__.__name__} 初始化失敗：必須提供 EconomyService 實例。'
            logger.error(err_msg)
            raise ValueError(err_msg)
        self.economy_service = economy_service
        if pool is None:
            try:
                self.pool = AsyncPgManager.get_pool()
            except RuntimeError as e:
                logger.error('無法初始化 %s：asyncpg 連接池未提供也無法從管理器獲取。', self.__class__.__name__)
                raise e
        else:
            self.pool = pool

    async def _check_and_deduct_bet(self, user_id: int, bet: int, game_name: str, min_bet: Optional[int]=None) -> int:
        """
        檢查玩家餘額、最低下注要求，並在事務中扣除下注金額。
        
        Args:
            user_id: 用戶ID
            bet: 下注金額
            game_name: 遊戲名稱
            min_bet: 最低下注要求，如果未提供則使用 DEFAULT_MIN_BET
            
        Returns:
            int: 扣除下注後的新餘額
            
        Raises:
            MinBetNotMetError: 下注金額低於最低要求時拋出
            InsufficientFundsError: 餘額不足時拋出
            UserNotFoundError: 用戶不存在時拋出
            GameSettlementError: 扣除下注失敗時拋出
        """
        effective_min_bet = min_bet if min_bet is not None else self.DEFAULT_MIN_BET
        if bet < effective_min_bet:
            raise MinBetNotMetError(bet_placed=bet, min_bet=effective_min_bet)
        
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    # 獲取當前餘額
                    balance_info = await self.economy_service.get_balance(user_id)
                    current_balance = balance_info['balance']
                    
                    # 檢查餘額是否足夠
                    if current_balance < bet:
                        raise InsufficientFundsError(required_amount=bet, current_balance=current_balance)
                    
                    # 扣除下注金額
                    new_balance = await self.economy_service.award_oil(user_id, -bet, f'{game_name}下注', connection=conn)
                    logger.debug('[%s] 用戶 %s 下注成功，新餘額: %s', game_name, user_id, new_balance)
                    return new_balance
                    
        except (MinBetNotMetError, InsufficientFundsError, UserNotFoundError):
            # 重新拋出已知的遊戲異常
            raise
        except Exception as e:
            logger.error('[%s] 下注失敗 (Exception) for user %s: %s', game_name, user_id, str(e), exc_info=True)
            raise GameSettlementError(game_id='', reason=f'處理下注時發生未預期的錯誤: {e}')

    async def _settle_winnings(self, user_id: int, payout: float, bet: int, game_name: str) -> Dict[str, Any]:
        """
        根據 payout 金額結算遊戲獎勵。
        
        Args:
            user_id: 用戶ID
            payout: 獎勵金額
            bet: 原始下注金額
            game_name: 遊戲名稱
            
        Returns:
            Dict[str, Any]: 包含 new_balance 和 awarded_amount 的結算結果
            
        Raises:
            GameSettlementError: 結算失敗時拋出
        """
        int_payout = 0
        new_balance = 0
        
        try:
            if payout > 0:
                int_payout = int(round(payout))
                if int_payout <= 0:
                    logger.warning('[%s] User %s: Payout %s rounded to %s. No award will be processed.', game_name, user_id, payout, int_payout)
                    # 獲取當前餘額
                    balance_info = await self.economy_service.get_balance(user_id)
                    new_balance = balance_info['balance']
                else:
                    # 發放獎勵
                    new_balance = await self.economy_service.award_oil(user_id, int_payout, f'{game_name}遊戲贏利')
                    logger.debug('[%s] User %s: 獎勵發放成功，金額: %s，新餘額: %s', game_name, user_id, int_payout, new_balance)
            else:
                # 如果沒有獎勵，獲取當前餘額
                balance_info = await self.economy_service.get_balance(user_id)
                new_balance = balance_info['balance']
            
            return {
                'new_balance': new_balance,
                'awarded_amount': float(int_payout) if int_payout > 0 else 0.0
            }
            
        except Exception as e:
            logger.error('[%s] Settlement failed unexpectedly for user %s: %s', game_name, user_id, str(e), exc_info=True)
            raise GameSettlementError(game_id='', reason=f'結算獎勵時發生未預期的錯誤: {e}')

    async def rollback_bet(self, user_id: int, bet_amount: int, game_name: str, reason: str='遊戲創建失敗') -> int:
        """
        回滾先前扣除的賭注。
        
        Args:
            user_id: 用戶ID
            bet_amount: 要回滾的金額
            game_name: 遊戲名稱
            reason: 回滾原因
            
        Returns:
            int: 回滾後的新餘額
            
        Raises:
            GameSettlementError: 回滾失敗時拋出
        """
        log_prefix = f'[{game_name}] Bet Rollback for user {user_id}:'
        
        if bet_amount <= 0:
            logger.warning('%s Invalid bet_amount for rollback: %s. Skipping.', log_prefix, bet_amount)
            raise GameSettlementError(game_id='', reason=f'無效的回滾金額: {bet_amount}')
        
        try:
            rollback_reason = f'{game_name}下注回滾 ({reason})'
            new_balance = await self.economy_service.award_oil(user_id, bet_amount, rollback_reason)
            
            logger.info('%s Successfully rolled back bet amount %s. New balance: %s', log_prefix, bet_amount, new_balance)
            return new_balance
                
        except Exception as e:
            logger.error('%s Failed to roll back bet: %s', log_prefix, str(e), exc_info=True)
            raise GameSettlementError(game_id='', reason=f'回滾賭注失敗: {str(e)}') from e