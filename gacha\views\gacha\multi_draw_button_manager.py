"""
Gacha系統十連抽視圖的按鈕管理器
"""
from typing import TYPE_CHECKING
import discord
from gacha.views.collection.favorite_component import FavoriteComponent
from .multi_draw_button_factory import MultiDrawButtonFactory

if TYPE_CHECKING:
    from .multi_draw_view import MultiDrawView # 避免循環導入

class MultiDrawButtonManager:
    """管理 MultiDrawView 的按鈕創建、添加和狀態更新。"""

    def __init__(self, view: 'MultiDrawView'):
        self.view = view

    def create_all_buttons(self):
        """創建所有按鈕實例並將它們附加到視圖實例上。"""
        view = self.view
        view.continue_button = MultiDrawButtonFactory.create_continue_button(view._continue_multi_draw_callback)
        
        current_initial_card_info = view._processed_cards_view_data[0] if view._processed_cards_view_data else None
        initial_card_id = current_initial_card_info.card.card_id if current_initial_card_info and current_initial_card_info.card else 0
        initial_is_favorite = current_initial_card_info.is_favorite if current_initial_card_info else False

        view.favorite_button = FavoriteComponent.create_initial_favorite_button(
            view=view,
            user_id=view.user_id,
            card_id=initial_card_id, 
            is_favorite=initial_is_favorite,
            row=0,
            custom_id='md_toggle_favorite'
        )
        view.prev_button = MultiDrawButtonFactory.create_prev_page_button(view._previous_button_callback)
        view.next_button = MultiDrawButtonFactory.create_next_page_button(view._next_button_callback)

    def add_buttons_to_view(self):
        """將視圖上已創建的按鈕添加到視圖的 items 中。"""
        view = self.view
        view.add_item(view.continue_button)
        view.add_item(view.favorite_button)
        view.add_item(view.prev_button)
        view.add_item(view.next_button)

    def refresh_buttons_state(self):
        """刷新所有按鈕的狀態。"""
        view = self.view
        # 更新翻頁按鈕狀態
        view.prev_button.disabled = view.total_cards <= 1 or view.current_index == 0
        view.next_button.disabled = view.total_cards <= 1 or view.current_index == view.total_cards - 1

        # 更新最愛按鈕狀態
        if view._processed_cards_view_data and 0 <= view.current_index < view.total_cards:
            current_processed_info = view._processed_cards_view_data[view.current_index]
            is_favorite = current_processed_info.is_favorite
            FavoriteComponent.update_button_state(view.favorite_button, is_favorite)
            view.favorite_button.disabled = False
        else:
            FavoriteComponent.update_button_state(view.favorite_button, False)
            view.favorite_button.disabled = True
            
        view.continue_button.disabled = False # 通常保持啟用 