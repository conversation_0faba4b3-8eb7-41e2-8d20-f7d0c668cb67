"""
Gacha系统存储库初始化模块
提供存储库类的单一引用点
"""

from gacha.repositories.user.user_repository import UserRepository
from gacha.repositories.card.master_card_repository import MasterCardRepository
from gacha.repositories.collection.user_collection_repository import (
    UserCollectionRepository,
)
from gacha.repositories.leaderboard.leaderboard_repository import LeaderboardRepository
from gacha.repositories.collection.user_wish_repository import UserWishRepository
from gacha.repositories.portfolio.player_portfolio_repository import (
    PlayerPortfolioRepository,
)
from gacha.repositories.market.stock_asset_repository import StockAssetRepository
from gacha.repositories.trading.card_trade_history_repository import (
    CardTradeHistoryRepository,
)

__all__ = [
    "UserRepository",
    "MasterCardRepository",
    "UserCollectionRepository",
    "LeaderboardRepository",
    "UserWishRepository",
    "PlayerPortfolioRepository",
    "StockAssetRepository",
    "CardTradeHistoryRepository",
]
