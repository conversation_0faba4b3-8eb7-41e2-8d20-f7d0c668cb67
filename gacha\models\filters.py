"""
Gacha系統篩選條件模型
定義用於卡片收藏系統的篩選條件結構
"""

from dataclasses import dataclass
from typing import Optional, List


@dataclass
class CollectionFilters:
    """卡片收藏篩選條件數據類

    封裝所有可能的卡片收藏篩選條件，提高可擴展性和可維護性
    """

    # 卡池類型篩選（例如：'main', 'special'）
    pool_type: Optional[str] = None

    # 稀有度篩選列表（例如：[1, 2]）- 使用數字稀有度
    rarity_in: Optional[List[int]] = None  # Changed type hint to List[int]

    # 系列篩選
    series: Optional[str] = None

    # 數量篩選（大於指定值）
    quantity_greater_than: Optional[int] = None

    # 卡片ID精確篩選
    card_id: Optional[int] = None

    # 卡片名稱模糊篩選
    card_name: Optional[str] = None

    # 是否只包含重複的卡片（數量大於1）
    # 注意：在賣卡命令中，推薦使用 operation=leave_one 代替此屬性
    only_duplicates: bool = False

    # 是否保留每種卡片的一張
    leave_one: bool = False

    # 稀有度排除列表（例如：[7] 排除 TS）- 使用數字稀有度
    rarity_not_in: Optional[List[int]] = None  # Changed type hint to List[int]

    # 系列排除
    series_not_in: Optional[List[str]] = None

    # 用於擴展的字典，可以存儲未來可能添加的篩選條件
    extra_filters: dict = None

    def __post_init__(self):
        """初始化後處理

        確保列表類型的屬性為空列表而非None
        """
        if self.rarity_in is None:
            self.rarity_in = []

        if self.rarity_not_in is None:
            self.rarity_not_in = []

        if self.series_not_in is None:
            self.series_not_in = []

        if self.extra_filters is None:
            self.extra_filters = {}

    def has_any_filter(self) -> bool:
        """檢查是否有除了 'exclude_favorites' 和 'include_favorites' 之外的任何篩選條件被設置"""
        # 檢查主要篩選屬性
        if (
            self.pool_type is not None
            or (self.rarity_in and len(self.rarity_in) > 0)
            or self.series is not None
            or self.quantity_greater_than is not None
            or self.card_id is not None
            or self.card_name is not None
            or self.only_duplicates is True  # 假設 only_duplicates=False 是無篩選
            or (self.rarity_not_in and len(self.rarity_not_in) > 0)
            or (self.series_not_in and len(self.series_not_in) > 0)
        ):
            return True

        # 檢查 extra_filters 是否有 'exclude_favorites' 和 'include_favorites' 以外的鍵
        if self.extra_filters:
            for key, value in self.extra_filters.items():
                if key not in ["exclude_favorites", "include_favorites"]:
                    # 假設任何其他鍵且其值不為 None 表示篩選器處於活動狀態
                    if value is not None:
                        return True
        return False
