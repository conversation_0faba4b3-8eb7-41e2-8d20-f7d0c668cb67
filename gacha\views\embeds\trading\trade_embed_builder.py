import discord
from typing import Dict, Optional
import logging
from gacha.repositories.card.master_card_repository import MasterCardRepository
logger = logging.getLogger(__name__)

class TradeEmbedBuilder:

    def __init__(self, bot):
        self.bot = bot
        try:
            if hasattr(self.bot, 'master_card_repo'):
                self.master_card_repo: Optional[MasterCardRepository] = self.bot.master_card_repo
                logger.info('TradeEmbedBuilder: MasterCardRepository successfully obtained from bot.master_card_repo.')
            else:
                self.master_card_repo = None
                logger.warning('TradeEmbedBuilder: bot.master_card_repo not found. MasterCardRepository could not be initialized. Card names might not be resolved.')
        except Exception as e:
            logger.error('TradeEmbedBuilder: Error initializing MasterCardRepository: %s. Card names may not be resolved.', e, exc_info=True)
            self.master_card_repo = None

    async def _get_card_name(self, card_id: Optional[str]) -> str:
        if not card_id:
            return '未知卡片'
        if not self.master_card_repo:
            logger.warning('TradeEmbedBuilder: MasterCardService not available. Returning ID for card_id: %s', card_id)
            return f'卡片ID: {card_id}'
        try:
            card = await self.master_card_repo.get_card(int(card_id))
            if card and hasattr(card, 'name'):
                return card.name
            else:
                logger.warning('TradeEmbedBuilder: Card with ID %s not found or has no name attribute.', card_id)
                return f'未知卡片 (ID: {card_id})'
        except Exception as e:
            logger.error('TradeEmbedBuilder: Error fetching card name for %s: %s', card_id, e, exc_info=True)
            return f'錯誤 (ID: {card_id})'

    async def build_trade_request_embed(self, trade_details: Dict, initiator: discord.User, target_user: discord.User) -> discord.Embed:
        """
        構建發送給目標用戶的交易請求 Embed。
        """
        offer_card_id = trade_details.get('offered_card_id')
        offer_quantity = trade_details.get('offered_quantity', 1)
        request_card_id = trade_details.get('requested_card_id')
        request_quantity = trade_details.get('request_quantity', 1)
        price = trade_details.get('price')
        trade_id = trade_details.get('trade_id', 'N/A')
        offer_card_name = await self._get_card_name(offer_card_id)
        description = f'{initiator.mention} 向您發起了一個交易請求！\n\n'
        description += '**他們提供：**\n'
        description += f'- {offer_card_name} (ID: `{offer_card_id}`) x {offer_quantity}\n\n'
        if request_card_id:
            request_card_name = await self._get_card_name(request_card_id)
            description += '**他們想要：**\n'
            description += f'- {request_card_name} (ID: `{request_card_id}`) x {request_quantity}\n'
        elif price is not None:
            description += '**他們索要油幣：**\n'
            description += f'- {price:.2f} 油幣\n'
        else:
            description += '**他們想要：**\n'
            description += '- (無特定要求，可能是贈送)\n'
        embed = discord.Embed(title='📬 新的交易請求', description=description, color=discord.Color.blue(), timestamp=discord.utils.utcnow())
        embed.set_author(name=f'來自 {initiator.display_name} 的交易', icon_url=initiator.display_avatar.url if initiator.display_avatar else None)
        embed.set_footer(text=f'交易 ID: {trade_id} | 請在30分鐘內回應')
        return embed

    async def build_trade_status_embed(self, trade_details: Dict, status: str, reason: Optional[str]=None, acting_user: Optional[discord.User]=None) -> discord.Embed:
        """
        構建交易狀態更新的 Embed (用於編輯原始訊息或發送通知)。
        """
        trade_id = trade_details.get('trade_id', 'N/A')
        initiator_id = trade_details.get('initiator_id')
        target_user_id = trade_details.get('receiver_id')
        initiator = self.bot.get_user(initiator_id) or await self.bot.fetch_user(initiator_id)
        target_user = self.bot.get_user(target_user_id) or await self.bot.fetch_user(target_user_id)
        offer_card_id = trade_details.get('offered_card_id')
        offer_quantity = trade_details.get('offered_quantity', 1)
        request_card_id = trade_details.get('requested_card_id')
        request_quantity = trade_details.get('request_quantity', 1)
        price = trade_details.get('price')
        offer_card_name = await self._get_card_name(offer_card_id)
        title = f'🔁 交易更新 - {status}'
        color = discord.Color.default()
        if status in ['已接受', '已成功', '完成']:
            color = discord.Color.green()
            title = f'✅ 交易 {status}!'
        elif status in ['已拒絕', '已取消']:
            color = discord.Color.red()
            title = f'🚫 交易 {status}'
        elif status == '已過期':
            color = discord.Color.orange()
            title = f'⏳ 交易 {status}'
        elif status == '失敗':
            color = discord.Color.dark_red()
            title = f'❌ 交易 {status}'
        description = f'交易 ID: `{trade_id}`\n'
        description += f"發起者: {(initiator.mention if initiator else f'用戶ID {initiator_id}')}\n"
        description += f"目標用戶: {(target_user.mention if target_user else f'用戶ID {target_user_id}')}\n\n"
        description += '**發起方提供：**\n'
        description += f'- {offer_card_name} (ID: `{offer_card_id}`) x {offer_quantity}\n'
        if request_card_id:
            request_card_name = await self._get_card_name(request_card_id)
            description += '**發起方要求：**\n'
            description += f'- {request_card_name} (ID: `{request_card_id}`) x {request_quantity}\n'
        elif price is not None:
            description += '**發起方索要油幣：**\n'
            description += f'- {price:.2f} 油幣\n'
        if reason:
            description += f'\n原因：{reason}'
        if acting_user:
            description += f'\n操作者：{acting_user.mention}'
        embed = discord.Embed(title=title, description=description, color=color, timestamp=discord.utils.utcnow())
        if acting_user and acting_user.display_avatar:
            embed.set_author(name=f'由 {acting_user.display_name} 操作', icon_url=acting_user.display_avatar.url)
        embed.set_footer(text=f'交易 ID: {trade_id}')
        return embed

    async def build_trade_notification_embed(self, original_trade_details: Dict, message: str, success: bool, recipient: discord.User) -> discord.Embed:
        """
        構建一個通用的交易結果通知 Embed。
        """
        trade_id = original_trade_details.get('trade_id', 'N/A')
        title = '✅ 交易通知' if success else '❌ 交易通知'
        color = discord.Color.green() if success else discord.Color.red()
        embed = discord.Embed(title=title, description=message, color=color, timestamp=discord.utils.utcnow())
        embed.set_author(name=f'給 {recipient.display_name} 的通知', icon_url=recipient.display_avatar.url if recipient.display_avatar else None)
        embed.set_footer(text=f'相關交易 ID: {trade_id}')
        return embed