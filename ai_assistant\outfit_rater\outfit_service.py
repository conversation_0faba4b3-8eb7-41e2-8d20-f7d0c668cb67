"""
Discord命令處理模組 - 提供與Discord機器人的整合
"""

import discord
import logging
import os
import tempfile
import asyncio
import datetime
import uuid
import json
from typing import Optional, Any

# 導入必要組件
from .outfit_rater import OutfitRater
from .rating_image_generator import RatingImageGenerator
# from .browser_pool import BrowserPoolManager # Removed
from ..message_handler import MessageHandler
from .. import config as ai_config
# Import PlaywrightManager for type hinting
from utils.playwright_manager import PlaywrightManager


# 設置日誌
logger = logging.getLogger(__name__) # Use __name__

# 初始化全局變量
rater: Optional[OutfitRater] = None
image_generator: Optional[RatingImageGenerator] = None
message_handler: Optional[MessageHandler] = None
# shared_browser_pool: Optional[BrowserPoolManager] = None # Removed

def _get_sanitized_user_display_name(processing_message: Optional[discord.WebhookMessage], user_id_str: Optional[str]) -> str:
    """ (Unchanged)
    Safely gets and sanitizes a user display name for use in filenames.
    Tries to get from processing_message.interaction_metadata, falls back to user_id, then to a default.
    """
    display_name_to_sanitize = None

    if processing_message:
        interaction_from_webhook = getattr(processing_message, 'interaction_metadata', None)
        if interaction_from_webhook:
            user_obj_from_interaction = getattr(interaction_from_webhook, 'user', None)
            if user_obj_from_interaction:
                display_name_to_sanitize = getattr(user_obj_from_interaction, 'display_name', None)

    if not display_name_to_sanitize and user_id_str:
        # If display_name couldn't be fetched, use a generic one based on user_id
        return f"user_{user_id_str[:8]}" # Using 8 chars of user_id for fallback

    if not display_name_to_sanitize:
        return "unknown_user"

    # Sanitize the display name
    # Allow alphanumeric, underscore, hyphen. Replace others or remove.
    # Here, we'll keep it simple by removing invalid characters.
    sanitized_name = "".join(c for c in display_name_to_sanitize if c.isalnum() or c in ('_', '-')).strip()

    if not sanitized_name: # If sanitization results in an empty string
        return f"user_{user_id_str[:8]}" if user_id_str else "unknown_user"
    
    return sanitized_name

async def init_services(playwright_manager: PlaywrightManager) -> bool:
    """
    初始化穿搭評分服務。
    Args:
        playwright_manager: The shared PlaywrightManager instance.
    Returns:
        True if initialization was successful, False otherwise.
    """
    global rater, image_generator, message_handler
    logger.info("Initializing OutfitRaterService components...")

    # Reset existing instances if any (e.g., during reload)
    rater = None
    image_generator = None
    message_handler = None

    try:
        if not playwright_manager:
             logger.error("PlaywrightManager instance is required for OutfitRaterService initialization.")
             return False

        # Initialize components using the shared manager
        rater = OutfitRater()
        image_generator = RatingImageGenerator(playwright_manager=playwright_manager)
        message_handler = MessageHandler(service_name="OutfitRater")

        logger.info("OutfitRaterService components initialized successfully using shared PlaywrightManager.")
        return True
    except Exception as e:
        logger.error(f"Error during OutfitRaterService initialization: {e}", exc_info=True)
        # Reset globals on failure
        rater = None
        image_generator = None
        message_handler = None
        return False

async def shutdown_services():
    """重置穿搭評分服務的全局引用。"""
    global rater, image_generator, message_handler
    logger.info("Resetting OutfitRaterService component references...")

    # Simply reset the global references. The shared PlaywrightManager
    # should be closed by the main application shutdown logic.
    rater = None
    image_generator = None
    message_handler = None
    logger.info("OutfitRaterService component references have been reset.")

async def rate_outfit_slash(interaction: discord.Interaction, image: Optional[discord.Attachment] = None, url: Optional[str] = None):
    """評分穿搭的Discord斜線命令"""
    # 檢查服務是否可用
    if not rater or not image_generator or not message_handler:
        await interaction.response.send_message("❌ 穿搭評分服務暫時不可用，請稍後再試 (服務未初始化)", ephemeral=True)
        logger.warning("rate_outfit_slash called but services are not initialized.")
        return

    # Ensure the image_generator has a valid manager
    if image_generator.playwright_manager is None:
        await interaction.response.send_message("❌ 穿搭評分服務圖像生成器配置錯誤，請聯繫管理員", ephemeral=True)
        logger.error("rate_outfit_slash: image_generator.playwright_manager is None after initialization.")
        return

    # Determine URL from interaction if no direct image attachment (Unchanged)
    # MessageHandler will try to find an image from history if `image` and `url` are None.
    # If a URL was passed in chat and triggered the command, interaction.message.content might have it.
    # `commands.py` mocks interaction.message for URL-only cases.
    url_from_content = None
    if not image and hasattr(interaction, 'message') and interaction.message and interaction.message.content:
        # A simple check; MessageHandler has more robust URL finding.
        # This is primarily to set the processing message correctly.
        if "http://" in interaction.message.content or "https://" in interaction.message.content:
             url_from_content = interaction.message.content # Or parse more strictly if needed

    processing_message_text = "📸 正在分析您的穿搭照片，請稍候..."
    if url_from_content and not image: # If URL seems to be the source
        processing_message_text = "🔗 正在從URL下載並分析您的穿搭照片，請稍候..."
    elif not image and not url_from_content: # If no direct image and no URL in message, MessageHandler will search history
        processing_message_text = "🖼️ 正在查找並分析圖像，請稍候..."

    # MessageHandler expects `image` (discord.Attachment) or `url` (str).
    # It will then produce `image_data` for the processor_func.
    # The `processor_func` should expect `image_data` as its first arg.
    await message_handler.handle_interaction(
        interaction=interaction,
        processing_message=processing_message_text,
        processor_func=_process_image_rating_in_background, # Pass the function directly
        image=image,  # Pass the direct image attachment if available
        url=url_from_content # Pass extracted URL if no direct image
        # user_prompt is not used by this service, so defaults to None in MessageHandler
    )

async def _process_image_rating_in_background(
    image_data: bytes, # Now expects image_data directly from MessageHandler
    user_id: str,      # Expects user_id from MessageHandler
    processing_message: discord.WebhookMessage, # Expects processing_message from MessageHandler
    request_id: str,   # Expects request_id from MessageHandler
    user_prompt: Optional[str] = None # Expects user_prompt, though not used here
):
    """在背景任務中處理圖像評分的主要邏輯"""
    # user_id_str is now user_id
    # request_id is now passed directly
    # No need for interaction here as MessageHandler handles initial response and followup.
    # We use processing_message (which is a WebhookMessage) to edit the status.

    user_display_name = _get_sanitized_user_display_name(processing_message, user_id)
    output_image_path = None # Initialize
    temp_user_image_for_generation = None # For storing image_data if needed for generator

    try:
        # image_data_for_rating is now simply image_data
        if not image_data:
            logger.error(f"[{request_id}] 沒有提供有效的圖像數據進行評分 (從 MessageHandler 收到 None)。")
            await processing_message.edit(content="❌ 未能獲取圖像數據進行評分，請重試。")
            return

        logger.info(f"[{request_id}] OutfitRater Service: Received {len(image_data)} bytes of image data for user {user_id}.")
        start_time = asyncio.get_event_loop().time()
        rating_result = await rater.rate_outfit_from_binary(
            image_data=image_data, # Use the passed image_data
            request_id=request_id,
            user_id=user_id # Pass the string user_id
        )
        # Add request_id to rating_result before passing it to image_generator for better tracking
        if rating_result: # Ensure rating_result is not None
            rating_result["request_id"] = request_id
        
        # Log the full rating result
        logger.info(f"[{request_id}] AI rating result for user {user_id}: {json.dumps(rating_result, ensure_ascii=False, indent=2) if rating_result else 'None'}")
        
        duration = asyncio.get_event_loop().time() - start_time
        logger.info(f"[{request_id}] OutfitRater AI processing completed, duration: {duration:.2f}s")

        if not rating_result or not rating_result.get("score"):
            logger.error(f"[{request_id}] AI評分返回結果無效或不包含分數。結果: {rating_result}")
            await processing_message.edit(content="😕 AI未能完成評分，請稍後再試或換張圖片試試。")
            return
        
        await processing_message.edit(content="👗 評分完成，正在生成評分圖片...")

        output_dir = os.path.join("ai_assistant", "outfit_rater", "ratings")
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate filename in the format: UserName_DateTime_UUID.webp
        timestamp_str = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        # request_id is already a UUID string from MessageHandler
        unique_filename = f"{user_display_name}_{timestamp_str}_{request_id}.webp"
        output_image_path = os.path.join(output_dir, unique_filename)

        # RatingImageGenerator needs a file path for user_image_path.
        # We have image_data (bytes). So, we must save it to a temporary file.
        # Ensure the temporary file for user image also uses a common image extension recognized by Pillow/Selenium if it matters for loading
        # Though for saving image_data bytes, any suffix is fine, but for consistency let's use .png or .jpg
        # For generate_rating_image, the user_image_path is the input, so its original format is preserved there.
        # The temp_user_image_for_generation is just to get the bytes to the generator if it needs a path.
        # Let's ensure this temp file (if created from raw bytes) has a common image suffix.
        # The suffix used by tempfile.mkstemp here is for the file that RatingImageGenerator will read.
        # RatingImageGenerator loads this then generates a new WebP image.
        temp_suffix = '.png' # Default suffix for the temp image file if created from bytes
        # A more robust way would be to try and sniff the image type from image_data if possible,
        # or use the original attachment's extension if available and image_data came from an attachment.
        # However, Pillow can usually open images without needing the correct extension if the format is standard.

        fd_gen, temp_user_image_for_generation = tempfile.mkstemp(suffix=temp_suffix) 
        os.close(fd_gen)
        with open(temp_user_image_for_generation, 'wb') as f_temp_gen:
            f_temp_gen.write(image_data)
        logger.info(f"[{request_id}] User image data saved to temporary file {temp_user_image_for_generation} for card generation.")
        
        user_image_for_generation_path = temp_user_image_for_generation
            
        img_gen_start_time = asyncio.get_event_loop().time()
        await image_generator.generate_rating_image(
            rating_result=rating_result,
            user_image_path=user_image_for_generation_path,
            output_path=output_image_path,
            user_name=user_display_name # Use the determined display name
        )
        img_gen_duration = asyncio.get_event_loop().time() - img_gen_start_time
        logger.info(f"[{request_id}] Rating image generation completed, duration: {img_gen_duration:.2f}s. Image path: {output_image_path}")

        with open(output_image_path, 'rb') as f_img_out:
            discord_file = discord.File(f_img_out, filename=os.path.basename(output_image_path))
            score_str = rating_result.get('score', 'N/A')
            response_content = f"✨ {user_display_name}的穿搭評分 ({score_str}) 準備好啦！ ✨"
            # Edit the original processing_message with the final result
            await processing_message.edit(content=response_content, attachments=[discord_file])

    except Exception as e:
        logger.error(f"[{request_id}] Error in _process_image_rating_in_background for user {user_id}: {str(e)}", exc_info=True)
        try:
            # Ensure processing_message exists before trying to edit it
            if processing_message:
                 await processing_message.edit(content="😵 處理您的請求時發生了意料之外的錯誤，攻城獅已收到通知！請稍後再試。")
            else: # Should not happen if MessageHandler worked as expected
                 logger.error(f"[{request_id}] processing_message was None, cannot send error status to user.")
        except discord.errors.NotFound: # Webhook message might have expired or been deleted
            logger.warning(f"[{request_id}] Failed to edit processing_message: WebhookMessage Not Found. Interaction may have expired or message deleted.")
        except Exception as followup_exc:
            logger.error(f"[{request_id}] Failed to send error status via processing_message.edit: {followup_exc}", exc_info=True)
    finally:
        # Clean up the temporary file created for the rating image generator
        if temp_user_image_for_generation and os.path.exists(temp_user_image_for_generation):
            try:
                os.remove(temp_user_image_for_generation)
                logger.info(f"[{request_id}] Deleted temporary user image file: {temp_user_image_for_generation}")
            except OSError as e_remove_gen:
                logger.error(f"[{request_id}] Failed to delete temporary user image file {temp_user_image_for_generation}: {e_remove_gen}")
        
        # The original image_path (if it was a temp file created in rate_outfit_slash) is not available here anymore.
        # MessageHandler is responsible for any temp files it might create for URL downloads if that feature is fully used.
        # The output_image_path is kept as it's sent to Discord. Consider a periodic cleanup for 'ratings' directory.
        pass

async def setup(bot):
    """初始化穿搭評分服務"""
    success = await init_services()
    logger.info("穿搭評分服務初始化" + ("成功" if success else "失敗"))
    return success 