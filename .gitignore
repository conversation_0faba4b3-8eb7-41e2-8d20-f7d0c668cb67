# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虛擬環境
venv/
ENV/
env/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo

# 數據庫文件
*.db
*.sqlite3
*.rdb
*.shm
*.wal

# 其他
.DS_Store
Thumbs.db

# 敏感配置信息
config.json
credentials.json
secrets.json
token.txt

# 縮略圖
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db

# 緩存
.cache/
__pycache__/
.parcel-cache/

# 臨時文件
*.bak
*.tmp
*.temp
*.swp
*~

# 本地歷史
.history/
.ionide/

# 個人配置
.npmrc
.yarnrc

# 測試相關
.coverage
htmlcov/
.pytest_cache/
.tox/
nosetests.xml
coverage.xml

# 遊戲開發相關
*.save
*.backup
assets/temp/
temp_assets/

# Discord相關
discord.log
.env.local
token.*.txt

# 性能分析
*.prof
profile_results/

# 编译文件（增强现有规则）
*.pyc
*.pyo
*.pyd

# 媒体文件（如果不需要版本控制）
# 注释掉如果需要提交这些资源
*.png
# *.jpg
# *.jpeg
# *.gif
# *.mp3
# *.wav

# IDE/编辑器特定文件
.cursorrules
*.TAG
.name

# 示例文件（通常是模板，如果需要提交请注释）
# *.example

# 特定數據庫文件
dump.rdb
battles/data/db/ladder.db-shm
battles/data/db/ladder.db-wal
battles/data/db/pve.db-shm
battles/data/db/pve.db-wal
database/gacha_waifu/
logs/
downloaded_gacha_master_cards/
# AI Assistant generated content
ai_assistant/outfit_rater/ratings/
ai_assistant/outfit_rater/data/