import discord
from typing import Any, Callable, TYPE_CHECKING, Awaitable, Optional
from utils.logger import logger

class GameDisplayService:

    async def display_game_end(self, interaction: discord.Interaction, game_name: str, game_result: Any, original_bet: int, view_provider: Callable[..., Awaitable[discord.ui.View]], game_specific_embed_builder: Callable[[Any, int], discord.Embed], send_new_message: bool=False, awarded_amount: Optional[float]=None) -> None:
        """處理遊戲結束消息的顯示，包括embeds和帶有重玩選項的view。
        
        參數:
            interaction: Discord 互動對象
            game_name: 遊戲名稱
            game_result: 遊戲結果數據
            original_bet: 原始下注金額
            view_provider: 提供視圖的異步函數
            game_specific_embed_builder: 構建特定遊戲嵌入的函數
            send_new_message: 是否發送新消息而不是編輯現有消息
            awarded_amount: 獎勵金額（可選）
        """
        try:
            embed = game_specific_embed_builder(game_result, original_bet)
            game_over_view = await view_provider()
            message_content = None
            
            if send_new_message:
                if interaction.response.is_done():
                    await interaction.followup.send(content=message_content, embed=embed, view=game_over_view, ephemeral=False)
                else:
                    await interaction.response.send_message(content=message_content, embed=embed, view=game_over_view, ephemeral=False)
            elif interaction.response.is_done():
                await interaction.edit_original_response(content=message_content, embed=embed, view=game_over_view)
            else:
                logger.error('Attempted to edit original response but interaction was not done. Sending ephemeral followup. Interaction ID: %s', interaction.id)
                await interaction.followup.send(content='處理遊戲顯示時發生錯誤，請稍後再試。', ephemeral=True)
        except Exception as e:
            logger.error('顯示遊戲結束時發生錯誤: %s', str(e), exc_info=True)
            raise RuntimeError(f"顯示遊戲結束時發生錯誤: {str(e)}")