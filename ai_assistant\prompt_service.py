import yaml
import logging # 新增 logging 導入
from typing import Tuple, Dict, Optional, List # 新增 List
import random # 新增 random

# 設定基本的日誌記錄器
# logger = logging.getLogger(__name__) # 移至 class 內部或設定為全域
# logging.basicConfig(level=logging.INFO) # 可以在應用程式層級設定

class PromptService:
    def __init__(self, prompts_file_path: str = "ai_assistant/prompts/news_prompts.yaml"):
        """
        初始化 PromptService，載入指定路徑的 YAML 提示詞檔案。

        Args:
            prompts_file_path (str): 提示詞 YAML 檔案的路徑。
        """
        self.prompts_file_path = prompts_file_path
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}") # 初始化 logger
        self.prompts = self._load_prompts()

    def _load_prompts(self) -> Dict:
        """
        從 YAML 檔案載入提示詞。

        Returns:
            Dict: 載入的提示詞字典。
        
        Raises:
            FileNotFoundError: 如果找不到提示詞檔案。
            yaml.YAMLError: 如果 YAML 檔案解析失敗。
        """
        try:
            with open(self.prompts_file_path, 'r', encoding='utf-8') as f:
                prompts_data = yaml.safe_load(f)
                if prompts_data is None:
                    # 如果 YAML 檔案為空或只包含註解，safe_load 可能返回 None
                    return {}
                return prompts_data
        except FileNotFoundError:
            # 可以在這裡加入日誌記錄
            self.logger.error(f"找不到提示詞檔案：{self.prompts_file_path}")
            raise
        except yaml.YAMLError as e:
            # 可以在這裡加入日誌記錄
            self.logger.error(f"解析 YAML 檔案失敗：{self.prompts_file_path} - {e}")
            raise

    def get_prompt_templates(self, news_type: str, character_archetype: str) -> Optional[Tuple[str, str]]:
        """
        根據新聞類型和角色原型獲取 system_prompt 和 user_prompt_template。

        Args:
            news_type (str): 新聞類型 (例如 "PTT_ANALYSIS")。
            character_archetype (str): 角色原型 (例如 "PTT_USER")。

        Returns:
            Optional[Tuple[str, str]]: 包含 (system_prompt, user_prompt_template) 的元組，
                                       如果找不到則返回 None。
        """
        news_type_prompts = self.prompts.get(news_type)
        if not news_type_prompts:
            self.logger.warning(f"在 {self.prompts_file_path} 中找不到新聞類型 '{news_type}' 的提示詞。")
            return None

        archetype_prompts = news_type_prompts.get(character_archetype)
        if not archetype_prompts:
            self.logger.warning(f"在 {self.prompts_file_path} 中找不到新聞類型 '{news_type}' 下角色原型 '{character_archetype}' 的提示詞。")
            return None

        system_prompt = archetype_prompts.get("system_prompt")
        
        user_prompts_list = archetype_prompts.get("user_prompts")
        user_prompt_template_single = archetype_prompts.get("user_prompt_template")

        selected_user_prompt = None

        if user_prompts_list and isinstance(user_prompts_list, list) and user_prompts_list:
            selected_user_prompt = random.choice(user_prompts_list)
        elif user_prompt_template_single and isinstance(user_prompt_template_single, str):
            selected_user_prompt = user_prompt_template_single
        
        if system_prompt is None or selected_user_prompt is None:
            missing_parts = []
            if system_prompt is None:
                missing_parts.append("system_prompt")
            if selected_user_prompt is None:
                missing_parts.append("user_prompts/user_prompt_template")
            
            self.logger.warning(f"新聞類型 '{news_type}' 下角色原型 '{character_archetype}' 的提示詞結構不完整 (缺少 {', '.join(missing_parts)})。")
            return None
            
        return system_prompt, selected_user_prompt

    def get_formatted_prompt(self, news_type: str, character_archetype: str, data: Dict) -> Optional[Tuple[str, str]]:
        """
        獲取格式化後的 system_prompt 和 user_prompt。

        Args:
            news_type (str): 新聞類型。
            character_archetype (str): 角色原型。
            data (Dict): 用於格式化 user_prompt_template 的數據字典。

        Returns:
            Optional[Tuple[str, str]]: 包含格式化後的 (system_prompt, user_prompt) 的元組，
                                       如果找不到模板或格式化失敗則返回 None。
        """
        templates = self.get_prompt_templates(news_type, character_archetype)
        if not templates:
            return None

        system_prompt, user_prompt_template = templates
        
        # 不再進行兼容性處理，直接使用傳入的 data
        # 調用者有責任確保 data 字典包含模板所需的所有鍵

        try:
            # 格式化 system_prompt (如果它也包含佔位符)
            # 根據設計文件，system_prompt 也可能包含如 {stock_name} 的佔位符
            formatted_system_prompt = system_prompt.format(**data)
            formatted_user_prompt = user_prompt_template.format(**data)
            return formatted_system_prompt, formatted_user_prompt
        except KeyError as e:
            self.logger.error(f"格式化提示詞時缺少鍵：{e}。新聞類型: {news_type}, 角色: {character_archetype}, 數據: {data}")
            return None
        except Exception as e:
            self.logger.error(f"格式化提示詞時發生未知錯誤：{e}。新聞類型: {news_type}, 角色: {character_archetype}")
            return None

if __name__ == '__main__':
    # 不再使用 basicConfig 設定日誌級別，使用統一的日誌系統
    # 測試 PromptService
    # 假設 ai_assistant/prompts/news_prompts.yaml 存在且內容正確
    try:
        prompt_service = PromptService()

        # 測試 PTT_ANALYSIS
        ptt_data = {
            "stock_name": "台積電",
            "stock_symbol": "2330",
            "price_a": "600",
            "change_direction": "漲",
            "price_b": "650",
            "sentiment": "positive"
        }
        ptt_prompts = prompt_service.get_formatted_prompt("PTT_ANALYSIS", "PTT_USER", ptt_data)
        if ptt_prompts:
            print("--- PTT 分析 ---")
            print(f"System Prompt:\n{ptt_prompts[0]}\n")
            print(f"User Prompt:\n{ptt_prompts[1]}\n")

        # 測試 CORPORATE_ANNOUNCEMENT - CORPORATE_SPOKESPERSON
        announcement_data_spokesperson = {
            "stock_name": "聯發科",
            "stock_symbol": "2454",
            "event_type": "新產品發布",
            "event_details": "成功研發天璣9999晶片，效能提升50%。",
            "sentiment": "positive"
        }
        corp_prompts_spokesperson = prompt_service.get_formatted_prompt("CORPORATE_ANNOUNCEMENT", "CORPORATE_SPOKESPERSON", announcement_data_spokesperson)
        if corp_prompts_spokesperson:
            print("--- 公司重大公告 (發言人) ---")
            print(f"System Prompt:\n{corp_prompts_spokesperson[0]}\n")
            print(f"User Prompt:\n{corp_prompts_spokesperson[1]}\n")

        # 測試 CORPORATE_ANNOUNCEMENT - OFFICIAL_PRESS_RELEASE
        announcement_data_press_release = {
            "stock_name": "鴻海",
            "stock_symbol": "2317",
            "press_release_subject": "季度財報亮眼",
            "core_information": "本季度營收同比增長20%，淨利潤創歷史新高。",
            "sentiment": "positive"
        }
        corp_prompts_press_release = prompt_service.get_formatted_prompt("CORPORATE_ANNOUNCEMENT", "OFFICIAL_PRESS_RELEASE", announcement_data_press_release)
        if corp_prompts_press_release:
            print("--- 公司重大公告 (新聞稿) ---")
            print(f"System Prompt:\n{corp_prompts_press_release[0]}\n")
            print(f"User Prompt:\n{corp_prompts_press_release[1]}\n")
            
        # 測試找不到的類型
        non_existent_prompts = prompt_service.get_formatted_prompt("NON_EXISTENT_TYPE", "ANY_USER", {})
        if non_existent_prompts is None:
            print("--- 測試找不到的類型 (預期結果：None) ---")
            print("成功處理找不到的提示詞類型。\n")

        # 測試缺少格式化鍵
        incomplete_data = {
            "stock_name": "台積電"
            # 缺少 "stock_symbol", "price_a", "change_direction", "price_b", "sentiment"
        }
        error_prompts = prompt_service.get_formatted_prompt("PTT_ANALYSIS", "PTT_USER", incomplete_data)
        if error_prompts is None:
            print("--- 測試缺少格式化鍵 (預期結果：None) ---")
            print("成功處理格式化提示詞時缺少鍵的情況。\n")

    except FileNotFoundError:
        print("測試失敗：找不到 ai_assistant/prompts/news_prompts.yaml。請確保檔案存在於正確的路徑。")
    except yaml.YAMLError:
        print("測試失敗：ai_assistant/prompts/news_prompts.yaml 格式錯誤。")
    except Exception as e:
        print(f"測試過程中發生未預期錯誤: {e}")