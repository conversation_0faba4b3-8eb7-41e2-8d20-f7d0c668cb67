"""
统一的PostgreSQL管理器
为所有系统提供统一的数据库连接和操作接口
"""

import psycopg2
from psycopg2.pool import SimpleConnectionPool
from psycopg2.extras import RealDictCursor
from typing import Dict, Any, Optional, List
import logging
from database.config import get_db_config
import logging # Added import
import threading

class PostgresqlManager: # Removed SingletonType metaclass dependency implicitly by removing the import
    """统一的PostgreSQL连接管理器

    职责：专注于连接池的管理，负责创建、获取和归还连接
    """

    _instances = {}  # 系统类型 -> 实例的映射
    _instance_lock = threading.Lock()

    def __new__(cls, system_type: str, db_config: Optional[Dict[str, Any]] = None):
        """确保每个系统类型有一个单独的实例"""
        # 统一大小写，避免区分大小写导致的问题
        system_type = system_type.upper()

        with cls._instance_lock:
            if system_type not in cls._instances:
                instance = super(PostgresqlManager, cls).__new__(cls)
                # 标记为未初始化
                instance._initialized = False
                cls._instances[system_type] = instance
                logging.getLogger(__name__).info(f"创建系统类型 [{system_type}] 的PostgresqlManager实例") # Use standard logging

        return cls._instances[system_type]

    def __init__(self, system_type: str, db_config: Optional[Dict[str, Any]] = None):
        """初始化PostgreSQL连接管理器

        Args:
            system_type: 系统类型 ("pve", "ladder", "gacha")
            db_config: 可选的数据库配置，如不提供则自动获取
        """
        # 统一大小写
        system_type = system_type.upper()

        # 防止重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return

        self.system_type = system_type

        # 如果未提供配置，则获取系统对应配置
        if db_config is None:
            db_config = get_db_config(system_type=system_type)

        self.db_config = db_config
        self.db_name = db_config.get('database', f'{system_type}_database')

        # 创建连接池
        try:
            self.pool = SimpleConnectionPool(
                5, 100,  # 最小/最大连接数，增加到5-100
                host=db_config.get('host', 'localhost'),
                port=db_config.get('port', 5432),
                database=self.db_name,
                user=db_config.get('user', 'postgres'),
                password=db_config.get('password', 'postgres')
            )
            logging.getLogger(__name__).info(f"[{system_type}] 数据库连接池创建成功: {self.db_name}") # Use standard logging
        except Exception as e:
            logging.getLogger(__name__).error(f"[{system_type}] 创建数据库连接池失败: {str(e)}") # Use standard logging
            self.pool = None

        self._initialized = True

    def get_connection(self):
        """獲取數據庫連接"""
        if not self.pool:
            raise Exception(f"數據庫連接池未初始化")
        return self.pool.getconn()

    def return_connection(self, conn):
        """归还连接到连接池

        Args:
            conn: 要归还的数据库连接
        """
        if self.pool and conn:
            self.pool.putconn(conn)

    def close(self):
        """关闭连接池，释放所有资源"""
        if self.pool:
            self.pool.closeall()
            logging.getLogger(__name__).info(f"[{self.system_type.upper()}] 数据库连接池已关闭") # Use standard logging

    def get_pool_stats(self):
        """获取连接池状态统计信息

        Returns:
            Dict: 包含连接池统计信息的字典
        """
        stats = {
            "system_type": self.system_type,
            "db_name": self.db_name,
            "pool_initialized": self.pool is not None,
            "used_connections": 0,
            "available_connections": 0,
            "total_connections": 0
        }

        if self.pool and hasattr(self.pool, '_used') and hasattr(self.pool, '_pool'):
            stats["used_connections"] = len(self.pool._used)
            stats["available_connections"] = len(self.pool._pool)
            stats["total_connections"] = stats["used_connections"] + stats["available_connections"]
            stats["usage_percentage"] = (stats["used_connections"] / stats["total_connections"] * 100) if stats["total_connections"] > 0 else 0

        return stats

    @classmethod
    def get_instance(cls, system_type: str, db_config: Optional[Dict[str, Any]] = None):
        """获取指定系统类型的实例

        Args:
            system_type: 系统类型
            db_config: 可选的数据库配置

        Returns:
            PostgresqlManager实例
        """
        return cls(system_type=system_type, db_config=db_config)

    @classmethod
    def test_connection(cls, system_type: str) -> Dict[str, Any]:
        """测试数据库连接

        Args:
            system_type: 系统类型

        Returns:
            测试结果字典，包含数据库信息和版本

        Raises:
            Exception: 当数据库连接失败时
        """
        db_config = get_db_config(system_type=system_type)
        db_name = db_config.get("database", f"{system_type}_database")

        conn = psycopg2.connect(
            host=db_config.get("host", "localhost"),
            port=db_config.get("port", 5432),
            database=db_name,
            user=db_config.get("user", "postgres"),
            password=db_config.get("password", "postgres")
        )

        cursor = conn.cursor()
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        cursor.close()
        conn.close()

        return {
            "database": db_name,
            "version": version,
            "system_type": system_type
        }
