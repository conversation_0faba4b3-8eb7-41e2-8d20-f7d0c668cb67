import asyncpg
from typing import Dict, List, Optional, Tuple, Any, AsyncIterator
import math
from enum import Enum
from contextlib import asynccontextmanager
import asyncio
from utils.logger import logger
from database.postgresql.async_manager import AsyncPgManager
from gacha.utils.redis_publisher import RedisEventPublisher
from gacha.models.models import Card, SeriesCollection, UserCard
from gacha.repositories.card.master_card_repository import MasterCardRepository
from gacha.repositories.collection.user_collection_repository import UserCollectionRepository
from .user_service import UserService
from gacha.models.filters import CollectionFilters
from gacha.constants import MarketStatsEventType
from gacha.exceptions import UserNotFoundError, RecordNotFoundError, DatabaseOperationError, CardNotFoundError, GachaSystemError, InsufficientFundsError, CardDeletionError, EventPublishError

class CollectionService:
    """
    收藏服務類，處理卡冊查看和系列圖鑑邏輯。

    職責劃分:
    1. 業務邏輯 - 實現各種卡片收藏相關的業務流程和決策
    2. 協調 - 協調不同 Repository 之間的操作
    3. 事件發布 - 管理市場統計事件的發布

    與Repository的職責區分:
    - Service: 處理業務邏輯，實現業務規則，管理工作流
    - Repository: 純粹的數據訪問層，不包含業務邏輯和決策

    主要功能:
    - 卡冊查看和分頁
    - 系列圖鑑查詢
    - 卡片交易轉移
    - 卡片刪除管理
    - 統計數據計算和發布
    """
    CARDS_PER_PAGE = 1
    SERIES_PER_PAGE = 10

    def __init__(self, pool: asyncpg.Pool, user_service: UserService, card_repo: Optional[MasterCardRepository]=None, collection_repo: Optional[UserCollectionRepository]=None, redis_publisher: Optional[RedisEventPublisher]=None):
        """初始化收藏服務 (Asyncpg 版本)"""
        if pool is None:
            err_msg = 'CollectionService 初始化失敗：必須提供 asyncpg 連接池。'
            logger.error(err_msg)
            raise ValueError(err_msg)
        self.pool = pool
        self.user_service = user_service
        if redis_publisher is None:
            logger.warning('CollectionService 初始化：RedisEventPublisher 未提供。如果需要發布事件，請確保注入。')
        self.redis_publisher = redis_publisher
        self.card_repo = card_repo if card_repo is not None else MasterCardRepository(self.pool)
        if collection_repo is not None:
            self.collection_repo = collection_repo
        else:
            err_msg = 'CollectionService 初始化失敗：必須提供 UserCollectionRepository 實例。'
            logger.error(err_msg)
            raise ValueError(err_msg)

    async def get_favorite_card_count(self, user_id: int) -> int:
        """(Async) 獲取用戶標記為最愛的卡片數量"""
        user = await self.user_service.get_user(user_id, create_if_missing=True)
        if not user:
            logger.warning('[CollectionService] User %s could not be found or created for get_favorite_card_count.', user_id)
            raise UserNotFoundError(user_id=user_id)
        
        return await self.collection_repo.get_user_favorite_card_count(user_id)

    async def get_user_cards_paginated(self, user_id: int, page: int=1, sort_by: str='rarity', sort_order: str='desc', filters: Optional[CollectionFilters]=None, *, pre_fetched_unique_cards: Optional[int]=None, pre_fetched_total_cards: Optional[int]=None) -> Dict[str, Any]:
        """(Async) 獲取用戶卡冊的分頁數據. Can accept pre-fetched stats."""
        user = await self.user_service.get_user(user_id, create_if_missing=True)
        if not user:
            raise UserNotFoundError(f"找不到用戶: {user_id}")
        
        if filters is None:
            filters = CollectionFilters()
        
        page_validated, sort_by_validated, sort_order_validated = self._validate_pagination_params(page, sort_by, sort_order)
        result = await self.collection_repo.get_user_cards_paginated(
            user_id, page_validated, self.CARDS_PER_PAGE, 
            sort_by_validated, sort_order_validated, filters, 
            pre_fetched_unique_cards=pre_fetched_unique_cards, 
            pre_fetched_total_cards=pre_fetched_total_cards
        )
        
        return {
            'cards': result.get('cards', []), 
            'total_pages': result.get('total_pages', 1), 
            'current_page': result.get('current_page', 1), 
            'total_cards': result.get('total_cards', 0), 
            'unique_cards': result.get('unique_cards', 0), 
            'filters': filters
        }

    async def get_cards_for_page(self, user_id: int, page: int=1, sort_by: str='rarity', sort_order: str='desc', filters: Optional[CollectionFilters]=None) -> List[UserCard]:
        """(Async) 獲取用戶卡冊特定頁面的卡片列表 (優化方法)"""
        user = await self.user_service.get_user(user_id, create_if_missing=True)
        if not user:
            logger.warning('[CollectionService] User %s could not be found or created for get_cards_for_page.', user_id)
            raise UserNotFoundError(user_id=user_id)
            
        if filters is None:
            filters = CollectionFilters()
            
        page, sort_by, sort_order = self._validate_pagination_params(page, sort_by, sort_order)
        logger.debug("[GACHA_DIAGNOSIS] CollectionService.get_cards_for_page: Calling collection_repo.get_user_cards_page_only with user_id=%s, page=%s, page_size=%s, sort_by='%s', sort_order='%s', filters=%s", user_id, page, self.CARDS_PER_PAGE, sort_by, sort_order, filters)
        
        result = await self.collection_repo.get_user_cards_page_only(user_id, page, self.CARDS_PER_PAGE, sort_by, sort_order, filters)
        logger.debug('[GACHA_DIAGNOSIS] CollectionService.get_cards_for_page: Received result from collection_repo.get_user_cards_page_only: %s', result)
        
        return result.get('cards', [])

    def _validate_pagination_params(self, page: int, sort_by: str, sort_order: str) -> Tuple[int, str, str]:
        """驗證分頁參數 (同步方法，無需修改)"""
        page = max(1, int(page))
        valid_sort_fields = ['rarity', 'name', 'series', 'quantity', 'star', 'custom_index']
        if sort_by not in valid_sort_fields:
            sort_by = 'rarity'
        if sort_order not in ['asc', 'desc']:
            sort_order = 'desc'
        return (page, sort_by, sort_order)

    async def get_all_series(self, pool_type: Optional[str]=None) -> List[str]:
        """(Async) 獲取所有可用的系列列表"""
        try:
            return await self.collection_repo._get_all_series(pool_type)
        except AttributeError:
            logger.error('[GACHA] UserCollectionRepository 缺少 _get_all_series 方法')
            raise DatabaseOperationError("收藏存儲庫缺少獲取系列列表的方法")

    async def get_all_series_paginated(self, page: int=1, pool_type: Optional[str]=None) -> Dict[str, Any]:
        """(Async) 獲取分頁的系列列表"""
        if page < 1:
            page = 1
        
        all_series = await self.get_all_series(pool_type)
        total_series = len(all_series)
        total_pages = math.ceil(total_series / self.SERIES_PER_PAGE) if total_series > 0 else 0
        page = max(1, min(page, total_pages)) if total_pages > 0 else 1
        start_idx = (page - 1) * self.SERIES_PER_PAGE
        end_idx = min(start_idx + self.SERIES_PER_PAGE, total_series)
        current_page_series = all_series[start_idx:end_idx]
        
        return {
            'series_list': current_page_series, 
            'total_pages': total_pages, 
            'current_page': page, 
            'total_series': total_series, 
            'pool_type': pool_type
        }

    async def get_card_details(self, card_id: int) -> Card:
        """(Async) 獲取卡片詳細信息"""
        try:
            card = await self.card_repo.get_card(card_id)
            if not card:
                raise CardNotFoundError(card_id=card_id)
            return card
        except Exception as e:
            logger.error('[GACHA] 獲取卡片詳情失敗: %s', str(e), exc_info=True)
            raise

    async def get_user_all_series_collections(self, user_id: int, series_list: List[str], pool_type: Optional[str]=None) -> Dict[str, SeriesCollection]:
        """(Async) 批量獲取用戶對多個系列的收集情況"""
        user = await self.user_service.get_user(user_id, create_if_missing=True)
        if not user:
            logger.warning('[CollectionService] User %s could not be found or created for get_user_all_series_collections.', user_id)
            raise UserNotFoundError(user_id=user_id)
            
        return await self.collection_repo.get_user_series_collections_batch(user_id, series_list, pool_type)

    async def get_user_overall_collection_stats(self, user_id: int) -> Dict[str, Any]:
        """(Async) 獲取用戶所有系列的總體收集狀況"""
        user = await self.user_service.get_user(user_id, create_if_missing=True)
        if not user:
            logger.warning('[CollectionService] User %s could not be found or created for get_user_overall_collection_stats.', user_id)
            raise UserNotFoundError(user_id=user_id)
            
        stats = await self.collection_repo.get_overall_collection_stats(user_id)
        
        total_collected = stats.get('total_collected', 0)
        total_cards = stats.get('total_cards', 0)
        completion_rate = total_collected / total_cards * 100 if total_cards > 0 else 0
        
        return {
            'total_collected': total_collected, 
            'total_cards': total_cards, 
            'completion_rate': completion_rate
        }

    async def get_user_collection_stats(self, user_id: int, filters: Optional[CollectionFilters]=None) -> Dict[str, Any]:
        """(Async) 獲取用戶收藏統計信息 (簡化版)"""
        user = await self.user_service.get_user(user_id, create_if_missing=True)
        if not user:
            logger.warning('[CollectionService] User %s could not be found or created for get_user_collection_stats.', user_id)
            raise UserNotFoundError(user_id=user_id)
            
        if filters is None:
            filters = CollectionFilters()
            
        return await self.collection_repo.get_user_collection_stats(user_id, filters)

    async def get_filtered_card_ids(self, user_id: int, filters: Optional[CollectionFilters]=None) -> List[int]:
        """(Async) 獲取符合過濾條件的卡片ID列表"""
        user = await self.user_service.get_user(user_id, create_if_missing=True)
        if not user:
            logger.warning('[CollectionService] User %s could not be found or created for get_filtered_card_ids.', user_id)
            raise UserNotFoundError(user_id=user_id)
            
        if filters is None:
            filters = CollectionFilters()
            
        return await self.collection_repo.get_filtered_card_ids(user_id, filters)

    async def get_pool_specific_collection_stats(self, user_id: int, pool_type: str) -> Dict[str, Any]:
        """(Async) 獲取用戶特定卡池的收集狀況"""
        user = await self.user_service.get_user(user_id, create_if_missing=True)
        if not user:
            logger.warning('[CollectionService] User %s could not be found or created for get_pool_specific_collection_stats.', user_id)
            raise UserNotFoundError(user_id=user_id)
            
        stats = await self.collection_repo.get_pool_specific_collection_stats(user_id, pool_type)
        
        total_collected = stats.get('collected_in_pool', 0)
        total_cards = stats.get('total_cards_in_pool', 0)
        completion_rate = total_collected / total_cards * 100 if total_cards > 0 else 0
        
        return {
            'total_collected': total_collected, 
            'total_cards': total_cards, 
            'completion_rate': completion_rate
        }

    async def find_card_page(self, user_id: int, card_id: int, sort_by: str='rarity', sort_order: str='desc', filters: Optional[CollectionFilters]=None) -> Tuple[int, int]:
        """(Async) 根據卡片ID查找卡片所在的頁碼和該篩選條件下的唯一卡片總數. Returns (page, total_unique_cards_for_filter). Page 0 if not found."""
        user = await self.user_service.get_user(user_id, create_if_missing=True)
        if not user:
            raise UserNotFoundError(f"找不到用戶: {user_id}")
        
        if filters is None:
            filters = CollectionFilters()
        
        _, sort_by_validated, sort_order_validated = self._validate_pagination_params(1, sort_by, sort_order)
        card_info = await self.collection_repo.get_user_card_position(user_id, card_id, sort_by_validated, sort_order_validated, filters)
        
        if card_info and 'position' in card_info and (card_info['position'] > 0):
            position = card_info['position']
            total_unique_for_filter = card_info.get('total', 0)
            page = (position - 1) // self.CARDS_PER_PAGE + 1
            logger.debug('[GACHA_SERVICE] find_card_page: card_id=%s, position=%s, total_unique=%s, page=%s', card_id, position, total_unique_for_filter, page)
            return (page, total_unique_for_filter)
        else:
            logger.info('[GACHA_SERVICE] find_card_page: Card %s (user: %s) not found or position invalid. Sort: %s %s, Filters: %s. CardInfo: %s', card_id, user_id, sort_by_validated, sort_order_validated, filters, card_info)
            return (0, 0)

    @asynccontextmanager
    async def _get_db_connection(self, existing_connection: Optional[asyncpg.Connection]=None) -> AsyncIterator[asyncpg.Connection]:
        """
        獲取資料庫連接。如果提供了現有連接，則使用它；否則從連接池獲取新連接。
        此方法本身不管理事務的開始和結束。
        """
        if existing_connection:
            yield existing_connection
        else:
            async with self.pool.acquire() as conn:
                yield conn

    async def get_user_card_quantity(self, user_id: int, master_card_id: int, connection: Optional[asyncpg.Connection]=None) -> int:
        """
        查詢指定用戶擁有特定種類卡片的數量。

        參數:
            user_id: 用戶 ID。
            master_card_id: 卡片種類的主 ID。
            connection: 可選的 asyncpg.Connection 對象。

        返回:
            指定用戶擁有該卡片的數量 (整數)。
            
        拋出:
            UserNotFoundError: 用戶不存在時
            CardNotFoundError: 卡片不存在時
            DatabaseOperationError: 數據庫操作失敗時
        """
        user = await self.user_service.get_user(user_id)
        if not user:
            logger.warning('[CollectionService] User %s not found for get_user_card_quantity.', user_id)
            raise UserNotFoundError(user_id=user_id)
            
        try:
            quantity = await self.collection_repo.get_card_quantity(user_id, master_card_id, connection=connection)
            return quantity
        except RecordNotFoundError:
            # 如果卡片記錄不存在，返回0而不是拋出異常
            return 0
        except Exception as e:
            logger.error('[SERVICE_GET_QUANTITY] Error getting card quantity for user %s, card %s: %s', user_id, master_card_id, e, exc_info=True)
            raise DatabaseOperationError(f"獲取卡片數量失敗: {e}")

    async def _publish_market_stats_event(self, event_type: MarketStatsEventType, payload: Any, user_id_for_log: int) -> bool:
        """發布市場統計事件的輔助方法。"""
        if not self.redis_publisher:
            logger.warning('RedisEventPublisher not available in CollectionService, skipping %s event for user: %s', event_type.value, user_id_for_log)
            # 根據重構原則，如果這是關鍵功能失敗，應該拋出異常。
            # 如果是非關鍵日誌/事件，可以只記錄警告。
            # 假設事件發布是需要的，但如果失敗不應中斷主流程，則只記錄。
            # 但如果嚴格遵循純異常，則應拋出。
            # 暫定為記錄警告，因為事件發布失敗不一定意味著核心操作失敗。
            # 若要更嚴格，則： raise EventPublishError(f"RedisEventPublisher not available for event {event_type.value}")
            return # 或 raise specific error if this is critical
        try:
            await self.redis_publisher.publish(event_type=event_type, payload=payload, batch_payload=False, user_id_for_log=user_id_for_log)
            return # 成功時不返回任何內容
        except Exception as e:
            logger.error('[SERVICE_EVENT] 發布 %s 事件失敗: %s', event_type.value, e, exc_info=True)
            # 拋出一個更具體的異常，或者一個通用的服務層異常
            raise EventPublishError(f"發布 {event_type.value} 事件失敗: {str(e)}", original_exception=e)

    async def transfer_card_quantity(self, sender_id: int, receiver_id: int, master_card_id: int, quantity: int, connection: asyncpg.Connection) -> None:
        """
        在兩個用戶之間轉移指定數量特定種類的卡片。
        此方法必須在一個事務 (transaction) 中執行。

        參數:
            sender_id: 發送方用戶 ID。
            receiver_id: 接收方用戶 ID。
            master_card_id: 要轉移的卡片種類的主 ID。
            quantity: 要轉移的數量 (正整數)。
            connection: 必需的 asyncpg.Connection 對象。

        返回: 
            None: 轉移成功時不返回任何內容。
            
        拋出:
            ValueError: 參數無效時
            UserNotFoundError: 用戶不存在時
            InsufficientFundsError: 發送方卡片數量不足時
            DatabaseOperationError: 數據庫操作失敗時
        """
        if quantity <= 0:
            logger.warning('[SERVICE_TRANSFER_CARD] Invalid quantity for transfer: %s. Must be positive.', quantity)
            raise ValueError(f"轉移數量必須為正數，當前值: {quantity}")
            
        if sender_id == receiver_id:
            logger.warning('[SERVICE_TRANSFER_CARD] Sender and receiver are the same user_id: %s. No action taken.', sender_id)
            raise ValueError(f"發送方和接收方不能是同一用戶: {sender_id}")
            
        sender = await self.user_service.get_user(sender_id)
        if not sender:
            logger.warning('[CollectionService] Sender User %s not found for transfer_card_quantity.', sender_id)
            raise UserNotFoundError(f"找不到發送方用戶: {sender_id}")
            
        receiver = await self.user_service.get_user(receiver_id)
        if not receiver:
            logger.warning('[CollectionService] Receiver User %s not found for transfer_card_quantity.', receiver_id)
            raise UserNotFoundError(f"找不到接收方用戶: {receiver_id}")
            
        try:
            was_favorite = await self.collection_repo.get_card_favorite_status(sender_id, master_card_id, connection=connection)
            
            sender_result = await self.collection_repo.decrement_card_quantity(user_id=sender_id, card_id=master_card_id, quantity_to_remove=quantity, connection=connection)
            if sender_result is None:
                logger.info('[SERVICE_TRANSFER_CARD] Sender %s has insufficient quantity of card %s for transfer of %s.', sender_id, master_card_id, quantity)
                raise InsufficientFundsError(required_amount=quantity, current_balance=0)
                
            sender_remaining_quantity, was_card_deleted = sender_result
            logger.debug('[SERVICE_TRANSFER_CARD] Sender %s card %s quantity decremented. Remaining: %s', sender_id, master_card_id, sender_remaining_quantity)
            
            receiver_new_quantity = await self.collection_repo.increment_card_quantity(user_id=receiver_id, card_id=master_card_id, quantity_to_add=quantity, connection=connection)
            logger.debug('[SERVICE_TRANSFER_CARD] Receiver %s card %s quantity incremented. New total: %s', receiver_id, master_card_id, receiver_new_quantity)
            
            # 發布事件
            await self._publish_market_stats_event(event_type=MarketStatsEventType.TOTAL_OWNED_UPDATE, payload=[(master_card_id, -quantity)], user_id_for_log=sender_id)
            
            if was_card_deleted:
                await self._publish_market_stats_event(event_type=MarketStatsEventType.UNIQUE_OWNER_UPDATE, payload=[(master_card_id, -1)], user_id_for_log=sender_id)
                if was_favorite:
                    logger.info('[SERVICE_TRANSFER_CARD] Card %s was favorite and was deleted for user %s, updating favorite stats.', master_card_id, sender_id)
                    await self._publish_market_stats_event(event_type=MarketStatsEventType.FAVORITE_COUNT_UPDATE, payload=[(master_card_id, -1)], user_id_for_log=sender_id)
                    
            await self._publish_market_stats_event(event_type=MarketStatsEventType.TOTAL_OWNED_UPDATE, payload=[(master_card_id, quantity)], user_id_for_log=receiver_id)
            
            is_new_owner = receiver_new_quantity == quantity
            if is_new_owner:
                await self._publish_market_stats_event(event_type=MarketStatsEventType.UNIQUE_OWNER_UPDATE, payload=[(master_card_id, 1)], user_id_for_log=receiver_id)
                
            return # 成功時不返回任何內容 (隱式返回 None)
            
        except ValueError as ve:
            logger.error('[SERVICE_TRANSFER_CARD] ValueError during transfer between %s and %s for card %s, quantity %s: %s', sender_id, receiver_id, master_card_id, quantity, ve, exc_info=True)
            raise
        except RuntimeError as re:
            logger.error('[SERVICE_TRANSFER_CARD] RuntimeError during transfer between %s and %s for card %s, quantity %s: %s', sender_id, receiver_id, master_card_id, quantity, re, exc_info=True)
            raise DatabaseOperationError(f"卡片轉移操作失敗: {re}")
        except Exception as e:
            logger.error('[SERVICE_TRANSFER_CARD] Unexpected error during transfer between %s and %s for card %s, quantity %s: %s', sender_id, receiver_id, master_card_id, quantity, e, exc_info=True)
            raise DatabaseOperationError(f"卡片轉移操作失敗: {e}")