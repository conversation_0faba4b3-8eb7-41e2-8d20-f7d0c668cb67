"""
數據庫工廠類
用於創建數據庫連接和存儲庫實例
"""

import logging
from typing import Any, Dict, Optional

from database.config import get_db_config

# 使用統一的DatabaseFactory代替獨立實現
from battles.data.db.battle_pve.core_pve import DatabaseFactory as CoreDatabaseFactory
from database.postgresql.manager import PostgresqlManager
from database.base_repository import BaseRepository # Corrected import path
from battles.data.db.battle_pve.repositorys.inventory_repository import InventoryRepository
from battles.data.db.battle_pve.repositorys.pve_repository import PveRepository
from battles.data.db.unit_of_work import UnitOfWork

logger = logging.getLogger("battles")


# 將原有的DatabaseFactory改為使用core_pve中的實現
class DatabaseFactory:
    """數據庫工廠類，負責創建數據庫管理器和存儲庫實例

    這個類是對core_pve.DatabaseFactory的包裝，以保持向後兼容性
    """

    @classmethod
    def get_instance(cls):
        """獲取單例實例"""
        return CoreDatabaseFactory

    def __init__(self):
        """初始化數據庫工廠

        注意：這個方法僅為向後兼容性保留，應使用靜態方法代替
        """
        # 這裡不再創建新的數據庫管理器實例
        pass

    def get_db_manager(self):
        """獲取數據庫管理器"""
        # 使用ladder系統配置獲取數據庫管理器
        db_config = get_db_config("ladder")
        return CoreDatabaseFactory.get_postgresql(db_config)

    def create_repository(self, repository_class):
        """創建存儲庫實例

        參數:
            repository_class: 存儲庫類

        返回:
            BaseRepository: 存儲庫實例
        """
        # 獲取數據庫管理器
        db_manager = self.get_db_manager()

        # 創建新的存儲庫實例
        repository = repository_class(db_manager)

        return repository

    def create_unit_of_work(self) -> UnitOfWork:
        """創建工作單元實例

        返回:
            UnitOfWork: 工作單元實例
        """
        # 獲取數據庫管理器
        db_manager = self.get_db_manager()

        unit_of_work = UnitOfWork(db_manager)

        # 註冊常用存儲庫

        unit_of_work.register_repository("pve", PveRepository)
        unit_of_work.register_repository("inventory", InventoryRepository)

        return unit_of_work

    def close_connections(self):
        """關閉所有數據庫連接"""
        # 委託給CoreDatabaseFactory處理
        CoreDatabaseFactory.close_all()
        logger.info("已關閉所有數據庫連接")
