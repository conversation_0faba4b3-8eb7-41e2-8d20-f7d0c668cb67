import discord
from discord.ui import View
from typing import List, Optional, Tuple
from gacha.models.models import Card


class SpecificTicketResultView(View):
    """
    View for displaying the result of a specific ticket exchange.
    """

    def __init__(
        self,
        original_interaction: discord.Interaction,
        ticket_name: str,
        exchanged_cards: List[Card],
        *,
        timeout: Optional[float] = 60.0,
    ):
        super().__init__(timeout=timeout)
        self.original_interaction = original_interaction
        self.ticket_name = ticket_name
        self.exchanged_cards = exchanged_cards
        self.message = None

        # 可以根據需要添加按鈕，例如關閉按鈕
        # self.add_item(discord.ui.Button(label="關閉", custom_id="close_result", style=discord.ButtonStyle.secondary))

    async def prepare_initial_message_payload(
        self,
    ) -> Tuple[discord.Embed, "SpecificTicketResultView"]:
        """
        準備初始消息的嵌入和視圖。
        """
        from gacha.views.embeds.shop.specific_ticket_result_embed_builder import (
            build_specific_ticket_result_embed, )

        embed = build_specific_ticket_result_embed(
            interaction=self.original_interaction,
            ticket_name=self.ticket_name,
            exchanged_cards=self.exchanged_cards,
        )

        return embed, self

    # 如需添加關閉按鈕，可以實現其回調：
    # @discord.ui.button(label="關閉", custom_id="close_result", style=discord.ButtonStyle.secondary)
    # async def close_button(self, interaction: discord.Interaction, button: discord.ui.Button):
    #     """關閉按鈕的回調函數。"""
    #     await interaction.response.defer_update() # 延遲交互
    #     await interaction.delete_original_response() # 刪除訊息

    async def interaction_check(
            self, interaction: discord.Interaction) -> bool:
        """確保只有發起兌換的用戶可以交互。"""
        return interaction.user.id == self.original_interaction.user.id
