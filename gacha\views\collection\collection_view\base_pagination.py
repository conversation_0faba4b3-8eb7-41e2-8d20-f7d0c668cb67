"""
Gacha系統基礎分頁視圖
提供用於卡片和系列列表的通用分頁功能
"""
import time
from typing import Optional
import discord
from discord.ui import Button, Modal, TextInput, View
from utils.logger import logger
from gacha.utils import interaction_utils
from .interaction_manager import InteractionManager

class BasePaginationJumpModal(Modal):
    """基礎分頁跳轉模態框"""
    page_number = TextInput(label='頁碼', placeholder='輸入要跳轉的頁碼', required=True, min_length=1, max_length=5)

    def __init__(self, view: 'BasePaginationView', title='跳轉到指定頁碼'):
        super().__init__(title=title)
        self.pagination_view = view

    async def on_submit(self, interaction: discord.Interaction):
        """提交處理"""
        try:
            page = int(self.page_number.value)
            page = max(1, min(page, self.pagination_view.total_pages))
            self.pagination_view.current_page = page
            self.pagination_view._refresh_button_states()
            await interaction.response.defer()
            await self.pagination_view._update_page(page, interaction)
        except ValueError:
            await interaction.response.send_message('請輸入有效的頁碼', ephemeral=True)
        except Exception as e:
            logger.error('Error in BasePaginationJumpModal on_submit: %s', e, exc_info=True)
            if not interaction.response.is_done():
                await interaction.response.send_message('處理頁碼跳轉時發生錯誤。', ephemeral=True)
            else:
                await interaction.followup.send('處理頁碼跳轉時發生錯誤。', ephemeral=True)

class BasePaginationView(View):
    """基礎分頁視圖，提供通用的分頁功能"""

    def __init__(self, user: discord.User, current_page: int, total_pages: int, timeout: int=120):
        """初始化基礎分頁視圖

        參數:
            user: Discord用戶對象
            current_page: 當前頁碼
            total_pages: 總頁數
            timeout: 超時時間(秒)
        """
        super().__init__(timeout=timeout)
        self.user = user
        self.user_id = user.id
        self.current_page = current_page
        self.total_pages = max(1, total_pages)
        self.message: Optional[discord.Message] = None
        self.interaction_manager = InteractionManager()
        self.btn_first: Optional[Button] = None
        self.btn_prev: Optional[Button] = None
        self.btn_jump: Optional[Button] = None
        self.btn_next: Optional[Button] = None
        self.btn_last: Optional[Button] = None
        self._add_pagination_buttons()
        self._refresh_button_states()

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """
        檢查与此分頁視圖互動的使用者是否為原始使用者。
        """
        return await interaction_utils.check_user_permission(interaction, self.user_id, '您不能操作他人的分頁介面！')

    def _add_pagination_buttons(self):
        """創建並添加分頁按鈕到視圖中。"""
        self.btn_first = Button(emoji='⏮️', style=discord.ButtonStyle.gray, custom_id='first_page', row=0)
        self.btn_first.callback = self.first_page_callback
        self.add_item(self.btn_first)
        self.btn_prev = Button(emoji='◀️', style=discord.ButtonStyle.gray, custom_id='prev_page', row=0)
        self.btn_prev.callback = self.prev_page_callback
        self.add_item(self.btn_prev)
        self.btn_jump = Button(style=discord.ButtonStyle.blurple, custom_id='jump', row=0)
        self.btn_jump.callback = self.jump_callback
        self.add_item(self.btn_jump)
        self.btn_next = Button(emoji='▶️', style=discord.ButtonStyle.gray, custom_id='next_page', row=0)
        self.btn_next.callback = self.next_page_callback
        self.add_item(self.btn_next)
        self.btn_last = Button(emoji='⏭️', style=discord.ButtonStyle.gray, custom_id='last_page', row=0)
        self.btn_last.callback = self.last_page_callback
        self.add_item(self.btn_last)

    def _refresh_button_states(self):
        """根據當前頁面和總頁數更新分頁按鈕的標籤和禁用狀態。"""
        if not all([self.btn_first, self.btn_prev, self.btn_jump, self.btn_next, self.btn_last]):
            logger.warning('Attempted to refresh button states before buttons were fully initialized.')
            return
        is_first_page = self.current_page == 1
        is_last_page = self.current_page == self.total_pages
        is_single_page = self.total_pages <= 1
        self.btn_first.disabled = is_first_page or is_single_page
        self.btn_prev.disabled = is_first_page or is_single_page
        self.btn_jump.label = f'{self.current_page}/{self.total_pages}'
        self.btn_jump.disabled = is_single_page
        self.btn_next.disabled = is_last_page or is_single_page
        self.btn_last.disabled = is_last_page or is_single_page

    async def _handle_page_change(self, new_page: int, interaction: discord.Interaction):
        """通用處理頁面變更的邏輯。"""
        if not await self.interaction_manager.try_defer(interaction):
            logger.warning('BasePaginationView: Failed to defer interaction %s for page change to %s. Aborting page update.', interaction.id, new_page)
            return
        self.current_page = new_page
        self._refresh_button_states()
        await self._update_page(self.current_page, interaction)

    async def first_page_callback(self, interaction: discord.Interaction):
        if self.current_page != 1:
            await self._handle_page_change(1, interaction)
        else:
            await self.interaction_manager.try_defer(interaction, ephemeral=True)

    async def prev_page_callback(self, interaction: discord.Interaction):
        if self.current_page > 1:
            await self._handle_page_change(max(1, self.current_page - 1), interaction)
        else:
            await self.interaction_manager.try_defer(interaction, ephemeral=True)

    async def jump_callback(self, interaction: discord.Interaction):
        if self.total_pages <= 1:
            await interaction.response.send_message('目前只有一頁，無需跳轉。', ephemeral=True)
            return
        modal = self._create_jump_modal()
        await interaction.response.send_modal(modal)

    async def next_page_callback(self, interaction: discord.Interaction):
        if self.current_page < self.total_pages:
            await self._handle_page_change(min(self.total_pages, self.current_page + 1), interaction)
        else:
            await self.interaction_manager.try_defer(interaction, ephemeral=True)

    async def last_page_callback(self, interaction: discord.Interaction):
        if self.current_page != self.total_pages:
            await self._handle_page_change(self.total_pages, interaction)
        else:
            await self.interaction_manager.try_defer(interaction, ephemeral=True)

    async def on_timeout(self):
        logger.info('BasePaginationView for user %s timed out. Message ID: %s', self.user_id, self.message.id if self.message else 'Unknown')
        self.stop()
        try:
            if self.message:
                for item in self.children:
                    if hasattr(item, 'disabled'):
                        item.disabled = True
                await self.message.edit(view=self)
            else:
                logger.warning('BasePaginationView: self.message is None on timeout, cannot disable buttons on message.')
        except discord.HTTPException as e:
            logger.error('BasePaginationView: Failed to edit message on timeout for user %s. Error: %s', self.user_id, e, exc_info=True)

    def _create_jump_modal(self) -> BasePaginationJumpModal:
        return BasePaginationJumpModal(self)

    def get_current_page_embed(self) -> discord.Embed:
        """獲取當前頁的Embed，子類必須實現此方法"""
        raise NotImplementedError('子類必須實現get_current_page_embed方法')

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """更新頁面，子類必須實現此方法"""
        raise NotImplementedError('子類必須實現_update_page方法')