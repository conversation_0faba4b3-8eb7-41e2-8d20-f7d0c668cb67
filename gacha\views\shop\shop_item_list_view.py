import discord
import math
from discord.ext import commands
from discord.ui import <PERSON><PERSON>, View, Select
from discord import ButtonStyle, SelectOption
from decimal import Decimal
from typing import TYPE_CHECKING, Optional, List
from gacha.services.shop.shop_service import ShopService
from gacha.models.shop_models import ShopItemDefinition
from gacha.views.embeds.shop.shop_item_list_embed_builder import ShopItemListEmbedBuilder
from gacha.utils.interaction_utils import disable_view_items, check_user_permission
from utils.logger import logger
from gacha.views.modals.ticket_exchange_modal import TicketExchangeModal
from gacha.views.collection.collection_view.base_pagination import BasePaginationView, BasePaginationJumpModal
if TYPE_CHECKING:
    pass

class ShopItemListView(BasePaginationView):
    EXCHANGE_ITEM_CUSTOM_ID = 'exchange_item'
    SHOP_ITEM_SELECT_DROPDOWN_CUSTOM_ID = 'shop_item_select_dropdown'

    def __init__(self, original_interaction: discord.Interaction, cog: commands.Cog, items: list[ShopItemDefinition], oil_ticket_balance: Decimal, category_name: str='商店商品列表', items_per_page: int=5, timeout: int=180):
        self.original_interaction = original_interaction
        self.cog = cog
        self.all_items = items
        self.oil_ticket_balance = oil_ticket_balance
        self.category_name = category_name
        self.items_per_page = items_per_page
        self.total_items = len(items)
        self.embed_builder = ShopItemListEmbedBuilder(items_per_page=items_per_page)
        self.selected_item_id_for_exchange: Optional[str] = None
        if self.items_per_page <= 0 or self.total_items == 0:
            total_pages = 1
        else:
            total_pages = math.ceil(self.total_items / self.items_per_page)
        super().__init__(user=original_interaction.user, current_page=1, total_pages=total_pages, timeout=timeout)
        self._add_custom_shop_list_items()

    def _add_custom_shop_list_items(self):
        """Adds custom items like the item selection dropdown and exchange button."""
        self._add_item_selection_dropdown()
        exchange_btn = Button(label='🛒 進行兌換', style=ButtonStyle.success, custom_id=self.EXCHANGE_ITEM_CUSTOM_ID, row=2)
        exchange_btn.callback = self.exchange_button_callback
        self.add_item(exchange_btn)

    def _add_item_selection_dropdown(self):
        """新增一個下拉式選單供用戶選擇要兌換的商品。Placed on row 1."""
        self.remove_item_by_custom_id(self.SHOP_ITEM_SELECT_DROPDOWN_CUSTOM_ID)
        start_index = (self.current_page - 1) * self.items_per_page
        end_index = start_index + self.items_per_page
        current_page_items = self.all_items[start_index:end_index]
        if not current_page_items:
            return
        options = [SelectOption(label=item.display_name[:100], value=item.id, description=(item.description or '選擇此商品')[:100]) for item in current_page_items]
        if len(options) > 25:
            logger.warning('Shop item list dropdown options truncated for user %s, page %s', self.user_id, self.current_page)
            options = options[:25]
        if not options:
            return
        select = Select(placeholder='選擇要兌換的商品...', options=options, custom_id=self.SHOP_ITEM_SELECT_DROPDOWN_CUSTOM_ID, min_values=1, max_values=1, row=1)
        select.callback = self.select_item_callback
        self.add_item(select)

    def remove_item_by_custom_id(self, custom_id: str):
        """Removes an item from the view based on its custom_id."""
        item_to_remove = next((child for child in self.children if hasattr(child, 'custom_id') and child.custom_id == custom_id), None)
        if item_to_remove:
            self.remove_item(item_to_remove)

    async def select_item_callback(self, interaction: discord.Interaction):
        if not await check_user_permission(interaction, self.user_id, '您無法操作此選單。'):
            return
        if not await self.interaction_manager.try_defer(interaction, ephemeral=True):
            logger.warning('select_item_callback: Failed to defer interaction %s. It might have been responded to.', interaction.id)
        self.selected_item_id_for_exchange = interaction.data['values'][0]
        logger.info('User %s selected shop item ID: %s for exchange.', self.user_id, self.selected_item_id_for_exchange)

    async def exchange_button_callback(self, interaction: discord.Interaction):
        if not await check_user_permission(interaction, self.user_id, '您無法操作此按鈕。'):
            return
        if not self.selected_item_id_for_exchange:
            await interaction.response.send_message('請先從下拉選單中選擇一個商品進行兌換。', ephemeral=True)
            return
        selected_item_def = next((item for item in self.all_items if item.id == self.selected_item_id_for_exchange), None)
        if not selected_item_def:
            await interaction.response.send_message('選擇的商品無效或已不存在，請刷新商店或重新選擇。', ephemeral=True)
            self.selected_item_id_for_exchange = None
            return
        if hasattr(self.cog, 'shop_service') and isinstance(self.cog.shop_service, ShopService):
            exchange_modal = TicketExchangeModal(cog=self.cog, shop_item_id=selected_item_def.id, original_message_to_edit=self.message)
            try:
                await interaction.response.send_modal(exchange_modal)
            except discord.HTTPException as e:
                logger.error('Error sending TicketExchangeModal for item %s: %s', selected_item_def.id, e, exc_info=True)
                try:
                    await interaction.followup.send('開啟兌換窗口時發生錯誤。', ephemeral=True)
                except discord.HTTPException as fe:
                    logger.error('Followup also failed after send_modal error: %s', fe)
        else:
            logger.error('ShopItemListView: cog.shop_service is not available or not of type ShopService.')
            await interaction.response.send_message('無法處理兌換請求，商店服務配置錯誤。', ephemeral=True)

    def get_current_page_embed(self) -> discord.Embed:
        """獲取當前頁的Embed。"""
        start_index = (self.current_page - 1) * self.items_per_page
        end_index = start_index + self.items_per_page
        current_page_items_data = self.all_items[start_index:end_index]
        return self.embed_builder.build_embed(items=current_page_items_data, current_page=self.current_page, total_items=self.total_items, oil_ticket_balance_value=self.oil_ticket_balance, category_name=self.category_name)

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """更新視圖到指定頁面，並編輯原始消息。"""
        self.current_page = page
        self._add_item_selection_dropdown()
        embed = self.get_current_page_embed()
        try:
            await interaction.edit_original_response(embed=embed, view=self)
        except discord.HTTPException as e:
            logger.error('ShopItemListView: Failed to edit message on page update (page %s): %s', page, e, exc_info=True)
            if isinstance(e, discord.NotFound):
                logger.warning('Original message for ShopItemListView not found.')

    async def on_timeout(self) -> None:
        logger.info('ShopItemListView for user %s timed out.', self.user_id)
        self.selected_item_id_for_exchange = None
        for item in self.children:
            if hasattr(item, 'disabled'):
                item.disabled = True
        if self.message:
            try:
                content = f"{self.message.content or ''}\\n*（商店列表已超時）*"
                await self.message.edit(content=content, view=self)
            except discord.HTTPException as e:
                logger.warning('Failed to edit message on ShopItemListView timeout: %s', e)
        self.stop()