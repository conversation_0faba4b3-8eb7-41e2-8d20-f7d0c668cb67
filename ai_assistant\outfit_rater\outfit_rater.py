"""
OutfitRater模組 - 處理穿搭評分的主要邏輯
"""

import logging
import re
import xml.etree.ElementTree as ET
from typing import Dict, Any, Optional
import time
from datetime import datetime

# 相對導入
from .. import config
from ..ai_service_base import AIServiceBase
from .rating_history import RatingHistory
from ..prompts import OUTFIT_SYSTEM_PROMPT, OUTFIT_USER_PROMPT, build_prompt_with_history

# 設置日誌
logger = logging.getLogger("OutfitRater")

class OutfitRater(AIServiceBase):
    """處理穿搭評分的類，繼承自AIServiceBase"""
    
    def __init__(self):
        """初始化OutfitRater"""
        # 調用父類的初始化方法
        super().__init__()
        
        # 初始化評分歷史管理器
        self.rating_history = RatingHistory()
        
        # 設置系統提示
        self.system_prompt = OUTFIT_SYSTEM_PROMPT
        
        logger.info(f"OutfitRater初始化完成，使用API: {self.api_url}, 模型: {self.model_name}")
    
    async def rate_outfit_from_binary(self, image_data: bytes, prompt: str = None, request_id: str = None, user_id: str = None) -> Dict[str, Any]:
        """
        從二進制圖像數據評分穿搭
        
        參數:
            image_data (bytes): 圖像的二進制數據
            prompt (str, optional): 自定義提示，如果不提供則使用默認提示
            request_id (str, optional): 請求標識符
            user_id (str, optional): 用戶ID，用於獲取歷史評分
            
        返回:
            Dict: 評分結果
        """
        # 記錄請求ID，用於追蹤
        if request_id:
            logger.info(f"處理請求ID: {request_id} 的穿搭評分")
        
        try:
            # 使用用戶提示或默認提示
            user_prompt = prompt or OUTFIT_USER_PROMPT
            
            # 獲取並整合用戶歷史記錄到系統提示中
            system_prompt = self.system_prompt
            if user_id:
                # 獲取並整合用戶歷史
                user_history_text = self.rating_history.format_history_for_prompt(user_id)
                if user_history_text:
                    system_prompt = build_prompt_with_history(system_prompt, user_history_text)
                    logger.info(f"為用戶 {user_id} 添加歷史評分記錄到系統提示中")
            
            # 使用父類的process_with_image方法處理圖像
            api_response = await self.process_with_image(
                image_data=image_data,
                prompt=user_prompt,
                system_prompt=system_prompt,
                request_id=request_id
            )
            
            # 提取評分結果
            result = self.extract_rating_result(api_response)
            
            # 如果提供了用戶ID，保存評分結果
            if user_id and result:
                await self.rating_history.save_rating(user_id, result)
                logger.info(f"已保存用戶 {user_id} 的評分結果")
            
            return result
        except Exception as e:
            logger.error(f"處理圖像數據時出錯: {str(e)}")
            raise
    
    async def rate_outfit_from_url(self, image_url: str, prompt: str = None, request_id: str = None, user_id: str = None) -> Dict[str, Any]:
        """
        從URL評分穿搭
        
        參數:
            image_url (str): 圖像的URL
            prompt (str, optional): 自定義提示，如果不提供則使用默認提示
            request_id (str, optional): 請求標識符
            user_id (str, optional): 用戶ID，用於獲取歷史評分
            
        返回:
            Dict: 評分結果
        """
        try:
            # 使用aiohttp下載圖像數據
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(image_url) as response:
                    if response.status == 200:
                        image_data = await response.read()
                        return await self.rate_outfit_from_binary(image_data, prompt, request_id, user_id)
                    else:
                        raise Exception(f"從URL下載圖像失敗，狀態碼: {response.status}")
        except Exception as e:
            logger.error(f"從URL處理圖像時出錯: {str(e)}")
            raise
    
    def _build_system_prompt_with_history(self, base_prompt: str, user_history_text: str) -> str:
        """
        構建包含用戶歷史評分的系統提示
        
        參數:
            base_prompt (str): 基礎系統提示
            user_history_text (str): 用戶歷史評分文本
            
        返回:
            str: 包含歷史評分的系統提示
        """
        # 使用統一提示詞模組中的 build_prompt_with_history 函數
        return build_prompt_with_history(base_prompt, user_history_text)
    
    def extract_rating_result(self, api_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        從API響應中提取評分結果，只使用正則表達式
        """
        response_text: Optional[str] = None
        try:
            response_text = self.extract_response_text(api_response)

            if not isinstance(response_text, str) or not response_text.strip():
                logger.warning(f"AI響應文本為空或無效: '{response_text}'")
                raise ValueError("AI響應文本為空或無效")

            # 清理響應文本，移除特殊編碼字符
            cleaned_response = re.sub(r'<0x[0-9A-Fa-f]{2}>', '', response_text)
            
            # 首先嘗試檢查是否有 ai_payload 標籤
            ai_payload_match = re.search(r'<ai_payload>(.*?)</ai_payload>', cleaned_response, re.DOTALL)
            if ai_payload_match:
                # 如果找到 ai_payload 標籤，在其內容中查找評分數據
                content = ai_payload_match.group(1)
            else:
                # 如果沒有找到 ai_payload 標籤，直接在整個響應中查找
                content = cleaned_response
                
            # 使用正則表達式提取評分數據
            score_match = re.search(r'<評分>(.*?)</評分>', content, re.DOTALL)
            review_match = re.search(r'<評價>(.*?)</評價>', content, re.DOTALL)
            suggestion_match = re.search(r'<建議>(.*?)</建議>', content, re.DOTALL)
            conclusion_match = re.search(r'<結論>(.*?)</結論>', content, re.DOTALL)
            
            # 獲取當前時間戳
            timestamp = datetime.now().isoformat(timespec='milliseconds')
            
            # 提取評分數值 (去除 "/10" 後綴)
            score_value = score_match.group(1) if score_match else "N/A"
            if score_value != "N/A":
                # 提取純數字部分
                score_number_match = re.match(r'^(\d+(?:\.\d+)?)(?:/10(?:分)?)?$', score_value.strip())
                if score_number_match:
                    score_value = score_number_match.group(1)  # 只保留數字部分
            
            return {
                "score": score_value,
                "review": review_match.group(1) if review_match else "無法提取評價",
                "suggestion": suggestion_match.group(1) if suggestion_match else "無法提取建議",
                "conclusion": conclusion_match.group(1) if conclusion_match else "無法提取結論",
                "timestamp": timestamp
            }

        except Exception as e:
            logger.error(f"提取評分結果時出錯: {str(e)}. Response text (first 500 chars): {str(response_text)[:500] if response_text is not None else 'None'}")
            
            fallback_timestamp = datetime.now().isoformat(timespec='milliseconds')
            
            return {
                "score": "N/A",
                "review": "評分處理時發生錯誤",
                "suggestion": "請稍後再試",
                "conclusion": "暫時無法提供評分",
                "timestamp": fallback_timestamp
            } 