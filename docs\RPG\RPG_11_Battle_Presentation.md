# RPG系統戰鬥畫面呈現方案 (Discord Embed)

本文檔描述了 Gacha RPG 系統在 Discord 上呈現自動戰鬥畫面的具體方案，重點關注 Embed 的更新流程和內容。

**注意：** 本方案的初始版本主要針對 **1v1 戰鬥** 進行設計。

## 1. 核心原則

*   **內部邏輯:** 核心戰鬥計算遵循設計文檔中定義的**邏輯回合制**，單位按速度等因素決定行動順序，完成一輪交互後視為一個邏輯回合結束。
*   **外部呈現:** 為了提供清晰的觀看節奏，Embed 的更新採用**固定的時間間隔**進行控制，但更新內容反映的是**剛剛計算完成的邏輯回合**的結果。

## 2. Embed 更新流程

採用「**顯示 -> 等待並計算 -> 時間到顯示**」的優化模式：

1.  **顯示階段 (回合 N):**
    *   `BattleCoordinatorService` (或 `BattleRunner`) 觸發 Embed 更新，顯示**邏輯回合 N** 的最終狀態和日誌（完整優先，超長則**截斷**）。
    *   Discord Cog 編輯對應消息。
2.  **等待並計算階段 (準備回合 N+1):**
    *   顯示完成後，系統**開始 2 秒的等待**。
    *   **與此同時**，系統在後台**開始異步計算**下一個**邏輯回合 (N+1)** 的所有交互，並記錄日誌。
3.  **時間到顯示階段 (回合 N+1):**
    *   2 秒等待時間結束。
    *   系統檢查後台計算是否已完成：
        *   **若已完成：** 立刻觸發 Embed 更新，顯示**邏輯回合 N+1** 的結果。
        *   **若未完成：** 等待計算完成後，再立刻觸發 Embed 更新，顯示**邏輯回合 N+1** 的結果。
4.  **循環:** 返回步驟 2，開始下一輪的「等待並計算」。此循環持續直到戰鬥結束。

## 3. Embed 內容規劃

Embed 應包含以下關鍵信息：

*   **標題 (Title):** 顯示樓層信息和戰鬥狀態 (例如 "第 5 層 - 戰鬥中", "第 5 層 - 玩家勝利!")。
*   **字段 (Fields):**
    *   **玩家卡牌:** 顯示玩家單個卡牌的簡要狀態 (例如: `[卡牌名稱] HP: 150/200 MP: 30/50 ✨🥶`)。
    *   **敵人:** 顯示單個敵人的簡要狀態 (例如: `[怪物名稱] HP: 180/180 ✨`)。
    *   **回合信息:** 顯示當前的**邏輯回合數** (例如 `回合: 3`)。
    *   **本回合戰報 (日誌):**
        *   **目標:** 顯示剛剛結束的那個邏輯回合內發生的關鍵事件。
        *   **處理方式:**
            1.  `BattleEmbedBuilder` 接收該回合的**完整** `BattleLogEntry` 列表。
            2.  嘗試將所有日誌格式化為易讀的文本字符串列表。
            3.  將列表拼接為單個字符串（例如用換行符分隔）。
            4.  **檢查長度:** 判斷拼接後的字符串總長度是否超過 Discord Embed 字段的字符限制（例如 1024 字符）。
            5.  **顯示策略:**
                *   **未超長:** 直接顯示完整的、格式化的日誌文本。
                *   **超長:** 對拼接後的日誌字符串進行**截斷**處理，確保其不超過字符限制，並在末尾添加 "..."。

## 4. 流程圖 (Sequence Diagram)

```mermaid
sequenceDiagram
    participant User
    participant Cog
    participant BattleCoordinatorService as BCS
    participant Battle # Represents internal state & calculation
    participant BattleEmbedBuilder as BEB
    participant DiscordAPI
    participant WaitTimer

    User->>Cog: /fight_floor 5
    Cog->>BCS: start_new_battle(user_id, floor=5)
    BCS->>Battle: __init__ & Calculate Logical Turn 1 (Initial State)
    Battle-->>BCS: Turn 1 Complete, initial_state, logs_for_turn_1

    BCS->>BEB: build_embed(initial_state, logs_for_turn_1)
    Note right of BEB: Prioritize full logs, truncate if too long.
    BEB-->>BCS: embed_for_turn_1
    BCS-->>Cog: embed_for_turn_1
    Cog->>DiscordAPI: Send Message(embed_for_turn_1)
    DiscordAPI-->>Cog: message_id
    Cog->>Cog: store message_id

    loop Subsequent Turns (N+1)
        BCS->>WaitTimer: Start(2 seconds) # Start waiting
        BCS->>Battle: Start Calculating Logical Turn N+1 (Async) # Start calculating in background
        WaitTimer-->>BCS: 2 Seconds Finished
        BCS->>Battle: Is Turn N+1 Calculation Complete?
        alt Calculation Not Complete
            BCS->>BCS: Wait for Calculation Completion Signal
            Battle-->>BCS: Turn N+1 Complete, state, logs_for_turn_N+1
        else Calculation Already Complete
             Battle-->>BCS: Turn N+1 Complete, state, logs_for_turn_N+1
        end

        BCS->>BCS: Check if battle ended
        alt Battle Ended
             BCS->>BEB: build_final_embed(battle_result)
             BEB-->>BCS: final_embed
             BCS-->>Cog: final_embed
             Cog->>DiscordAPI: Edit Message(message_id, final_embed)
             break # Exit loop
        end

        BCS->>BEB: build_embed(state, logs_for_turn_N+1)
        Note right of BEB: Prioritize full logs, truncate if too long.
        BEB-->>BCS: embed_for_turn_N+1
        BCS-->>Cog: embed_for_turn_N+1
        Cog->>DiscordAPI: Edit Message(message_id, embed_for_turn_N+1) # Display N+1 results
        # Now loop back to start waiting for Turn N+2
    end

```

## 5. 待討論細節

*   日誌截斷的具體字符數限制。
*   Embed 中玩家/敵人狀態的具體格式和圖標選擇。
*   (未來) 擴展到多單位戰鬥時，日誌摘要算法的實現。