"""
Gacha系統用戶收藏存儲庫 - 功能 Mixin (最愛、星級、特殊查詢) (Asyncpg 版本)
"""
import asyncpg
from typing import Dict, List, Optional, Any
from gacha.models.models import UserCard
from utils.logger import logger
from gacha.exceptions import CardNotFoundError, DatabaseOperationError, RepositoryError

class UserCollectionFeaturesMixin:
    """提供用戶收藏特定功能相關方法的 Mixin (Asyncpg 版本)"""

    async def _execute_set_is_favorite_db(self, user_id: int, card_id: int, is_favorite: bool, connection: Optional[asyncpg.Connection]=None) -> bool:
        """(Async) 實際執行資料庫更新 is_favorite 的操作 - 讓調用者處理異常"""
        query = f'\n            UPDATE {self.table_name}\n            SET is_favorite = $1\n            WHERE user_id = $2 AND card_id = $3\n        '
        status_msg = await self._execute(query, (is_favorite, user_id, card_id), connection=connection)
        if status_msg != 'UPDATE 1':
            raise CardNotFoundError(f'設置 is_favorite 操作未影響任何行 (可能卡片不存在): user_id={user_id}, card_id={card_id}, is_favorite={is_favorite}')
        return True

    async def _update_favorite_status(self, user_id: int, card_id: int, status: bool, return_is_favorite: bool=True, connection: Optional[asyncpg.Connection]=None) -> Any:
        """(Async) 更新卡片的 is_favorite 欄位 (內部輔助方法) - 讓調用者處理異常."""
        await self._execute_set_is_favorite_db(user_id, card_id, status, connection=connection)
        return status if return_is_favorite else True

    async def toggle_favorite_card(self, user_id: int, card_id: int, connection: Optional[asyncpg.Connection]=None) -> bool:
        """(Async) 切換卡片的最愛狀態. 成功返回新的狀態 (True/False)."""
        check_query = f'SELECT is_favorite FROM {self.table_name} WHERE user_id = $1 AND card_id = $2'
        result = await self._fetchrow(check_query, (user_id, card_id), connection=connection)
        if not result:
            logger.warning('[GACHA_FEATURE_MIXIN] 嘗試切換不存在的收藏卡片: user_id=%s, card_id=%s', user_id, card_id)
            raise CardNotFoundError(f'找不到要切換最愛的卡片: user_id={user_id}, card_id={card_id}')
        current_state = result.get('is_favorite', False)
        new_state = not current_state
        updated_status_val = await self._update_favorite_status(user_id, card_id, new_state, return_is_favorite=True, connection=connection)
        if updated_status_val == new_state:
            return new_state
        else:
            logger.error('[GACHA_FEATURE_MIXIN] toggle_favorite_card: _update_favorite_status 返回意外狀態: user=%s, card=%s, expected=%s, got=%s', user_id, card_id, new_state, updated_status_val)
            raise RepositoryError('切換最愛狀態後狀態不一致')

    async def set_favorite_status(self, user_id: int, card_id: int, status: bool, connection: Optional[asyncpg.Connection]=None) -> bool:
        """(Async) 直接設置卡片的最愛狀態. 成功返回True."""
        check_query = f'SELECT is_favorite FROM {self.table_name} WHERE user_id = $1 AND card_id = $2'
        result = await self._fetchrow(check_query, (user_id, card_id), connection=connection)
        if not result:
            logger.warning('[GACHA_FEATURE_MIXIN] 嘗試設置不存在的收藏卡片最愛狀態: user_id=%s, card_id=%s', user_id, card_id)
            raise CardNotFoundError(f'找不到要設置最愛的卡片: user_id={user_id}, card_id={card_id}')
        current_state = result.get('is_favorite', False)
        if current_state == status:
            return True
        return await self._update_favorite_status(user_id, card_id, status, return_is_favorite=False, connection=connection)

    async def get_non_favorite_cards(self, user_id: int) -> List[UserCard]:
        """(Async) 獲取用戶所有非最愛標記的卡片."""
        query = f'\n            SELECT uc.*, mc.*, cd.description AS custom_description, cd.user_id AS description_user_id\n            FROM {self.table_name} uc\n            JOIN gacha_master_cards mc ON uc.card_id = mc.card_id\n            LEFT JOIN gacha_card_descriptions cd ON uc.card_id = cd.card_id AND cd.user_id = uc.user_id\n            WHERE uc.user_id = $1 AND (uc.is_favorite = false OR uc.is_favorite IS NULL)\n            ORDER BY mc.rarity DESC, mc.name ASC -- 改為 DESC (高稀有度優先)\n        '
        results = await self._fetch(query, (user_id,))
        return self._process_paginated_results(results if results else [])

    async def get_duplicate_cards(self, user_id: int, min_quantity: int=2) -> List[UserCard]:
        """(Async) 獲取用戶所有數量大於等於指定值的卡片."""
        if min_quantity < 1:
            raise ValueError('最小數量必須大於等於1')
        query = f'\n            SELECT uc.*, mc.*, cd.description AS custom_description, cd.user_id AS description_user_id\n            FROM {self.table_name} uc\n            JOIN gacha_master_cards mc ON uc.card_id = mc.card_id\n            LEFT JOIN gacha_card_descriptions cd ON uc.card_id = cd.card_id AND cd.user_id = uc.user_id\n            WHERE uc.user_id = $1 AND uc.quantity >= $2\n            ORDER BY mc.rarity DESC, uc.quantity DESC, mc.name ASC -- 改為 DESC (高稀有度優先)\n        '
        results = await self._fetch(query, (user_id, min_quantity))
        return self._process_paginated_results(results if results else [])