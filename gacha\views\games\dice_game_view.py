"""
Gacha系統骰子遊戲視圖
創建並格式化用於骰子大小遊戲的Discord Embed和交互組件
"""
from typing import Optional, TYPE_CHECKING, Dict, Any
import functools
import discord
from discord.ui import Button, View
from utils.logger import logger
from gacha.views.collection.collection_view import InteractionManager
if TYPE_CHECKING:
    from gacha.views.handlers.dice_callback_handler import DiceCallbackHandler
    from gacha.services.games.dice_game_service import DiceGameService
    from gacha.services.core.economy_service import EconomyService
    from gacha.services.ui.game_display_service import GameDisplayService

class DiceGameView(View):
    """骰子大小遊戲視圖"""

    def __init__(self, user: discord.User, dice_game_service: 'DiceGameService', economy_service: 'EconomyService', game_display_service: 'GameDisplayService', original_bet: int, game_state: Optional[Dict[str, Any]]=None):
        """初始化骰子大小遊戲視圖

        參數:
            user: Discord用戶對象
            dice_game_service: DiceGameService 實例
            economy_service: EconomyService 實例
            game_display_service: GameDisplayService 實例
            original_bet: 遊戲的原始下注金額
            game_state: 遊戲狀態 (用於遊戲結束後顯示重玩按鈕)
        """
        super().__init__(timeout=180)
        self.user = user
        self.user_id = user.id
        self.bet = original_bet
        self.game_state = game_state
        self.message: Optional[discord.Message] = None
        self._dice_game_service = dice_game_service
        self._economy_service = economy_service
        self._game_display_service = game_display_service
        self._original_bet = original_bet
        from gacha.views.handlers.dice_callback_handler import DiceCallbackHandler
        self.handler = DiceCallbackHandler(dice_game_service=self._dice_game_service, economy_service=self._economy_service, game_display_service=self._game_display_service, interaction_manager=InteractionManager(), user_id=self.user_id, view=self)

    async def init_buttons(self):
        """Async method to initialize buttons based on game state and user balance."""
        self.clear_items()
        if self.game_state is None:
            self._add_initial_choice_buttons()
        else:
            await self._add_replay_buttons()

    def _add_initial_choice_buttons(self):
        """Adds buttons for the initial dice choice (Big/Small) and bet amounts."""
        big_button = Button(label='大 (4-6)', style=discord.ButtonStyle.green, custom_id='dice:choice:big')
        big_button.callback = functools.partial(self.handler.handle_choice_selected, choice='big')
        self.add_item(big_button)
        small_button = Button(label='小 (1-3)', style=discord.ButtonStyle.red, custom_id='dice:choice:small')
        small_button.callback = functools.partial(self.handler.handle_choice_selected, choice='small')
        self.add_item(small_button)
        bet_amounts = [10, 50, 100, 500]
        min_bet = self._dice_game_service.MIN_BET if hasattr(self._dice_game_service, 'MIN_BET') else 10
        for amount in bet_amounts:
            if amount >= min_bet:
                bet_button = Button(label=f'{amount}油幣', style=discord.ButtonStyle.blurple, custom_id=f'dice:bet:{amount}')
                bet_button.callback = functools.partial(self.handler.handle_bet_selected, new_bet=amount)
                self.add_item(bet_button)

    async def _add_replay_buttons(self):
        """Adds buttons for replaying the game after it's over."""
        if self.game_state is None:
            return
        user_id = self.user_id
        latest_balance = 0
        try:
            balance_info = await self._economy_service.get_balance(user_id)
            latest_balance = balance_info.get('balance', 0)
        except Exception as e:
            logger.error('Error fetching latest balance for user %s in DiceGameView replay buttons: %s', user_id, e)
        current_bet = self._original_bet
        min_bet = self._dice_game_service.MIN_BET if hasattr(self._dice_game_service, 'MIN_BET') else 10
        play_again_same_button = Button(label=f"再來一局 ({current_bet} 油幣, {('大' if self.game_state.get('choice') == 'big' else '小')})", style=discord.ButtonStyle.green, custom_id=f'dice:replay:same:{current_bet}', disabled=latest_balance < current_bet)
        play_again_same_button.callback = self.handler.handle_replay_callback
        self.add_item(play_again_same_button)
        current_choice = self.game_state.get('choice', 'big')
        opposite_choice = 'small' if current_choice == 'big' else 'big'
        opposite_choice_name = '小 (1-3)' if opposite_choice == 'small' else '大 (4-6)'
        change_choice_button = Button(label=f'改押 {opposite_choice_name} ({current_bet} 油幣)', style=discord.ButtonStyle.blurple, custom_id=f'dice:replay:change:{current_bet}:{opposite_choice}', disabled=latest_balance < current_bet)
        change_choice_button.callback = self.handler.handle_replay_callback
        self.add_item(change_choice_button)
        double_bet_amount = current_bet * 2
        double_bet_button = Button(label=f'雙倍下注 ({double_bet_amount} 油幣)', style=discord.ButtonStyle.red, custom_id=f'dice:replay:double:{current_bet}', disabled=latest_balance < double_bet_amount)
        double_bet_button.callback = self.handler.handle_replay_callback
        self.add_item(double_bet_button)
        min_bet_button = Button(label=f'最低下注 ({min_bet} 油幣)', style=discord.ButtonStyle.secondary, custom_id=f'dice:replay:min:{current_bet}', disabled=latest_balance < min_bet or current_bet == min_bet)
        min_bet_button.callback = self.handler.handle_replay_callback
        self.add_item(min_bet_button)

    def get_initial_embed(self, current_balance: Optional[int]=None) -> discord.Embed:
        """Gets the embed for the initial choice interface based on current view state."""
        from gacha.views.embeds.games.dice_game_embed_builder import DiceGameEmbedBuilder
        builder = DiceGameEmbedBuilder(user=self.user, bet=self.bet)
        embed = builder.build_game_embed()
        if current_balance is not None:
            embed.add_field(name='💳 當前餘額', value=f'<:oil1:1368446294594424872> 餘額:`{current_balance}`', inline=True)
        return embed

    async def on_timeout(self):
        """超時處理"""
        try:
            for item in self.children:
                item.disabled = True
            if self.message:
                await self.message.edit(view=self)
        except Exception as e:
            logger.error('骰子遊戲視圖超時處理時發生錯誤: %s', str(e))
            pass