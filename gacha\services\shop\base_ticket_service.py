import math
import datetime
import asyncio
from typing import Dict, Any, Optional, List, TypeVar, Callable, Union
from decimal import Decimal
from functools import wraps
from gacha.services.core.user_service import UserService
from gacha.repositories.card.master_card_repository import MasterCardRepository
from gacha.repositories.collection.user_collection_repository import UserCollectionRepository
from gacha.app_config import config_service
from gacha.models.shop_models import ShopItemDefinition, ExchangeSessionData
from gacha.models.models import Card
from gacha.exceptions import (
    ShopSystemError, InsufficientOilTicketsError, ShopCardDetailsNotFoundError,
    OilTicketDeductionError, TransactionError
)
from utils.logger import logger
T = TypeVar('T')

class BaseTicketService:
    """票券兌換服務的基礎類，提供共享功能和工具方法"""

    def __init__(self, user_service: UserService, master_card_repo: MasterCardRepository, user_collection_repo: UserCollectionRepository, pool: Any):
        self.user_service = user_service
        self.master_card_repo = master_card_repo
        self.user_collection_repo = user_collection_repo
        self.pool = pool
        self.config_service = config_service

    def get_available_oil_tickets(self, decimal_balance: Optional[Decimal]) -> int:
        """將小數油票餘額轉換為可用整數油票"""
        return math.floor(decimal_balance) if decimal_balance is not None else 0

    async def get_user_data_for_exchange(self, user_id: int, connection: Optional[Any]=None) -> Dict[str, Any]:
        """一次性並行獲取用戶兌換所需的多種數據。"""
        try:
            user_balance_task = self.user_service.get_user_balance(user_id)
            oil_ticket_balance_task = self.user_service.get_oil_ticket_balance(user_id)
            user_balance, oil_ticket_balance = await asyncio.gather(user_balance_task, oil_ticket_balance_task)
            available_tickets = self.get_available_oil_tickets(oil_ticket_balance)
            
            return {
                'user_balance': user_balance,
                'oil_ticket_balance': oil_ticket_balance,
                'available_tickets_for_display': available_tickets
            }
        except Exception as e:
            logger.error('Error in get_user_data_for_exchange for user %s: %s', user_id, e, exc_info=True)
            raise ShopSystemError(f"獲取用戶兌換數據時出錯: {str(e)}")

    async def get_ticket_shop_definitions(self) -> List[ShopItemDefinition]:
        """從設定檔讀取可兌換的券道具定義，並轉換為 ShopItemDefinition 物件列表。"""
        definitions = list(self.config_service.gacha_core.ticket_shop_items.values())
        return sorted(definitions, key=lambda x: x.sort_order if x.sort_order is not None else 0)

    def with_sufficient_balance(func: Callable[..., T]) -> Callable[..., T]:
        """裝飾器：確保用戶有足夠的油票餘額"""

        @wraps(func)
        async def wrapper(self, user_id: int, ticket_definition: ShopItemDefinition, quantity: int, *args, **kwargs):
            total_oil_ticket_cost = ticket_definition.cost_oil_tickets * quantity
            current_balance_decimal = await self.user_service.get_oil_ticket_balance(user_id)
            current_balance_tickets = self.get_available_oil_tickets(current_balance_decimal)
            
            if current_balance_tickets < total_oil_ticket_cost:
                logger.warning('User %s: Insufficient balance (%s). Required: %s.', user_id, current_balance_tickets, total_oil_ticket_cost)
                raise InsufficientOilTicketsError(required_amount=total_oil_ticket_cost, current_balance=current_balance_tickets)
                
            return await func(self, user_id, ticket_definition, quantity, *args, **kwargs)
        return wrapper

    async def _execute_exchange_transaction(self, conn, user_id: int, session_id: str, ticket_definition: ShopItemDefinition, quantity: int, cards_to_grant: List[Union[int, Card]]) -> Dict[str, Any]:
        """
        執行兌換交易的核心邏輯，在數據庫事務中完成油票扣除和卡片授予。

        Args:
            conn: 數據庫連接
            user_id: 用戶ID
            session_id: 兌換會話ID
            ticket_definition: 兌換券定義
            quantity: 兌換數量
            cards_to_grant: 要授予的卡片ID列表或Card對象列表

        Returns:
            Dict with granted cards info
        
        Raises:
            TransactionError: 交易執行失敗
            UserNotFoundError: 找不到用戶
            InsufficientBalanceError: 油票餘額不足
            ShopCardDetailsNotFoundError: 無法獲取卡片詳情
        """
        if not cards_to_grant:
            return {'granted_cards_info_for_embed': []}
            
        total_oil_ticket_cost = ticket_definition.cost_oil_tickets * quantity
        reason_for_deduction = f'兌換 {ticket_definition.display_name} x {quantity}'
        transaction_details = f'Type: ticket_exchange, Reference ID: {session_id}'
        logger.info('Attempting to deduct %s oil tickets from user %s. Reason: %s. Details: %s', total_oil_ticket_cost, user_id, reason_for_deduction, transaction_details)
        
        original_card_ids_to_grant = []
        if cards_to_grant:
            if isinstance(cards_to_grant[0], int):
                original_card_ids_to_grant = list(cards_to_grant)
            elif hasattr(cards_to_grant[0], 'card_id'):
                original_card_ids_to_grant = [card.card_id for card in cards_to_grant]
            else:
                logger.error('Invalid cards_to_grant type: %s', type(cards_to_grant[0]))
                raise TransactionError('無效的卡片數據類型。')
                
        unique_card_ids = list(set(original_card_ids_to_grant))
        unique_card_details_map = {}
        unique_card_details = await self.master_card_repo.get_cards_details_by_ids(unique_card_ids)
        
        if not unique_card_details:
            logger.error('Failed to fetch card details for IDs: %s', unique_card_ids)
            raise ShopCardDetailsNotFoundError(card_ids=unique_card_ids, missing_count=len(unique_card_ids))
            
        for card in unique_card_details:
            unique_card_details_map[card.card_id] = card
            
        full_card_details = []
        for card_id in original_card_ids_to_grant:
            if card_id in unique_card_details_map:
                full_card_details.append(unique_card_details_map[card_id])
            else:
                logger.warning('Card ID %s was in original list but not found in details. Skipping.', card_id)
                
        async with conn.transaction():
            try:
                # 直接扣除油票，如果失敗會拋出異常
                await self.user_service.deduct_oil_tickets(user_id=user_id, amount=total_oil_ticket_cost, connection=conn)
                    
                exchange_result = {}
                if original_card_ids_to_grant:
                    exchange_result = await self.user_collection_repo.bulk_add_cards_and_check_new(user_id=user_id, card_ids=original_card_ids_to_grant, connection=conn)
                    logger.info('Session %s: Granted %s cards to user %s. New cards: %s', session_id, len(original_card_ids_to_grant), user_id, exchange_result.get('new_card_ids', []))
                    
                return {
                    'granted_cards_info_for_embed': [card.__dict__ for card in full_card_details] if full_card_details else []
                }
            except Exception as e_final:
                logger.error(
                    'Error during exchange transaction for session %s. Type: %s, Args: %s, Exception: %s',
                    session_id,
                    type(e_final),
                    e_final.args,
                    e_final,
                    exc_info=True
                )
                # 直接向上傳播異常，不再包裝成 TransactionError
                raise