import threading
from abc import ABCMeta


class SingletonType(ABCMeta):
    """
    線程安全的單例元類（Singleton Metaclass）

    使用方法:
    ```python
    class MyClass(metaclass=SingletonType):
        pass

    # 每次創建的都是同一個實例
    instance1 = MyClass()
    instance2 = MyClass()
    assert instance1 is instance2  # True
    ```
    """

    def __new__(mcs, name, bases, attrs):
        """為每個使用此元類的類創建獨立的_instance和_instance_lock屬性"""
        attrs["_instance"] = None
        attrs["_instance_lock"] = threading.Lock()
        return super(SingletonType, mcs).__new__(mcs, name, bases, attrs)

    def __call__(cls, *args, **kwargs):
        """確保每次調用類都返回同一個實例"""
        if cls._instance is None:
            with cls._instance_lock:
                if cls._instance is None:
                    cls._instance = super(SingletonType, cls).__call__(*args, **kwargs)
        return cls._instance