"""
基礎處理器類，提供統一的錯誤處理和通用功能
"""
from typing import TYPE_CHECKING, Optional
from abc import ABC
import discord
from utils.logger import logger
if TYPE_CHECKING:
    from gacha.views.collection.collection_view.card_view import CollectionView

class BaseHandler(ABC):
    """所有處理器的基礎類"""

    def __init__(self, view: 'CollectionView'):
        self.view = view

    @property
    def user(self) -> discord.User:
        """獲取用戶對象"""
        return self.view.user

    @property
    def user_id(self) -> int:
        """獲取用戶ID"""
        return self.view.user.id

    @property
    def current_card(self):
        """獲取當前卡片 - 統一接口"""
        return self.view.current_card

    async def check_interaction_permission(self, interaction: discord.Interaction) -> bool:
        """檢查交互權限"""
        return await self.view.interaction_check(interaction)

    async def check_cards_available(self, interaction: discord.Interaction, error_message: str='當前頁面沒有卡片') -> bool:
        """統一的卡片可用性檢查"""
        from ..utils import check_cards_available
        return await check_cards_available(self.view, interaction, error_message)

    async def get_current_card_info(self, interaction: discord.Interaction) -> Optional[dict]:
        """獲取當前卡片信息，包含常用的狀態檢查

        返回:
            dict: 包含卡片信息的字典，如果無可用卡片則返回 None
            {
                'user_card': UserCard,
                'card_id': int,
                'is_favorite': bool,
                'star_level': int,
                'quantity': int
            }
        """
        if not await self.check_cards_available(interaction):
            return None
        current_card = self.current_card
        if not current_card or not hasattr(current_card, 'card'):
            await self._send_error_message(interaction, '當前頁面卡片數據異常')
            return None
        return {'user_card': current_card, 'card_id': current_card.card.card_id, 'is_favorite': getattr(current_card, 'is_favorite', False), 'star_level': getattr(current_card, 'star_level', 0), 'quantity': getattr(current_card, 'quantity', 0)}

    async def _send_error_message(self, interaction: discord.Interaction, message: str, ephemeral: bool=True):
        """統一的錯誤消息發送"""
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(message, ephemeral=ephemeral)
            else:
                await interaction.followup.send(message, ephemeral=ephemeral)
        except Exception as e:
            logger.error('Failed to send error message: %s', e, exc_info=True)

    async def _send_success_message(self, interaction: discord.Interaction, message: str, ephemeral: bool=True):
        """統一的成功消息發送"""
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(message, ephemeral=ephemeral)
            else:
                await interaction.followup.send(message, ephemeral=ephemeral)
        except Exception as e:
            logger.error('Failed to send success message: %s', e, exc_info=True)

    async def handle_interaction_error(self, interaction: discord.Interaction, error: Exception, operation_name: str='操作', log_prefix: str='HANDLER'):
        """統一的交互錯誤處理

        Args:
            interaction: Discord 交互對象
            error: 發生的錯誤
            operation_name: 操作名稱，用於錯誤信息顯示
            log_prefix: 日誌前綴
        """
        error_msg = f'{operation_name}失敗: {str(error)}'
        logger.error('[%s] %s (Interaction ID: %s)', log_prefix, error_msg, interaction.id, exc_info=True)
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(error_msg, ephemeral=True)
            else:
                await interaction.followup.send(error_msg, ephemeral=True)
        except discord.errors.InteractionResponded:
            await interaction.followup.send(error_msg, ephemeral=True)
        except Exception as response_error:
            logger.error('[%s] 無法發送錯誤訊息: %s (Interaction ID: %s)', log_prefix, str(response_error), interaction.id, exc_info=True)

    async def send_success_message(self, interaction: discord.Interaction, title: str, description: str, ephemeral: bool=True):
        """發送成功消息的統一方法"""
        embed = discord.Embed(title=title, description=description, color=discord.Color.green())
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=embed, ephemeral=ephemeral)
            else:
                await interaction.followup.send(embed=embed, ephemeral=ephemeral)
        except Exception as e:
            logger.error('[HANDLER] 發送成功消息失敗: %s', e, exc_info=True)

    async def send_error_message(self, interaction: discord.Interaction, title: str, description: str, ephemeral: bool=True):
        """發送錯誤消息的統一方法"""
        embed = discord.Embed(title=title, description=description, color=discord.Color.red())
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=embed, ephemeral=ephemeral)
            else:
                await interaction.followup.send(embed=embed, ephemeral=ephemeral)
        except Exception as e:
            logger.error('[HANDLER] 發送錯誤消息失敗: %s', e, exc_info=True)
__all__ = ['BaseHandler']