import os
import sys
import glob
from datetime import datetime

def manage_backups(backup_dir, max_backups_str, backup_pattern="backup_*.dump"):
    """
    Manages backups in a directory by keeping only the latest 'max_backups' number of files.
    """
    # 移除参数两端可能存在的引号
    backup_dir_cleaned = backup_dir.strip('"').strip("'")
    backup_pattern_cleaned = backup_pattern.strip('"').strip("'")

    if not os.path.isdir(backup_dir_cleaned):
        print(f"Error: Backup directory '{backup_dir_cleaned}' does not exist.")
        sys.exit(1)

    try:
        max_backups = int(max_backups_str)
        if max_backups <= 0:
            raise ValueError("max_backups must be a positive integer.")
    except ValueError as e:
        print(f"Error: Invalid value for max_backups ('{max_backups_str}'). {e}")
        sys.exit(1)

    search_path = os.path.join(backup_dir_cleaned, backup_pattern_cleaned)
    backup_files = glob.glob(search_path)

    if not backup_files:
        print(f"No backup files found matching '{backup_pattern_cleaned}' in '{backup_dir_cleaned}'.")
        return

    def get_sort_key(filepath):
        filename = os.path.basename(filepath)
        # 假设文件名格式为 "backup_YYYYMMDD_HHMMSS.dump"
        # 例如: backup_20231027_153000.dump
        name_part = os.path.splitext(filename)[0] # 移除 .dump
        parts = name_part.split('_')
        # parts 会是 ["backup", "YYYYMMDD", "HHMMSS"]
        if len(parts) == 3 and parts[0] == "backup":
            timestamp_str = parts[1] + "_" + parts[2] # "YYYYMMDD_HHMMSS"
            try:
                dt_obj = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                return dt_obj
            except ValueError as ve:
                # 如果解析失败，打印警告并回退到使用文件修改时间
                # print(f"Warning: Could not parse timestamp from filename '{filename}': {ve}. Using file modification time.")
                pass 
        # 如果文件名格式不匹配或解析失败，使用文件的最后修改时间作为排序依据
        try:
            return datetime.fromtimestamp(os.path.getmtime(filepath))
        except Exception as e_mtime:
            # print(f"Warning: Could not get modification time for '{filepath}': {e_mtime}. Placing it at the end.")
            # 如果连修改时间都获取失败，返回一个很早的时间，使其排在最前面被删除（或最后，取决于排序顺序）
            # 这里我们希望无法确定时间的排在前面，以便可能被删除
            return datetime.min 

    try:
        # 按时间戳升序排序 (最旧的在前)
        backup_files.sort(key=get_sort_key)
    except Exception as e_sort: # 捕获 get_sort_key 中可能未处理的异常
        print(f"Warning: Error during sorting backup files: {e_sort}. Attempting sort by modification time.")
        try:
            backup_files.sort(key=os.path.getmtime)
        except Exception as e_mtime_sort:
            print(f"Fatal: Could not sort files by modification time either: {e_mtime_sort}. Aborting cleanup.")
            sys.exit(1)

    num_backups_to_delete = len(backup_files) - max_backups

    if num_backups_to_delete > 0:
        print(f"Found {len(backup_files)} backups. Keeping the newest {max_backups}. Deleting {num_backups_to_delete} oldest backups...")
        for i in range(num_backups_to_delete):
            file_to_delete = backup_files[i]
            try:
                os.remove(file_to_delete)
                print(f"Deleted old backup: {file_to_delete}")
            except OSError as e:
                print(f"Error deleting file {file_to_delete}: {e}")
    else:
        print(f"Found {len(backup_files)} backups. No old backups to delete (max_backups: {max_backups}).")

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python manage_backups.py <backup_directory> <max_backups_to_keep> [backup_pattern]")
        print("Example: python manage_backups.py \"D:\\PostgreSQL_Backups\" 72 \"backup_*.dump\"")
        sys.exit(1)
    
    backup_dir_arg = sys.argv[1]
    max_backups_arg = sys.argv[2]
    backup_pattern_arg = sys.argv[3] if len(sys.argv) > 3 else "backup_*.dump"

    manage_backups(backup_dir_arg, max_backups_arg, backup_pattern_arg) 