"""
Gacha系統經濟相關指令的COG。
包含 /balance, /daily, /hourly, /sw (sell_card) 指令。
"""
import discord
from discord import app_commands
from discord.ext import commands
from datetime import datetime
import time
from typing import Optional, List, Dict, Any, Union
from dataclasses import asdict
from decimal import Decimal
from gacha.services.core.economy_service import EconomyService
from gacha.services.core.collection_service import CollectionService
from utils.logger import logger
from gacha.models.filters import CollectionFilters
from gacha.views import utils as view_utils
from gacha.views.utils import common_pool_type_autocomplete, common_series_autocomplete
from gacha.app_config import config_service
from gacha.constants import RarityLevel
from gacha.views.ui_components.confirmation import ConfirmationFactory, ConfirmationViewBase
from gacha.exceptions import UserNotFoundError, DatabaseOperationError, StoredPriceError
from gacha.utils.cog_error_handler import handle_gacha_error
BotType = Union[commands.Bot, commands.AutoShardedBot]

class EconomyCog(commands.Cog, name='經濟'):
    """處理經濟相關指令，如餘額查詢、每日/每小時獎勵和卡片販賣。"""

    def __init__(self, bot: BotType):
        self.bot = bot
        logger.info('EconomyCog initialized.')

    async def cog_load(self):
        logger.info('EconomyCog has been loaded.')

    @app_commands.command(name='balance', description='查看你的油幣餘額')
    async def balance(self, interaction: discord.Interaction):
        """處理餘額查詢命令"""
        try:
            await interaction.response.defer(ephemeral=False)
            user_id = interaction.user.id
            economy_service: EconomyService = self.bot.economy_service
            
            # 直接調用 get_balance 方法，如果發生錯誤會拋出異常
            user_info = await economy_service.get_balance(user_id)
            
            # 獲取油票餘額
            oil_ticket_balance = await economy_service.user_service.user_repo.get_oil_ticket_balance(user_id) or Decimal('0.00')
            
            display_name = interaction.user.display_name
            embed = discord.Embed(color=discord.Color.blue(), timestamp=datetime.now())
            embed.add_field(name='油幣餘額', value=f"<:oil1:1368446294594424872> 餘額:`{user_info['balance']}`", inline=False)
            available_tickets = int(oil_ticket_balance)
            embed.add_field(name='油票餘額', value=f'💰 油票:`{available_tickets}`', inline=False)
            embed.add_field(name='總抽卡次數', value=f"{user_info['total_draws']}次", inline=False)
            embed.add_field(name='每日獎勵', value='使用`/daily`領取每日獎勵', inline=False)
            embed.add_field(name='每小時獎勵', value='使用`/hourly`領取每小時獎勵', inline=False)
            embed.set_thumbnail(url='https://cdn.dev.conquest.bot/thumbnails/transfer.png')
            embed.set_author(name=f'{display_name}的帳戶信息', icon_url=interaction.user.display_avatar.url if interaction.user.display_avatar else None)
            embed.set_footer(text='油幣可用於抽卡（/w）和21點遊戲（/blackjack）')
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            # 使用統一的錯誤處理器
            await handle_gacha_error(interaction, e, "查詢餘額時")

    @app_commands.command(name='daily', description='領取每日獎勵油幣')
    async def daily(self, interaction: discord.Interaction):
        """處理每日獎勵領取命令"""
        await interaction.response.defer(thinking=True, ephemeral=False)
        try:
            user_id = interaction.user.id
            economy_service: EconomyService = self.bot.economy_service
            
            # 直接調用 claim_daily_reward 方法，如果發生錯誤會拋出異常
            result = await economy_service.claim_daily_reward(user_id)
            
            # 成功領取獎勵
            embed = discord.Embed(
                title='每日獎勵已領取', 
                description=f'成功領取{result["reward"]}油幣！', 
                color=discord.Color.green(), 
                timestamp=datetime.now()
            )
            embed.add_field(name='當前餘額', value=f"<:oil1:1368446294594424872> 餘額:`{result['new_balance']}`", inline=False)
            if interaction.user.display_avatar:
                embed.set_thumbnail(url=interaction.user.display_avatar.url)
            embed.set_footer(text='油幣可用於抽卡和21點遊戲')
            await interaction.followup.send(embed=embed, ephemeral=False)
            
        except Exception as e:
            # 使用統一的錯誤處理器
            await handle_gacha_error(interaction, e, "領取每日獎勵時")

    @app_commands.command(name='hourly', description='領取每小時獎勵油幣')
    async def hourly(self, interaction: discord.Interaction):
        """處理每小時獎勵領取命令"""
        await interaction.response.defer(thinking=True, ephemeral=False)
        try:
            user_id = interaction.user.id
            current_nickname = interaction.user.display_name
            economy_service: EconomyService = self.bot.economy_service
            
            # 確保用戶暱稱是最新的（如果不存在或不同則更新）
            await economy_service.user_service.ensure_nickname_updated(user_id, current_nickname)
            
            # 直接調用 claim_hourly_reward 方法，如果發生錯誤會拋出異常
            result = await economy_service.claim_hourly_reward(user_id)
            
            # 成功領取獎勵
            embed = discord.Embed(
                title='每小時獎勵已領取', 
                description=f'成功領取{result["reward"]}油幣！', 
                color=discord.Color.green(), 
                timestamp=datetime.now()
            )
            embed.add_field(name='當前餘額', value=f"<:oil1:1368446294594424872> 餘額:`{result['new_balance']}`", inline=False)
            
            if 'next_claim_time' in result:
                next_time = result['next_claim_time']
                next_time_str = f'<t:{int(next_time)}:R>'
                embed.add_field(name='下次可領取時間', value=next_time_str, inline=False)
                
            if interaction.user.display_avatar:
                embed.set_thumbnail(url=interaction.user.display_avatar.url)
                
            embed.set_footer(text='油幣可用於抽卡和21點遊戲')
            await interaction.followup.send(embed=embed, ephemeral=False)
            
        except Exception as e:
            # 使用統一的錯誤處理器
            await handle_gacha_error(interaction, e, "領取每小時獎勵時")

    @staticmethod
    async def _handle_bulk_sell_result(interaction: discord.Interaction, sell_result: Dict[str, Any], title: str='賣出成功', ephemeral: bool=False):
        """處理批量賣卡結果 (移植自 TransactionResultHandler)"""
        if not sell_result:
            try:
                error_embed = discord.Embed(title='批量賣卡失敗', description='處理結果為空，無法完成操作。', color=discord.Color.red())
                if not interaction.response.is_done():
                    await interaction.response.send_message(embed=error_embed, ephemeral=True)
                else:
                    await interaction.followup.send(embed=error_embed, ephemeral=True)
            except Exception as e:
                logger.error('[SELL_COG_HELPER] 發送批量賣卡失敗訊息時出錯: %s', e, exc_info=True)
            return False
        
        success_embed = EconomyCog._create_bulk_success_embed(interaction.user, sell_result, title)
        try:
            if not interaction.response.is_done():
                await interaction.followup.send(embed=success_embed, ephemeral=ephemeral)
            else:
                await interaction.response.send_message(embed=success_embed, ephemeral=ephemeral)
        except discord.errors.InteractionResponded:
            await interaction.edit_original_response(embed=success_embed, view=None)
        except Exception as e:
            logger.error('[SELL_COG_HELPER] 發送批量賣卡成功訊息時出錯: %s', e, exc_info=True)
            try:
                await interaction.channel.send(embed=success_embed)
            except Exception as e2:
                logger.error('[SELL_COG_HELPER] 備用發送批量賣卡成功訊息也失敗: %s', e2, exc_info=True)
            return False
        return True

    @staticmethod
    def _create_bulk_success_embed(user: discord.User, sell_result: Dict[str, Any], title: str) -> discord.Embed:
        """創建批量賣卡成功的嵌入式消息 (移植自 TransactionResultHandler)"""
        total_sold_count = sell_result.get('total_cards_sold', 0)
        total_oil_gained = sell_result.get('total_revenue', 0)
        new_balance = sell_result.get('new_balance', '未知')
        new_balance_display = new_balance if new_balance is not None else '未知'
        
        # 直接將 description 設為空字串，避免與欄位資訊重複
        embed = discord.Embed(title=title, description='', color=discord.Color.green(), timestamp=datetime.now())
        embed.set_author(name=user.display_name, icon_url=user.display_avatar.url if user.display_avatar else None)
        embed.add_field(name='總共賣出', value=f'{total_sold_count}張卡片', inline=True)
        embed.add_field(name='總獲得', value=f'{int(total_oil_gained)}油幣', inline=True)
        embed.add_field(name='當前餘額', value=f'{new_balance_display}油幣', inline=True)
        cards_for_stats_display = sell_result.get('sold_cards_summary', [])
        if cards_for_stats_display:
            EconomyCog._add_card_summary_fields(embed, cards_for_stats_display)
        else:
            logger.warn(f'[SELL_COG_EMBED] No cards_summary_for_stats available for user {user.id} in sell_result. Rarity stats field will be missing or show a default message if _add_card_summary_fields handles empty.')
        sold_card_examples_data = sell_result.get('sold_cards_for_examples', [])
        if sold_card_examples_data:
            sorted_card_examples = sorted(sold_card_examples_data, key=lambda x: x.get('price_per_unit', 0), reverse=True)
            card_examples_to_display = sorted_card_examples[:10]
            example_text_parts = []
            for card_item in card_examples_to_display:
                rarity_int = card_item.get('rarity')
                rarity_display_text = '未知'
                if rarity_int is not None:
                    try:
                        rarity_level_enum = RarityLevel(rarity_int)
                        display_code = config_service.get_rarity_display_codes().get(rarity_level_enum.value)
                        if display_code:
                            rarity_display_text = display_code
                        else:
                            rarity_display_text = rarity_level_enum.name
                    except ValueError:
                        logger.warn(f'[SELL_COG_EMBED] Invalid rarity value {rarity_int} in sold_card_examples_data for item: {card_item}')
                        rarity_display_text = f'R:{rarity_int}'
                item_name = card_item.get('name', '未知卡片')
                item_quantity_sold = card_item.get('quantity_sold', 0)
                item_price_per_unit = card_item.get('price_per_unit', 0)
                text = f'**{item_name}** ({rarity_display_text}) x{item_quantity_sold} - {item_price_per_unit}油幣'
                if card_item.get('is_favorite', False):
                    text += ' ❤️'
                example_text_parts.append(text)
            example_text = '\n'.join(example_text_parts)
            if len(sorted_card_examples) > 10:
                example_text += f'\n...以及 {len(sorted_card_examples) - 10} 種其他卡片'
            embed.add_field(name='賣出卡片範例', value=example_text, inline=False)
        else:
            embed.add_field(name='賣出卡片範例', value='沒有卡片被賣出，或無範例數據。', inline=False)
        
        # 使用提供的 Discord CDN 直接 GIF 圖片連結
        embed.set_thumbnail(url="https://cdn.discordapp.com/attachments/1336020673730187334/1375776759953363074/watashi-ni-tenshi-money.gif?ex=6832eb74&is=683199f4&hm=b778efe267717dd1bbca456cf2edaa13b2f5516aa9b99f30d945970a61281acb&")
        
        return embed

    @staticmethod
    def _add_card_summary_fields(embed: discord.Embed, cards_summary: List[Dict[str, Any]]):
        """添加卡片摘要字段到嵌入式 (移植自 TransactionResultHandler) - 現在只處理稀有度統計"""
        pool_rarity_counts: Dict[str, Dict[int, int]] = view_utils.get_pool_rarity_stats(cards_summary)
        for pool_type, rarity_counts in pool_rarity_counts.items():
            if not rarity_counts:
                continue
            sorted_rarities = sorted(list(rarity_counts.keys()), reverse=True)
            rarity_stats = []
            for rarity_int_val in sorted_rarities:
                count = rarity_counts[rarity_int_val]
                try:
                    rarity_level_enum = RarityLevel(rarity_int_val)
                    emoji = view_utils.get_rarity_display_code(rarity_level_enum, pool_type=pool_type)
                except ValueError:
                    emoji = '❓'
                rarity_stats.append(f'{emoji} x{count}')
            if rarity_stats:
                pool_name = config_service.get_pool_type_names().get(pool_type, pool_type)
                embed.add_field(name=f'{pool_name}稀有度統計', value=' | '.join(rarity_stats), inline=False)

    @app_commands.command(name='sw', description='賣出卡片獲取油幣 (統一接口)')
    @app_commands.describe(card_id_param='卡片ID，直接指定要賣出的卡片 (如果同時提供名稱，ID優先)', card_name='卡片名稱，根據名稱尋找卡片', series='系列名稱，指定要賣出的卡片系列', rarity='稀有度，指定要賣出的卡片稀有度 (C, R, SR, SSR, UR, LR, EX)', pool_type='卡池類型，指定要賣出的卡片卡池', operation="操作類型：'one'(賣1張)、'leave_one'(賣到剩1張)、'all'(全部賣出)", include_favorites='是否包含最愛卡片（默認為False，True則包含）')
    @app_commands.choices(rarity=[app_commands.Choice(name='普通 (C)', value='C'), app_commands.Choice(name='稀有 (R)', value='R'), app_commands.Choice(name='超稀有 (SR)', value='SR'), app_commands.Choice(name='特殊稀有 (SSR)', value='SSR'), app_commands.Choice(name='極稀有 (UR)', value='UR'), app_commands.Choice(name='傳說稀有 (LR)', value='LR'), app_commands.Choice(name='特殊限定 (EX)', value='EX')], operation=[app_commands.Choice(name='賣出1張 (one)', value='one'), app_commands.Choice(name='賣到剩1張 (leave_one)', value='leave_one'), app_commands.Choice(name='全部賣出 (all)', value='all')])
    async def sell_card(self, interaction: discord.Interaction, card_id_param: Optional[int]=None, card_name: Optional[str]=None, series: Optional[str]=None, rarity: Optional[str]=None, pool_type: Optional[str]=None, operation: str='one', include_favorites: bool=False):
        """處理賣卡命令 - 統一調用 EconomyService.sell_cards_universal"""
        try:
            if not interaction.response.is_done():
                await interaction.response.defer(ephemeral=False, thinking=True)
            user_id = interaction.user.id
            economy_service: EconomyService = self.bot.economy_service
            collection_service: CollectionService = self.bot.collection_service
            filters = CollectionFilters()
            final_card_id: Optional[int] = card_id_param
            if card_name and (not final_card_id):
                name_filter_for_search = CollectionFilters(card_name=card_name)
                found_card_ids = await collection_service.get_filtered_card_ids(user_id, name_filter_for_search)
                if not found_card_ids:
                    await interaction.followup.send(f'找不到名稱包含「{card_name}」的卡片。', ephemeral=True)
                    return
                final_card_id = found_card_ids[0]
                logger.info("[SW_COG] User %s searched for card name '%s', found IDs: %s, selected: %s", user_id, card_name, found_card_ids, final_card_id)
            if final_card_id:
                filters.card_id = final_card_id
            if series:
                filters.series = series
            if pool_type and pool_type.lower() != 'all':
                filters.pool_type = pool_type
            if rarity:
                rarity_level_enum = None
                input_rarity_code = rarity.upper()
                for level in RarityLevel:
                    current_level_display_code = config_service.get_rarity_display_codes().get(level.value)
                    if current_level_display_code == input_rarity_code:
                        rarity_level_enum = level
                        break
                if rarity_level_enum:
                    filters.rarity_in = [rarity_level_enum.value]
                else:
                    await interaction.followup.send(f'無法識別的稀有度代碼: {rarity}', ephemeral=True)
                    return
            operation_type_str = str(operation).upper()
            if operation_type_str not in ['ONE', 'LEAVE_ONE', 'ALL']:
                await interaction.followup.send(f"無效的操作類型: {operation}. 請使用 'one', 'leave_one', 或 'all'。", ephemeral=True)
                return
            force_sell_flag = include_favorites
            confirmation_needed = False
            confirm_title = ''
            confirm_description = ''
            confirm_field_name = '操作影響'
            confirm_field_value = '請確認您的操作。'
            is_broad_sell_all = operation_type_str == 'ALL' and (not filters.has_any_filter())
            is_broad_leave_one = operation_type_str == 'LEAVE_ONE' and (not filters.has_any_filter())
            if is_broad_sell_all:
                confirmation_needed = True
                confirm_title = '⚠️ 危險警告：即將賣出所有卡片 ⚠️'
                confirm_description = f"你確定要賣出收藏中所有{('**包括最愛**的' if force_sell_flag else '**未標記為最愛**的')}卡片嗎？\n此操作**無法撤銷**！"
                confirm_field_value = '所有卡片' + (' (包括最愛)' if force_sell_flag else ' (不包括最愛)')
            elif is_broad_leave_one:
                confirmation_needed = True
                confirm_title = '⚠️ 警告：即將賣出所有重複卡片 ⚠️'
                confirm_description = f"你確定要賣出所有重複卡片，對每種卡片保留1張 ({('**包括最愛**' if force_sell_flag else '**不包括最愛**')})嗎？\n此操作**無法撤銷**！"
                confirm_field_value = '所有重複卡片，每種保留1張' + (' (包括最愛)' if force_sell_flag else ' (不包括最愛)')
            elif force_sell_flag and filters.has_any_filter():
                confirmation_needed = True
                confirm_title = '⚠️ 確認賣出最愛卡片 ⚠️'
                confirm_description = '你選擇了包含最愛卡片進行售賣。此操作會將符合篩選條件的最愛卡片一併賣出。\n此操作**無法撤銷**！'
                confirm_field_value = '符合篩選條件的最愛卡片'
            if not filters.has_any_filter() and operation_type_str == 'ONE':
                await interaction.followup.send('**卡片賣出提示**\n\n若要賣出單張卡片，請指定卡片 (例如 `card_id=123` 或 `card_name="某卡片"`)。\n若要批量操作，請使用 `operation=all` 或 `operation=leave_one` 並可配合篩選條件 (如 `series`, `rarity`)。', ephemeral=True)
                return

            async def perform_sell_action(current_interaction: discord.Interaction):
                try:
                    if not current_interaction.response.is_done():
                        await current_interaction.response.defer(ephemeral=False, thinking=True)
                    logger.info('[SW_COG_EXEC] User %s performing sell: filters=%s, operation=%s, force_sell=%s', user_id, asdict(filters), operation_type_str, force_sell_flag)
                    
                    try:
                        sell_result = await economy_service.sell_cards_universal(user_id=user_id, filters=filters, operation_type=operation_type_str, force_sell=force_sell_flag)
                        result_title = f'賣出操作 ({operation}) 成功'
                        await EconomyCog._handle_bulk_sell_result(current_interaction, sell_result, title=result_title, ephemeral=False)
                    except ValueError as e:
                        # 處理業務邏輯錯誤（如沒有符合條件的卡片）
                        error_embed = discord.Embed(title=f'賣出操作 ({operation}) 提醒', description=str(e), color=discord.Color.gold())
                        if current_interaction.response.is_done():
                            await current_interaction.followup.send(embed=error_embed, ephemeral=True)
                        else:
                            await current_interaction.response.send_message(embed=error_embed, ephemeral=True)
                    except StoredPriceError as e:
                        # 處理價格相關錯誤
                        error_embed = discord.Embed(title=f'賣出操作 ({operation}) 失敗', description=f"價格錯誤: {str(e)}", color=discord.Color.red())
                        if current_interaction.response.is_done():
                            await current_interaction.followup.send(embed=error_embed, ephemeral=True)
                        else:
                            await current_interaction.response.send_message(embed=error_embed, ephemeral=True)
                    except UserNotFoundError as e:
                        # 處理用戶不存在錯誤
                        error_embed = discord.Embed(title=f'賣出操作 ({operation}) 失敗', description=f"用戶不存在: {str(e)}", color=discord.Color.red())
                        if current_interaction.response.is_done():
                            await current_interaction.followup.send(embed=error_embed, ephemeral=True)
                        else:
                            await current_interaction.response.send_message(embed=error_embed, ephemeral=True)
                    except Exception as e:
                        # 處理其他未預期錯誤
                        logger.error('[SW_COG_EXEC] User %s: Unexpected error during sell operation: %s', user_id, e, exc_info=True)
                        error_embed = discord.Embed(title=f'賣出操作 ({operation}) 失敗', description="執行售賣操作時發生內部錯誤。", color=discord.Color.red())
                        if current_interaction.response.is_done():
                            await current_interaction.followup.send(embed=error_embed, ephemeral=True)
                        else:
                            await current_interaction.response.send_message(embed=error_embed, ephemeral=True)
                except Exception as ex_perform:
                    logger.error('[SW_COG_EXEC] User %s: Error during perform_sell_action: %s', user_id, ex_perform, exc_info=True)
                    if current_interaction.response.is_done():
                        await current_interaction.followup.send('執行售賣操作時發生內部錯誤。', ephemeral=True)
                    else:
                        await current_interaction.response.send_message('執行售賣操作時發生內部錯誤。', ephemeral=True)
            if confirmation_needed:

                async def adapted_confirm_callback(button_interaction: discord.Interaction, is_confirmed: bool):
                    if is_confirmed:
                        await perform_sell_action(button_interaction)
                await ConfirmationFactory.create_confirmation(interaction=interaction, title=confirm_title, description=confirm_description, user_id=user_id, callback=adapted_confirm_callback, field_name=confirm_field_name, field_value=confirm_field_value, confirm_label='確認賣出', cancel_label='取消', confirm_style=discord.ButtonStyle.danger, cancel_style=discord.ButtonStyle.secondary, ephemeral=False)
            else:
                await perform_sell_action(interaction)
        except Exception as e:
            logger.error('[SW_COG] User %s: Unhandled error in sell_card command: %s', user_id, e, exc_info=True)
            if not interaction.response.is_done():
                await interaction.response.send_message('處理賣卡命令時發生未預期錯誤。', ephemeral=True)
            else:
                await interaction.followup.send('處理賣卡命令時發生未預期錯誤。', ephemeral=True)

    @sell_card.autocomplete('series')
    async def series_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        collection_service: CollectionService = self.bot.collection_service
        return await common_series_autocomplete(interaction, current, collection_service)

    @sell_card.autocomplete('pool_type')
    async def pool_type_autocomplete(self, interaction: discord.Interaction, current: str) -> List[app_commands.Choice[str]]:
        return await common_pool_type_autocomplete(interaction, current, show_individual_pools_only=True)

async def setup(bot: BotType):
    """載入 EconomyCog"""
    await bot.add_cog(EconomyCog(bot))
    logger.info('EconomyCog has been added to the bot.')