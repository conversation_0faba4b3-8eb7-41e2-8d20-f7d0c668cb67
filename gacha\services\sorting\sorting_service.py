import asyncpg
from typing import Dict, List, Optional, Any, Union, Tuple
from utils.logger import logger
from database.postgresql.async_manager import AsyncPgManager
from gacha.repositories.collection.user_collection_repository import UserCollectionRepository
from gacha.services.sorting.sorting_index_manager import SortingIndexManager
from gacha.services.core.collection_service import CollectionService
from gacha.exceptions import (
    CardNotMarkedAsFavoriteError, 
    InvalidPositionError, 
    CardAlreadyAtPositionError,
    CardAlreadyAtTopError,
    CardAlreadyAtBottomError,
    CardNotSortedError,
    PreviousCardNotFoundError,
    NextCardNotFoundError,
    NoCardUpdatesProvidedError,
    NoCardIdsProvidedError,
    GachaSystemError
)

class SortingService:
    """排序服務類 (Asyncpg 版本)，處理卡片自定義排序邏輯"""
    PAGE_SIZE = CollectionService.CARDS_PER_PAGE

    def __init__(self, pool: asyncpg.Pool, collection_repo: UserCollectionRepository):
        """初始化排序服務 (Asyncpg 版本)"""
        if pool is None:
            try:
                self.pool = AsyncPgManager.get_pool()
            except RuntimeError as e:
                logger.error('無法初始化 SortingService：asyncpg 連接池未提供也無法從管理器獲取。')
                raise e
        else:
            self.pool = pool
        self.collection_repo = collection_repo
        self.index_manager = SortingIndexManager(self.collection_repo)

    async def move_card_to_position(self, user_id: int, card_id: int, target_position: int) -> Dict[str, Any]:
        """(Async) 卡片排序的核心方法 - 移動卡片到指定位置"""
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    card_info = await self.collection_repo.get_card_sort_info(user_id, card_id)
                    if not card_info or card_info['sort_index'] is None:
                        raise CardNotMarkedAsFavoriteError(card_id=card_id)
                    
                    total_sorted_cards = await self.collection_repo.count_sorted_cards(user_id)
                    if target_position < 1 or (total_sorted_cards > 0 and target_position > total_sorted_cards):
                        logger.warning('[GACHA][SORT] 目標位置無效: %s, 卡片總數: %s', target_position, total_sorted_cards)
                        raise InvalidPositionError(target_position=target_position, total_cards=total_sorted_cards)
                    
                    current_position = card_info['rank']
                    if current_position == target_position:
                        return {
                            'position': target_position,
                            'page': (target_position - 1) // self.PAGE_SIZE + 1
                        }
                    
                    new_index = await self.index_manager.calculate_index_for_position(user_id, card_id, current_position, target_position, connection=conn)
                    await self.collection_repo.set_custom_sort_index(user_id, card_id, new_index, connection=conn)
                    updated_card_info = await self.collection_repo.get_card_sort_info(user_id, card_id, connection=conn)
                    actual_position = updated_card_info['rank'] if updated_card_info else target_position
                    
                    if actual_position != target_position:
                        logger.warning('[GACHA][SORT] 實際位置(%s)與目標位置(%s)不一致', actual_position, target_position)
            
            new_page = (actual_position - 1) // self.PAGE_SIZE + 1
            return {
                'position': actual_position,
                'page': new_page,
                'debug_info': {
                    'target_position': target_position,
                    'actual_position': actual_position,
                    'target_page': (target_position - 1) // self.PAGE_SIZE + 1,
                    'actual_page': new_page,
                    'new_index': new_index,
                    'page_size': self.PAGE_SIZE
                }
            }
        except (CardNotMarkedAsFavoriteError, InvalidPositionError):
            # 直接向上傳播已知異常
            raise
        except Exception as e:
            logger.error('[GACHA][SORT] 移動卡片到指定位置失敗: %s', str(e), exc_info=True)
            raise GachaSystemError(f'操作失敗: {str(e)}')

    async def move_card_to_top(self, user_id: int, card_id: int) -> Dict[str, Any]:
        """(Async) 將卡片移至自定義排序的最前面（位置1）"""
        try:
            return await self.move_card_to_position(user_id, card_id, 1)
        except Exception as e:
            logger.error('[GACHA][SORT] 移動卡片到頂部失敗: %s', str(e), exc_info=True)
            # 直接向上傳播異常
            raise

    async def move_card_to_bottom(self, user_id: int, card_id: int) -> Dict[str, Any]:
        """(Async) 將卡片移至自定義排序的最後面"""
        try:
            total_cards = await self.collection_repo.count_sorted_cards(user_id)
            card_info = await self.collection_repo.get_card_sort_info(user_id, card_id)
            
            if not card_info or card_info['sort_index'] is None:
                raise CardNotMarkedAsFavoriteError(card_id=card_id)
                
            current_position = card_info['rank']
            if total_cards == 0 or (current_position > 0 and current_position == total_cards):
                target_pos = 1 if total_cards == 0 else total_cards
                page = (target_pos - 1) // self.PAGE_SIZE + 1
                if current_position == total_cards and total_cards > 0:
                    return {
                        'position': target_pos,
                        'page': page
                    }
            
            target_position = total_cards if total_cards > 0 else 1
            return await self.move_card_to_position(user_id, card_id, target_position)
        except CardNotMarkedAsFavoriteError:
            # 直接向上傳播已知異常
            raise
        except Exception as e:
            logger.error('[GACHA][SORT] 移動卡片到底部失敗: %s', str(e), exc_info=True)
            raise GachaSystemError(f'操作失敗: {str(e)}')

    async def move_card_up(self, user_id: int, card_id: int) -> Dict[str, Any]:
        """(Async) 將卡片在排序中向上移動一位 (與前一張卡交換索引)"""
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    sort_info = await self.collection_repo.get_card_sort_info(user_id, card_id, connection=conn)
                    if not sort_info or sort_info.get('sort_index') is None:
                        raise CardNotSortedError(card_id=card_id)
                    
                    current_index = sort_info['sort_index']
                    current_rank = sort_info['rank']
                    if current_rank == 1:
                        return {
                            'position': 1,
                            'page': 1
                        }
                    
                    prev_card_info = await self.collection_repo.get_prev_sorted_card(user_id, current_index, connection=conn)
                    if not prev_card_info or prev_card_info.get('sort_index') is None:
                        logger.error('[GACHA][SORT] move_card_up: 找不到有效的前一張卡片，但 rank 為 %s。 User=%s, Card=%s', current_rank, user_id, card_id)
                        raise PreviousCardNotFoundError()
                    
                    prev_card_id = prev_card_info['card_id']
                    prev_index = prev_card_info['sort_index']
                    await self.collection_repo.set_custom_sort_index(user_id, card_id, prev_index, connection=conn)
                    await self.collection_repo.set_custom_sort_index(user_id, prev_card_id, current_index, connection=conn)
                    new_rank = current_rank - 1
                    new_page = (new_rank - 1) // self.PAGE_SIZE + 1
                    
                    return {
                        'position': new_rank,
                        'page': new_page
                    }
        except (CardNotSortedError, PreviousCardNotFoundError):
            # 直接向上傳播已知異常
            raise
        except Exception as e:
            logger.error('[GACHA][SORT] 上移卡片失敗: %s', str(e), exc_info=True)
            raise GachaSystemError(f'操作失敗: {str(e)}')

    async def move_card_down(self, user_id: int, card_id: int) -> Dict[str, Any]:
        """(Async) 將卡片在排序中向下移動一位 (與後一張卡交換索引)"""
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    sort_info = await self.collection_repo.get_card_sort_info(user_id, card_id, connection=conn)
                    if not sort_info or sort_info.get('sort_index') is None:
                        raise CardNotSortedError(card_id=card_id)
                    
                    current_index = sort_info['sort_index']
                    current_rank = sort_info['rank']
                    total_sorted_cards = await self.collection_repo.count_sorted_cards(user_id, connection=conn)
                    
                    if current_rank >= total_sorted_cards:
                        return {
                            'position': total_sorted_cards,
                            'page': (total_sorted_cards - 1) // self.PAGE_SIZE + 1
                        }
                    
                    next_card_info = await self.collection_repo.get_next_sorted_card(user_id, current_index, connection=conn)
                    if not next_card_info or next_card_info.get('sort_index') is None:
                        logger.error('[GACHA][SORT] move_card_down: 找不到有效的後一張卡片，但 rank 為 %s/%s。 User=%s, Card=%s', current_rank, total_sorted_cards, user_id, card_id)
                        raise NextCardNotFoundError()
                    
                    next_card_id = next_card_info['card_id']
                    next_index = next_card_info['sort_index']
                    await self.collection_repo.set_custom_sort_index(user_id, card_id, next_index, connection=conn)
                    await self.collection_repo.set_custom_sort_index(user_id, next_card_id, current_index, connection=conn)
                    new_rank = current_rank + 1
                    new_page = (new_rank - 1) // self.PAGE_SIZE + 1
                    
                    return {
                        'position': new_rank,
                        'page': new_page
                    }
        except (CardNotSortedError, NextCardNotFoundError):
            # 直接向上傳播已知異常
            raise
        except Exception as e:
            logger.error('[GACHA][SORT] 下移卡片失敗: %s', str(e), exc_info=True)
            raise GachaSystemError(f'操作失敗: {str(e)}')

    async def update_card_custom_sort_index(self, user_id: int, card_id: int, custom_sort_index: int) -> Dict[str, Any]:
        """(Async) 更新指定卡片的自定義排序索引"""
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    await self.collection_repo.get_user_card(user_id, card_id, connection=conn)
                    await self.collection_repo.set_custom_sort_index(user_id, card_id, custom_sort_index, connection=conn)
                    updated_card_info = await self.collection_repo.get_card_sort_info(user_id, card_id, connection=conn)
                    new_position = updated_card_info['rank'] if updated_card_info and updated_card_info['sort_index'] is not None else 0
            
            new_page = (new_position - 1) // self.PAGE_SIZE + 1 if new_position > 0 else 1
            return {
                'position': new_position,
                'page': new_page,
                'custom_sort_index': custom_sort_index
            }
        except Exception as e:
            logger.error('[GACHA][SORT] 更新卡片排序索引時發生未知錯誤: %s', str(e), exc_info=True)
            raise GachaSystemError(f'操作失敗: {str(e)}')

    async def bulk_update_sort_indexes(self, user_id: int, card_updates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """(Async) 批量更新多張卡片的排序索引"""
        try:
            if not card_updates:
                raise NoCardUpdatesProvidedError()
            
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    updates_for_repo: List[Tuple[int, Optional[int]]] = []
                    for update_item in card_updates:
                        card_id = update_item.get('card_id')
                        sort_index = update_item.get('sort_index')
                        if card_id is None:
                            raise ValueError('批量更新中缺少 card_id')
                        updates_for_repo.append((card_id, sort_index))
                    await self.collection_repo.bulk_update_sort_indexes(user_id, updates_for_repo, connection=conn)
            
            return {
                'updated_count': len(card_updates)
            }
        except NoCardUpdatesProvidedError:
            # 直接向上傳播已知異常
            raise
        except ValueError as ve:
            logger.error('[GACHA][SORT] 批量更新排序索引時參數錯誤: %s', str(ve), exc_info=True)
            raise GachaSystemError(f'參數錯誤: {str(ve)}')
        except Exception as e:
            logger.error('[GACHA][SORT] 批量更新排序索引失敗: %s', str(e), exc_info=True)
            raise GachaSystemError(f'操作失敗: {str(e)}')

    async def reorder_cards(self, user_id: int, card_ids: List[int]) -> Dict[str, Any]:
        """(Async) 根據提供的卡片ID列表重新排序所有卡片"""
        try:
            if not card_ids:
                raise NoCardIdsProvidedError()
            
            base_index = 1000
            step = 1000
            card_updates = [{'card_id': card_id, 'sort_index': base_index + pos * step} for pos, card_id in enumerate(card_ids, start=1)]
            await self.bulk_update_sort_indexes(user_id, card_updates)
            
            return {
                'reordered_count': len(card_ids)
            }
        except NoCardIdsProvidedError:
            # 直接向上傳播已知異常
            raise
        except Exception as e:
            logger.error('[GACHA][SORT] 重新排序卡片失敗: %s', str(e), exc_info=True)
            raise GachaSystemError(f'操作失敗: {str(e)}')

    async def reset_card_sort_index(self, user_id: int, card_id: int) -> Dict[str, Any]:
        """(Async) 重置卡片的排序索引 (設為 NULL，相當於從排序列表中移除)"""
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    await self.collection_repo.set_custom_sort_index(user_id, card_id, None, connection=conn)
            
            return {
                'card_id': card_id,
                'position': 0,
                'page': 1
            }
        except Exception as e:
            logger.error('[GACHA][SORT] 重置卡片排序索引失敗: %s', str(e), exc_info=True)
            raise GachaSystemError(f'操作失敗: {str(e)}')

    async def get_current_card_position(self, user_id: int, card_id: int) -> int:
        """(Async) 獲取卡片當前的位置"""
        try:
            card_info = await self.collection_repo.get_card_sort_info(user_id, card_id)
            return card_info['rank'] if card_info and card_info['sort_index'] is not None else 0
        except Exception as e:
            logger.error('[GACHA][SORT] 獲取卡片位置失敗: %s', str(e), exc_info=True)
            return 0