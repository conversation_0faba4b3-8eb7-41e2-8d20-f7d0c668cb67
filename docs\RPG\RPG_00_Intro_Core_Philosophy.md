## Gacha RPG系統設計方案 V7 (超詳細完整版 + 架構方案)

**核心設計哲學：**

*   **數據驅動與配置化：** 遊戲的核心靜態定義（卡牌RPG屬性、技能、怪物、關卡、傷害修正等）完全由外部配置文件 (JSON) 驅動。AI輔助生成和維護這些配置文件。
*   **JSON驅動的動態行為：** 尤其是傷害計算和效果修正，通過JSON配置的參數和規則，由通用的代碼邏輯動態執行，最大限度減少硬編碼。
*   **數據庫職責：** 存儲玩家的動態進度數據、核心ID關聯，以及極少量必要的元數據。
*   **運行時性能：** 服務器啟動時將所有配置文件加載到內存，以保證運行時的高效訪問。
*   **全局共享技能等級：** 通用主動技能和通用被動技能的等級是玩家賬號全局共享的。
*   **卡牌獨特性：** 通過基礎屬性、成長、被動槽數量、以及與升星綁定的**天賦被動技能**來區分卡牌。
*   **簡化戰鬥操作：** 戰鬥全自動，玩家策略體現在戰前卡牌養成、技能配置和出戰順序。
*   **架構清晰與擴展性：** 採用分層架構，領域層的戰鬥系統設計力求職責單一、高內聚、低耦合，易於擴展和維護。 