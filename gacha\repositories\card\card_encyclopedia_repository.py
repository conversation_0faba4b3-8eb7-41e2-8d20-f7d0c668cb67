# gacha/repositories/card_encyclopedia_repository.py (Refactored for Asyncpg)

import asyncpg
from datetime import datetime
from typing import Dict, List, Optional, Any, Union

from database.base_repository import BaseRepository  # Corrected import path
from gacha.models.models import Card
from utils.logger import logger  # Use new logger path
from gacha.exceptions import (
    RecordNotFoundError,
    DatabaseOperationError,
)  # Added DatabaseOperationError for potential direct use


class CardEncyclopediaRepository(BaseRepository):
    """卡片圖鑑存儲庫 (Asyncpg 版本)，提供卡片圖鑑相關查詢功能"""

    def __init__(self, pool: asyncpg.Pool):  # Accept pool directly
        """初始化卡片圖鑑存儲庫 (Asyncpg 版本)

        參數:
            pool: asyncpg 連接池實例
        """
        super().__init__(pool)  # Pass pool to async BaseRepository
        self.master_card_table = "gacha_master_cards"
        self.highest_star_table = "gacha_card_highest_star"
        self.card_descriptions_table = "gacha_card_descriptions"
        self.user_table = "gacha_users"

    async def update_highest_star(
        self,
        card_id: int,
        user_id: int,
        star_level: int,
        connection: Optional[asyncpg.Connection] = None,
    ) -> None:
        """(Async) 更新或插入卡片最高星級記錄。
        如果提供了 connection，則在該連接上執行，否則創建新事務。
        
        Raises:
            DatabaseOperationError: 當數據庫操作失敗時
        """

        async def _update_logic(conn: asyncpg.Connection) -> None:
            try:
                # 在事務中檢查當前記錄並鎖定
                check_query = f"SELECT user_id, star_level FROM {self.highest_star_table} WHERE card_id = $1 FOR UPDATE"
                current_record = await conn.fetchrow(check_query, card_id)
                now = datetime.now()

                if current_record:
                    current_star = current_record.get("star_level", 0)
                    if star_level > current_star:
                        update_query = f"""
                            UPDATE {self.highest_star_table}
                            SET user_id = $1, star_level = $2, achieved_at = $3
                            WHERE card_id = $4
                        """
                        await conn.execute(update_query, user_id, star_level, now, card_id)
                    # 如果星級不夠高，不更新，但也不视为错误
                else:
                    # 記錄不存在，創建新記錄
                    insert_query = f"""
                        INSERT INTO {self.highest_star_table} (card_id, user_id, star_level, achieved_at)
                        VALUES ($1, $2, $3, $4)
                    """
                    await conn.execute(insert_query, card_id, user_id, star_level, now)
                    
            except asyncpg.PostgresError as e:
                logger.error(f"Database error in update_highest_star for card_id {card_id}, user_id {user_id}: {str(e)}", exc_info=True)
                raise DatabaseOperationError(f"更新卡片 {card_id} 最高星級失敗: {str(e)}")
            except Exception as e:
                logger.error(f"Unexpected error in update_highest_star for card_id {card_id}, user_id {user_id}: {str(e)}", exc_info=True)
                raise DatabaseOperationError(f"更新卡片 {card_id} 最高星級時發生未知錯誤: {str(e)}")

        if connection:
            # 使用傳入的連接，不創建內部事務
            await _update_logic(connection)
        else:
            # 沒有傳入連接，創建內部事務
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    await _update_logic(conn)

    async def update_card_description(
        self, user_id: int, card_id: int, description: str
    ) -> None:
        """(Async) 更新或插入卡片自訂描述 (使用 UPSERT 邏輯)
        
        Raises:
            DatabaseOperationError: If the database operation fails.
        """
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    now = datetime.now()
                    upsert_query = f"""
                        INSERT INTO {self.card_descriptions_table} (card_id, user_id, description, created_at, updated_at)
                        VALUES ($1, $2, $3, $4, $5)
                        ON CONFLICT (card_id, user_id) DO UPDATE SET
                            description = EXCLUDED.description,
                            updated_at = EXCLUDED.updated_at;
                    """
                    await conn.execute(
                        upsert_query, card_id, user_id, description, now, now
                    )
            # 纯异常模式：成功时不返回任何值
        except asyncpg.PostgresError as e:
            logger.error(f"Database error in update_card_description for card_id {card_id}, user_id {user_id}: {str(e)}", exc_info=True)
            raise DatabaseOperationError(f"更新卡片 {card_id} 描述失敗: {str(e)}")
        except Exception as e: # Catch any other unexpected errors
            logger.error(f"Unexpected error in update_card_description for card_id {card_id}, user_id {user_id}: {str(e)}", exc_info=True)
            raise DatabaseOperationError(f"更新卡片 {card_id} 描述時發生未知錯誤: {str(e)}")

    async def get_card_encyclopedia_data(
        self, card_id: int
    ) -> Optional[Dict[str, Any]]:
        """(Async) 獲取卡片的完整圖鑑信息"""
        query = f"""
            SELECT
                mc.card_id, mc.name, mc.series, mc.rarity, mc.image_url,
                mc.description as card_description, mc.current_market_sell_price, mc.creation_date,
                mc.pool_type, mc.is_active, mc.original_id,
                hs.user_id as highest_star_user_id, hs.star_level as highest_star_level,
                hs.achieved_at,
                gu.user_id as user_id, gu.nickname,
                cd.description as custom_description, cd.user_id as description_user_id
            FROM {self.master_card_table} mc
            LEFT JOIN {self.highest_star_table} hs ON mc.card_id = hs.card_id
            LEFT JOIN {self.user_table} gu ON hs.user_id = gu.user_id
            LEFT JOIN {self.card_descriptions_table} cd ON mc.card_id = cd.card_id AND cd.user_id = hs.user_id
            WHERE mc.card_id = $1
        """
        try:
            result = await self._fetchrow(query, (card_id,))
            if not result:
                # Consistent with RecordNotFoundError from other repo methods
                raise RecordNotFoundError(f"找不到 card_id 為 {card_id} 的圖鑑數據") 
            return dict(result)
        except asyncpg.PostgresError as e:
            logger.error(f"Database error in get_card_encyclopedia_data for card_id {card_id}: {str(e)}", exc_info=True)
            raise DatabaseOperationError(f"獲取卡片 {card_id} 圖鑑數據失敗: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error in get_card_encyclopedia_data for card_id {card_id}: {str(e)}", exc_info=True)
            raise DatabaseOperationError(f"獲取卡片 {card_id} 圖鑑數據時發生未知錯誤: {str(e)}")

    async def get_paginated_card_id(
        self,
        page: int = 1,
        sort_by: str = "rarity",
        sort_order: str = "desc",
        pool_type: Optional[str] = None,
        rarity: Optional[Union[int, List[int]]] = None,
        series_name: Optional[str] = None,
        card_id: Optional[int] = None, # Specific card_id to find its page
        card_name: Optional[str] = None,
    ) -> Dict[str, Any]: # Returns dict with card_id, current_page, total_pages, total_cards
        """(Async) 獲取分頁的圖鑑卡片ID，或特定卡片ID所在的頁面信息
        
        Raises:
            RecordNotFoundError: If no card matches the criteria or if the specified card_id is not found.
            DatabaseOperationError: If any database operation fails.
        """
        
        if page < 1:
            page = 1 # Or raise ValueError("頁碼不能小於1")
        
        conditions = ["is_active = TRUE"]
        params = []
        param_idx = 1
        
        # Build filter conditions for counting and general pagination
        filter_conditions = []
        filter_params = []
        filter_param_idx = 1

        if pool_type:
            filter_conditions.append(f"pool_type = ${filter_param_idx}")
            filter_params.append(pool_type)
            filter_param_idx += 1
            
        if rarity is not None:
            if isinstance(rarity, list):
                if rarity: # Ensure list is not empty
                    filter_conditions.append(f"rarity = ANY(${filter_param_idx}::integer[])")
                    filter_params.append(rarity)
                    filter_param_idx += 1
            else:
                filter_conditions.append(f"rarity = ${filter_param_idx}")
                filter_params.append(rarity)
                filter_param_idx += 1
            
        if series_name:
            filter_conditions.append(f"series = ${filter_param_idx}")
            filter_params.append(series_name)
            filter_param_idx += 1
        
        # Card name filter applies only if not searching for a specific card_id page
        if card_name and card_id is None: 
            filter_conditions.append(f"name ILIKE ${filter_param_idx}")
            filter_params.append(f"%{card_name}%")
            filter_param_idx += 1

        # Always add is_active = TRUE to filter_conditions as well
        active_filter = "is_active = TRUE"
        if active_filter not in filter_conditions:
             filter_conditions.insert(0, active_filter)

        where_clause_for_filters = " AND ".join(filter_conditions) if filter_conditions else "is_active = TRUE" # ensure is_active if no other filters

        sort_map = {
            "rarity": "rarity", "name": "name", "creation_date": "creation_date",
            "star": "rarity" # Fallback for star sorting
        }
        sort_column = sort_map.get(sort_by, "rarity")
        order = "DESC" if sort_order.lower() == "desc" else "ASC"
        
        # Base order by clause, will be prepended by star sort if needed
        base_order_by_clause = f"{sort_column} {order}, mc.card_id ASC"

        join_clause = ""
        star_sort_column = ""

        if sort_by == "star":
            join_clause = f"LEFT JOIN {self.highest_star_table} hs ON mc.card_id = hs.card_id"
            # For star sorting, handle NULLs (e.g., cards with no stars yet)
            # Sort NULLs last for DESC (higher stars first), NULLs first for ASC (lower stars first)
            if order == "DESC":
                star_sort_column = "hs.star_level DESC NULLS LAST"
            else: # ASC
                star_sort_column = "hs.star_level ASC NULLS FIRST"
            # Primary sort by star, then by the original sort_column as secondary, then by card_id
            order_by_clause = f"ORDER BY {star_sort_column}, mc.{sort_column} {order}, mc.card_id ASC"
        else:
            order_by_clause = f"ORDER BY mc.{sort_column} {order}, mc.card_id ASC"

        try:
            # Get total count based on filters
            # The join for star sorting should not affect the total count of master cards
            count_query = f"SELECT COUNT(DISTINCT mc.card_id) FROM {self.master_card_table} mc WHERE {where_clause_for_filters}"
            total_cards = await self._fetchval(count_query, filter_params)

            if total_cards == 0:
                raise RecordNotFoundError("在指定的篩選條件下找不到任何卡片。")

            total_pages = total_cards # Since it's one card per page in encyclopedia

            # If a specific card_id is provided, we need to find its rank/page
            if card_id is not None:
                # CTE to rank cards based on the provided sorting and filters
                # The filters in where_clause_for_filters should apply to the set of cards being ranked
                rank_query = f"""
                    WITH RankedCards AS (
                        SELECT mc.card_id, ROW_NUMBER() OVER ({order_by_clause}) as rn
                        FROM {self.master_card_table} mc
                        {join_clause}  -- Add join for star sorting if active
                        WHERE {where_clause_for_filters} 
                    )
                    SELECT rn FROM RankedCards WHERE card_id = ${filter_param_idx};
                """
                current_params_for_rank = filter_params + [card_id]
                found_rank = await self._fetchval(rank_query, current_params_for_rank)
                
                if found_rank is None:
                    raise RecordNotFoundError(f"找不到 ID 為 {card_id} 的卡片，或該卡片不符合目前的篩選條件。")
                
                current_page = int(found_rank)
                # The card_id for this page is the one we searched for
                card_id_for_page = card_id 
            else:
                # Regular pagination: find card_id for the given page number
                current_page = max(1, min(page, total_pages)) # Adjust page to be within valid range
                offset = current_page - 1
                
                pagination_query = f"""
                    SELECT mc.card_id FROM {self.master_card_table} mc
                    {join_clause}  -- Add join for star sorting if active
                    WHERE {where_clause_for_filters}
                    {order_by_clause}
                    LIMIT 1 OFFSET ${filter_param_idx};
                """
                current_params_for_pagination = filter_params + [offset]
                card_id_for_page = await self._fetchval(pagination_query, current_params_for_pagination)
                
                if card_id_for_page is None:
                    # This should ideally not happen if total_cards > 0 and page is adjusted
                    # but as a safeguard:
                    raise RecordNotFoundError(f"在頁碼 {current_page} (總頁數: {total_pages}) 找不到卡片。")

            return {
                "card_id": card_id_for_page,
                "current_page": current_page,
                "total_pages": total_pages,
                "total_cards": total_cards
            }

        except asyncpg.PostgresError as e:
            logger.error(f"Database error in get_paginated_card_id: {str(e)}", exc_info=True)
            raise DatabaseOperationError(f"獲取分頁卡片ID失敗: {str(e)}")
        except RecordNotFoundError: # Re-raise specific RecordNotFoundError
            raise
        except Exception as e: # Catch any other unexpected errors
            logger.error(f"Unexpected error in get_paginated_card_id: {str(e)}", exc_info=True)
            raise DatabaseOperationError(f"獲取分頁卡片ID時發生未知錯誤: {str(e)}")

    async def get_filtered_cards(
        self,
        pool_type: Optional[str] = None,
        rarity: Optional[Union[int, List[int]]] = None,
        series_name: Optional[str] = None,
        card_name: Optional[str] = None,
        limit: int = 20,
        offset: int = 0,
        sort_by: str = "rarity",
        sort_order: str = "desc",
    ) -> List[Dict[str, Any]]:
        """(Async) 獲取經過篩選的卡片列表"""
        
        # Create filter conditions
        conditions = ["is_active = TRUE"]
        params = []
        param_counter = 1
        
        if pool_type:
            conditions.append(f"pool_type = ${param_counter}")
            params.append(pool_type)
            param_counter += 1
            
        if rarity is not None:
            if isinstance(rarity, list):
                conditions.append(f"rarity = ANY(${param_counter}::integer[])")
                params.append(rarity)
            else:
                conditions.append(f"rarity = ${param_counter}")
                params.append(rarity)
            param_counter += 1
            
        if series_name:
            conditions.append(f"series = ${param_counter}")
            params.append(series_name)
            param_counter += 1
            
        if card_name:
            conditions.append(f"name ILIKE ${param_counter}")
            params.append(f"%{card_name}%")
            param_counter += 1
        
        where_clause = " AND ".join(conditions)
        
        # Determine sorting
        sort_map = {
            "rarity": "rarity",
            "name": "name", 
            "creation_date": "creation_date",
            "star": "rarity"  # Fallback to rarity
        }
        sort_column = sort_map.get(sort_by, "rarity")
        order = "DESC" if sort_order.lower() == "desc" else "ASC"
        
        # Add pagination parameters
        params.extend([limit, offset])
        
        query = f"""
            SELECT card_id, name, series, rarity, image_url, description,
                   creation_date, pool_type, current_market_sell_price
            FROM {self.master_card_table}
            WHERE {where_clause}
            ORDER BY {sort_column} {order}, card_id ASC
            LIMIT ${param_counter} OFFSET ${param_counter + 1}
        """
        
        results = await self._fetch(query, params)
        return [dict(row) for row in results] if results else []

    async def get_card_count_by_filters(
        self,
        pool_type: Optional[str] = None,
        rarity: Optional[Union[int, List[int]]] = None,
        series_name: Optional[str] = None,
        card_name: Optional[str] = None,
    ) -> int:
        """(Async) 獲取符合篩選條件的卡片總數"""
        
        # Create filter conditions
        conditions = ["is_active = TRUE"]
        params = []
        param_counter = 1
        
        if pool_type:
            conditions.append(f"pool_type = ${param_counter}")
            params.append(pool_type)
            param_counter += 1
            
        if rarity is not None:
            if isinstance(rarity, list):
                conditions.append(f"rarity = ANY(${param_counter}::integer[])")
                params.append(rarity)
            else:
                conditions.append(f"rarity = ${param_counter}")
                params.append(rarity)
            param_counter += 1
            
        if series_name:
            conditions.append(f"series = ${param_counter}")
            params.append(series_name)
            param_counter += 1
            
        if card_name:
            conditions.append(f"name ILIKE ${param_counter}")
            params.append(f"%{card_name}%")
            param_counter += 1
        
        where_clause = " AND ".join(conditions)
        
        query = f"SELECT COUNT(*) FROM {self.master_card_table} WHERE {where_clause}"
        count = await self._fetchval(query, params)
        return count or 0

    async def get_all_series(self) -> List[str]:
        """(Async) 獲取所有卡片系列"""
        query = f"SELECT DISTINCT series FROM {self.master_card_table} WHERE is_active = TRUE ORDER BY series"
        results = await self._fetch(query)
        return [row["series"] for row in results] if results else []

    async def get_total_card_count(self) -> int:
        """(Async) 獲取總卡片數量"""
        query = f"SELECT COUNT(*)::integer as total FROM {self.master_card_table} WHERE is_active = TRUE"
        count = await self._fetchval(query)
        return count or 0

    async def search_cards_by_name(self, name_query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """(Async) 根據名稱搜索卡片"""
        query = f"""
            SELECT card_id, name, series, rarity, pool_type
            FROM {self.master_card_table}
            WHERE is_active = TRUE AND name ILIKE $1
            ORDER BY name
            LIMIT $2
        """
        params = (f"%{name_query}%", limit)
        results = await self._fetch(query, params)
        return [dict(row) for row in results] if results else []
