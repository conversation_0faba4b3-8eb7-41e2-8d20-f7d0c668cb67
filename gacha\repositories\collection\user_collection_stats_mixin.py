"""
Gacha系統用戶收藏存儲庫 - 統計功能 Mixin (Asyncpg 版本)
"""
import asyncpg
from typing import Dict, List, Optional, Any
from gacha.models.models import SeriesCollection
from utils.logger import logger
from gacha.models.filters import CollectionFilters
from gacha.exceptions import DatabaseOperationError, RepositoryError

class UserCollectionStatsMixin:
    """提供用戶收藏統計相關方法的 Mixin (Asyncpg 版本)"""

    async def get_user_collection_stats(self, user_id: int, filters: Optional[CollectionFilters]=None, *, where_clause: Optional[str]=None, params: Optional[List[Any]]=None) -> Dict[str, int]:
        """(Async) 獲取用戶收藏統計信息 (優化為單一查詢)"""
        where_clause_internal: str
        params_internal: List[Any]
        if where_clause is not None and params is not None:
            where_clause_internal = where_clause
            params_internal = list(params)
        else:
            if filters is None:
                filters = CollectionFilters()
            w_clause, p_list = self._build_filter_conditions(user_id, filters)
            where_clause_internal = w_clause
            params_internal = p_list
        combined_query = f'\n            SELECT\n                COUNT(DISTINCT uc.card_id)::integer as unique_cards,\n                COALESCE(SUM(uc.quantity), 0)::integer as total_quantity\n            FROM {self.table_name} uc\n            JOIN gacha_master_cards mc ON uc.card_id = mc.card_id\n            WHERE {where_clause_internal}\n        '
        result = await self._fetchrow(combined_query, params_internal)
        if result:
            unique_cards = result.get('unique_cards', 0)
            total_quantity = result.get('total_quantity', 0)
        else:
            unique_cards = 0
            total_quantity = 0
            logger.warning('[GACHA] get_user_collection_stats: 查詢未返回結果 for user_id=%s, filters=%s', user_id, filters)
        return {'unique_cards': unique_cards, 'total_quantity': total_quantity}

    async def _get_all_series(self, pool_type: Optional[str]=None) -> List[str]:
        """(Async) 获取所有系列"""
        params = []
        pool_type_condition = ''
        param_index = 1
        if pool_type:
            pool_type_condition = f'AND pool_type = ${param_index}'
            params.append(pool_type)
            param_index += 1
        series_query = f'\n            SELECT DISTINCT series\n            FROM gacha_master_cards\n            WHERE is_active = TRUE {pool_type_condition}\n            ORDER BY series\n        '
        series_results = await self._fetch(series_query, params)
        return [result['series'] for result in series_results] if series_results else []

    async def get_user_all_series_collection(self, user_id: int) -> List[SeriesCollection]:
        """(Async) 获取用户收集的所有系列信息"""
        series_list = await self._get_all_series()
        collections_dict = await self.get_user_series_collections_batch(user_id, series_list)
        result = []
        for series in series_list:
            collection = collections_dict.get(series)
            if collection:
                result.append(collection)
        return result

    async def get_user_series_collections_batch(self, user_id: int, series_list: List[str], pool_type: Optional[str]=None) -> Dict[str, SeriesCollection]:
        """(Async) 批量获取用户对多个系列的收集信息"""
        if not series_list:
            return {}
        params_total = []
        params_collected = [user_id]
        param_index_total = 1
        param_index_collected = 1
        series_placeholder_total = f'${param_index_total}'
        params_total.append(series_list)
        param_index_total += 1
        series_placeholder_collected = f'${param_index_collected + 1}'
        params_collected.append(series_list)
        param_index_collected += 1
        pool_type_condition_total = ''
        pool_type_condition_collected = ''
        if pool_type:
            pool_type_placeholder_total = f'${param_index_total}'
            pool_type_condition_total = f'AND pool_type = {pool_type_placeholder_total}'
            params_total.append(pool_type)
            param_index_total += 1
            pool_type_placeholder_collected = f'${param_index_collected + 1}'
            pool_type_condition_collected = f'AND mc.pool_type = {pool_type_placeholder_collected}'
            params_collected.append(pool_type)
            param_index_collected += 1
        total_counts_query = f'\n            SELECT series, COUNT(DISTINCT card_id)::integer as count\n            FROM gacha_master_cards\n            WHERE series = ANY({series_placeholder_total}::text[]) AND is_active = TRUE {pool_type_condition_total}\n            GROUP BY series\n        '
        total_counts_result = await self._fetch(total_counts_query, params_total)
        total_counts = {row['series']: row['count'] for row in total_counts_result}
        collected_counts_query = f'\n            SELECT mc.series, COUNT(DISTINCT uc.card_id)::integer as count\n            FROM {self.table_name} uc\n            JOIN gacha_master_cards mc ON uc.card_id = mc.card_id\n            WHERE uc.user_id = $1 AND mc.series = ANY({series_placeholder_collected}::text[]) AND mc.is_active = TRUE {pool_type_condition_collected}\n            GROUP BY mc.series\n        '
        collected_counts_result = await self._fetch(collected_counts_query, params_collected)
        collected_counts = {row['series']: row['count'] for row in collected_counts_result}
        result = {}
        for series in series_list:
            total_cards = total_counts.get(series, 0)
            if total_cards > 0:
                collected_cards = collected_counts.get(series, 0)
                result[series] = SeriesCollection(series=series, total_cards=total_cards, collected_cards=collected_cards)
        return result

    async def get_overall_collection_stats(self, user_id: int) -> Dict[str, Any]:
        """(Async) 獲取用戶所有系列的總體收集狀況"""
        query = f'\n            SELECT\n                (SELECT COUNT(DISTINCT card_id)::integer FROM gacha_master_cards WHERE is_active = TRUE) as total_cards,\n                COUNT(DISTINCT uc.card_id)::integer as total_collected\n            FROM {self.table_name} uc\n            JOIN gacha_master_cards mc ON uc.card_id = mc.card_id\n            WHERE uc.user_id = $1 AND mc.is_active = TRUE\n        '
        result = await self._fetchrow(query, (user_id,))
        if not result:
            return {'total_cards': 0, 'total_collected': 0}
        total_cards = result.get('total_cards', 0)
        total_collected = result.get('total_collected', 0)
        return {'total_collected': total_collected, 'total_cards': total_cards}

    async def get_pool_specific_collection_stats(self, user_id: int, pool_type: str) -> Dict[str, Any]:
        """(Async) 獲取用戶在特定卡池的收集統計"""
        query = f'\n            SELECT\n                (SELECT COUNT(DISTINCT card_id)::integer FROM gacha_master_cards WHERE pool_type = $1 AND is_active = TRUE) as total_cards_in_pool,\n                COUNT(DISTINCT uc.card_id)::integer as collected_in_pool\n            FROM {self.table_name} uc\n            JOIN gacha_master_cards mc ON uc.card_id = mc.card_id\n            WHERE uc.user_id = $2 AND mc.pool_type = $1 AND mc.is_active = TRUE\n        '
        result = await self._fetchrow(query, (pool_type, user_id))
        if not result:
            return {'total_cards_in_pool': 0, 'collected_in_pool': 0}
        total_cards_in_pool = result.get('total_cards_in_pool', 0)
        collected_in_pool = result.get('collected_in_pool', 0)
        return {'total_cards_in_pool': total_cards_in_pool, 'collected_in_pool': collected_in_pool}

    async def get_user_cards_summary(self, user_id: int, pool_type: Optional[str]=None) -> List[Dict[str, Any]]:
        """(Async) 獲取用戶卡片摘要 (卡池, 稀有度, 唯一數量, 總數量)"""
        params: List[Any] = [user_id]
        pool_type_condition = ''
        param_idx = 1
        if pool_type:
            param_idx += 1
            pool_type_condition = f'AND mc.pool_type = ${param_idx}'
            params.append(pool_type)
        query = f'\n            SELECT\n                mc.pool_type,\n                mc.rarity,\n                COUNT(DISTINCT uc.card_id)::integer as count,\n                SUM(uc.quantity)::integer as total_quantity\n            FROM {self.table_name} uc\n            JOIN gacha_master_cards mc ON uc.card_id = mc.card_id\n            WHERE uc.user_id = $1 {pool_type_condition} AND mc.is_active = TRUE\n            GROUP BY mc.pool_type, mc.rarity\n            ORDER BY mc.pool_type, mc.rarity\n        '
        results = await self._fetch(query, params)
        return [dict(row) for row in results]

    async def get_rarities_total_count(self, pool_type_filter: Optional[str]=None) -> Dict[str, Dict[int, int]]:
        """(Async) 獲取各卡池各稀有度卡片的總數"""
        params: List[Any] = []
        pool_filter_condition_sql = ''
        if pool_type_filter:
            params.append(pool_type_filter)
            pool_filter_condition_sql = 'WHERE pool_type = $1 AND is_active = TRUE'
        else:
            pool_filter_condition_sql = 'WHERE is_active = TRUE'
        query = f'\n            SELECT\n                pool_type,\n                rarity,\n                COUNT(card_id)::integer as count\n            FROM gacha_master_cards\n            {pool_filter_condition_sql}\n            GROUP BY pool_type, rarity\n            ORDER BY pool_type, rarity\n        '
        results = await self._fetch(query, params)
        output: Dict[str, Dict[int, int]] = {}
        if results:
            for row in results:
                pool = row['pool_type']
                rarity_value = row['rarity']
                count = row['count']
                pool_data = output.setdefault(pool, {})
                pool_data[rarity_value] = count
        return output