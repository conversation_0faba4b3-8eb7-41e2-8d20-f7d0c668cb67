"""
Gacha系統抽卡Embed構建器
"""
from typing import Any, Dict, Optional
from decimal import Decimal
import discord
from utils.logger import logger
from gacha.models.models import Card
from gacha.views.utils import format_oil
from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder
from gacha.views import utils as view_utils
from gacha.constants import RarityLevel
from gacha.app_config import config_service

class DrawEmbedBuilder(BaseEmbedBuilder):
    """構建抽卡結果嵌入消息的類"""

    def __init__(self, user: discord.User, card: Card, balance: int, is_new_card: bool=False, pool_type: Optional[str]=None, nickname: Optional[str]=None, is_wish: bool=False, star_level: int=0, is_favorite: bool=False, owner_count: int=0, interaction: Optional[discord.Interaction]=None):
        """初始化抽卡Embed構建器

        參數:
            user: Discord用戶對象
            card: 抽到的卡片 (應包含 rarity)
            balance: 用戶剩餘油幣
            is_new_card: 是否為新卡片，默認為False
            pool_type: 抽取的卡池類型，默認為None
            nickname: 用戶暱稱，如果有的話優先使用
            is_wish: 是否為許願卡片，默認為False
            star_level: 卡片星級，默認為0
            is_favorite: 是否為最愛卡片，默認為False
            owner_count: 該卡片的擁有者數量
            interaction: 可選的 Discord interaction
            # dynamic_price_details: REMOVED - 價格將從 card.current_market_sell_price 獲取
        """
        draw_data = {'user': user, 'card': card, 'balance': balance, 'is_new_card': is_new_card, 'pool_type': pool_type, 'nickname': nickname, 'is_wish': is_wish, 'star_level': star_level, 'is_favorite': is_favorite, 'owner_count': owner_count}
        super().__init__(data=draw_data, interaction=interaction)
        self.user = user
        self.card = card
        self.balance = balance
        self.is_new_card = is_new_card
        self.actual_pool_type = pool_type if pool_type else self.card.pool_type
        self.nickname = nickname
        self.is_wish = is_wish
        self.star_level = star_level
        self.is_favorite = is_favorite
        self.owner_count = owner_count

    def build_embed(self) -> discord.Embed:
        """構建抽卡結果的Embed顯示

        返回:
            格式化後的Discord Embed對象
        """
        card_rarity_enum = None
        try:
            if isinstance(self.card.rarity, int):
                card_rarity_enum = RarityLevel(self.card.rarity)
            elif isinstance(self.card.rarity, RarityLevel):
                card_rarity_enum = self.card.rarity
        except ValueError:
            logger.warning('無法將卡片稀有度 %s 解析為 RarityLevel。', self.card.rarity)
        embed_color = self._get_rarity_color(rarity_level=card_rarity_enum, pool_type=self.actual_pool_type)
        embed = self._create_base_embed(color=embed_color)
        pool_prefix_for_author = config_service.get_config('gacha_core_settings.pool_type_prefixes').get(self.actual_pool_type, self.actual_pool_type)
        self._set_draw_author(embed=embed, user=self.user, nickname=self.nickname, is_multi_draw=False, single_draw_is_wish=self.is_wish, single_draw_pool_prefix=pool_prefix_for_author, single_draw_is_new_card=self.is_new_card)
        use_as_description = self.is_new_card or self.star_level == 0
        field_name_for_details = None
        if not use_as_description:
            field_name_for_details = view_utils.get_star_emoji_string(self.star_level)
            if not field_name_for_details:
                field_name_for_details = '卡片資訊'
        self._add_card_primary_details_field_or_description(embed=embed, card=self.card, is_favorite=self.is_favorite, is_new_card=self.is_new_card, is_wish=self.is_wish, star_level=self.star_level, owner_count=self.owner_count, as_description=use_as_description, field_name=field_name_for_details, inline=False)
        self._add_market_price_and_balance_field(embed=embed, current_market_sell_price=self.card.current_market_sell_price, card_rarity_for_fallback=card_rarity_enum, pool_type_for_fallback=self.actual_pool_type, balance=self.balance, field_name='', inline=False)
        self._set_card_visuals(embed=embed, card_image_url=self.card.image_url, card_rarity_enum=card_rarity_enum, pool_type=self.actual_pool_type)
        self._set_draw_specific_footer(embed=embed, card_for_id=self.card, is_wish=self.is_wish, include_bot_signature=True)
        return embed