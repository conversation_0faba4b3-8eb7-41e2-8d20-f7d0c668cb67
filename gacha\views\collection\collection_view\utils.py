"""
Gacha系統收藏視圖實用工具
提供用於格式化和顯示收集進度的通用函數
"""

from typing import Callable, TYPE_CHECKING, Optional, NamedTuple
import discord

# get_completion_indicator function has been moved to gacha/views/utils.py

# --- 導入 ConfirmationFactory ---
from gacha.views.ui_components.confirmation import ConfirmationFactory  # 標準化確認視圖

if TYPE_CHECKING:
    from gacha.models.models import UserCard
    from .card_view import CollectionView


class CardStateInfo(NamedTuple):
    """卡片狀態信息，提供統一的狀態訪問接口"""

    card: Optional["UserCard"]
    is_available: bool
    is_favorite: bool
    card_id: Optional[int]
    star_level: int
    quantity: int
    has_duplicates: bool


def get_card_state(view: "CollectionView") -> CardStateInfo:
    """獲取當前卡片的統一狀態信息

    這是系統中檢查卡片狀態的標準方法，避免在多處重複相同的檢查邏輯。

    參數:
        view: CollectionView 實例

    返回:
        CardStateInfo: 包含所有卡片狀態信息的命名元組
    """
    current_card = view.current_card

    if not current_card:
        return CardStateInfo(
            card=None,
            is_available=False,
            is_favorite=False,
            card_id=None,
            star_level=0,
            quantity=0,
            has_duplicates=False,
        )

    return CardStateInfo(
        card=current_card,
        is_available=True,
        is_favorite=getattr(current_card, "is_favorite", False),
        card_id=(
            getattr(current_card.card, "card_id", None)
            if hasattr(current_card, "card")
            else None
        ),
        star_level=getattr(current_card, "star_level", 0),
        quantity=getattr(current_card, "quantity", 0),
        has_duplicates=getattr(current_card, "quantity", 0) > 1,
    )


async def check_cards_available(
    view: "CollectionView",
    interaction: discord.Interaction,
    error_message: str = "當前頁面沒有卡片",
) -> bool:
    """統一的卡片可用性檢查

    參數:
        view: CollectionView 實例
        interaction: Discord交互對象
        error_message: 錯誤提示訊息

    返回:
        bool: 是否有可用卡片
    """
    state = get_card_state(view)

    if not state.is_available:
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(error_message, ephemeral=True)
            else:
                await interaction.followup.send(error_message, ephemeral=True)
        except Exception:
            pass
        return False

    return True


async def create_sell_confirmation(
    interaction: discord.Interaction,
    title: str,
    description: str,
    field_name: str,
    field_value: str,
    # confirm_id: str, # 不再需要
    # cancel_id: str, # 不再需要
    confirm_callback: Callable,  # 接收 (interaction, is_confirmed)
):
    """創建通用賣出確認視圖 (使用 ConfirmationFactory)

    參數:
        interaction: Discord交互對象
        title: 警告標題
        description: 警告描述
        field_name: 提示字段名
        field_value: 提示字段值
        confirm_callback: 確認按鈕回調函數 (接收 interaction, is_confirmed)
    """

    # 適配新的回調簽名
    async def adapted_callback(
        interaction_obj: discord.Interaction, is_confirmed: bool
    ):
        if is_confirmed:
            # 原 confirm_callback 只接收 interaction，這裡我們傳遞 button_interaction
            await confirm_callback(interaction_obj)
        # else: # 取消時由 ConfirmationFactory 的 on_cancel 自動處理

    await ConfirmationFactory.create_confirmation(
        interaction=interaction,
        title=title,
        description=description,
        user_id=interaction.user.id,
        callback=adapted_callback,
        field_name=field_name,
        field_value=field_value,
        confirm_label="確認賣出",  # 保持原 SellConfirmationView 的樣式
        cancel_label="取消",
        confirm_style=discord.ButtonStyle.danger,
        cancel_style=discord.ButtonStyle.secondary,
        footer_text="請仔細考慮後再確認。",
        ephemeral=True,
    )
