"""
Builds the Discord Embed for the Gacha Collection View.
"""

from typing import TYPE_CHECKING, List
from decimal import Decimal

import discord

from gacha.models.filters import CollectionFilters
from gacha.models.models import Card, UserCard
from gacha.views.utils import format_oil
from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder
from gacha.views import utils as view_utils
from gacha.constants import RarityLevel
from gacha.app_config import config_service

# 本地排序字段和排序順序映射
SORT_FIELDS = {
    "rarity": "稀有度",
    "name": "名稱",
    "series": "系列",
    "quantity": "數量",
    "star": "星級",
    "position": "自訂",
}

SORT_ORDERS = {"desc": "降序", "asc": "升序"}

if TYPE_CHECKING:
    from .card_view import CollectionView


class CollectionEmbedBuilder(BaseEmbedBuilder):
    """Handles the creation of the Embed for the CollectionView."""

    def __init__(self, view: "CollectionView"):
        interaction = getattr(view, "interaction", None)
        super().__init__(data=view, interaction=interaction)
        self.view = view

    def build_embed(self) -> discord.Embed:
        """Builds the embed for the current page."""
        nickname = self.view.state.user_nickname
        embed = self._create_base_embed()
        title = self._get_formatted_title(nickname)
        embed.set_author(name=title, icon_url=self.view.state.user_avatar_url)

        page_info = f"第{self.view.current_page}/{self.view.total_pages}頁 • 排序: {self._get_sort_text()}"

        if not self.view.cards or len(self.view.cards) == 0:
            self._add_empty_page_info(embed, page_info)
        elif not self.view.state.current_card:
            self._add_empty_page_info(embed, page_info)
        else:
            self._add_card_info(embed, self.view.state.current_card, page_info)

        return embed

    def _get_formatted_title(self, nickname: str) -> str:
        """Gets the formatted embed title."""
        title_parts = [f"{nickname}的卡冊"]
        filters = self.view.filters

        if filters.pool_type:
            pool_name = config_service.get_pool_type_names().get(
                filters.pool_type, filters.pool_type.capitalize()
            )
            title_parts.append(f"[{pool_name}]")

        if filters.rarity_in and len(filters.rarity_in) > 0:
            display_rarities = [
                config_service.get_rarity_display_codes().get(r, str(r))
                for r in filters.rarity_in
            ]
            if len(set(display_rarities)) == 1:
                try:
                    rarity_int = filters.rarity_in[0]
                    rarity_level_enum = RarityLevel(rarity_int)
                    friendly_name = view_utils.get_user_friendly_rarity_name(
                        rarity_level_enum
                    )
                    title_parts.append(f"[{friendly_name}]")
                except (ValueError, IndexError):
                    title_parts.append(f"[{display_rarities[0]}]")
            else:
                title_parts.append(
                    f"[{', '.join(sorted(list(set(display_rarities))))}]"
                )

        if filters.series:
            title_parts.append(f"[系列: {filters.series}]")
        if filters.card_name:
            title_parts.append(f"[名稱: {filters.card_name}]")
        if filters.quantity_greater_than and filters.quantity_greater_than > 0:
            title_parts.append("[僅重複]")
        return " ".join(title_parts)

    def _add_empty_page_info(self, embed: discord.Embed, page_info: str):
        """Adds information for an empty page."""
        embed.add_field(name="當前頁沒有卡片", value="請嘗試其他頁碼", inline=False)
        self._set_common_footer(embed, page_info=page_info)
        embed.description = f"收集: {self.view.unique_cards}/{self.view.total_cards}張"

    def _add_card_info(
            self,
            embed: discord.Embed,
            user_card: UserCard,
            page_info: str):
        """Adds card information to the embed."""
        if not user_card.card:
            self._add_empty_page_info(embed, page_info)
            return

        card = user_card.card

        rarity_level_enum = None
        try:
            if isinstance(card.rarity, int):
                rarity_level_enum = RarityLevel(card.rarity)
            elif isinstance(card.rarity, RarityLevel):
                rarity_level_enum = card.rarity
        except ValueError:
            pass

        embed.color = view_utils.get_rarity_color(
            rarity_level=rarity_level_enum, pool_type=card.pool_type
        )

        star_display = ""
        if hasattr(user_card, "star_level") and user_card.star_level > 0:
            star_display = f"{view_utils.get_star_emoji_string(user_card.star_level)}"

        prefix_emoji = (
            view_utils.get_ui_emoji("heart")
            if user_card.is_favorite
            else view_utils.get_ui_emoji("old_card")
        )

        if star_display:
            card_info_value = (
                f"{prefix_emoji} **{card.name}**\n{prefix_emoji} *{card.series}*"
            )
            if (
                hasattr(user_card, "custom_description")
                and user_card.custom_description
            ):
                card_info_value += f"\n「{user_card.custom_description}」"
            embed.add_field(
                name=f"{star_display}",
                value=card_info_value,
                inline=False)
        else:
            card_info_value = f"{prefix_emoji} *{card.series}*"
            if (
                hasattr(user_card, "custom_description")
                and user_card.custom_description
            ):
                card_info_value += f"\n「{user_card.custom_description}」"
            embed.add_field(
                name=f"{prefix_emoji} **{card.name}**",
                value=card_info_value,
                inline=False,
            )

        # 市場價格處理
        price_text_label = "市場價"
        sell_price_display_text = "價格計算中"

        if (
            hasattr(card, "current_market_sell_price")
            and card.current_market_sell_price is not None
        ):
            stored_price = card.current_market_sell_price
            if not isinstance(stored_price, Decimal):
                try:
                    stored_price = Decimal(str(stored_price))
                except BaseException:
                    stored_price = None

            if stored_price is not None:
                sell_price_display_text = (
                    f"`{int(stored_price)}` {config_service.get_oil_emoji()}"
                )

        rarity_display = view_utils.get_user_friendly_rarity_name(
            rarity_level_enum)
        pool_display_text = config_service.get_config('gacha_core_settings.pool_type_prefixes').get(
            card.pool_type, ""
        )
        owner_count_text = self._format_owner_count(
            getattr(user_card, "owner_count", None), format_type="display"
        )

        description_lines = [
            f"<:ReplyCont:1357534065841930290>{rarity_display} | {pool_display_text}"
        ]
        if owner_count_text:
            description_lines.append(
                f"<:ReplyCont:1357534065841930290>{owner_count_text}"
            )
        description_lines.append(
            f"<:Reply:1357534074830590143>持有: `{user_card.quantity}`張 | {price_text_label}: {sell_price_display_text}"
        )

        card_details_value = "\n".join(description_lines)

        embed.add_field(
            name=f"<a:RARITYyellow:1357560475302494228> 卡片信息 `{self.view.unique_cards}/{self.view.total_cards}張`",
            value=card_details_value,
            inline=False,
        )

        if hasattr(card, "image_url") and card.image_url:
            embed.set_image(url=card.image_url)

        self._set_rarity_thumbnail(embed, rarity_level=rarity_level_enum)
        self._set_common_footer(embed, card=card, page_info=page_info)

    def _get_sort_text(self) -> str:
        """Gets the text description of the current sorting."""
        if self.view.sort_by == "position":
            return "自訂排序"
        else:
            field = SORT_FIELDS.get(self.view.sort_by, self.view.sort_by)
            order = SORT_ORDERS.get(self.view.sort_order, self.view.sort_order)
            return f"{field} {order}"
