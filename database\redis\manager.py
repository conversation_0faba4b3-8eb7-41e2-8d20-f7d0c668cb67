# database/redis/manager.py (Refactored for redis.asyncio)
import asyncio
import json
import socket
import time
from typing import Any, Dict, List, Optional, Tuple, Union

# Use redis.asyncio instead of the separate aioredis library
import redis.asyncio as aioredis
from redis.exceptions import ConnectionError as RedisConnectionError
from redis.exceptions import TimeoutError as RedisTimeoutError
# Keep asyncio.TimeoutError for asyncio specific timeouts
from asyncio import TimeoutError as AsyncioTimeoutError

from utils.logger import logger
from database.redis.config import is_redis_enabled
from database.redis.cache.serializers import SimpleSerializer

class RedisManager:
    """Redis 連接管理器 (Asyncio 版本 using redis.asyncio)，提供與 Redis 服務器交互的功能"""

    # 健康檢查配置
    HEALTH_CHECK_INTERVAL = 60  # 秒
    CONNECTION_RETRY_INTERVAL = 5  # 秒
    MAX_RETRY_ATTEMPTS = 3  # 最大重試次數

    def __init__(
        self,
        host: str = "localhost",
        port: int = 6379,
        db: int = 0,
        password: Optional[str] = None,
        decode_responses: bool = False,
        url: Optional[str] = None
    ):
        """初始化 Redis 連接管理器 (Asyncio 版本)"""
        if isinstance(host, dict):
            config = host
            self.host = config.get("host", "localhost")
            self.port = config.get("port", 6379)
            self.db = config.get("db", 0)
            self.password = config.get("password")
            self.decode_responses = config.get("decode_responses", decode_responses)
            self.redis_url = config.get("url")  # 直接使用配置提供的URL
        else:
            self.host = host
            self.port = port
            self.db = db
            self.password = password
            self.decode_responses = decode_responses
            self.redis_url = url

        # 如果未提供直接的URL，則构建Redis URL
        if not self.redis_url:
            if self.password:
                self.redis_url = f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
            else:
                self.redis_url = f"redis://{self.host}:{self.port}/{self.db}"

        self.redis_client: Optional[aioredis.Redis] = None
        self._connected = False
        self._last_health_check = 0
        self._connection_lock = asyncio.Lock()
        self._health_check_task: Optional[asyncio.Task] = None

        logger.info("RedisManager initialized. Call `async_init` to connect and start health check.")

    async def async_init(self):
        """異步初始化，建立連接並啟動健康檢查"""
        if self.redis_client:
            logger.warning("RedisManager async_init called more than once.")
            return

        logger.info(f"Attempting to connect to Redis at {self.redis_url}")
        try:
            # Create Redis client using from_url (redis.asyncio)
            self.redis_client = aioredis.from_url(
                self.redis_url,
                decode_responses=self.decode_responses,
                socket_timeout=3.0,
                socket_connect_timeout=3.0,
                health_check_interval=30.0,
            )
            await self._test_connection() # Perform initial connection test

            if self._connected:
                self._health_check_task = asyncio.create_task(self._health_check_loop())
            else:
                 logger.error("Initial Redis connection failed. Health check not started.")

        except Exception as e:
            logger.error(f"Failed to initialize Redis client: {e}", exc_info=True)
            self._connected = False
            if self.redis_client:
                 await self.close()
                 self.redis_client = None

    async def _test_connection(self) -> None:
        """(Async) 測試 Redis 連接是否可用"""
        if not self.redis_client:
             self._connected = False
             logger.warning("Redis client not initialized, cannot test connection.")
             return

        try:
            await self.redis_client.ping()
            if not self._connected:
                 logger.info("Successfully connected to Redis server.")
            self._connected = True
            self._last_health_check = time.time()
        # Updated Exception Types
        except (RedisConnectionError, RedisTimeoutError, socket.timeout, AsyncioTimeoutError) as e:
            if self._connected:
                 logger.error(f"Lost connection to Redis server: {e}")
            self._connected = False
        except Exception as e:
            if self._connected:
                 logger.error(f"Redis connection test failed unexpectedly: {e}", exc_info=True)
            self._connected = False

    async def _health_check_loop(self) -> None:
        """(Async) 健康檢查循環，定期檢查 Redis 連接是否可用"""
        logger.info("Starting Redis health check loop.")
        while True:
            try:
                current_time = time.time()
                interval = self.HEALTH_CHECK_INTERVAL if self._connected else self.CONNECTION_RETRY_INTERVAL

                if not self._connected or (current_time - self._last_health_check) > self.HEALTH_CHECK_INTERVAL:
                     async with self._connection_lock:
                          if not self._connected or (time.time() - self._last_health_check) > self.HEALTH_CHECK_INTERVAL:
                              await self._test_connection()

                await asyncio.sleep(interval)

            except asyncio.CancelledError:
                 logger.info("Redis health check loop cancelled.")
                 break
            except Exception as e:
                logger.error(f"Health check loop error: {e}", exc_info=True)
                await asyncio.sleep(self.CONNECTION_RETRY_INTERVAL)

    async def _safe_redis_operation(
        self, operation_coro, default_return=None
    ):
        """(Async) 安全執行 Redis 操作，處理連接錯誤和重試邏輯"""
        if not self.redis_client:
             logger.error("Redis client not initialized. Cannot perform operation.")
             return default_return

        for attempt in range(self.MAX_RETRY_ATTEMPTS):
            try:
                return await operation_coro()
            # Updated Exception Types
            except (RedisConnectionError, RedisTimeoutError, socket.timeout, AsyncioTimeoutError) as e:
                logger.warning(
                    f"Redis operation failed (Attempt {attempt + 1}/{self.MAX_RETRY_ATTEMPTS}): {e}"
                )
                async with self._connection_lock:
                    self._connected = False

                if attempt == self.MAX_RETRY_ATTEMPTS - 1:
                    logger.error("Redis operation failed after max retries.")
                    return default_return

                await asyncio.sleep(self.CONNECTION_RETRY_INTERVAL)
                async with self._connection_lock:
                    if not self._connected:
                         logger.info(f"Retrying connection test (Attempt {attempt + 1})")
                         await self._test_connection()
                if not self._connected:
                     logger.warning("Redis still not connected after retry wait. Skipping further attempts.")
                     return default_return

            except Exception as e:
                logger.error(f"Redis operation error: {e}", exc_info=True)
                return default_return
        return default_return

    @property
    def is_connected(self) -> bool:
        """檢查 Redis 連接是否可用 (同步檢查狀態)"""
        return self._connected

    async def get(self, key: str, default: Optional[Any] = None) -> Any:
        """(Async) 獲取Redis鍵值，使用SimpleSerializer進行反序列化"""
        if not self.redis_client: return default

        async def operation():
             if not self.redis_client: return None
             return await self.redis_client.get(key)

        value = await self._safe_redis_operation(operation)

        if value is None:
            return default

        if isinstance(value, bytes):
             try:
                  # For this general 'get', we assume it might be a serialized object
                  return SimpleSerializer.deserialize(value)
             except Exception as e: 
                  logger.warning(f"Failed to deserialize value for key '{key}' with SimpleSerializer, returning raw bytes: {e}")
                  # If deserialization fails, it's safer to return raw bytes
                  # as the caller of generic get() might have its own handling or expect bytes.
                  # However, SimpleSerializer.deserialize already returns the original data on failure.
                  return value 
        
        logger.debug(f"Value for key '{key}' was not bytes or deserialization was handled by SimpleSerializer, returning as is: {type(value)}")
        return value

    async def get_string(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """(Async) 從Redis獲取一個期望為字符串的值。"""
        if not self.redis_client:
            logger.debug(f"Redis client not available, returning default for key '{key}'")
            return default

        async def operation():
            if not self.redis_client: return None
            # Assuming redis_client is configured with decode_responses=False,
            # so 'get' will return bytes. If decode_responses=True, it might return str.
            return await self.redis_client.get(key)

        value = await self._safe_redis_operation(operation)

        if value is None:
            logger.debug(f"No value found for key '{key}', returning default.")
            return default

        if isinstance(value, bytes):
            try:
                decoded_value = value.decode('utf-8')
                logger.debug(f"Successfully decoded bytes to string for key '{key}'.")
                return decoded_value
            except UnicodeDecodeError as e:
                logger.error(f"Failed to decode UTF-8 string from bytes for key '{key}': {e}")
                return default # Or raise an error, or return None consistently
        elif isinstance(value, str):
            # This case handles if redis_client was set up with decode_responses=True
            logger.debug(f"Value for key '{key}' is already a string.")
            return value
        else:
            logger.warning(f"Value for key '{key}' is neither bytes nor string, but {type(value)}. Returning default.")
            return default

    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
    ) -> bool:
        """(Async) 設置Redis鍵值，使用SimpleSerializer進行序列化"""
        if not self.redis_client: return False

        logger.debug(f"RedisManager.set (async): Object type={value.__class__.__name__ if hasattr(value, '__class__') else type(value).__name__}")

        try:
            if not isinstance(value, (bytes, str)):
                 serialized_value = SimpleSerializer.serialize(value)
            else:
                 serialized_value = value
        except Exception as e:
             logger.error(f"Failed to serialize value for Redis key '{key}': {e}", exc_info=True)
             return False

        async def operation():
            if not self.redis_client: return None
            if ttl is not None:
                # redis.asyncio uses 'ex' for seconds TTL
                return await self.redis_client.set(key, serialized_value, ex=ttl)
            else:
                return await self.redis_client.set(key, serialized_value)

        result = await self._safe_redis_operation(operation)
        return result is True

    async def delete(self, *keys: str) -> bool:
        """(Async) 刪除 Redis 中的一個或多個鍵"""
        if not self.redis_client or not keys: return False

        async def operation():
            if not self.redis_client: return 0
            return await self.redis_client.delete(*keys)

        result = await self._safe_redis_operation(operation, default_return=0)
        return result > 0

    async def exists(self, key: str) -> bool:
        """(Async) 檢查 Redis 中是否存在指定的鍵"""
        if not self.redis_client: return False

        async def operation():
            if not self.redis_client: return 0
            return await self.redis_client.exists(key)

        result = await self._safe_redis_operation(operation, default_return=0)
        return result > 0

    async def expire(self, key: str, seconds: int) -> bool:
        """(Async) 設置 Redis 中鍵的過期時間"""
        if not self.redis_client: return False

        async def operation():
            if not self.redis_client: return False
            return await self.redis_client.expire(key, seconds)

        result = await self._safe_redis_operation(operation, default_return=False)
        return bool(result)

    async def ttl(self, key: str) -> int:
        """(Async) 獲取 Redis 中鍵的剩餘生存時間"""
        if not self.redis_client: return -2

        async def operation():
            if not self.redis_client: return -2
            return await self.redis_client.ttl(key)

        result = await self._safe_redis_operation(operation, default_return=-2)
        return result

    # --- 排行榜相關方法 (Async) ---
    async def zadd(self, name: str, mapping: Dict[str, float]) -> int:
        """(Async) 向有序集合添加元素"""
        if not self.redis_client or not mapping: return 0

        async def operation():
            if not self.redis_client: return 0
            return await self.redis_client.zadd(name, mapping)

        result = await self._safe_redis_operation(operation, default_return=0)
        return result

    async def zrange(
        self,
        name: str,
        start: int,
        end: int,
        desc: bool = False,
        withscores: bool = False,
    ) -> List:
        """(Async) 獲取有序集合的範圍"""
        if not self.redis_client: return []

        async def operation():
            if not self.redis_client: return []
            return await self.redis_client.zrange(
                name, start, end, desc=desc, withscores=withscores
            )

        result = await self._safe_redis_operation(operation, default_return=[])
        return result

    async def zrank(self, name: str, value: str) -> Optional[int]:
        """(Async) 獲取有序集合中成員的排名 (0-based)"""
        if not self.redis_client: return None

        async def operation():
            if not self.redis_client: return None
            return await self.redis_client.zrank(name, value)

        result = await self._safe_redis_operation(operation, default_return=None)
        return result

    async def zrevrank(self, name: str, value: str) -> Optional[int]:
        """(Async) 獲取有序集合中成員的反向排名 (0-based)"""
        if not self.redis_client: return None

        async def operation():
            if not self.redis_client: return None
            return await self.redis_client.zrevrank(name, value)

        result = await self._safe_redis_operation(operation, default_return=None)
        return result

    async def zscore(self, name: str, value: str) -> Optional[float]:
        """(Async) 獲取有序集合中成員的分數"""
        if not self.redis_client: return None

        async def operation():
            if not self.redis_client: return None
            return await self.redis_client.zscore(name, value)

        result = await self._safe_redis_operation(operation, default_return=None)
        if isinstance(result, bytes):
             try:
                  return float(result)
             except (ValueError, TypeError):
                  logger.error(f"Could not convert zscore result to float for {name}:{value}")
                  return None
        # Ensure result is float or None
        return float(result) if result is not None else None


    async def zrangebyscore(
        self,
        name: str,
        min_score: Union[float, str],
        max_score: Union[float, str],
        start: Optional[int] = None,
        num: Optional[int] = None,
        withscores: bool = False,
    ) -> List:
        """(Async) 根據分數範圍獲取有序集合的成員"""
        if not self.redis_client: return []

        offset = start if start is not None else 0
        count = num if num is not None else -1

        async def operation():
            if not self.redis_client: return []
            # redis.asyncio uses zrange with byscore=True
            return await self.redis_client.zrange(
                name, min_score, max_score, byscore=True, offset=offset, count=count, withscores=withscores
            )

        result = await self._safe_redis_operation(operation, default_return=[])
        return result

    # --- 鎖相關方法 (Async) ---
    async def acquire_lock(self, lock_name: str, timeout: int = 10) -> Optional[str]:
        """(Async) 獲取分布式鎖"""
        if not self.redis_client: return None

        import uuid
        lock_id = str(uuid.uuid4())
        lock_key = f"lock:{lock_name}"

        async def operation():
            if not self.redis_client: return False
            return await self.redis_client.set(lock_key, lock_id, nx=True, ex=timeout)

        success = await self._safe_redis_operation(operation, default_return=False)
        return lock_id if success else None

    async def release_lock(self, lock_name: str, lock_id: str) -> bool:
        """(Async) 釋放分布式鎖"""
        if not self.redis_client: return False

        lock_key = f"lock:{lock_name}"
        script = """
        if redis.call('get', KEYS[1]) == ARGV[1] then
            return redis.call('del', KEYS[1])
        else
            return 0
        end
        """
        try:
            async def operation():
                 if self.redis_client:
                      # redis.asyncio eval takes script, keys=[], args=[]
                      return await self.redis_client.eval(script, keys=[lock_key], args=[lock_id])
                 return 0

            result = await self._safe_redis_operation(operation, default_return=0)
            return bool(result)
        except Exception as e:
            logger.error(f"Redis release lock failed: {e}", exc_info=True)
            return False

    # --- 鍵掃描 (Async) ---
    async def keys(self, pattern: str) -> List[str]:
        """(Async) 獲取匹配指定模式的所有鍵 (謹慎使用)"""
        if not self.redis_client: return []

        async def operation():
            if not self.redis_client: return []
            return await self.redis_client.keys(pattern)

        result = await self._safe_redis_operation(operation, default_return=[])
        if result and not self.decode_responses:
             return [k.decode('utf-8', errors='ignore') if isinstance(k, bytes) else k for k in result]
        return result if isinstance(result, list) else []

    async def scan_iter(self, match: Optional[str] = None, count: Optional[int] = None):
        """(Async) 使用 scan 命令異步迭代獲取匹配的鍵"""
        if not self.redis_client:
             async def empty_iterator():
                  if False: yield
             return empty_iterator()

        try:
             kwargs = {}
             if count is not None:
                  kwargs['count'] = count
             # redis.asyncio scan_iter returns an async iterator
             return self.redis_client.scan_iter(match=match, **kwargs)
        except Exception as e:
             logger.error(f"Redis scan_iter failed: {e}", exc_info=True)
             async def empty_iterator_on_error():
                  if False: yield
             return empty_iterator_on_error()

    async def close(self) -> None:
        """(Async) 關閉Redis連接"""
        logger.info("Attempting to close Redis connection...")
        if self._health_check_task and not self._health_check_task.done():
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                logger.info("Health check task successfully cancelled.")
            except Exception as e:
                 logger.error(f"Error during health check task cancellation: {e}", exc_info=True)

        if self.redis_client:
            try:
                # redis.asyncio uses close() which handles pool disconnection
                await self.redis_client.close()
                # await self.redis_client.connection_pool.disconnect() # Might be needed depending on version
                logger.info("Redis client closed.")
                self.redis_client = None
                self._connected = False
            except Exception as e:
                logger.error(f"Error closing Redis client: {e}", exc_info=True)
        else:
             logger.info("Redis client was not initialized or already closed.")

    def pipeline(self, transaction: bool = True):
         """獲取Redis管線實例 (需要使用 async with)"""
         if not self.redis_client:
              logger.error("Cannot create pipeline, Redis client not initialized.")
              raise RuntimeError("Redis client not initialized, cannot create pipeline.")
         # redis.asyncio pipeline returns a pipeline object
         return self.redis_client.pipeline(transaction=transaction)

    # Helper methods remain synchronous
    def cache_type_adapter(self, cache_type: str) -> str:
        """標準化緩存類型"""
        # 簡化實現，直接返回輸入的緩存類型字符串
        return str(cache_type)

    def generate_cache_key(self, prefix: str, cache_type: str, key: str) -> str:
        """生成標準格式的緩存鍵"""
        cache_type_str = self.cache_type_adapter(cache_type)
        return f"{prefix}:{cache_type_str}:{key}"
