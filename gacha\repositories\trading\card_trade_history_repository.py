import asyncpg
from typing import List, Optional, Union
from datetime import datetime
from gacha.models.trade_models import CardTradeHistoryModel
from database.base_repository import BaseRepository
from utils.logger import logger
from gacha.exceptions import RecordNotFoundError, DatabaseOperationError

class CardTradeHistoryRepository(BaseRepository[CardTradeHistoryModel]):
    """
    Repository for handling database operations related to the card_trade_history table.
    This table stores records of completed P2P card trades.
    """

    def __init__(self, pool: asyncpg.Pool):
        """
        Initializes the repository with an asyncpg connection pool.

        Args:
            pool: The asyncpg connection pool.
        """
        super().__init__(pool)

    async def add_trade_history(self, trade_data: CardTradeHistoryModel, connection: Optional[asyncpg.Connection]=None) -> CardTradeHistoryModel:
        """
        Adds a new completed trade record to the card_trade_history table.
        The 'id' and 'completed_at' fields are typically generated by the database.

        Args:
            trade_data: A CardTradeHistoryModel instance containing the trade details.
                        The 'id' and 'completed_at' fields from this model will be ignored,
                        as they are set by the database or within this method.
            connection: Optional asyncpg.Connection to use for the operation.

        Returns:
            A CardTradeHistoryModel instance representing the newly inserted record,
            including the database-generated 'id' and 'completed_at'.

        Raises:
            DatabaseOperationError: If the database operation fails.
            RecordNotFoundError: If the insert operation does not return the expected record.
        """
        query = '\n            INSERT INTO card_trade_history (\n                initiator_user_id, receiver_user_id,\n                offered_master_card_id, offered_quantity,\n                requested_master_card_id, requested_quantity,\n                price_amount, trade_type, fee_charged\n            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)\n            RETURNING id, initiator_user_id, receiver_user_id,\n                      offered_master_card_id, offered_quantity,\n                      requested_master_card_id, requested_quantity,\n                      price_amount, trade_type, fee_charged, completed_at;\n        '
        actual_requested_quantity = trade_data.requested_quantity
        if trade_data.requested_master_card_id is None:
            actual_requested_quantity = None
        params = (trade_data.initiator_user_id, trade_data.receiver_user_id, trade_data.offered_master_card_id, trade_data.offered_quantity, trade_data.requested_master_card_id, actual_requested_quantity, trade_data.price_amount, trade_data.trade_type, trade_data.fee_charged)
        record = await self._fetchrow(query, params, connection=connection)
        if record:
            return CardTradeHistoryModel(**dict(record))
        else:
            logger.error('Failed to insert trade history for initiator %s, data: %s', trade_data.initiator_user_id, trade_data.model_dump_json(indent=2))
            raise RecordNotFoundError('Failed to insert trade history, no record returned after executing query.')

    async def get_trade_history_by_user(self, user_id: int, limit: int=20, offset: int=0, connection: Optional[asyncpg.Connection]=None) -> List[CardTradeHistoryModel]:
        """
        Retrieves trade history for a specific user (as initiator or receiver), paginated.

        Args:
            user_id: The Discord User ID.
            limit: Maximum number of records to return.
            offset: Offset for pagination.
            connection: Optional asyncpg.Connection to use for the operation.

        Returns:
            A list of CardTradeHistoryModel instances, ordered by completion_at DESC.
            
        Raises:
            DatabaseOperationError: If the database operation fails.
        """
        query = '\n            SELECT id, initiator_user_id, receiver_user_id,\n                   offered_master_card_id, offered_quantity,\n                   requested_master_card_id, requested_quantity,\n                   price_amount, trade_type, fee_charged, completed_at\n            FROM card_trade_history\n            WHERE initiator_user_id = $1 OR receiver_user_id = $2\n            ORDER BY completed_at DESC, id DESC\n            LIMIT $3 OFFSET $4;\n        '
        params = (user_id, user_id, limit, offset)
        records = await self._fetch(query, params, connection=connection)
        return [CardTradeHistoryModel(**dict(row)) for row in records]

    async def get_trade_history_entry_by_id(self, trade_id: int, connection: Optional[asyncpg.Connection]=None) -> Optional[CardTradeHistoryModel]:
        """
        Retrieves a specific trade history entry by its ID.
        
        Args:
            trade_id: The ID of the trade history entry to retrieve.
            connection: Optional asyncpg.Connection to use for the operation.
            
        Returns:
            CardTradeHistoryModel if found, None if not found.
            
        Raises:
            DatabaseOperationError: If the database operation fails.
        """
        query = '\n            SELECT id, initiator_user_id, receiver_user_id,\n                   offered_master_card_id, offered_quantity,\n                   requested_master_card_id, requested_quantity,\n                   price_amount, trade_type, fee_charged, completed_at\n            FROM card_trade_history\n            WHERE id = $1;\n        '
        params = (trade_id,)
        record = await self._fetchrow(query, params, connection=connection)
        return CardTradeHistoryModel(**dict(record)) if record else None