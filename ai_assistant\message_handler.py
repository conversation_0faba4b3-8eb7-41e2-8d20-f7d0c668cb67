"""
消息處理工具模組 - 提供通用的Discord消息處理功能
"""

import discord
import logging
import os
import asyncio
import io
import uuid
import re
import datetime
from typing import Optional, Dict, Any, List, Union, Callable, Coroutine
import aiohttp

from .ai_service_base import AIServiceBase  # 確保導入AIServiceBase用於圖像處理

# 設置日誌
logger = logging.getLogger("MessageHandler")

class MessageHandler:
    """Discord消息處理類，提供通用的消息處理功能"""
    
    def __init__(self, service_name: str):
        """
        初始化消息處理器
        
        參數:
            service_name (str): 服務名稱，用於日誌和識別
        """
        self.service_name = service_name
        self.logger = logging.getLogger(f"{service_name}_MessageHandler")
        self.logger.info(f"{service_name} 消息處理器已初始化")
    
    async def handle_interaction(
        self,
        interaction: discord.Interaction,
        processing_message: str,
        processor_func: Callable,
        image: Optional[discord.Attachment] = None,
        url: Optional[str] = None,
        user_prompt: Optional[str] = None,
        allow_history_scan: bool = True
    ):
        """處理圖像互動的簡化流程"""
        try:
            # 1. 只有當互動還未被回應時才延遲回應
            if not interaction.response.is_done():
                await interaction.response.defer(thinking=True)
            
            # 2. 建立請求ID用於日誌追蹤
            request_id = str(uuid.uuid4())
            self.logger.info(f"創建新的請求 ID: {request_id} 用戶: {interaction.user.display_name}")
            
            # 3. 發送處理中的提示消息
            response_message = await interaction.followup.send(processing_message, wait=True)
            
            # 4. 嘗試獲取圖像數據
            image_data = None
            if image:
                image_data = await image.read() if image.content_type and image.content_type.startswith("image/") else None
            elif url:
                image_data = await self._download_image_from_url(url)
            elif allow_history_scan:
                # 嘗試從最近消息尋找圖像
                image_data = await self._find_image_in_history(interaction)
            
            # 5. 檢查是否成功獲取圖像
            if not image_data and (image or url or allow_history_scan):
                await response_message.edit(content="❌ 無法獲取或處理圖像。請確保上傳了有效的圖片或提供了有效的圖片URL。")
                self.logger.warning(f"[{request_id}] Failed to obtain image data (image: {image is not None}, url: {url is not None}, history_scan_allowed: {allow_history_scan}).")
                return
            
            # 6. 壓縮大型圖像
            if image_data:
                image_data = self._resize_image_if_needed(image_data)
            
            # 7. 獲取用戶資訊
            user_id = str(interaction.user.id) if interaction.user else None
            
            # 8. 啟動背景任務處理請求
            asyncio.create_task(
                processor_func(image_data, user_id, response_message, request_id, user_prompt)
            )
            
        except discord.NotFound:
            self.logger.error("互動已超時，無法回應")
        except Exception as e:
            self.logger.error(f"處理圖像互動時出錯: {str(e)}")
            try:
                if not interaction.response.is_done():
                    await interaction.response.send_message("處理請求時出錯，請稍後再試。", ephemeral=True)
                else:
                    await interaction.followup.send("處理請求時出錯，請稍後再試。", ephemeral=True)
            except:
                pass
    
    async def _download_image_from_url(self, url: str) -> Optional[bytes]:
        """從URL下載圖像"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        return await response.read()
            return None
        except Exception as e:
            self.logger.error(f"從URL下載圖像時出錯: {str(e)}")
            return None
    
    async def _find_image_in_history(self, interaction: discord.Interaction) -> Optional[bytes]:
        """從最近消息歷史中查找圖像"""
        try:
            # 檢查消息內容中是否包含圖片URL
            if interaction.message and interaction.message.content:
                urls = re.findall(r'https?://\S+\.(?:png|jpg|jpeg|gif|webp)(?:\?\S*)?', interaction.message.content)
                if urls:
                    return await self._download_image_from_url(urls[0])
            
            # 檢查最近消息中是否有圖像附件
            channel = interaction.channel
            if channel:
                async for message in channel.history(limit=5):
                    # 檢查附件
                    if message.attachments:
                        for attachment in message.attachments:
                            if attachment.content_type and attachment.content_type.startswith("image/"):
                                return await attachment.read()
                    # 檢查URL
                    elif message.content:
                        urls = re.findall(r'https?://\S+\.(?:png|jpg|jpeg|gif|webp)(?:\?\S*)?', message.content)
                        if urls:
                            return await self._download_image_from_url(urls[0])
            return None
        except Exception as e:
            self.logger.error(f"從歷史查找圖像時出錯: {str(e)}")
            return None
    
    def _resize_image_if_needed(self, image_data: bytes) -> bytes:
        """如果必要，壓縮圖像"""
        if not image_data:
            return None
            
        # 檢查大小是否超過5MB
        size_mb = len(image_data) / (1024 * 1024)
        if size_mb > 5:
            self.logger.info(f"圖像過大 ({size_mb:.2f}MB)，進行壓縮")
            return AIServiceBase.resize_image(image_data, max_size=5*1024*1024)
        return image_data
    
    @staticmethod
    async def process_text_interaction(
        interaction: discord.Interaction,
        processing_message: str,
        processor_func: Callable,
        prompt: str
    ):
        """處理純文本互動的簡化流程"""
        try:
            # 1. 只有當互動還未被回應時才延遲回應
            if not interaction.response.is_done():
                await interaction.response.defer(thinking=True)
            
            # 2. 建立請求ID用於日誌追蹤
            request_id = str(uuid.uuid4())
            logger.info(f"創建新的文本請求 ID: {request_id} 用戶: {interaction.user.display_name}")
            
            # 3. 發送處理中的提示消息
            response_message = await interaction.followup.send(processing_message, wait=True)
            
            # 4. 獲取用戶資訊
            user_id = str(interaction.user.id) if interaction.user else None
            
            # 5. 啟動背景任務處理請求
            asyncio.create_task(
                processor_func(prompt, user_id, response_message, request_id)
            )
            
        except discord.NotFound:
            logger.error("互動已超時，無法回應")
        except Exception as e:
            logger.error(f"處理文本互動時出錯: {str(e)}")
            try:
                if not interaction.response.is_done():
                    await interaction.response.send_message("處理請求時出錯，請稍後再試。", ephemeral=True)
                else:
                    await interaction.followup.send("處理請求時出錯，請稍後再試。", ephemeral=True)
            except:
                pass 