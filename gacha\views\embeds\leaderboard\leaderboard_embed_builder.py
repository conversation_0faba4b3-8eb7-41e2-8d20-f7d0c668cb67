"""
Gacha系統排行榜Embed構建器
"""
from typing import Any, Dict, List, Optional
from datetime import datetime
import discord
from gacha.models.models import Card
from gacha.views.utils import format_oil
from gacha.services.leaderboard.leaderboard_service import LeaderboardService
from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder
from gacha.views import utils as view_utils
from gacha.constants import RarityLevel
from gacha.app_config import config_service, LeaderboardConfigDetail

class LeaderboardEmbedBuilder(BaseEmbedBuilder):
    """構建排行榜嵌入消息的類"""
    MEDAL_EMOJIS = {1: '🥇', 2: '🥈', 3: '🥉'}

    def __init__(self, leaderboard_data: Dict[str, Any], leaderboard_type: str, items_per_page: int, asset_symbol: Optional[str]=None):
        """初始化排行榜Embed構建器

        參數:
            leaderboard_data: 排行榜數據
            leaderboard_type: 排行榜類型
            items_per_page: 每頁顯示數量
            asset_symbol: 股票代碼 (可選, 用於特定股票排行)
        """
        lb_data = {'leaderboard_data': leaderboard_data, 'leaderboard_type': leaderboard_type, 'items_per_page': items_per_page, 'asset_symbol': asset_symbol}
        super().__init__(data=lb_data)
        self.leaderboard_data = leaderboard_data
        self.leaderboard_type = leaderboard_type
        self.items_per_page = items_per_page
        self.asset_symbol = asset_symbol

    def build_embed(self) -> discord.Embed:
        """構建排行榜嵌入消息

        返回:
            discord.Embed: 排行榜嵌入消息
        """
        if not self.leaderboard_data or not self.leaderboard_data.get('results'):
            return self._create_base_embed(title='排行榜', description='暫無數據', color=discord.Color.light_gray())
        config_from_settings = config_service.get_config('gacha_core_settings.leaderboard_config').get(self.leaderboard_type)
        if isinstance(config_from_settings, LeaderboardConfigDetail):
            base_title = config_from_settings.title
            base_color = config_from_settings.color
            base_description = config_from_settings.description
        else:
            fallback_dict = {'title': f'{self.leaderboard_type} 排行榜', 'color': 7506394, 'description': ''}
            base_title = fallback_dict['title']
            base_color = fallback_dict['color']
            base_description = fallback_dict['description']
        current_page = self.leaderboard_data.get('current_page', 1)
        total_pages = self.leaderboard_data.get('total_pages', 1)
        total_users = self.leaderboard_data.get('total_users', 0)
        results = self.leaderboard_data.get('results', [])
        embed_title = base_title
        if self.leaderboard_type == 'stock_holding' and self.asset_symbol:
            asset_name_from_data = None
            if results and results[0] and ('asset_name' in results[0]):
                asset_name_from_data = results[0]['asset_name']
            if asset_name_from_data:
                embed_title = f'{asset_name_from_data} ({self.asset_symbol}) 持有排行'
            else:
                embed_title = f'{base_title} ({self.asset_symbol})'
        embed = self._create_base_embed(title=embed_title, color=base_color)
        embed.description = base_description
        if results:
            leaderboard_text = ''
            rank_start = (current_page - 1) * self.items_per_page + 1
            for i, entry in enumerate(results):
                rank = rank_start + i
                entry_text = self._format_entry(entry)
                if rank in self.MEDAL_EMOJIS:
                    medal = self.MEDAL_EMOJIS[rank]
                    leaderboard_text += f'{medal} '
                else:
                    leaderboard_text += f'`{rank}.` '
                leaderboard_text += f"**{entry.get('user_name', '未知用戶')}**\n{entry_text}\n\n"
            if embed.description:
                embed.description += f'\n\n{leaderboard_text}'
            else:
                embed.description = leaderboard_text
        elif embed.description:
            embed.description += '\n\n目前此排行暫無數據。'
        else:
            embed.description = '暫無數據'
        footer_text_parts = []
        query_time = self.leaderboard_data.get('query_time')
        if query_time is not None:
            footer_text_parts.append(f'查詢: {query_time:.2f}s')
        updated_at = self.leaderboard_data.get('updated_at')
        if updated_at and isinstance(updated_at, datetime):
            footer_text_parts.append(f"更新於: {discord.utils.format_dt(updated_at, style='f')}")
        footer_text_parts.append(f'頁 {current_page}/{total_pages}')
        if total_users > 0:
            footer_text_parts.append(f'共 {total_users} 名玩家')
        if footer_text_parts:
            embed.set_footer(text=' • '.join(footer_text_parts))
        return embed

    def _format_entry(self, entry: Dict[str, Any]) -> str:
        """格式化排行榜條目

        參數:
            entry: 條目數據

        返回:
            str: 格式化後的條目字符串
        """
        if self.leaderboard_type == 'rarity':
            rarities_ordered = [7, 6, 5, 4, 3, 2, 1]
            rarity_parts = []
            for rarity_num in rarities_ordered:
                count = entry.get(f'rarity_{rarity_num}_count', 0)
                if count > 0:
                    try:
                        rarity_level_enum = RarityLevel(rarity_num)
                        emoji = view_utils.get_rarity_display_code(rarity_level_enum)
                    except ValueError:
                        emoji = '❓'
                    rarity_parts.append(f'{emoji} x`{count}`')
            return ' | '.join(rarity_parts) if rarity_parts else '尚未收集任何卡片'
        elif self.leaderboard_type == 'completion':
            percentage = entry.get('completion_percentage', 0)
            progress = int(percentage / 10)
            empty_bar = '<:emptybar:1366671928449957898>'
            green_bar = '<:greenbar:1366671921902784583>'
            progress_bar = green_bar * progress + empty_bar * (10 - progress)
            return f"完成度: `{entry.get('collected_cards', 0)}/{entry.get('total_cards', 0)} ({percentage}%)`\n進度: {progress_bar}"
        elif self.leaderboard_type == 'collection_unique':
            import datetime
            debug_file_path = '/leaderboard_embed_debug.txt'

            def write_debug(msg):
                try:
                    with open(debug_file_path, 'a', encoding='utf-8') as f_debug:
                        f_debug.write(f'{datetime.datetime.now()} - {msg}\n')
                except Exception as e_debug:
                    print(f'CRITICAL DEBUG: Failed to write to {debug_file_path}: {e_debug}', flush=True)
            write_debug(f"_format_entry (collection_unique) CALLED for user {entry.get('user_name', entry.get('user_id'))}")
            available_pool_types = [key.replace('_pool_total_cards', '') for key in entry.keys() if key.endswith('_pool_total_cards')]
            pool_percentages_parts = []
            for pool_type in available_pool_types:
                field_prefix = pool_type.replace('-', '_')
                collected = entry.get(f'{field_prefix}_pool_unique_cards', 0)
                if collected > 0:
                    total = entry.get(f'{field_prefix}_pool_total_cards', 1)
                    original_pool_type = pool_type.replace('_', '-') if '-' in pool_type else pool_type
                    pool_name = config_service.get_pool_type_names().get(original_pool_type, original_pool_type.capitalize())
                    pool_percent = self._calculate_percentage(collected, total)
                    pool_first_char = pool_name[0] if pool_name else original_pool_type[0]
                    pool_percentages_parts.append(f'({pool_first_char}{pool_percent}%)')
            pool_percentages = '<:ReplyCont:1357534065841930290>' + ''.join(pool_percentages_parts)
            rarity_fields = []
            for key in entry.keys():
                if key.startswith('rarity_') and key.endswith('_unique_cards'):
                    rarity_num = key.split('_')[1]
                    if rarity_num.isdigit():
                        rarity_int = int(rarity_num)
                        if rarity_int in config_service.get_config('gacha_core_settings.rarity_sort_values'):
                            rarity_code = config_service.get_rarity_display_codes().get(rarity_int)
                            if rarity_code:
                                collected = entry.get(key, 0)
                                total_key = f'rarity_{rarity_num}_total_cards'
                                total = entry.get(total_key, 0)
                                rarity_fields.append((rarity_int, rarity_code, collected, total))
            merged_rarities: Dict[str, Dict[str, int]] = {}
            for rarity_int, rarity_code, collected, total in rarity_fields:
                if rarity_code not in merged_rarities:
                    merged_rarities[rarity_code] = {'collected': 0, 'total': 0, 'sort_key': rarity_int}
                merged_rarities[rarity_code]['collected'] += collected
                merged_rarities[rarity_code]['total'] += total
            sorted_rarities_items = sorted([item for item in merged_rarities.items() if item[1]['total'] > 0], key=lambda item: config_service.get_config('gacha_core_settings.rarity_sort_values').get(item[1]['sort_key'], 99), reverse=True)
            rarity_strings = []
            for i, (rarity_code, data) in enumerate(sorted_rarities_items):
                collected = data['collected']
                total = data['total']
                rarity_int_val = data['sort_key']
                emoji = ''
                if rarity_int_val is not None:
                    try:
                        rarity_level_enum = RarityLevel(rarity_int_val)
                        emoji = view_utils.get_rarity_display_code(rarity_level_enum)
                    except ValueError:
                        emoji = '❓'
                else:
                    emoji = f'{rarity_code}'
                current_rarity_string = f'{emoji} {collected}/{total}'
                rarity_strings.append(current_rarity_string)
            rarity_lines = []
            for i in range(0, len(rarity_strings), 4):
                chunk = rarity_strings[i:i + 4]
                line_prefix = '<:Reply:1357534074830590143>' if i == len(rarity_strings) - len(chunk) else '<:ReplyCont:1357534065841930290>'
                rarity_lines.append(f'{line_prefix}' + ' | '.join(chunk))
            rarity_display = '\n'.join(rarity_lines)
            return f'{pool_percentages}\n{rarity_display}'
        elif self.leaderboard_type == 'draws':
            oil_text = format_oil(entry.get('oil_balance', 0), label='餘額')
            return f"抽卡次數: {entry.get('total_draws', 0)} | {oil_text}"
        elif self.leaderboard_type == 'oil':
            oil_text = format_oil(entry.get('oil_balance', 0), label='餘額')
            return f"{oil_text} | 抽卡次數: {entry.get('total_draws', 0)}"
        elif self.leaderboard_type == 'portfolio_value':
            total_value = entry.get('total_portfolio_value', 0)
            return f'總資產價值: {format_oil(total_value)}'
        elif self.leaderboard_type == 'stock_holding':
            stock_quantity = entry.get('stock_quantity', 0)
            return f'持有數量: `{stock_quantity}` 股'
        elif self.leaderboard_type == 'trade_volume':
            total_volume = entry.get('total_trade_volume', 0)
            return f'總交易額: {format_oil(total_volume)}'
        elif self.leaderboard_type == 'trade_count':
            total_trades = entry.get('total_trade_count', 0)
            return f'總交易次數: `{total_trades}` 次'
        else:
            return '未知排行榜類型'

    def _calculate_percentage(self, value: int, total: int) -> float:
        """計算百分比，避免除以零錯誤

        參數:
            value: 分子
            total: 分母

        返回:
            float: 百分比值，保留一位小數
        """
        if not total:
            return 0.0
        value_float = float(value)
        total_float = float(total)
        return round(value_float * 100.0 / total_float, 1)