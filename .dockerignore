# Git 相關
.git
.gitignore
.github/

# Python 相關
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虛擬環境
venv/
env/
ENV/
.venv/
.env/
.ENV/

# IDE 相關
.vscode/
.idea/
*.swp
*.swo
*~
.editorconfig

# 作業系統相關
.DS_Store
Thumbs.db
*.db
desktop.ini

# Logs 相關
logs/
*.log
npm-debug.log*

# 快取相關
.mypy_cache/
.pytest_cache/
.ruff_cache/
__pycache__/
.coverage
htmlcov/
.tox/

# Docker 相關
Dockerfile
docker-compose*.yml
.dockerignore
DOCKER_README.md
docker-manage.bat

# 環境文件
.env
.env.*
.env.example

# Batch 文件和测试文件
*.bat
測試.bat
運行.bat
*.cmd
*.ps1
*.sh

# 資料庫相關
dump.rdb
# 保留 schema.sql 用於資料庫初始化
# *.sql

# 其他
node_modules/
package-lock.json
package.json
cursor_referral_codes.json
memory-bank/
DICKPK_Project_Description.md
README.md

# 臨時文件夾
temp/
tmp/
.tmp/
image_processing/temp/

# 大型資料目錄 
downloaded_gacha_master_cards/

# 測試目錄和文件
tests/
test/
__tests__/
pytest.ini
.pytest_cache/ 