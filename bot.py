import subprocess
import sys
import time
from typing import Optional, Tuple

sys.path.append(".")
import os

import discord
from discord.ext import commands
from dotenv import load_dotenv

# 最早載入環境變數，在導入 logger 前
dotenv_path = os.path.join(os.path.dirname(__file__), '.env')
if not os.path.exists(dotenv_path):
    dotenv_path = os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), ".")), '.env')

if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path=dotenv_path, override=True)
    print(f"環境變數文件位置: {dotenv_path}")
else:
    print(f".env 文件未在預期位置找到: {dotenv_path}")

# 環境變數載入後才導入使用 logger 的模組
# utils.logger 會在導入時自動初始化日誌系統
from database.postgresql.async_manager import AsyncPgManager
from utils.logger import logger # 這裡會觸發 utils/logger.py 中的日誌初始化
from command_registry import get_command_registry
from reaction_utils import load_reaction_count
# Import PlaywrightManager
from utils.playwright_manager import PlaywrightManager

# 記錄環境變數已載入的信息
logger.info("環境變數已載入完成")

# 設定機器人 intents
intents = discord.Intents.default()
intents.messages = True
intents.guilds = True
intents.message_content = True
intents.reactions = True
intents.members = True

# 創建機器人實例
bot = commands.AutoShardedBot(command_prefix="$$", intents=intents, shard_count=2)

# ServiceProxy 類別已移除，因為未使用且服務直接附加到 bot 物件

# 全局變量，用於存儲市場統計消費者進程引用
market_stats_consumer_process: Optional[subprocess.Popen] = None

@bot.tree.error
async def on_app_command_error(interaction, error):
    """應用命令錯誤處理"""
    command_name = interaction.command.name if interaction.command else 'UnknownCommand'
    user_info = f"'{interaction.user}' (ID: {interaction.user.id})"
    logger.error(
        f"App Command Error: {str(error)} for command '{command_name}' by user {user_info}",
        exc_info=True
    )
    
    error_msg = f"執行指令時發生錯誤: {str(error)}"
    if interaction.response.is_done():
        await interaction.followup.send(error_msg, ephemeral=True)
    else:
        await interaction.response.send_message(error_msg, ephemeral=True)

@bot.event
async def on_message(message):
    """訊息事件處理"""
    if message.author == bot.user:
        return
    await bot.process_commands(message)

# Define setup_hook for async initialization before on_ready
async def setup_hook():
    """異步設置鉤子 - 在 on_ready 之前調用"""
    logger.info("== 開始異步設置 ==")
    
    # 初始化數據庫連接池
    try:
        await AsyncPgManager.initialize_pool()
        pool = AsyncPgManager.get_pool()
        bot.pool = pool
        pool_status = "有效" if pool and not pool.is_closing() else "無效或已關閉"
        logger.info(f"Asyncpg 連接池已初始化。狀態: {pool_status}")
        bot.db_initialized = pool is not None and not pool.is_closing()
    except Exception as db_e:
        logger.error(f"Asyncpg 連接池初始化失敗: {db_e}", exc_info=True)
        bot.db_initialized = False
        return

    # 初始化 Redis Manager
    try:
        from database.redis.config import is_redis_enabled
        from database.redis.manager import RedisManager
        import os
        
        # 檢查環境變數中 Redis 是否啟用
        if is_redis_enabled():
            logger.info("Redis 已啟用，正在初始化 RedisManager...")
            # 從環境變數獲取 Redis 配置
            redis_url = os.environ.get('REDIS_URL')
            redis_host = os.environ.get('REDIS_HOST', 'localhost')
            redis_port = int(os.environ.get('REDIS_PORT', '6379'))
            redis_db = int(os.environ.get('REDIS_DB', '0'))
            redis_password = os.environ.get('REDIS_PASSWORD', '')
            
            # 初始化 RedisManager (使用 URL 或個別參數)
            if redis_url:
                redis_manager = RedisManager(url=redis_url)
                logger.info(f"使用提供的 REDIS_URL 初始化")
            else:
                redis_manager = RedisManager(
                    host=redis_host,
                    port=redis_port,
                    db=redis_db,
                    password=redis_password if redis_password else None
                )
                logger.info(f"使用 REDIS_HOST/PORT/DB 參數初始化")
                
            await redis_manager.async_init()
            bot.redis_manager = redis_manager
            logger.info(f"Redis Manager 初始化完成。連接狀態: {redis_manager.is_connected}")
        else:
            logger.info("Redis 已禁用，跳過 RedisManager 初始化")
            bot.redis_manager = None
    except Exception as redis_e:
        logger.error(f"Redis Manager 初始化失敗: {redis_e}", exc_info=True)
        bot.redis_manager = None

    # 初始化 Playwright Manager
    try:
        logger.info("正在初始化 PlaywrightManager...")
        playwright_manager = PlaywrightManager() # Can configure headless/browser type here if needed
        await playwright_manager.initialize()
        bot.playwright_manager = playwright_manager # Attach manager to bot instance
        logger.info("PlaywrightManager 初始化完成。")
    except Exception as pw_e:
        logger.error(f"PlaywrightManager 初始化失敗: {pw_e}", exc_info=True)
        bot.playwright_manager = None # Ensure it's None on failure

    # 使用 GachaSystem 作為統一的服務管理器
    try:
        from gacha.gacha_system import gacha_system
        
        logger.info("正在使用 GachaSystem 初始化所有服務...")
        success = await gacha_system.initialize(bot=bot)
        
        if success:
            bot.gacha_system = gacha_system
            logger.info("GachaSystem 初始化成功，所有服務已準備就緒")

            # ServiceProxy 實例化已移除
            
            # 直接設置常用服務屬性到 bot 實例
            service_mappings = {
                'economy_service': 'economy',
                'collection_service': 'collection', 
                'gacha_service': 'gacha',
                'wish_service': 'wish',
                'card_status_service': 'card_status',
                'draw_engine_service': 'draw_engine',
                'validation_service': 'validation',
                'user_service': 'user',
                'leaderboard_service': 'leaderboard',
                'market_price_service': 'market_price',
                'shop_service': 'shop',
                'blackjack_game_service': 'blackjack_game',
                'dice_game_service': 'dice_game',
                'mines_service': 'mines',
                'market_view_service': 'market_view',
                'stock_trading_service': 'stock_trading',
                'price_update_service': 'price_update',
                'game_display_service': 'game_display',
                'encyclopedia_service': 'encyclopedia',
                'favorite_service': 'favorite',
                'sorting_service': 'sorting',
                'star_enhancement_service': 'star_enhancement',
                'profile_service': 'profile',
                'draw_notifier': 'draw_notifier',
                'ai_service': 'ai_service',
                'prompt_service': 'prompt_service',
                'redis_service_management': 'redis_service_management',
                'scheduled_task_orchestrator': 'scheduled_task_orchestrator'
            }
            
            for attr_name, service_key in service_mappings.items():
                service_instance = bot.gacha_system.get_service(service_key)
                if service_instance:
                    setattr(bot, attr_name, service_instance)
                    logger.debug(f"服務 {service_key} 已作為 bot.{attr_name} 設置")
                else:
                    logger.warning(f"未能獲取服務 {service_key}，無法設置 bot.{attr_name}")
            
            # 設置常用倉庫屬性
            repo_mappings = {
                'user_repo': 'user',
                'master_card_repo': 'master_card',
                'user_collection_repo': 'user_collection',
                'leaderboard_repo': 'leaderboard'
            }
            
            for attr_name, repo_name in repo_mappings.items():
                repo = bot.gacha_system.get_repository(repo_name)
                if repo:
                    setattr(bot, attr_name, repo)
        else:
            logger.error("GachaSystem 初始化失敗")
            bot.db_initialized = False
            return
            
    except Exception as gacha_init_e:
        logger.error(f"GachaSystem 初始化失敗: {gacha_init_e}", exc_info=True)
        bot.db_initialized = False
        return

    logger.info("所有核心服務初始化完成")
    
    # 啟動後初始化服務
    try:
        # 啟動 PriceUpdateService worker
        price_update_service = getattr(bot, 'price_update_service', None) # 從 bot 物件直接獲取
        if price_update_service:
            logger.info("正在啟動 PriceUpdateService...")
            await price_update_service.start()
            logger.info("PriceUpdateService worker 已啟動")

        # 初始化市場統計
        logger.info("正在執行市場統計初始化...")
        import scripts.initialize_market_stats
        await scripts.initialize_market_stats.main()
        logger.info("市場統計初始化完成")
            
    except Exception as startup_e:
        logger.error(f"啟動服務時發生錯誤: {startup_e}", exc_info=True)

# Register the setup hook
bot.setup_hook = setup_hook


# 修改為使用 async 初始化
@bot.event
async def on_ready():
    """當機器人準備就緒時觸發"""
    logger.info("== Bot Ready ==")
    logger.info(f"Logged in as: {bot.user.name} (ID: {bot.user.id})")
    logger.info(f"Discord.py Version: {discord.__version__}")
    logger.info(f"Shard ID: {bot.shard_id if bot.shard_id is not None else 'N/A'}")

    load_reaction_count()
    logger.info("Reaction counts loaded.")

    db_status = "OK" if getattr(bot, "db_initialized", False) else "ERROR"
    logger.info(f"Database connection status: {db_status}")

    if hasattr(bot, 'pool') and bot.pool:
        pool_status = '有效' if not bot.pool.is_closing() else '無效或已關閉'
        logger.info(f"Database pool status: {pool_status}")

    # 註冊所有命令
    registry = get_command_registry(bot)
    await registry.register_all_commands()

    report = registry.get_registration_report()
    status = report.get("status", {})

    logger.info("== Command Registration Status ==")
    for system, is_registered in status.items():
        status_msg = "OK" if is_registered else "ERROR"
        logger.info(f"Command system '{system}': {status_msg}")

    overall_status = "OK" if report.get('overall_success', False) else "ERROR"
    logger.info(f"Overall command registration: {overall_status}")

    # 啟動市場統計消費者服務
    await start_market_stats_consumer()

@bot.event
async def on_resumed():
    logger.info("== Session RESUMED ==")
    logger.info("Attempting to re-initialize/check GachaSystem after session resumption...")
    try:
        if hasattr(bot, 'gacha_system') and bot.gacha_system:
            # 确保 GachaSystem 的 initialize 方法是幂等的或可以安全地重新调用
            success = await bot.gacha_system.initialize(bot=bot) 
            if success:
                logger.info("GachaSystem re-initialization/check successful after RESUMED.")
                # 可选：如果服务重新初始化可能影响指令的可用性，
                # 并且 CommandRegistry 设计为可以安全地重新注册，则可以考虑重新注册。
                # logger.info("Attempting to re-register commands after RESUMED...")
                # registry = get_command_registry(bot)
                # await registry.register_all_commands()
                # logger.info("Commands re-registered after RESUMED.")
            else:
                # GachaSystem.initialize() 返回 False，表示重新初始化/检查未完全成功
                logger.warning("GachaSystem re-initialization/check reported failure (returned False) after RESUMED.")
        else:
            logger.error("GachaSystem not found on bot object or not initialized during RESUMED event. Cannot re-initialize.")
    except Exception as e:
        logger.error(f"Error during GachaSystem re-initialization/check after RESUMED: {e}", exc_info=True)

def _check_database_status(bot_instance: commands.Bot) -> Tuple[str, str]:
    """檢查數據庫狀態並返回狀態字符串和詳細信息"""
    try:
        db_pool_instance = getattr(bot_instance, 'pool', None)
        db_ok = db_pool_instance is not None and not db_pool_instance.is_closing()
        status = "✅ 正常" if db_ok else "❌ 異常"
        details = f"• Database Pool: {'✅' if db_ok else '❌'}"
    except Exception as db_err:
        status = "❌ 異常"
        details = f"• Database Pool: ❌ ({db_err})"
    return status, details

def _check_gacha_system_status(bot_instance: commands.Bot) -> Tuple[str, str]:
    """檢查 GachaSystem 狀態並返回狀態字符串和詳細信息"""
    try:
        gacha_system_ok = (hasattr(bot_instance, 'gacha_system') and 
                           bot_instance.gacha_system and 
                           bot_instance.gacha_system.is_initialized)
        status = "✅ 正常" if gacha_system_ok else "❌ 異常"
        
        if gacha_system_ok:
            service_count = len(bot_instance.gacha_system.services)
            repo_count = len(bot_instance.gacha_system.repositories)
            details = (
                f"• GachaSystem: ✅\\n"
                f"• 服務數量: {service_count}\\n"
                f"• 倉庫數量: {repo_count}"
            )
        else:
            details = "• GachaSystem: ❌ 未初始化"
    except Exception as gacha_err:
        status = "❌ 異常"
        details = f"• GachaSystem: ❌ ({gacha_err})"
    return status, details

def _check_redis_status(bot_instance: commands.Bot) -> Tuple[str, str]:
    """檢查 Redis 狀態並返回狀態字符串和詳細信息"""
    try:
        redis_ok = (hasattr(bot_instance, 'redis_manager') and 
                    bot_instance.redis_manager and 
                    bot_instance.redis_manager.is_connected)
        status = "✅ 正常" if redis_ok else "❌ 離線"
        details = f"• Redis: {'✅' if redis_ok else '❌'}"
    except Exception as redis_err:
        status = "❌ 異常"
        details = f"• Redis: ❌ ({redis_err})"
    return status, details

def _check_command_registration_status(bot_instance: commands.Bot) -> Tuple[str, str, bool]:
    """檢查命令註冊狀態並返回狀態字符串、詳細信息和成功標誌"""
    command_registry = get_command_registry(bot_instance)
    command_report = command_registry.get_registration_report()
    
    if command_report:
        cmd_registration_ok = all(s for s in command_report.get("status", {}).values())
        status = "✅ 正常" if cmd_registration_ok else "❌ 異常"
        status_items = command_report.get("status", {}).items()
        cmd_details_list = [f"• {k}: {'✅' if v else '❌'}" for k, v in status_items]
        details = "\\n".join(cmd_details_list) if cmd_details_list else "無詳細信息"
    else:
        status = "❌ 異常"
        details = "報告不可用"
        cmd_registration_ok = False
    return status, details, cmd_registration_ok

@bot.command()
async def health(ctx):
    """檢查系統健康狀態"""
    embed = discord.Embed(title="系統健康狀態報告", color=0x00ff00)

    db_status_str, db_details = _check_database_status(bot)
    embed.add_field(name=f"數據庫狀態: {db_status_str}", value=db_details, inline=False)

    gacha_status_str, gacha_details = _check_gacha_system_status(bot)
    embed.add_field(name=f"服務架構: {gacha_status_str}", value=gacha_details, inline=False)

    redis_status_str, redis_details = _check_redis_status(bot)
    embed.add_field(name=f"緩存狀態: {redis_status_str}", value=redis_details, inline=False)

    cmd_status_str, cmd_details, cmd_registration_ok = _check_command_registration_status(bot)
    embed.add_field(name=f"命令註冊狀態: {cmd_status_str}", value=cmd_details, inline=False)

    # 整體狀態評估
    # Re-evaluate db_ok and gacha_system_ok based on the return values of helper functions
    db_ok = "✅" in db_status_str
    gacha_system_ok = "✅" in gacha_status_str
    redis_ok = "✅" in redis_status_str # Added Redis to overall status

    overall_ok = db_ok and gacha_system_ok and cmd_registration_ok and redis_ok
    overall_status_msg = "✅ 系統健康" if overall_ok else "⚠️ 系統部分異常"
    embed.add_field(name="整體狀態", value=overall_status_msg, inline=False)

    # 機器人運行時間
    uptime = time.time() - bot.uptime if hasattr(bot, "uptime") else 0
    hours, remainder = divmod(int(uptime), 3600)
    minutes, seconds = divmod(remainder, 60)
    uptime_str = f"{hours}小時 {minutes}分鐘 {seconds}秒"

    bot_info = (
        f"• 延遲: {bot.latency * 1000:.2f}ms\\n"
        f"• 運行時間: {uptime_str}\\n"
        f"• 已連接伺服器: {len(bot.guilds)}"
    )
    embed.add_field(name="機器人狀態", value=bot_info, inline=False)

    await ctx.send(embed=embed)


@bot.command()
async def ratelimit(ctx):
    """顯示當前的 API 速率限制狀態"""
    shard_id = bot.shard_id if hasattr(bot, "shard_id") else None
    embed = discord.Embed(title="Discord API 速率限制狀態", color=0x00aaff)
    shard_info = f"當前分片ID: {shard_id}\n總分片數: {bot.shard_count}"
    embed.add_field(name="機器人分片信息", value=shard_info, inline=False)
    embed.add_field(name="速率限制記錄", value="尚無記錄", inline=False)
    embed.add_field(name="機器人延遲", value=f"{bot.latency * 1000:.2f}ms", inline=False)
    await ctx.send(embed=embed)


@bot.event
async def on_close():
    """處理機器人關閉事件"""
    logger.info("機器人正在關閉...")
    
    # 關閉 GachaSystem (這會處理 DrawNotifier 等服務的 session 關閉)
    if hasattr(bot, 'gacha_system') and bot.gacha_system.is_initialized:
        try:
            logger.info("正在關閉 GachaSystem...")
            await bot.gacha_system.shutdown()
            logger.info("GachaSystem 已成功關閉。")
        except Exception as e:
            logger.error("關閉 GachaSystem 時發生錯誤: %s", e, exc_info=True)
    
    # 關閉 Playwright Manager
    if hasattr(bot, 'playwright_manager') and bot.playwright_manager:
        try:
            logger.info("正在關閉 PlaywrightManager...")
            await bot.playwright_manager.close()
            logger.info("PlaywrightManager 已成功關閉。")
        except AttributeError:
            logger.warning("PlaywrightManager 沒有可用的 close 方法。")
        except Exception as e:
            logger.error("關閉 PlaywrightManager 時發生錯誤: %s", e, exc_info=True)

    # 關閉 Redis Manager
    if hasattr(bot, 'redis_manager') and bot.redis_manager:
        try:
            logger.info("正在關閉 RedisManager...")
            await bot.redis_manager.close()
            logger.info("RedisManager 已成功關閉。")
        except AttributeError:
            logger.warning("RedisManager 沒有可用的 close 方法。")
        except Exception as e:
            logger.error("關閉 RedisManager 時發生錯誤: %s", e, exc_info=True)

    # 關閉數據庫連接池
    if hasattr(bot, 'pool') and bot.pool:
        try:
            logger.info("正在關閉數據庫連接池...")
            await bot.pool.close()
            logger.info("數據庫連接池已成功關閉。")
        except Exception as e:
            logger.error("關閉數據庫連接池時發生錯誤: %s", e, exc_info=True)

    # 停止市場統計消費者進程 (如果正在運行)
    await stop_market_stats_consumer()
    
    logger.info("機器人關閉程序完成。")

async def stop_market_stats_consumer():
    """停止市場統計消費者服務"""
    global market_stats_consumer_process
    
    if market_stats_consumer_process:
        try:
            if sys.platform == "win32":
                subprocess.run(
                    ["taskkill", "/F", "/T", "/PID", str(market_stats_consumer_process.pid)],
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE
                )
                logger.info("[MarketStatsConsumer] 已發送終止命令")
            else:
                market_stats_consumer_process.terminate()
                try:
                    market_stats_consumer_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    market_stats_consumer_process.kill()
                pid = market_stats_consumer_process.pid
                logger.info(f"[MarketStatsConsumer] 服務已停止，PID: {pid}")
            
            market_stats_consumer_process = None
        except Exception as e:
            logger.error(f"[MarketStatsConsumer] 停止服務時出錯: {e}", exc_info=True)

async def start_market_stats_consumer():
    """啟動市場統計消費者服務作為子進程"""
    global market_stats_consumer_process
    
    if market_stats_consumer_process and market_stats_consumer_process.poll() is None:
        logger.info("[MarketStatsConsumer] 服務已在運行中")
        return
    
    try:
        python_executable = sys.executable
        
        if sys.platform == "win32":
            main_pid = os.getpid()
            
            import tempfile
            
            bat_content = f"""@echo off
chcp 65001 > nul
title Market Stats Service
echo Starting Market Stats Service...

echo.
echo [1] Setting up environment...
set PYTHONUNBUFFERED=1

echo.
echo [2] Running market_stats_consumer...
echo ----------------------------------------
\"{python_executable}\" -m gacha.services.market.market_stats_consumer
echo ----------------------------------------

echo.
echo Service ended
REM pause
"""
            # 創建臨時批處理文件
            with tempfile.NamedTemporaryFile(mode='w', suffix=".bat", delete=False, encoding='utf-8') as tmp_bat_file:
                tmp_bat_file.write(bat_content)
                bat_path = tmp_bat_file.name
            
            logger.info(f"[MarketStatsConsumer] Temporary batch file created at: {bat_path}")

            # 检查文件是否存在
            if not os.path.exists(bat_path):
                logger.error(f"[MarketStatsConsumer] CRITICAL: Batch file {bat_path} does not exist after creation!")
                return # 如果文件不存在，则不继续执行
            logger.info(f"[MarketStatsConsumer] Batch file {bat_path} confirmed to exist.")
            
            # 新的执行方式：直接用 cmd /c 执行批处理文件
            popen_args = ['cmd', '/c', bat_path]
            logger.info(f"[MarketStatsConsumer] Args for Popen (direct cmd /c): {popen_args}")

            market_stats_consumer_process = subprocess.Popen(
                popen_args, # 修改这里
                shell=False, 
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
            logger.info(
                f"[MarketStatsConsumer] 服務已在新控制台啟動，"
                f"使用臨時批處理文件: {bat_path}"
            )
            # Consider scheduling bat_path for deletion after the process ends or on bot shutdown
            # For simplicity, leaving it as is, but in a long-running app, cleanup is good.
        else:
            cmd = [
                python_executable, 
                "-m", 
                "gacha.services.market.market_stats_consumer"
            ]
            market_stats_consumer_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
    except Exception as e:
        logger.error(f"[MarketStatsConsumer] 啟動服務時出錯: {e}", exc_info=True)

# 啟動機器人
if __name__ == "__main__":
    bot.uptime = time.time()
    token = os.getenv("DISCORD_TOKEN")
    if not token:
        logger.critical(
            "錯誤：找不到 DISCORD_TOKEN 環境變數，"
            "請檢查 .env 文件。機器人無法啟動。"
        )
        sys.exit(1)

    # 註冊程序退出處理函數
    import atexit

    def cleanup_on_exit():
        """程序退出時執行的清理函數"""
        logger.info("程序退出，清理資源...")
        if market_stats_consumer_process:
            try:
                if sys.platform == "win32":
                    subprocess.run(
                        ["taskkill", "/F", "/T", "/PID", str(market_stats_consumer_process.pid)],
                        stdout=subprocess.PIPE, 
                        stderr=subprocess.PIPE
                    )
                else:
                    market_stats_consumer_process.terminate()
                logger.info("[MarketStatsConsumer] 服務已在程序退出時停止")
            except Exception as e:
                logger.error(f"[MarketStatsConsumer] 停止服務時出錯: {e}")

    atexit.register(cleanup_on_exit)

    try:
        logger.info("正在啟動機器人...")
        logger.info(f"機器人將使用 {bot.shard_count} 個分片運行")
        shard_mode = '自動' if isinstance(bot, commands.AutoShardedBot) else '手動'
        logger.info(f"分片模式: {shard_mode}")
        bot.run(token)
    except KeyboardInterrupt:
        logger.info("收到鍵盤中斷信號，正在關閉機器人...")
        cleanup_on_exit()
    except Exception:
        logger.exception("啟動機器人時發生未預期的錯誤")
