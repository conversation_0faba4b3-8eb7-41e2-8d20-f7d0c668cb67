from __future__ import annotations
'\nGacha系統抽卡決策引擎服務\n\n負責處理抽卡過程中「決定抽取哪張卡片」的核心演算法和邏輯。專注於：\n- 根據卡池配置和稀有度確率決定抽取的卡片\n- 處理許願邏輯和權重計算\n- 提供隨機選擇卡片的功能\n\n此服務不處理用戶數據、收藏或數據庫事務，僅專注於抽卡決策邏輯。\n'
import asyncpg
import random
import time
from typing import Dict, List, Optional, Tuple, Any, Set, Union
from utils.logger import logger
from gacha.models.models import Card
from gacha.repositories.card.master_card_repository import MasterCardRepository
from gacha.constants import RarityLevel
from gacha.services.core.wish_service import WishService
from gacha.utils.random_utils import select_from_weighted_list
from gacha.app_config import config_service
from gacha.exceptions import GachaSystemError

class DrawEngineService:
    """
    抽卡引擎服務，處理抽卡的核心決策邏輯

    負責：
    1. 決定抽取卡片的稀有度
    2. 根據卡池配置選擇卡池
    3. 應用許願邏輯
    4. 從數據庫獲取符合條件的卡片
    """

    def __init__(self, card_repo: MasterCardRepository, wish_service: Optional[WishService]=None):
        """初始化抽卡引擎服務"""
        if card_repo is None:
            err_msg = 'DrawEngineService 初始化失敗：必須提供 MasterCardRepository 實例。'
            logger.error(err_msg)
            raise ValueError(err_msg)
        self.card_repo = card_repo
        self.wish_service = wish_service

    async def determine_cards_to_draw(self, user_id: int, pool_types: List[str], count: int) -> Tuple[List[Dict[str, Any]], List[int]]:
        """
        決定要抽取的卡片列表

        Args:
            user_id: 用戶ID
            pool_types: 卡池類型列表
            count: 抽卡數量

        Returns:
            Tuple[List[Dict[str, Any]], List[int]]:
                第一個元素是卡片信息列表，每個元素是字典 {"card": Card, "pool_type": str}
                第二個元素是卡片ID列表
        """
        if not pool_types:
            logger.error('[DRAW_ENGINE] 用戶 %s 抽卡時未提供有效的卡池類型', user_id)
            return ([], [])
        if count <= 0:
            logger.error('[DRAW_ENGINE] 用戶 %s 抽卡時提供了無效的抽卡數量: %s', user_id, count)
            return ([], [])
        user_wish_info = await self._get_user_wish_info(user_id)
        user_wish_power_level = user_wish_info.get('power_level', 0)
        wish_cards_by_rarity = user_wish_info.get('wish_cards_by_rarity', {})
        draw_decisions = []
        all_drawn_card_ids = []
        for _ in range(count):
            selected_pool = self._select_pool_type(pool_types)
            selected_rarity = self._select_rarity(selected_pool)
            wished_card_ids = wish_cards_by_rarity.get(selected_rarity, [])
            card_id, is_wish = await self._apply_wish_logic_and_select_card(user_id=user_id, rarity=selected_rarity, pool_type=selected_pool, wish_power_level=user_wish_power_level, wished_card_ids=wished_card_ids)
            if card_id:
                all_drawn_card_ids.append(card_id)
                draw_decisions.append({'card_id': card_id, 'pool_type': selected_pool, 'is_wish': is_wish})
        final_results = await self._get_cards_details(draw_decisions)
        return (final_results, all_drawn_card_ids)

    async def _get_user_wish_info(self, user_id: int) -> Dict[str, Any]:
        """獲取用戶許願信息"""
        result = {'power_level': 0, 'wish_cards_by_rarity': {}}
        if not self.wish_service:
            return result
        try:
            power_level = await self.wish_service.user_wish_repo.get_user_wish_power_level(user_id)
            result['power_level'] = power_level
            wish_cards = await self.wish_service.user_wish_repo.get_all_user_wishes_with_rarity_grouped(user_id)
            result['wish_cards_by_rarity'] = wish_cards
        except Exception as e:
            logger.warning('[DRAW_ENGINE] 獲取用戶 %s 許願信息失敗: %s', user_id, e, exc_info=True)
        return result

    async def _apply_wish_logic_and_select_card(self, user_id: int, rarity: RarityLevel, pool_type: str, wish_power_level: int, wished_card_ids: List[int]) -> Tuple[Optional[int], bool]:
        """
        應用許願邏輯並選擇一張卡片

        Args:
            user_id: 用戶ID
            rarity: 卡片稀有度
            pool_type: 卡池類型
            wish_power_level: 許願強度
            wished_card_ids: 該稀有度的許願卡片ID列表

        Returns:
            Tuple[Optional[int], bool]: (選中的卡片ID, 是否是許願卡)
        """
        all_card_ids = await self.card_repo.get_all_cards_by_rarity(rarity, [pool_type])
        if not all_card_ids:
            logger.warning('[DRAW_ENGINE] 卡池 %s 中沒有稀有度為 %s 的卡片', pool_type, rarity)
            return (None, False)
        if not self.wish_service or not wished_card_ids:
            return (self._randomly_select_card(all_card_ids), False)
        available_cards_set = set(all_card_ids)
        valid_wish_cards = [cid for cid in wished_card_ids if cid in available_cards_set]
        if not valid_wish_cards:
            return (self._randomly_select_card(all_card_ids), False)
        weighted_cards = self.wish_service.apply_wish_weights(all_card_ids, valid_wish_cards, wish_power_level, lambda x: x)
        selected_card = self.wish_service.select_with_weights(weighted_cards)
        is_wish_card = selected_card in valid_wish_cards if selected_card else False
        if selected_card is None:
            logger.warning('[DRAW_ENGINE] 用戶 %s 的許願選擇邏輯失敗，回退到隨機選擇', user_id)
            return (self._randomly_select_card(all_card_ids), False)
        return (selected_card, is_wish_card)

    async def _get_cards_details(self, draw_decisions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """獲取卡片詳細信息"""
        if not draw_decisions:
            return []
        unique_card_ids = list({decision['card_id'] for decision in draw_decisions if decision['card_id']})
        cards_map = {}
        if unique_card_ids:
            cards = await self.card_repo.get_cards_details_by_ids(unique_card_ids)
            cards_map = {card.card_id: card for card in cards}
        final_results = []
        for decision in draw_decisions:
            card_id = decision['card_id']
            card = cards_map.get(card_id)
            if card:
                final_results.append({'card': card, 'pool_type': decision['pool_type'], 'is_wish': decision.get('is_wish', False)})
        return final_results

    def _select_pool_type(self, valid_pools: List[str]) -> str:
        """
        從有效卡池列表中根據權重選擇一個卡池類型

        Args:
            valid_pools: 有效卡池列表

        Returns:
            選中的卡池類型
        """
        if not valid_pools:
            logger.warning("[DRAW_ENGINE] 未提供有效卡池，默認使用'main'")
            return 'main'
        if len(valid_pools) == 1:
            return valid_pools[0]
        mixed_pool_config = config_service.get_mixed_pool_draw_config()
        if not mixed_pool_config:
            logger.warning('[DRAW_ENGINE] 無法獲取混合池配置，使用第一個有效卡池')
            return valid_pools[0]
        valid_pool_configs = []
        for item in mixed_pool_config:
            pool_name = getattr(item, 'pool_name', None)
            weight = getattr(item, 'probability', None)
            if pool_name and weight and (pool_name in valid_pools):
                valid_pool_configs.append((pool_name, float(weight)))
        if not valid_pool_configs:
            logger.warning('[DRAW_ENGINE] 混合池配置中沒有匹配的有效卡池: %s', valid_pools)
            return valid_pools[0]
        selected_pool = select_from_weighted_list(valid_pool_configs)
        return selected_pool if selected_pool else valid_pools[0]

    def _select_rarity(self, pool_type: str) -> RarityLevel:
        """
        從指定卡池的稀有度配置中選擇一個稀有度

        Args:
            pool_type: 卡池類型

        Returns:
            選中的稀有度
        """
        pool_rarity_config = config_service.get_all_pool_rarity_configs().get(pool_type)
        if not pool_rarity_config:
            logger.warning("[DRAW_ENGINE] 卡池 '%s' 沒有稀有度配置，默認使用COMMON", pool_type)
            return RarityLevel.COMMON
        weighted_items = []
        for item in pool_rarity_config:
            rarity_val = getattr(item, 'rarity_level', None)
            weight = getattr(item, 'probability', None)
            if rarity_val is not None and weight is not None:
                try:
                    weighted_items.append((RarityLevel(rarity_val), float(weight)))
                except (ValueError, TypeError) as e:
                    logger.error("[DRAW_ENGINE] 卡池 '%s' 的稀有度配置無效: %s", pool_type, e)
                    continue
        if not weighted_items:
            logger.error("[DRAW_ENGINE] 卡池 '%s' 沒有有效的稀有度配置項，默認使用COMMON", pool_type)
            return RarityLevel.COMMON
        selected_rarity = select_from_weighted_list(weighted_items)
        return selected_rarity if selected_rarity else RarityLevel.COMMON

    def _randomly_select_card(self, card_ids: List[int]) -> Optional[int]:
        """
        從卡片ID列表中隨機選擇一張卡片

        Args:
            card_ids: 卡片ID列表

        Returns:
            選中的卡片ID，如果列表為空則返回None
        """
        if not card_ids:
            return None
        return random.choice(card_ids)