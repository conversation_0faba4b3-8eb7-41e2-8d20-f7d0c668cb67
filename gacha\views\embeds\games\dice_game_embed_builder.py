# -*- coding: utf-8 -*-
"""
Gacha系統骰子遊戲Embed構建器 - 重構版
提供更簡潔、現代化的視覺設計
"""

from datetime import datetime
from typing import Any, Dict, Optional

import discord

from gacha.views.utils import format_oil  # 導入格式化函數
from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder  # 導入基類


class DiceGameEmbedBuilder(BaseEmbedBuilder):
    """構建骰子大小遊戲嵌入消息的類 - 現代化設計"""

    # 骰子表情符號
    DICE_EMOJI = {
        1: "<:d1:1368848535604170814>",
        2: "<:d2:1368848527744041041>",
        3: "<:d3:1368848539278639205>",
        4: "<:d4:1368848542860574822>",
        5: "<:d5:1368848531699273831>",
        6: "<:d6:1368848546295582810>",
    }

    # 常用表情符號
    EMOJI = {
        "dice": "🎲",
        "win": "💰",
        "lose": "💸",
        "balance": "💳",
        "big": "⬆️",
        "small": "⬇️",
    }

    # 遊戲顏色
    COLORS = {
        "default": 0x2B2D31,  # 深灰色，現代UI風格
        "win": 0x57F287,  # 成功綠
        "lose": 0xED4245,  # 失敗紅
    }

    def __init__(
        self,
        user: discord.User,
        bet: Optional[int] = None,
        game_state: Optional[Dict[str, Any]] = None,
        new_balance: Optional[int] = None,
    ):
        """初始化骰子遊戲Embed構建器

        參數:
            user: Discord用戶對象
            bet: 當前下注金額 (用於遊戲說明Embed)
            game_state: 遊戲狀態 (用於結果Embed)
            new_balance: 新的油幣餘額 (用於結果Embed)
        """
        dice_data = {
            "user": user,
            "bet": bet,
            "game_state": game_state,
            "new_balance": new_balance,
        }
        # DiceGameEmbedBuilder 目前不直接處理 interaction
        super().__init__(data=dice_data)  # 調用基類初始化並傳遞 data
        self.user = user
        self.bet = bet
        self.game_state = game_state
        self.new_balance = new_balance
        self.dice_image_url = "https://cdn.discordapp.com/attachments/1336020673730187334/1368848475118108702/dice.png?ex=6819b6fc&is=6818657c&hm=88bac71216025c6b9347449b863ebf1d9f0647cb1f96ae306d941a097acaf06b&"

    def build_game_embed(self) -> discord.Embed:
        """獲取現代化的遊戲說明Embed

        返回:
            格式化後的Discord Embed對象
        """
        if self.bet is None:
            return self._create_base_embed(  # MODIFIED: _initialize_embed to _create_base_embed
                title="錯誤", description="缺少下注信息", color=discord.Color.red()
            )

        # 使用現代化的設計風格
        embed = discord.Embed(
            color=discord.Color(
                self.COLORS["default"]),
            timestamp=datetime.now())

        # 設置作者欄位，使用用戶頭像
        embed.set_author(
            name=f"{self.user.display_name}",
            icon_url=self.user.display_avatar.url)

        # 設置右上角圖片為骰子圖片
        embed.set_thumbnail(url=self.dice_image_url)

        # 添加標題作為第一個欄位，讓它突出
        embed.add_field(
            name=f"{self.EMOJI['dice']} 骰子大小遊戲",
            value="選擇押注大或小，贏取獎勵！",
            inline=False,
        )

        # 用分隔行創建視覺分組
        embed.add_field(
            name="**┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈**",
            value="**遊戲規則**",
            inline=False)

        # 使用表情符號使規則更視覺化
        embed.add_field(
            name=f"{self.EMOJI['big']} 大", value="骰子點數 4-6", inline=True
        )

        embed.add_field(
            name=f"{self.EMOJI['small']} 小", value="骰子點數 1-3", inline=True
        )

        embed.add_field(name="賠率", value="猜中獲得 1:1 賠率", inline=True)

        # 下注信息分組
        embed.add_field(
            name="**┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈**",
            value="**下注信息**",
            inline=False)

        # 簡潔地顯示下注金額和可能贏取
        embed.add_field(
            name="當前下注", value=format_oil(self.bet, label="下注"), inline=True
        )

        embed.add_field(
            name="可能贏取",
            value=format_oil(
                self.bet * 2,
                label="總計"),
            inline=True)

        # 提示性的頁腳
        embed.set_footer(text="點擊【大】或【小】按鈕開始遊戲")

        return embed

    def build_result_embed(self) -> discord.Embed:
        """創建現代化的遊戲結果Embed

        返回:
            格式化後的Discord Embed對象
        """
        if self.game_state is None:
            return self._create_base_embed(  # MODIFIED: _initialize_embed to _create_base_embed
                title="錯誤", description="缺少遊戲狀態信息", color=discord.Color.red()
            )

        # 獲取遊戲結果資訊
        bet = self.game_state["bet"]
        choice = self.game_state["choice"]
        dice_value = self.game_state["dice_value"]
        is_win = self.game_state["result"] == "win"
        payout = self.game_state["payout"]

        # 準備結果文本
        result_text = "大" if dice_value >= 4 else "小"
        choice_text = "大" if choice == "big" else "小"
        choice_emoji = self.EMOJI["big"] if choice == "big" else self.EMOJI["small"]
        result_emoji = self.EMOJI["win"] if is_win else self.EMOJI["lose"]

        # 顏色根據結果變化
        color = self.COLORS["win"] if is_win else self.COLORS["lose"]

        # 創建embed
        embed = discord.Embed(
            color=discord.Color(color),
            timestamp=datetime.now())

        # 設置作者欄位
        embed.set_author(
            name=f"{self.user.display_name}",
            icon_url=self.user.display_avatar.url)

        # 設置右上角圖片
        embed.set_thumbnail(url=self.dice_image_url)

        # 遊戲結果標題
        title_value = "恭喜你贏了！" if is_win else "可惜你輸了！"
        embed.add_field(
            name=f"{self.EMOJI['dice']} 骰子大小遊戲結果",
            value=f"**{title_value}**",
            inline=False,
        )

        # 結果分隔線
        embed.add_field(
            name="**┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈**",
            value="**擲骰結果**",
            inline=False)

        # 骰子結果，使用比較大的表情符號
        embed.add_field(
            name="骰子點數",
            value=f"{self.DICE_EMOJI.get(dice_value, '🎲')}",
            inline=True,
        )

        # 添加結果分類
        embed.add_field(name="結果", value=f"**{result_text}**", inline=True)

        # 玩家的選擇
        embed.add_field(
            name="你的選擇", value=f"{choice_emoji} **{choice_text}**", inline=True
        )

        # 金錢資訊分隔線
        embed.add_field(
            name="**┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈**",
            value="**金額信息**",
            inline=False)

        # 下注金額
        embed.add_field(
            name="下注金額", value=format_oil(bet, label="下注"), inline=True
        )

        # 輸贏結果
        if is_win:
            profit = payout - bet
            embed.add_field(
                name=f"{self.EMOJI['win']} 贏得",
                value=format_oil(profit),
                inline=True)
        else:
            embed.add_field(
                name=f"{self.EMOJI['lose']} 輸掉",
                value=format_oil(bet),
                inline=True)

        # 餘額顯示
        if self.new_balance is not None:
            embed.add_field(
                name=f"{self.EMOJI['balance']} 當前餘額",
                value=format_oil(self.new_balance, label="餘額"),
                inline=True,
            )

        # 添加頁腳
        footer_text = "再玩一局？點擊下方按鈕繼續玩" if is_win else "別灰心！再來一局吧"
        embed.set_footer(text=footer_text)

        return embed

    def build_embed(self) -> discord.Embed:
        """
        根據初始化時提供的參數，構建適當的 Embed。
        這是對 BaseEmbedBuilder 中抽象方法的實現。
        """
        if self.game_state is not None:
            # 如果有遊戲狀態，構建結果 Embed
            return self.build_result_embed()
        elif self.bet is not None:
            # 如果只有下注金額，構建遊戲說明 Embed
            return self.build_game_embed()
        else:
            # 如果兩者都沒有，返回一個錯誤或預設 Embed
            return self._create_base_embed(  # MODIFIED: _initialize_embed to _create_base_embed
                title="錯誤",
                description="缺少必要的遊戲信息來構建 Embed。",
                color=discord.Color.red(),
            )
