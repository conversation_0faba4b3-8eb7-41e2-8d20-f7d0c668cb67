# -*- coding: utf-8 -*-
"""
Gacha系統21點遊戲Embed構建器
"""

from datetime import datetime
import re
from typing import Any, Dict, Optional

import discord
from utils.logger import logger


# 導入 format_oil 和卡牌 Emoji 常數
from gacha.views.utils import format_oil

# 修改導入路徑為絕對路徑
from gacha.services.games.blackjack_service import HIDDEN_CARD_EMOJI


from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder  # 導入基類


def _format_hand_value(hand_state: Dict[str, Any]) -> str:
    """格式化手牌點數顯示 (例如 19 或 19/(9))"""
    hard_value = hand_state.get("hard_value", 0)
    soft_value = hand_state.get("soft_value", 0)  # 從 game_state 獲取 soft_value

    # 檢查手牌中是否有 Ace
    has_ace = any(
        card.endswith("1>") for card in hand_state.get("cards", [])
    )  # 簡易檢查是否為 Ace Emoji

    # 只有在有 Ace 且 soft_value 與 hard_value 不同且 hard_value <= 21 時顯示 soft_value
    if has_ace and soft_value != hard_value and hard_value <= 21 and soft_value > 0:
        calculated_soft_value = soft_value  # 暫用 game_state 的 soft_value
        if (
            calculated_soft_value != hard_value and calculated_soft_value > 0
        ):  # 確保 soft_value 有意義
            return f"{hard_value}/({calculated_soft_value})"  # 顯示 hard/(soft)
        else:
            return str(
                hard_value
            )  # 如果 soft value 等於 hard value 或無意義，只顯示 hard value
    else:
        return str(hard_value)  # 其他情況只顯示 hard_value


def _get_card_value(card_emoji: str) -> int:
    """從卡牌Emoji中獲取點數

    參數:
        card_emoji: 卡牌的Emoji字符串，例如 <:S10:1367810459662684259>

    返回:
        卡牌的點數值 (1-11)
    """
    # 使用正則表達式從Emoji提取牌值數字
    # 格式: <:S10:1367810459662684259> 我們需要提取中間的數字部分
    match = re.search(r":([SHDC])(\d+):", card_emoji)
    if not match:
        return 0

    value = match.group(2)
    if value == "1":  # A
        return 11
    elif value in ("11", "12", "13"):  # J, Q, K
        return 10
    else:
        # 數字牌2-10
        return int(value)


class BlackjackEmbedBuilder(BaseEmbedBuilder):  # 繼承基類
    """構建21點遊戲嵌入消息的類"""

    # 添加21點遊戲圖片URL常量
    BLACKJACK_IMAGE_URL = "https://cdn.discordapp.com/attachments/1336020673730187334/1367925599309664366/blackjack.png?ex=68165b7d&is=681509fd&hm=923f3b744db190f5efd2f6a820218f4823a3b7ae48eb7fc1bfe098fae8cf43d6&"

    def __init__(
        self,
        game_state: Dict[str, Any],
        user: discord.User,
        balance: Optional[int] = None,
    ):
        """初始化21點Embed構建器

        參數:
            game_state: 遊戲狀態
            user: Discord用戶對象
            balance: 玩家當前餘額 (可選)
        """
        blackjack_data = {"game_state": game_state, "user": user, "balance": balance}
        # BlackjackEmbedBuilder 目前不直接處理 interaction
        super().__init__(data=blackjack_data)  # 調用基類初始化並傳遞 data
        self.game_state = game_state
        self.user = user
        self.balance = balance  # 儲存餘額

    def build_embed(self) -> discord.Embed:  # 重命名方法以符合基類要求
        """創建遊戲狀態的Embed (進行中或結束)

        返回:
            格式化後的Discord Embed對象
            
        Raises:
            GachaRuntimeError: 當遊戲狀態缺少關鍵數據時
        """
        # 安全地獲取遊戲狀態，如果缺少則使用預設值
        player_hand = self.game_state.get("player_hand", {})
        dealer_hand = self.game_state.get("dealer_hand", {})
        bet = self.game_state.get("bet", 0)
        game_over = self.game_state.get("game_over", False)
        result = self.game_state.get("result")
        payout = self.game_state.get("payout", 0)

        # 如果關鍵數據缺失，直接拋出異常
        if not player_hand:
            from gacha.exceptions import GachaRuntimeError
            raise GachaRuntimeError("遊戲狀態缺少玩家手牌數據")
            
        if not dealer_hand:
            from gacha.exceptions import GachaRuntimeError
            raise GachaRuntimeError("遊戲狀態缺少莊家手牌數據")

        title = "21點遊戲"
        color = self.DEFAULT_EMBED_COLOR  # MODIFIED: 使用基類默認顏色
        result_emoji = ""
        description_content = []  # 用列表儲存描述行

        win_loss_amount = payout - bet
        old_balance = 0
        if self.balance is not None:
            old_balance = self.balance - win_loss_amount

        loss_percentage = 0.0
        # 避免 old_balance 為 0 的情況
        if old_balance > 0 and win_loss_amount < 0:
            loss_percentage = bet / old_balance

        # --- 決定標題和顏色 ---
        if game_over:
            color = self._get_result_color(result)  # 使用輔助方法獲取顏色
            if result == "blackjack":
                result_emoji = "👑✨"
                win_amount_str = (
                    format_oil(win_loss_amount, label="贏得")
                    if win_loss_amount > 0
                    else format_oil(0, label="贏得")
                )
                title = f"{result_emoji} **BLACKJACK!** {win_amount_str}"
            elif win_loss_amount > 0:  # 一般贏
                result_emoji = "✅💰"
                win_amount_str = format_oil(win_loss_amount, label="贏得")
                title = f"{result_emoji} **獲勝!** {win_amount_str}"
            elif win_loss_amount == 0:  # 平局
                result_emoji = "🔄"
                bet_amount_str = format_oil(bet, label="返還")
                title = f"{result_emoji} **平局!** {bet_amount_str}"
            else:  # 輸
                loss_amount_str = format_oil(win_loss_amount, label="輸掉")
                if bet > 0 and old_balance > 0 and loss_percentage >= 0.1:  # 慘輸
                    result_emoji = "💀💸"
                    title = f"{result_emoji} **慘敗!** {loss_amount_str}"
                else:  # 一般輸
                    result_emoji = "❌"
                    title = f"{result_emoji} **失敗!** {loss_amount_str}"
        else:  # 遊戲進行中
            title = "⏱️ 21點遊戲進行中"

        # --- 構建描述 ---
        # 添加遊戲結果摘要 (僅遊戲結束時)
        if game_over and result:
            result_summary = self._get_result_summary(result)  # 使用輔助方法
            if result_summary:
                description_content.append(result_summary)
                description_content.append("─────────────────────")

        # 莊家手牌
        description_content.append("**莊家手牌:**")
        dealer_cards_list = dealer_hand.get("cards", [])
        if dealer_hand.get("show_first_only", False) and not game_over:
            first_card = dealer_cards_list[0] if dealer_cards_list else ""
            dealer_value_str = str(_get_card_value(first_card)) if first_card else "0"
            dealer_cards_str = (
                f"{dealer_cards_list[0]} {HIDDEN_CARD_EMOJI}"
                if dealer_cards_list
                else HIDDEN_CARD_EMOJI
            )
        else:
            dealer_value_str = str(dealer_hand.get("hard_value", "?"))
            dealer_cards_str = (
                " ".join(dealer_cards_list) if dealer_cards_list else "N/A"
            )

        dealer_value_int = int(dealer_value_str) if dealer_value_str.isdigit() else 0
        if dealer_value_int == 21:
            description_content.append(
                f"# **{dealer_value_str}** 🔥 {dealer_cards_str}"
            )
        elif dealer_value_int > 21:
            description_content.append(
                f"# **{dealer_value_str}** 💥 {dealer_cards_str}"
            )
        else:
            description_content.append(f"# **{dealer_value_str}** {dealer_cards_str}")

        description_content.append("")  # 空行

        # 玩家手牌
        description_content.append("**你的手牌:**")
        player_cards_list = player_hand.get("cards", [])
        player_value_str = str(player_hand.get("hard_value", "?"))
        player_cards_str = " ".join(player_cards_list) if player_cards_list else "N/A"

        player_value_int = int(player_value_str) if player_value_str.isdigit() else 0
        if player_value_int == 21:
            description_content.append(
                f"# **{player_value_str}** 🔥 {player_cards_str}"
            )
        elif player_value_int > 21:
            description_content.append(
                f"# **{player_value_str}** 💥 {player_cards_str}"
            )
        else:
            description_content.append(f"# **{player_value_str}** {player_cards_str}")

        # --- 創建 Embed (使用基類初始化) ---
        embed = self._create_base_embed(  # MODIFIED: _initialize_embed to _create_base_embed
            title=title,
            description="\n".join(description_content),
            color=color,
            timestamp=datetime.now(),
        )

        # --- 設置頁腳 ---
        # 將format_oil格式化的內容改為純文字
        footer_parts = [f"下注: {bet}"]
        if self.balance is not None:
            footer_parts.append(f"餘額: {self.balance}")

        footer_text = " | ".join(footer_parts)

        # 設置footer並加入用戶頭像
        embed.set_footer(text=footer_text, icon_url=self.user.display_avatar.url)

        # 設置右上角縮圖為21點圖片
        embed.set_thumbnail(url=self.BLACKJACK_IMAGE_URL)

        return embed

    def build_bet_options_embed(self, current_bet: int) -> discord.Embed:
        """創建下注選項的Embed

        參數:
            current_bet: 當前下注金額

        返回:
            格式化後的Discord Embed對象
        """
        # 使用純文字格式
        bet_text = f"下注: {current_bet}"
        # 使用基類初始化
        embed = self._create_base_embed(  # MODIFIED: _initialize_embed to _create_base_embed
            title="選擇下注金額",
            description=f"當前{bet_text}\n你可以保持當前下注金額或加倍下注。",
            color=self.DEFAULT_EMBED_COLOR,  # MODIFIED: 使用基類默認藍色
        )

        # 設置右上角縮圖為21點圖片
        embed.set_thumbnail(url=self.BLACKJACK_IMAGE_URL)

        # 設置footer並加入用戶頭像
        embed.set_footer(text=bet_text, icon_url=self.user.display_avatar.url)

        return embed

    def _get_result_summary(self, result: str) -> str:
        """獲取遊戲結果摘要文本"""
        summaries = {
            "blackjack": "🎊 **恭喜獲得 BLACKJACK 21點!** 🎊\n",
            "dealer_blackjack": "⚠️ **莊家獲得 BLACKJACK!** ⚠️\n",
            "bust": "💥 **爆牌了!** 💥\n",
            "dealer_bust": "🎯 **莊家爆牌! 你贏了!** 🎯\n",
            "player_win": "🏆 **你的點數更高! 獲勝!** 🏆\n",
            "dealer_win": "📉 **莊家點數更高! 失敗!** 📉\n",
            "push": "🔄 **點數相同! 平局!** 🔄\n",
        }
        return summaries.get(result, "")

    def _get_result_text(self, result: str) -> str:
        """獲取結果文本 (保留，可能用於其他地方)"""
        result_texts = {
            "blackjack": "21點！",
            "dealer_blackjack": "莊家21點！",
            "bust": "爆牌了！",
            "dealer_bust": "莊家爆牌！",
            "dealer_win": "莊家點數更高！",
            "player_win": "你的點數更高！",
            "push": "平局！",
        }
        return result_texts.get(result, "遊戲結束")

    def _get_result_color(self, result: str) -> discord.Color:
        """獲取結果對應的顏色"""
        result_colors = {
            "blackjack": discord.Color.gold(),  # 使用 discord.Color.gold()
            "dealer_blackjack": discord.Color.dark_red(),
            "bust": discord.Color.red(),
            "dealer_bust": discord.Color.green(),
            "dealer_win": discord.Color.red(),
            "player_win": discord.Color.green(),
            "push": discord.Color.blue(),
        }
        return result_colors.get(
            result, self.DEFAULT_EMBED_COLOR
        )  # MODIFIED: 默認使用基類顏色
