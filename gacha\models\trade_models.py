from typing import Optional, Literal
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum  # <<< 新增導入


class TradeType(str, Enum):  # <<< 新增 TradeType Enum
    CARD_FOR_OIL = "CARD_FOR_OIL"
    CARD_FOR_CARD = "CARD_FOR_CARD"
    GIFT_CARD = "GIFT_CARD"


class TradeStatus(str, Enum):  # <<< 新增 TradeStatus Enum
    PENDING = "pending"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    CANCELLED = "cancelled"
    EXPIRED = "expired"


class CardTradeHistoryModel(BaseModel):
    """
    Pydantic model for representing an entry in the card_trade_history table.
    This table stores records of completed P2P card trades.
    """

    id: Optional[int] = Field(
        default=None,
        description="The unique identifier for the trade history entry, auto-generated by DB.",
    )
    initiator_user_id: int = Field(
        description="The Discord User ID of the trade initiator."
    )
    receiver_user_id: int = Field(
        description="The Discord User ID of the trade receiver."
    )

    offered_master_card_id: int = Field(
        description="The master ID of the card type offered by the initiator."
    )
    offered_quantity: int = Field(
        default=1, description="The quantity of the card offered by the initiator.")

    requested_master_card_id: Optional[int] = Field(
        default=None,
        description="The master ID of the card type requested from the receiver (for card-for-card trades).",
    )
    requested_quantity: Optional[int] = Field(
        default=1,
        description="The quantity of the card requested from the receiver (relevant if requested_master_card_id is not None).",
    )

    price_amount: Optional[int] = Field(
        default=None,
        description="The amount of currency paid by the receiver (for card-for-oil trades).",
    )
    trade_type: TradeType = Field(
        description="The type of P2P trade."
    )  # <<< 修改類型為 TradeType
    fee_charged: Optional[int] = Field(
        default=0, description="The fee amount charged for the trade, if any."
    )
    completed_at: datetime = Field(
        description="The timestamp when the trade was completed."
    )

    class Config:
        from_attributes = True
