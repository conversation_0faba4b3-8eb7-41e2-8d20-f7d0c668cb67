"""
Gacha系統系列收集Embed構建器
"""
from datetime import datetime
from typing import Any, Dict, List, Optional
import discord
from gacha.models.models import Card, SeriesCollection
from gacha.views.utils import get_completion_indicator
from gacha.app_config import config_service
from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder

class SeriesEmbedBuilder(BaseEmbedBuilder):
    """構建系列收集嵌入消息的類"""

    def __init__(self, user: discord.User, nickname: Optional[str]=None):
        """初始化系列Embed構建器

        參數:
            user: Discord用戶對象
            nickname: 用戶暱稱，如果有的話優先使用
        """
        series_data = {'user': user, 'nickname': nickname}
        super().__init__(data=series_data)
        self.user = user
        self.nickname = nickname
        self.display_name = nickname or user.display_name

    def build_single_series_embed(self, series_collection: SeriesCollection) -> discord.Embed:
        """創建單個系列收集情況的Embed顯示

        參數:
            series_collection: 系列收集信息

        返回:
            格式化後的Discord Embed對象
        """
        completion_rate = series_collection.completion_rate
        indicator, color = get_completion_indicator(completion_rate)
        embed = self._create_base_embed(title=f'{indicator} 「{series_collection.series}」系列收集進度', description=f'{self.display_name}已收集此系列的卡片', color=color, timestamp=datetime.now())
        progress = completion_rate / 100.0
        bar_length = 10
        filled_length = min(bar_length, round(bar_length * progress))
        empty_length = bar_length - filled_length
        filled_char = '■'
        empty_char = '□'
        progress_bar = filled_char * filled_length + empty_char * empty_length
        embed.add_field(name='收集進度', value=f'{series_collection.collected_cards}/{series_collection.total_cards}張卡片（{completion_rate:.1f}%）\n{progress_bar}', inline=False)
        if completion_rate == 100:
            embed.add_field(name='恭喜！', value='你已完成此系列的收集！', inline=False)
        elif completion_rate >= 75:
            embed.add_field(name='接近完成', value='繼續抽卡以完成此系列的收集', inline=False)
        elif completion_rate >= 50:
            embed.add_field(name='過半完成', value='已收集一半以上的卡片', inline=False)
        else:
            embed.add_field(name='收集提示', value='使用`/mws`命令查看其他系列', inline=False)
        embed.set_thumbnail(url=self.user.display_avatar.url)
        embed.set_footer(text=f"第{series_collection.collected_cards}/{series_collection.total_cards}張 • {datetime.now().strftime('%Y-%m-%d')}")
        return embed

    def build_embed(self, series_list: List[str], series_collections: Dict[str, SeriesCollection], overall_completion_percentage: float, total_collected: int, total_all_cards: int, current_page: int, total_pages: int, total_series: int, pool_type: Optional[str]=None) -> discord.Embed:
        """創建系列列表分頁的Embed

        參數:
            series_list: 當前頁的系列名稱列表
            series_collections: 當前頁系列的收集數據
            overall_completion_percentage: 總體完成百分比
            total_collected: 總收集卡片數
            total_all_cards: 總卡片數
            current_page: 當前頁碼
            total_pages: 總頁數
            total_series: 總系列數
            pool_type: 卡池類型 (可選)

        返回:
            格式化後的Discord Embed對象
        """
        overall_indicator, overall_color = get_completion_indicator(overall_completion_percentage)
        title = f'{self.display_name}的卡片收藏 {overall_indicator} {overall_completion_percentage:.1f}%'
        if pool_type:
            pool_name = config_service.get_pool_type_names().get(pool_type, pool_type.capitalize())
            title += f' [{pool_name}]'
        embed = self._create_base_embed(title=title, description=f'總體收集進度: {total_collected}/{total_all_cards}張卡片\n使用`/mws [系列名稱]`查看詳細收集情況', color=overall_color, timestamp=datetime.now())
        indicator_legend = '<:nocheck:1357796970160455892>未收集 | <a:check3:1365494496577458256>1-49% | <a:check4:1365494507163877457>50%+ | <a:check5:1365494516966228058>75%+ | <a:check2:1357795058782441766>100%'
        embed.description += '\n' + indicator_legend
        if not series_list or len(series_list) == 0:
            embed.description += '\n暫無可用系列'
        else:
            field_count = 0
            for series_name in series_list:
                series_collection = series_collections.get(series_name)
                if series_collection:
                    completion_rate = series_collection.completion_rate
                    indicator, _ = get_completion_indicator(completion_rate)
                    embed.add_field(name=f'{indicator} {series_name}', value=f'收集情況`{series_collection.collected_cards}/{series_collection.total_cards}`', inline=True)
                else:
                    embed.add_field(name=f'<:nocheck:1357796970160455892> {series_name}', value='收集情況`0/0`', inline=True)
                field_count += 1
                if field_count % 2 == 0 and field_count < len(series_list):
                    embed.add_field(name='\u200b', value='\u200b', inline=True)
            if field_count % 2 == 1:
                embed.add_field(name='\u200b', value='\u200b', inline=True)
                embed.add_field(name='\u200b', value='\u200b', inline=True)
        embed.set_footer(text=f'第{current_page}/{total_pages}頁 • 共{total_series}個系列')
        return embed