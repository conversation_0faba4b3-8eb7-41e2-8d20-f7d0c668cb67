import asyncpg
from typing import Dict, List, Optional, Any, Union
import logging
from utils.logger import logger
from gacha.repositories.collection.user_collection_repository import UserCollectionRepository
from database.postgresql.async_manager import AsyncPgManager

class SortingIndexManager:
    """卡片排序索引管理器 (Asyncpg 版本)"""
    SORT_INDEX_STEP = 1000000
    DEFAULT_WINDOW_SIZE = 20

    def __init__(self, collection_repo: UserCollectionRepository):
        """初始化索引管理器 (Asyncpg 版本)"""
        self.collection_repo = collection_repo
        self.pool = collection_repo.pool

    async def calculate_index_for_position(self, user_id: int, card_id: int, current_position: int, target_position: int, connection: Optional[asyncpg.Connection]=None) -> int:
        """(Async) 計算移動到指定位置的新索引值"""
        conn = connection
        logger.debug('[GACHA][SORT][CALC] 開始計算索引: 用戶=%s, 卡片=%s, 當前位置=%s, 目標位置=%s', user_id, card_id, current_position, target_position)
        adjusted_target = target_position
        if current_position < target_position:
            adjusted_target = target_position + 1
            logger.debug('[GACHA][SORT][CALC] 調整目標位置: %s -> %s (補償位置偏移)', target_position, adjusted_target)
        else:
            adjusted_target = target_position
        if adjusted_target == 1:
            min_index = await self.collection_repo.get_min_custom_sort_index(user_id, connection=conn)
            if min_index is not None:
                new_index = min_index - self.SORT_INDEX_STEP
                logger.debug('[GACHA][SORT][CALC] 目標位置=1, 計算的索引值=%s, 現有最小索引=%s', new_index, min_index)
                return new_index
            else:
                logger.debug('[GACHA][SORT][CALC] 目標位置=1, 列表為空, 索引值=0')
                return 0
        total_sorted_cards = await self.collection_repo.count_sorted_cards(user_id, connection=conn)
        if adjusted_target == total_sorted_cards and total_sorted_cards > 0:
            max_index = await self.collection_repo.get_max_custom_sort_index(user_id, connection=conn)
            new_index = max_index + self.SORT_INDEX_STEP if max_index is not None else self.SORT_INDEX_STEP
            logger.debug('[GACHA][SORT][CALC] 目標位置=%s(最後), 計算的索引值=%s, 現有最大索引=%s', adjusted_target, new_index, max_index)
            return new_index
        if adjusted_target > total_sorted_cards:
            max_index = await self.collection_repo.get_max_custom_sort_index(user_id, connection=conn)
            new_index = max_index + self.SORT_INDEX_STEP if max_index is not None else self.SORT_INDEX_STEP
            logger.debug('[GACHA][SORT][CALC] 目標位置超出範圍(%s>%s), 索引值=%s', adjusted_target, total_sorted_cards, new_index)
            return new_index
        target_prev_card = None
        if adjusted_target > 1:
            target_prev_card = await self.collection_repo.get_card_at_position(user_id, adjusted_target - 1)
        target_card = await self.collection_repo.get_card_at_position(user_id, adjusted_target)
        if target_card and target_card.get('card_id') == card_id:
            next_position = adjusted_target + 1
            if next_position <= total_sorted_cards:
                target_card = await self.collection_repo.get_card_at_position(user_id, next_position)
            else:
                target_card = None
        if target_prev_card and target_card:
            prev_index = target_prev_card['sort_index']
            next_index = target_card['sort_index']
            if target_prev_card.get('card_id') == card_id and adjusted_target > 2:
                prev_prev_card = await self.collection_repo.get_card_at_position(user_id, adjusted_target - 2)
                if prev_prev_card:
                    prev_index = prev_prev_card['sort_index']
            gap = next_index - prev_index
            if gap <= 1:
                logger.debug('[GACHA][SORT][CALC] 索引間隔過小(%s)，執行正規化', gap)
                anchor_card_id = target_prev_card['card_id'] if target_prev_card else target_card['card_id']
                await self._renormalize_local_indexes(user_id, anchor_card_id, self.DEFAULT_WINDOW_SIZE * 2, connection=conn)
                if adjusted_target > 1:
                    target_prev_card = await self.collection_repo.get_card_at_position(user_id, adjusted_target - 1)
                target_card = await self.collection_repo.get_card_at_position(user_id, adjusted_target)
                if target_card and target_card.get('card_id') == card_id:
                    next_position = adjusted_target + 1
                    if next_position <= total_sorted_cards:
                        target_card = await self.collection_repo.get_card_at_position(user_id, next_position)
                    else:
                        target_card = None
                if target_prev_card and target_card:
                    prev_index = target_prev_card['sort_index']
                    next_index = target_card['sort_index']
                    gap = next_index - prev_index
                    if gap <= 1:
                        logger.error('[GACHA][SORT] 正規化後索引間隔仍然不足')
                        raise RuntimeError('正規化後索引間隔仍然不足')
            position_ratio = 0.25
            new_index = prev_index + int(gap * position_ratio)
            logger.debug('[GACHA][SORT][CALC] 目標位置=%s, 索引值=%s, 前後索引=%s/%s, 間隔=%s', adjusted_target, new_index, prev_index, next_index, gap)
            return new_index
        elif target_prev_card:
            new_index = target_prev_card['sort_index'] + self.SORT_INDEX_STEP
            logger.debug('[GACHA][SORT][CALC] 目標位置=%s, 索引值=%s, 前一索引=%s', adjusted_target, new_index, target_prev_card['sort_index'])
            return new_index
        elif target_card:
            new_index = max(0, target_card['sort_index'] - self.SORT_INDEX_STEP)
            logger.debug('[GACHA][SORT][CALC] 目標位置=%s, 索引值=%s, 目標索引=%s', adjusted_target, new_index, target_card['sort_index'])
            return new_index
        else:
            new_index = adjusted_target * self.SORT_INDEX_STEP
            logger.debug('[GACHA][SORT][CALC] 目標位置=%s, 默認索引值=%s', adjusted_target, new_index)
            return new_index

    async def _get_surrounding_cards(self, user_id: int, target_position: int, current_position: int, connection: Optional[asyncpg.Connection]=None) -> Dict[str, Any]:
        """(Async) 獲取目標位置周圍的卡片信息"""
        result = {'before': None, 'after': None}
        if target_position > 1:
            result['before'] = await self.collection_repo.get_card_at_position(user_id, target_position - 1)
        result['after'] = await self.collection_repo.get_card_at_position(user_id, target_position)
        return result

    async def _renormalize_local_indexes(self, user_id: int, anchor_card_id: int, window_size: int=DEFAULT_WINDOW_SIZE, connection: Optional[asyncpg.Connection]=None) -> bool:
        """(Async) 局部正規化排序索引"""
        conn_to_use = connection
        if not conn_to_use:
            logger.error('[GACHA][SORT] _renormalize_local_indexes called without a transaction connection.')
            return False
        try:
            logger.debug('[GACHA][SORT] 執行局部索引正規化: user_id=%s, anchor_card=%s, window=%s', user_id, anchor_card_id, window_size)
            anchor_card_info = await self.collection_repo.get_card_sort_info(user_id, anchor_card_id)
            if not anchor_card_info or anchor_card_info['sort_index'] is None:
                logger.warning('[GACHA][SORT] 局部正規化失敗：錨點卡片不存在或沒有排序索引')
                return False
            anchor_position = anchor_card_info['rank']
            start_position = max(1, anchor_position - window_size // 2)
            end_position = anchor_position + window_size // 2
            cards_in_window = []
            for pos in range(start_position, end_position + 1):
                card = await self.collection_repo.get_card_at_position(user_id, pos)
                if card:
                    cards_in_window.append(card)
            if not cards_in_window:
                logger.warning('[GACHA][SORT] 局部正規化失敗：窗口內沒有卡片')
                return False
            total_cards_in_window = len(cards_in_window)
            step = self.SORT_INDEX_STEP
            before_window_card = await self.collection_repo.get_card_at_position(user_id, start_position - 1) if start_position > 1 else None
            after_window_card = await self.collection_repo.get_card_at_position(user_id, end_position + 1)
            start_index = before_window_card['sort_index'] if before_window_card else 0
            end_index = after_window_card['sort_index'] if after_window_card else start_index + (total_cards_in_window + 2) * step
            index_range = end_index - start_index
            if index_range < total_cards_in_window + 1:
                logger.warning('[GACHA][SORT] 局部正規化：索引範圍不足(%s)，使用標準步長', index_range)
                index_step = step
                current_index = start_index + index_step
            else:
                index_step = index_range // (total_cards_in_window + 1)
                current_index = start_index + index_step
            updates = [{'card_id': card['card_id'], 'sort_index': current_index + i * index_step} for i, card in enumerate(cards_in_window)]
            update_success = await self.collection_repo.bulk_update_sort_indexes(user_id, updates)
            if update_success:
                logger.debug('[GACHA][SORT] 局部正規化成功：更新了 %s 張卡片的索引', len(updates))
                return True
            else:
                logger.warning('[GACHA][SORT] 局部正規化失敗：無法更新索引')
                raise RuntimeError('局部正規化批量更新失敗')
        except Exception as e:
            logger.error('[GACHA][SORT] 局部正規化錯誤: %s', str(e), exc_info=True)
            raise