"""
Profile View - 處理檔案相關的 Discord UI 互動
"""
import discord
from discord import ui
from typing import Optional, Any # Removed Dict
import asyncio
import io # Added for discord.File

from utils.logger import logger
from gacha.services.core.profile_service import ProfileService # For type hinting
from gacha.services.ui.profile_image_generator import ProfileImageGenerator # For type hinting
from gacha.exceptions import ProfileBackgroundError, ProfileCardSetError, LikeProfileError # 添加异常导入

# Need to import ProfileImageGenerator and ProfileService for the refresh logic
# Assuming they are accessible, otherwise cog might need to pass them or bot instance
# For now, we'll try to get them from interaction.client like ProfileService in like_profile


class ProfileLikeView(ui.View):
    """檔案按讚與刷新視圖"""
    
    def __init__(self, 
                 owner_id: int, 
                 bot_instance: discord.Client, 
                 profile_service: ProfileService, 
                 image_generator: ProfileImageGenerator, 
                 private_channel_id: Optional[int] = None):
        super().__init__(timeout=None)  # 永久有效
        self.owner_id = owner_id
        self.bot = bot_instance 
        self.profile_service = profile_service
        self.image_generator = image_generator
        self.private_channel_id = private_channel_id

    @ui.button(label="❤️ 按讚", style=discord.ButtonStyle.primary, custom_id="profile_like_button", row=0)
    async def like_profile(self, interaction: discord.Interaction, button: ui.Button):
        """處理按讚互動"""
        try:
            if interaction.user.id == self.owner_id:
                await interaction.response.send_message("❌ 您不能為自己的檔案按讚", ephemeral=True)
                return
            
            await self.profile_service.like_profile(interaction.user.id, self.owner_id)
            await interaction.response.send_message("❤️ 按讚成功！", ephemeral=True)
                
        except LikeProfileError as e:
            logger.error(f"處理按讚互動失敗: {e}", exc_info=True)
            if hasattr(e, 'cooldown') and e.cooldown:
                # 從Redis獲取冷卻時間
                cooldown_key = f"profile_like_cd:{interaction.user.id}:{self.owner_id}"
                try:
                    ttl = await self.profile_service.redis_service.ttl(cooldown_key)
                    if ttl > 0:
                        minutes, seconds = divmod(ttl, 60)
                        cooldown_text = f"（剩餘時間: {minutes}分{seconds}秒）"
                        await interaction.response.send_message(f"❌ {e} {cooldown_text}", ephemeral=True)
                        return
                except Exception:
                    pass
                
                await interaction.response.send_message(f"❌ {e}", ephemeral=True)
            else:
                await interaction.response.send_message(f"❌ 按讚時發生錯誤: {e}", ephemeral=True)
        except Exception as e: 
            logger.error(f"處理按讚互動失敗: {e}", exc_info=True)
            await interaction.response.send_message(f"❌ 按讚時發生錯誤: {e}", ephemeral=True)

    @ui.button(label="🔄 刷新", style=discord.ButtonStyle.secondary, custom_id="profile_refresh_button", row=0)
    async def refresh_profile(self, interaction: discord.Interaction, button: ui.Button):
        """處理檔案刷新互動"""
        if interaction.user.id != self.owner_id:
            await interaction.response.send_message("❌ 你不是此檔案的擁有者，無法刷新。", ephemeral=True)
            return
        
        await interaction.response.defer(ephemeral=False, thinking=True)
        
        try:
            target_user = await self.bot.fetch_user(self.owner_id)
            if not target_user:
                await interaction.followup.send("❌ 找不到目標用戶。", ephemeral=True)
                return

            logger.info(f"开始刷新用户 {self.owner_id} 的个人资料图片 (由 {interaction.user.id} 请求)")

            # 使 Redis 缓存失效
            await self.profile_service.invalidate_profile_cache(self.owner_id)
            logger.info(f"用户 {self.owner_id} 的个人资料图片 Redis 缓存已清除。")

            user_avatar_url = str(target_user.display_avatar.url) if target_user.display_avatar else None
            # 從 discord.User 物件獲取 display_name
            discord_display_name = target_user.display_name
            profile_data = await self.profile_service.get_profile_data(
                self.owner_id, 
                discord_display_name=discord_display_name, 
                avatar_url=user_avatar_url
            )
            image_bytes = await self.image_generator.generate_profile_image(profile_data)
            
            # Use a new BytesIO object for each File instance if sending multiple times
            file_for_edit = discord.File(io.BytesIO(image_bytes), filename="profile.png")
            
            new_view = ProfileLikeView(
                owner_id=self.owner_id, 
                bot_instance=self.bot,
                profile_service=self.profile_service,
                image_generator=self.image_generator,
                private_channel_id=self.private_channel_id
            )

            if self.private_channel_id:
                try:
                    channel = self.bot.get_channel(self.private_channel_id) or await self.bot.fetch_channel(self.private_channel_id)
                    if channel and isinstance(channel, discord.TextChannel):
                        # Create a new BytesIO for the temp message to avoid issues with reading a closed stream
                        file_for_upload = discord.File(io.BytesIO(image_bytes), filename="profile.png")
                        temp_message = await channel.send(file=file_for_upload)
                        new_image_url = temp_message.attachments[0].url
                        # 更新 Redis 缓存
                        await self.profile_service.update_cached_profile_image_url(self.owner_id, new_image_url)
                        logger.info(f"刷新后的图片已上传至私密频道，新URL已缓存至Redis: {new_image_url}")
                        await interaction.message.edit(content=new_image_url, attachments=[], view=new_view)
                    else:
                        logger.warning(f"无法找到或访问私密频道 (ID: {self.private_channel_id})。将直接发送图片文件。")
                        await interaction.message.edit(content=None, attachments=[file_for_edit], view=new_view)
                except Exception as e_upload:
                    logger.error(f"上传刷新后的图片到私密频道失败: {e_upload}", exc_info=True)
                    await interaction.message.edit(content=None, attachments=[file_for_edit], view=new_view)
            else: 
                await interaction.message.edit(content=None, attachments=[file_for_edit], view=new_view)

            await interaction.followup.send("✅ 個人檔案已刷新！", ephemeral=True)
            logger.info(f"用户 {self.owner_id} 的个人资料已成功刷新。")

        except Exception as e:
            logger.error(f"刷新個人檔案圖片失敗: {e}", exc_info=True)
            await interaction.followup.send(f"❌ 刷新時發生錯誤: {e}", ephemeral=True)

# The following views and modal are being removed as their functionality
# is now handled by parameters in the /profile command.
# class ProfileSetView(ui.View): ...
# class SubCardSlotView(ui.View): ...
# class CardIdModal(ui.Modal): ... 