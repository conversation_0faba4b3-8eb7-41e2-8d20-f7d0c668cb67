import asyncpg
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from gacha.models.models import Card, CardStatus, CardWithStatus
from gacha.repositories.collection.user_collection_repository import UserCollectionRepository
from gacha.services.core.wish_service import WishService
from database.postgresql.async_manager import AsyncPgManager
from utils.logger import logger

class CardStatusService:
    """卡片狀態服務 (Asyncpg 版本)，統一處理卡片狀態的獲取"""

    def __init__(self, pool: asyncpg.Pool, collection_repo: UserCollectionRepository):
        """初始化卡片狀態服務 (Asyncpg 版本)"""
        if pool is None:
            try:
                self.pool = AsyncPgManager.get_pool()
            except RuntimeError as e:
                logger.error('無法初始化 CardStatusService：asyncpg 連接池未提供也無法從管理器獲取。')
                raise e
        else:
            self.pool = pool
        self.collection_repo = collection_repo
        self.wish_service = None

    async def get_card_status(self, user_id: int, card_id: int) -> CardStatus:
        """(Async) 獲取單張卡片的狀態"""
        status = CardStatus()
        try:
            user_card = await self.collection_repo.get_user_card(user_id, card_id)
            if user_card:
                status.star_level = user_card.star_level
                status.is_favorite = user_card.is_favorite
                status.quantity = user_card.quantity
            else:
                status.is_new_card = True
                status.quantity = 0
            if self.wish_service is not None:
                try:
                    wish_status = await self.wish_service.get_wishes_status_batch(user_id, [card_id])
                    status.is_wish = wish_status.get(card_id, False)
                except Exception as e:
                    logger.warning('獲取許願狀態時出錯: %s', str(e))
                    status.is_wish = False
            else:
                status.is_wish = False
            return status
        except Exception as e:
            logger.error('[GACHA] 獲取卡片狀態失敗: %s', str(e), exc_info=True)
            return CardStatus()

    async def get_cards_status_batch(self, user_id: int, card_ids: List[int]) -> Dict[int, CardStatus]:
        """(Async) 批量獲取多張卡片的狀態"""
        if not card_ids:
            return {}
        try:
            status_dict = {card_id: CardStatus() for card_id in card_ids}
            favorite_status_task = self.collection_repo.get_favorite_status_batch(user_id, card_ids)
            star_levels_task = self.collection_repo.get_star_levels_batch(user_id, card_ids)
            collection_status_task = self.collection_repo.check_cards_owned_batch(user_id, card_ids)
            tasks = [favorite_status_task, star_levels_task, collection_status_task]
            wish_status = {}
            if self.wish_service is not None:
                try:
                    wish_status_task = self.wish_service.get_wishes_status_batch(user_id, card_ids)
                    tasks.append(wish_status_task)
                except Exception as e:
                    logger.warning('獲取許願狀態時出錯: %s', str(e))
            results = await asyncio.gather(*tasks)
            favorite_status, star_levels, collection_status = results[:3]
            if self.wish_service is not None and len(results) > 3:
                wish_status = results[3]
            for card_id in card_ids:
                is_owned = collection_status.get(card_id, False)
                status_dict[card_id].is_favorite = favorite_status.get(card_id, False)
                status_dict[card_id].star_level = star_levels.get(card_id, 0)
                status_dict[card_id].is_wish = wish_status.get(card_id, False)
                status_dict[card_id].is_new_card = not is_owned
                status_dict[card_id].quantity = 1 if is_owned else 0
            return status_dict
        except Exception as e:
            logger.error('[GACHA] 批量獲取卡片狀態失敗: %s', str(e), exc_info=True)
            return {card_id: CardStatus() for card_id in card_ids}

    def update_card_status_from_operation(self, card_status: CardStatus, operation_result: Dict[str, Any]) -> CardStatus:
        """從抽卡操作結果更新卡片狀態"""
        card_status.is_new_card = operation_result.get('is_new', card_status.is_new_card)
        card_status.star_level = operation_result.get('star_level', card_status.star_level)
        card_status.quantity = operation_result.get('quantity', card_status.quantity)
        return card_status

    async def check_if_new_cards(self, user_id: int, card_ids: List[int]) -> Dict[int, bool]:
        """(Async) 批量檢查卡片是否為新卡"""
        if not card_ids:
            return {}
        try:
            collection_status = await self.collection_repo.check_cards_owned_batch(user_id, card_ids)
            return {card_id: not owned for card_id, owned in collection_status.items()}
        except Exception as e:
            logger.error('[GACHA] 批量檢查新卡失敗: %s', str(e), exc_info=True)
            return {card_id: False for card_id in card_ids}

    async def enrich_cards_with_status(self, user_id: int, cards: List[Card], pool_types: Optional[Dict[int, str]]=None, rarity_infos: Optional[Dict[int, Dict[str, Any]]]=None) -> List[CardWithStatus]:
        """(Async) 豐富卡片列表，添加狀態信息"""
        if not cards:
            return []
        card_ids = [card.card_id for card in cards]
        status_dict = await self.get_cards_status_batch(user_id, card_ids)
        result = []
        for card in cards:
            card_id = card.card_id
            status = status_dict.get(card_id, CardStatus())
            pool_type = pool_types.get(card_id, card.pool_type) if pool_types else card.pool_type
            card_with_status = CardWithStatus.create(card=card, status=status, pool_type=pool_type)
            result.append(card_with_status)
        return result

    async def create_card_with_status(self, user_id: int, card: Card, pool_type: Optional[str]=None, rarity_info: Optional[Dict[str, Any]]=None) -> CardWithStatus:
        """(Async) 為單張卡片創建帶狀態的包裝對象"""
        status = await self.get_card_status(user_id, card.card_id)
        actual_pool_type = pool_type or card.pool_type
        return CardWithStatus.create(card=card, status=status, pool_type=actual_pool_type)