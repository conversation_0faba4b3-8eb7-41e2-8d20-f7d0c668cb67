"""Gacha系統核心類提供統一的Gacha系統初始化和管理接口，作為整個GACHA系統的協調中心"""# pylint: disable=unused-import,too-many-instance-attributes,no-value-for-parameter,too-many-function-args# 注意：某些 pylint 錯誤在靜態分析中顯示，但在實際運行中不會發生，# 因為服務初始化是條件性的且在異步環境中進行，所有構造函數參數都通過關鍵字正確傳遞
import asyncio
from typing import Any, Dict, Optional, List, Tuple
import logging
from discord.ext import commands
from utils.logger import logger
from gacha.exceptions import GachaSystemError
import os
import importlib

class GachaSystem:
    """
    Gacha系統核心類

    負責初始化和協調所有GACHA相關服務和倉庫，
    提供統一的訪問點，確保所有服務間的依賴關係正確建立。
    """

    def __init__(self):
        """初始化Gacha系統"""
        self._initialized = False
        self._bot = None
        self._services: Dict[str, Any] = {}
        self._repositories: Dict[str, Any] = {}
        self._db_pool = None

    async def _init_repository(self, repo_name: str, repo_class_path: str, *args, **kwargs):
        """輔助方法：初始化單個倉庫"""
        if repo_name in self._repositories:
            logger.debug(f"[GACHA] 倉庫 {repo_name} 已經初始化，跳過")
            return True
        try:
            module_path, class_name = repo_class_path.rsplit('.', 1)
            module = importlib.import_module(module_path)
            RepoClass = getattr(module, class_name)
            self._repositories[repo_name] = RepoClass(self._db_pool, *args, **kwargs)
            logger.info(f"[GACHA] 倉庫 {repo_name} 初始化成功")
            return True
        except ImportError:
            logger.warning(f"[GACHA] 倉庫模組 {repo_class_path} 不存在或無法導入，跳過 {repo_name} 初始化")
        except AttributeError:
            logger.warning(f"[GACHA] 倉庫類 {class_name} 在 {module_path} 中未找到，跳過 {repo_name} 初始化")
        except Exception as e:
            logger.error(f"[GACHA] 倉庫 {repo_name} 初始化失敗: {e}", exc_info=True)
        return False

    async def _init_service(self, service_name: str, service_class_path: str, 
                            repo_dependencies: Optional[List[str]] = None, 
                            service_dependencies: Optional[List[str]] = None, 
                            *args, **constructor_kwargs):
        """輔助方法：初始化單個服務"""
        if service_name in self._services:
            logger.debug(f"[GACHA] 服務 {service_name} 已經初始化，跳過")
            return True
            
        # 檢查倉庫依賴
        if repo_dependencies:
            for dep_name in repo_dependencies:
                if dep_name not in self._repositories or self._repositories.get(dep_name) is None:
                    logger.warning(f"[GACHA] 服務 {service_name} 跳過初始化 - 缺少倉庫依賴: {dep_name}")
                    return False
        
        # 檢查服務依賴
        if service_dependencies:
            for dep_name in service_dependencies:
                if dep_name not in self._services or self._services.get(dep_name) is None:
                    logger.warning(f"[GACHA] 服務 {service_name} 跳過初始化 - 缺少服務依賴: {dep_name}")
                    return False
        try:
            module_path, class_name = service_class_path.rsplit('.', 1)
            module = importlib.import_module(module_path)
            ServiceClass = getattr(module, class_name)
            
            # 調用者負責準備 *args 和 **kwargs 以匹配 ServiceClass 的構造函數
            # 包括傳遞 db_pool (如果需要) 和已解析的依賴實例
            logger.debug(f"[GACHA] Attempting to initialize service '{service_name}' with args: {args} and kwargs: {constructor_kwargs}")
            self._services[service_name] = ServiceClass(*args, **constructor_kwargs)
            logger.info(f"[GACHA] 服務 {service_name} 初始化成功")
            return True
        except ImportError:
            logger.warning(f"[GACHA] 服務模組 {service_class_path} 不存在或無法導入，跳過 {service_name} 初始化")
        except AttributeError:
            logger.warning(f"[GACHA] 服務類 {class_name} 在 {module_path} 中未找到，跳過 {service_name} 初始化")
        except Exception as e:
            logger.error(f"[GACHA] 服務 {service_name} 初始化失敗: {e}", exc_info=True)
        return False

    async def initialize(self, bot=None):
        """
        初始化Gacha系統

        該方法會按照以下順序執行初始化：
        1. 初始化數據庫連接池
        2. 初始化倉庫實例
        3. 初始化基礎服務實例 (由 _initialize_services 處理)
        4. 建立核心服務之間的依賴關係並初始化它們 (由 _setup_service_dependencies 處理)
        5. 初始化擴展服務 (由 _init_extended_services 處理)

        Args:
            bot: Discord機器人實例

        Returns:
            bool: 初始化是否成功
        """
        try:
            if self._initialized:
                logger.info('[GACHA] 系統已經初始化，跳過重複初始化')
                return True
            self._bot = bot
            await self._initialize_database_pool()
            await self._initialize_repositories()
            await self._initialize_services() # 初始化基礎服務
            await self._setup_service_dependencies() # 初始化核心服務並設置依賴
            await self._init_extended_services() # 初始化擴展服務
            self._initialized = True
            logger.info('[GACHA] 系統初始化完成')
            return True
        except Exception as e:
            logger.error('[GACHA] 系統初始化失敗: %s', e, exc_info=True)
            raise GachaSystemError(f'初始化Gacha系統時發生錯誤: {e}') from e

    async def _initialize_database_pool(self):
        """初始化數據庫連接池"""
        from database.postgresql.async_manager import AsyncPgManager
        from gacha.app_config import config_service
        db_url = config_service.get_config('DATABASE_URL')
        if not db_url:
            raise GachaSystemError('無法獲取數據庫連接URL，請檢查配置')
        logger.info('[GACHA] 初始化數據庫連接池')
        await AsyncPgManager.initialize_pool()
        self._db_pool = AsyncPgManager.get_pool()
        async with self._db_pool.acquire() as conn:
            await conn.execute('SELECT 1')
        logger.info('[GACHA] 數據庫連接池初始化成功')

    async def _initialize_repositories(self):
        """初始化所有數據庫倉庫"""
        logger.info('[GACHA] 開始初始化倉庫')
        
        redis_client = None
        if self._bot and hasattr(self._bot, 'redis_manager') and self._bot.redis_manager:
            redis_client = self._bot.redis_manager.redis_client
            logger.info('[GACHA] 使用 Bot 的 Redis 客戶端初始化倉庫')
        else:
            logger.warning('[GACHA] Redis 客戶端不可用，某些倉庫功能可能受限')

        await self._init_repository('master_card', 'gacha.repositories.card.master_card_repository.MasterCardRepository')
        # UserCollectionRepository 需要 redis_client 作為額外參數
        if redis_client:
            await self._init_repository('user_collection', 'gacha.repositories.collection.user_collection_repository.UserCollectionRepository', redis_client=redis_client)
        else:
            # 嘗試不使用 redis_client 初始化，如果構造函數允許 redis_client=None
            try:
                await self._init_repository('user_collection', 'gacha.repositories.collection.user_collection_repository.UserCollectionRepository', redis_client=None)
                if 'user_collection' not in self._repositories or self._repositories['user_collection'] is None:
                     logger.warning('[GACHA] UserCollectionRepository 初始化失敗（即使尝试使用None redis_client），相關功能可能受限')
                else:
                    logger.info('[GACHA] UserCollectionRepository 使用 None redis_client 初始化成功')
            except Exception as e:
                 logger.error(f'[GACHA] UserCollectionRepository 初始化完全失敗（嘗試使用None redis_client時出錯）: {e}')

        await self._init_repository('user', 'gacha.repositories.user.user_repository.UserRepository')
        await self._init_repository('user_wish', 'gacha.repositories.collection.user_wish_repository.UserWishRepository')
        await self._init_repository('leaderboard', 'gacha.repositories.leaderboard.leaderboard_repository.LeaderboardRepository')
        await self._init_repository('encyclopedia', 'gacha.repositories.card.card_encyclopedia_repository.CardEncyclopediaRepository') # Moved from _init_other_gacha_services
        
        logger.info('[GACHA] 倉庫初始化完成，共初始化 %s 個倉庫', len(self._repositories))

    async def _initialize_services(self):
        """初始化基礎服務"""
        logger.info('[GACHA] 開始初始化基礎服務')

        # UserService
        await self._init_service('user', 'gacha.services.core.user_service.UserService', 
                                 pool=self._db_pool)

        # CardStatusService
        await self._init_service('card_status', 'gacha.services.core.card_status_service.CardStatusService',
                                 repo_dependencies=['user_collection'],
                                 pool=self._db_pool, 
                                 collection_repo=self._repositories.get('user_collection'))

        # DrawEngineService
        await self._init_service('draw_engine', 'gacha.services.core.draw_engine_service.DrawEngineService',
                                 repo_dependencies=['master_card'],
                                 card_repo=self._repositories.get('master_card'),
                                 wish_service=None)

        # ValidationService
        await self._init_service('validation', 'gacha.services.common.validation_service.ValidationService',
                                 repo_dependencies=['user', 'master_card'],
                                 user_repo=self._repositories.get('user'), 
                                 master_card_repo=self._repositories.get('master_card'))

        # RedisEventPublisher
        try:
            from database.redis.config import is_redis_enabled
            if is_redis_enabled():
                shared_redis_manager = getattr(self._bot, 'redis_manager', None) if self._bot else None
                if shared_redis_manager and shared_redis_manager.is_connected:
                    await self._init_service('redis_publisher', 'gacha.utils.redis_publisher.RedisEventPublisher',
                                             redis_manager=shared_redis_manager)
                elif shared_redis_manager:
                    logger.warning('[GACHA] RedisEventPublisher 初始化失敗 - 共享的 bot.redis_manager 未连接。')
                else:
                    logger.warning('[GACHA] RedisEventPublisher 初始化失敗 - 未在 bot 对象上找到 redis_manager。')
            else:
                logger.info('[GACHA] Redis 未啟用，跳過 RedisEventPublisher 初始化')
        except ImportError:
            logger.warning('[GACHA] RedisEventPublisher 模組不存在或無法導入，跳過初始化')
        except Exception as e:
            logger.error(f'[GACHA] RedisEventPublisher 初始化过程中发生错误: {e}', exc_info=True)

        # DrawNotifier (假設其構造函數不需要額外參數，除了 self._db_pool 可能由 _init_service 內部處理或根本不需要)
        # 根據日誌，DrawNotifier 初始化成功，其構造函數可能不需要參數或只接受 pool。
        # 如果 DrawNotifier() 不需要任何參數，則目前的調用方式是正確的。
        # 若 DrawNotifier 需要 pool，則應像其他服務一樣明確傳遞。鑑於它成功了，暫時不改。
        await self._init_service('draw_notifier', 'gacha.services.notification.draw_notifier.DrawNotifier')
        
        logger.info('[GACHA] 基礎服務初始化完成，共初始化 %s 個服務', len(self._services))

    async def _setup_service_dependencies(self):
        """建立服務間的依賴關係並初始化複雜服務"""
        logger.info('[GACHA] 開始設置服務間依賴關係並初始化複雜服務')
        
        # 首先初始化 MarketPriceService，因為它被多個服務共享
        await self._init_market_price_service()

        # 初始化 EconomyService（需要多個依賴）
        await self._init_economy_service()
        
        # 初始化 WishService（需要 EconomyService）
        await self._init_wish_service()
        
        # 更新 DrawEngineService 的 wish_service 依賴
        draw_engine_service = self.get_service('draw_engine')
        wish_service = self.get_service('wish')
        if draw_engine_service and wish_service:
            draw_engine_service.wish_service = wish_service
            logger.info('[GACHA] DrawEngineService 的 wish_service 依賴已更新')
        elif not draw_engine_service:
            logger.warning('[GACHA] DrawEngineService 未初始化，無法更新 wish_service 依賴')
        elif not wish_service:
            logger.warning('[GACHA] WishService 未初始化，無法更新 DrawEngineService 的依賴')
        
        # 初始化 GachaService
        await self._init_gacha_service()
        
        # 初始化 CollectionService
        await self._init_collection_service()
        
        logger.info('[GACHA] 服務依賴關係設置完成')

    async def _init_market_price_service(self):
        """初始化 MarketPriceService"""
        await self._init_service(
            'market_price', 
            'gacha.services.market.market_price_service.MarketPriceService',
            repo_dependencies=['master_card'],
            pool=self._db_pool, 
            master_card_repo=self._repositories.get('master_card'),
            redis_manager=getattr(self._bot, 'redis_manager', None) if self._bot else None
        )

    async def _init_economy_service(self):
        """初始化 EconomyService"""
        redis_manager_for_hourly = getattr(self._bot, 'redis_manager', None) if self._bot else None
        if not redis_manager_for_hourly:
             logger.warning('[GACHA] Bot Redis Manager 不可用於 EconomyService 的每小時獎勵功能')

        await self._init_service(
            'economy', 
            'gacha.services.core.economy_service.EconomyService',
            repo_dependencies=['master_card', 'user_collection'],
            service_dependencies=['user', 'validation', 'market_price'],
            pool=self._db_pool,
            user_service=self.get_service('user'),
            card_repo=self._repositories.get('master_card'),
            collection_repo=self._repositories.get('user_collection'),
            collection_service=None,  # 稍後在 CollectionService 初始化後設置
            market_price_service=self.get_service('market_price'),
            validation_service=self.get_service('validation'),
            redis_publisher=self.get_service('redis_publisher'),
            redis_manager_legacy_hourly=redis_manager_for_hourly
        )

    async def _init_wish_service(self):
        """初始化 WishService"""
        await self._init_service(
            'wish',
            'gacha.services.core.wish_service.WishService',
            repo_dependencies=['user_wish', 'master_card', 'user_collection'],
            service_dependencies=['user', 'validation', 'economy'],
            pool=self._db_pool,
            user_service=self.get_service('user'),
            user_wish_repo=self._repositories.get('user_wish'),
            collection_repo=self._repositories.get('user_collection'),
            card_repo=self._repositories.get('master_card'),
            economy_service=self.get_service('economy'),
            validation_service=self.get_service('validation'),
            redis_publisher=self.get_service('redis_publisher')
        )

    async def _init_gacha_service(self):
        """初始化 GachaService"""
        await self._init_service(
            'gacha',
            'gacha.services.core.gacha_service.GachaService',
            repo_dependencies=['user_collection'],
            service_dependencies=['user', 'card_status', 'draw_engine', 'validation', 'wish'],
            pool=self._db_pool,
            user_service=self.get_service('user'),
            collection_repo=self._repositories.get('user_collection'),
            wish_service=self.get_service('wish'),
            card_status_service=self.get_service('card_status'),
            draw_engine_service=self.get_service('draw_engine'),
            validation_service=self.get_service('validation'),
            redis_publisher=self.get_service('redis_publisher')
        )

    async def _init_collection_service(self):
        """初始化 CollectionService"""
        await self._init_service(
            'collection',
            'gacha.services.core.collection_service.CollectionService',
            repo_dependencies=['user_collection', 'master_card'],
            service_dependencies=['user'],
            pool=self._db_pool,
            user_service=self.get_service('user'),
            card_repo=self._repositories.get('master_card'),
            collection_repo=self._repositories.get('user_collection'),
            redis_publisher=self.get_service('redis_publisher')
        )
        # 更新 EconomyService 的 collection_service 依賴
        economy_service = self.get_service('economy')
        collection_service = self.get_service('collection')
        if economy_service and collection_service:
            economy_service.collection_service = collection_service
            logger.info('[GACHA] EconomyService 的 collection_service 依賴已更新')
        elif not economy_service:
            logger.warning('[GACHA] EconomyService 未初始化，無法更新 collection_service 依賴')
        elif not collection_service:
             logger.warning('[GACHA] CollectionService 未初始化，無法更新 EconomyService 的依賴')

    async def _init_extended_services(self):
        """初始化擴展服務 - 包括之前在 bot.py 中初始化的所有服務"""
        logger.info('[GACHA] 開始初始化擴展服務')
        
        # Redis Service for management
        await self._init_redis_service()
        
        # Market related services
        await self._init_market_services()
        
        # Shop Service
        await self._init_shop_service()
        
        # Game Services
        await self._init_game_services()
        
        # Other Gacha-related services
        await self._init_other_gacha_services()
        
        # AI Services
        await self._init_ai_services()
        
        # Scheduled Task Orchestrator
        await self._init_scheduled_orchestrator()
        
        logger.info('[GACHA] 擴展服務初始化完成')

    async def _init_redis_service(self):
        """初始化 Redis 管理服務"""
        await self._init_service('redis_service_management', 'database.redis.service.RedisService')

    async def _init_market_services(self):
        """初始化市場相關服務"""
        # MarketPriceService is initialized in _setup_service_dependencies
        # PriceUpdateService
        await self._init_service(
            'price_update', 
            'gacha.services.market.price_update_service.PriceUpdateService',
            repo_dependencies=['master_card'],
            service_dependencies=['market_price'],
            # Args for PriceUpdateService constructor
            master_card_repo=self._repositories.get('master_card'),
            market_price_service=self.get_service('market_price')
        )
        
        # StockTradingService
        await self._init_service(
            'stock_trading', 
            'gacha.services.market.stock_trading_service.StockTradingService',
            service_dependencies=['user'],
            # Args for StockTradingService constructor
            bot=self._bot, 
            pool=self._db_pool,
            user_service=self.get_service('user')
        )
                    
        # MarketViewService
        await self._init_service(
            'market_view', 
            'gacha.services.market.market_view_service.MarketViewService',
            repo_dependencies=['master_card'],
            service_dependencies=['stock_trading'],
            # Args for MarketViewService constructor
            bot=self._bot, 
            pool=self._db_pool,
            stock_trading_service=self.get_service('stock_trading'),
            master_card_repo=self._repositories.get('master_card')
        )

    async def _init_shop_service(self):
        """初始化商店服務"""
        await self._init_service(
            'shop', 
            'gacha.services.shop.shop_service.ShopService',
            repo_dependencies=['master_card', 'user_collection'],
            service_dependencies=['user'],
            # Args for ShopService constructor
            user_service=self.get_service('user'),
            master_card_repo=self._repositories.get('master_card'),
            user_collection_repo=self._repositories.get('user_collection'),
            pool=self._db_pool
        )

    async def _init_game_services(self):
        """初始化遊戲服務"""
        # Game Display Service (no specific dependencies, simple init)
        await self._init_service('game_display', 'gacha.services.ui.game_display_service.GameDisplayService')
            
        # BlackjackGameService
        await self._init_service(
            'blackjack_game', 
            'gacha.services.games.blackjack_service.BlackjackGameService',
            service_dependencies=['economy', 'game_display'],
            # Args for BlackjackGameService constructor
            pool=self._db_pool,
            economy_service=self.get_service('economy'),
            game_display_service=self.get_service('game_display')
        )
                
        # DiceGameService
        await self._init_service(
            'dice_game', 
            'gacha.services.games.dice_game_service.DiceGameService',
            service_dependencies=['economy', 'game_display'],
            # Args for DiceGameService constructor
            economy_service=self.get_service('economy'),
            game_display_service=self.get_service('game_display')
        )
                
        # MinesService
        await self._init_service(
            'mines', 
            'gacha.services.games.mines_service.MinesService',
            service_dependencies=['economy', 'game_display'],
            # Args for MinesService constructor
            economy_service=self.get_service('economy'),
            game_display_service=self.get_service('game_display')
        )

    async def _init_other_gacha_services(self):
        """初始化其他 Gacha 相關服務"""
        # EncyclopediaService
        await self._init_service(
            'encyclopedia', 
            'gacha.services.core.encyclopedia_service.EncyclopediaService',
            repo_dependencies=['encyclopedia', 'master_card', 'user_collection'],
            # Args for EncyclopediaService constructor
            pool=self._db_pool,
            encyclopedia_repo=self._repositories.get('encyclopedia'),
            master_card_repo=self._repositories.get('master_card'),
            user_collection_repo=self._repositories.get('user_collection')
        )
            
        # FavoriteService
        await self._init_service(
            'favorite', 
            'gacha.services.core.favorite_service.FavoriteService',
            repo_dependencies=['master_card', 'user_collection'],
            service_dependencies=['user'], 
            # Args for FavoriteService constructor
            pool=self._db_pool,
            user_service=self.get_service('user'),
            card_repo=self._repositories.get('master_card'),
            collection_repo=self._repositories.get('user_collection'),
            redis_publisher=self.get_service('redis_publisher')
        )
            
        # SortingService
        await self._init_service(
            'sorting', 
            'gacha.services.sorting.sorting_service.SortingService',
            repo_dependencies=['user_collection'],
            # Args for SortingService constructor
            pool=self._db_pool,
            collection_repo=self._repositories.get('user_collection')
        )
            
        # StarEnhancementService
        await self._init_service(
            'star_enhancement', 
            'gacha.services.core.star_enhancement_service.StarEnhancementService',
            repo_dependencies=['user_collection', 'encyclopedia'],
            service_dependencies=['economy'], 
            # Args for StarEnhancementService constructor
            pool=self._db_pool,
            collection_repo=self._repositories.get('user_collection'),
            encyclopedia_repo=self._repositories.get('encyclopedia'),
            economy_service=self.get_service('economy'),
            redis_publisher=self.get_service('redis_publisher')
        )
            
        # ProfileService
        profile_redis_manager = getattr(self._bot, 'redis_manager', None) if self._bot else None
        if profile_redis_manager:
            await self._init_service(
                'profile', 
                'gacha.services.core.profile_service.ProfileService',
                service_dependencies=['user', 'collection'],
                # Args for ProfileService constructor
                pool=self._db_pool,
                user_service=self.get_service('user'),
                collection_service=self.get_service('collection'),
                redis_service=profile_redis_manager 
            )
        else:
            logger.warning('[GACHA] ProfileService 跳過初始化 - redis_manager 未在 bot 對象上找到。')
            
        # LeaderboardService
        # Assuming LeaderboardService constructor takes bot and pool
        await self._init_service(
            'leaderboard', 
            'gacha.services.leaderboard.leaderboard_service.LeaderboardService',
            bot=self._bot, 
            pool=self._db_pool 
        )

    async def _init_ai_services(self):
        """初始化 AI 服務"""
        # AIServiceBase (Assuming constructor takes no arguments)
        await self._init_service('ai_service', 'ai_assistant.ai_service_base.AIServiceBase')
        
        # PromptService
        prompts_file_path = "ai_assistant/prompts/news_prompts.yaml"
        await self._init_service('prompt_service', 'ai_assistant.prompt_service.PromptService', 
                                 # Args for PromptService constructor
                                 prompts_file_path=prompts_file_path)
            
    async def _init_scheduled_orchestrator(self):
        """初始化計劃任務協調器"""
        redis_client_for_scheduler = None
        if self._bot and hasattr(self._bot, 'redis_manager') and self._bot.redis_manager:
            redis_client_for_scheduler = self._bot.redis_manager.redis_client
        
        await self._init_service(
            'scheduled_task_orchestrator', 
            'gacha.services.scheduled.scheduled_task_orchestrator.ScheduledTaskOrchestrator',
            service_dependencies=['ai_service', 'prompt_service', 'price_update', 'redis_service_management'],
            # Args for ScheduledTaskOrchestrator constructor
            pool=self._db_pool,
            ai_service=self.get_service('ai_service'),
            prompt_service=self.get_service('prompt_service'),
            redis_client=redis_client_for_scheduler,
            price_update_service=self.get_service('price_update'),
            redis_service=self.get_service('redis_service_management')
        )

    @property
    def is_initialized(self) -> bool:
        """檢查系統是否已初始化"""
        return self._initialized

    def get_service(self, service_name: str) -> Optional[Any]:
        """
        獲取指定服務實例

        Args:
            service_name: 服務名稱

        Returns:
            服務實例或None（如果服務不存在）
        """
        return self._services.get(service_name)

    def get_repository(self, repo_name: str) -> Optional[Any]:
        """
        獲取指定倉庫實例

        Args:
            repo_name: 倉庫名稱

        Returns:
            倉庫實例或None（如果倉庫不存在）
        """
        return self._repositories.get(repo_name)

    @property
    def services(self) -> Dict[str, Any]:
        """獲取所有服務實例的字典"""
        return self._services

    @property
    def repositories(self) -> Dict[str, Any]:
        """獲取所有倉庫實例的字典"""
        return self._repositories

    @property
    def db_pool(self):
        """獲取數據庫連接池"""
        return self._db_pool

    async def shutdown(self):
        """安全關閉GachaSystem，釋放資源"""
        logger.info('[GACHA] 開始關閉 GachaSystem...')
        
        # 關閉 DrawNotifier 的 session
        draw_notifier_service = self.get_service('draw_notifier')
        if draw_notifier_service and hasattr(draw_notifier_service, 'close_session'):
            try:
                logger.info('[GACHA] 正在關閉 DrawNotifier session...')
                await draw_notifier_service.close_session()
                logger.info('[GACHA] DrawNotifier session 已關閉。')
            except Exception as e:
                logger.error('[GACHA] 關閉 DrawNotifier session 時出錯: %s', e, exc_info=True)
        
        # 關閉其他可能需要釋放資源的服務 (如果有的話)
        # 例如，如果 PriceUpdateService 或其他服務有後台任務或打開的連接
        price_update_service = self.get_service('price_update')
        if price_update_service and hasattr(price_update_service, 'stop_scheduler'):
            try:
                logger.info('[GACHA] 正在停止 PriceUpdateService 排程器...')
                await price_update_service.stop_scheduler() 
                logger.info('[GACHA] PriceUpdateService 排程器已停止。')
            except Exception as e:
                logger.error('[GACHA] 停止 PriceUpdateService 排程器時出錯: %s', e, exc_info=True)

        # 關閉 Redis 連接 (如果 GachaSystem 管理它自己的 Redis 連接)
        # 目前 RedisManager 是在 bot 層級管理，所以這裡可能不需要單獨處理
        # 但如果 GachaSystem 有自己的 Redis 實例，就需要關閉

        # 清理服務和倉庫引用
        self._services.clear()
        self._repositories.clear()
        self._initialized = False
        logger.info('[GACHA] GachaSystem 已成功關閉。')


gacha_system = GachaSystem()