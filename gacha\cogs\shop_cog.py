import discord
from discord.ext import commands
from discord import app_commands
from discord.ui import Select, View, select
from decimal import Decimal
from gacha.services.shop.shop_service import ShopService
from gacha.models.shop_models import ShopItemDefinition
from gacha.views.shop.shop_item_list_view import ShopItemListView
from gacha.views.embeds.shop.shop_item_list_embed_builder import ShopItemListEmbedBuilder
from gacha.exceptions import ShopSystemError, InvalidSessionError
from utils.logger import logger

class ShopCategoryView(View):

    def __init__(self, cog: commands.Cog, timeout=180):
        super().__init__(timeout=timeout)
        self.cog = cog

    @select(placeholder='選擇一個商品分類...', options=[discord.SelectOption(label='兌換券', value='ticket_exchange', description='瀏覽可兌換的卡片兌換券'), discord.SelectOption(label='裝備', value='equipment_shop', description='瀏覽可購買的裝備 (未來功能)')])
    async def category_select(self, interaction: discord.Interaction, select_item: Select):
        """
        處理商店分類選擇下拉選單的回調。
        根據選擇的分類顯示對應的商品列表。
        """
        await interaction.response.defer(ephemeral=True)
        selected_category = select_item.values[0]
        if selected_category == 'ticket_exchange':
            try:
                all_items: list[ShopItemDefinition] = await self.cog.shop_service.get_ticket_shop_definitions()
                oil_ticket_balance: Decimal = await self.cog.shop_service.user_service.get_oil_ticket_balance(interaction.user.id)
                
                if not all_items:
                    await interaction.followup.send('抱歉，該分類目前沒有可顯示的商品。', ephemeral=True)
                    return
                    
                items_per_page = 4
                view = ShopItemListView(original_interaction=interaction, cog=self.cog, items=all_items, oil_ticket_balance=oil_ticket_balance, category_name='油票兌換券商店', items_per_page=items_per_page)
                embed = view.get_current_page_embed()
                await interaction.edit_original_response(embed=embed, view=view)
                view.message = await interaction.original_response()
                
            except ShopSystemError as e:
                logger.error("Shop system error for user %s in category '%s': %s", interaction.user.id, selected_category, e, exc_info=True)
                await interaction.followup.send(f'商店操作出錯：{str(e)}', ephemeral=True)
            except Exception as e:
                logger.error("Error fetching shop data for user %s in category '%s': %s", interaction.user.id, selected_category, e, exc_info=True)
                await interaction.followup.send('抱歉，商店資料載入時發生錯誤，請稍後再試或聯繫管理員。', ephemeral=True)
        elif selected_category == 'equipment_shop':
            await interaction.followup.send('此分類商品正在準備中，敬請期待！', ephemeral=True)
        else:
            await interaction.followup.send(f'您選擇了：{selected_category} (功能待實作)', ephemeral=True)

class ShopCog(commands.Cog):

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        # 從 bot 對象中獲取 shop_service
        if hasattr(bot, 'shop_service'):
            self.shop_service = bot.shop_service
            logger.info('ShopCog loaded with shop_service.')
        else:
            logger.error('ShopCog: shop_service not found on bot instance!')
            raise RuntimeError('ShopCog requires shop_service to be available on bot instance')

    @app_commands.command(name='shop', description='開啟 Gacha 油票商店')
    async def shop(self, interaction: discord.Interaction):
        """
        顯示商店主頁面，包含商品分類概覽和下拉式選單。
        """
        try:
            embed = discord.Embed(title='Gacha 油票商店', description='歡迎來到油票商店！請選擇您感興趣的商品分類。🛍️', color=discord.Color.gold())
            embed.add_field(name='1. 兌換券', value='使用油票兌換各種卡片兌換券。', inline=False)
            embed.add_field(name='2. 裝備', value='(未來擴展，敬請期待) 各式強力裝備。', inline=False)
            view = ShopCategoryView(cog=self)
            await interaction.response.send_message(embed=embed, view=view)
        except Exception as e:
            logger.error("Error displaying shop for user %s: %s", interaction.user.id, e, exc_info=True)
            await interaction.response.send_message('抱歉，開啟商店時發生錯誤，請稍後再試或聯繫管理員。', ephemeral=True)

async def setup(bot: commands.Bot):
    """
    將 ShopCog 添加到機器人。
    """
    await bot.add_cog(ShopCog(bot))
    logger.info('ShopCog added to bot.')