from gacha.app_config import config_service
from utils.logger import logger
from functools import partial
from concurrent.futures import ThreadPoolExecutor
import os
from matplotlib.font_manager import FontProperties, fontManager
import matplotlib.dates as mdates
import matplotlib.ticker as mticker
import matplotlib.pyplot as plt
import asyncio
import asyncpg
import discord
from typing import Dict, Any, Optional, Tuple, List, Union
from decimal import Decimal, ROUND_HALF_UP
import math
from datetime import datetime, timedelta
import io
import json
import matplotlib
matplotlib.use('Agg')

# 設定中文字體
def setup_chinese_fonts():
    """設定中文字體支援"""
    try:
        # 取得專案根目錄路徑
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        
        # 嘗試使用專案中的中文字體檔案
        fonts_to_try = [
            os.path.join(project_root, 'fonts', 'NotoSansTC-Regular.ttf'),
            os.path.join(project_root, 'fonts', 'NotoSerifCJKtc-Medium.otf'),
        ]
        
        font_loaded = False
        
        for font_path in fonts_to_try:
            if os.path.exists(font_path):
                try:
                    fontManager.addfont(font_path)
                    font_prop = FontProperties(fname=font_path)
                    font_name = font_prop.get_name()
                    
                    # 設定字體優先順序：專案字體 > 系統中文字體 > 預設字體
                    plt.rcParams['font.family'] = [font_name, 'Microsoft JhengHei', 'DejaVu Sans']
                    plt.rcParams['axes.unicode_minus'] = False
                    
                    logger.info(f'中文字體載入成功: {font_name} (路徑: {font_path})')
                    font_loaded = True
                    break
                except Exception as e:
                    logger.warning(f'載入字體 {font_path} 失敗: {e}')
                    continue
        
        # 如果專案字體都載入失敗，嘗試使用系統中文字體
        if not font_loaded:
            system_chinese_fonts = ['Microsoft JhengHei', 'Microsoft YaHei']
            plt.rcParams['font.family'] = system_chinese_fonts + ['DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            logger.info(f'使用系統中文字體: {system_chinese_fonts}')
        
    except Exception as e:
        logger.error(f'字體設定失敗: {e}')
        # 最後的後備方案
        plt.rcParams['font.family'] = ['Microsoft JhengHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

# 初始化字體設定
setup_chinese_fonts()

class StockTradingService:
    CHART_FIG_FACE_COLOR = '#2E2E2E'
    CHART_AX_FACE_COLOR = '#363636'
    CHART_TEXT_COLOR = '#FFFFFF'
    CHART_LINE_COLOR = '#00BFFF'
    CHART_GRID_COLOR = '#555555'
    
    def __init__(self, bot: discord.Client, pool: asyncpg.Pool, user_service: Any):
        if bot is None:
            raise ValueError('StockTradingService requires a discord.Client instance (bot).')
        if pool is None:
            raise ValueError('StockTradingService requires an asyncpg connection pool.')
        if user_service is None:
            raise ValueError('StockTradingService requires a UserService instance.')
        
        self.bot = bot
        self.pool = pool
        self.user_service = user_service
        self.transaction_fee_rate = Decimal(str(config_service._settings.gacha_stock_integration.stock_market.transaction_fee_rate))
        self.minimum_transaction_fee = Decimal(str(config_service._settings.gacha_stock_integration.stock_market.minimum_transaction_fee))
        self.per_share_fee_amount = Decimal(str(config_service._settings.gacha_stock_integration.stock_market.per_share_fee_amount))
        self.executor = ThreadPoolExecutor(max_workers=config_service._settings.gacha_stock_integration.stock_market.chart_thread_pool_workers)

    async def _get_asset_info_for_transaction(self, conn: asyncpg.Connection, asset_symbol_upper: str) -> Tuple[int, Decimal, str]:
        """
        Fetches and locks asset information (id, current price, symbol) for a transaction.
        
        Returns:
            Tuple of (asset_id, current_price, locked_symbol)
            
        Raises:
            MarketStockNotFoundError: If the stock symbol doesn't exist
            MarketDataUnavailableError: If the stock price is invalid (≤0)
        """
        from gacha.exceptions import MarketStockNotFoundError, MarketDataUnavailableError
        
        stock_info = await conn.fetchrow('SELECT asset_id, current_price, asset_symbol FROM virtual_assets WHERE asset_symbol = $1 FOR UPDATE', asset_symbol_upper)
        if not stock_info:
            raise MarketStockNotFoundError(f'股票代碼 {asset_symbol_upper} 不存在。')
            
        asset_id = stock_info['asset_id']
        current_price = Decimal(stock_info['current_price'])
        locked_symbol = stock_info['asset_symbol']
        
        if current_price <= Decimal(0):
            raise MarketDataUnavailableError(f'股票 {locked_symbol} 目前價格異常 (≤0)，無法交易。')
            
        return (asset_id, current_price, locked_symbol)

    def _calculate_transaction_details(self, quantity: int, price: Decimal, transaction_type: str) -> Tuple[Decimal, Decimal, Decimal]:
        """
        Calculates total amount, fee, and net amount for a transaction.
        
        Returns:
            Tuple[Decimal, Decimal, Decimal]: (total_amount_before_fee, final_fee, net_amount_with_fee)
            
        Raises:
            InvalidGameActionError: If the transaction type is unknown.
        """
        from gacha.exceptions import InvalidGameActionError
        
        total_amount_before_fee = price * quantity
        fee_percentage = (total_amount_before_fee * self.transaction_fee_rate).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
        fee_per_share_total = (Decimal(str(quantity)) * self.per_share_fee_amount).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
        calculated_fee_before_min = max(fee_percentage, fee_per_share_total)
        final_fee = max(calculated_fee_before_min, self.minimum_transaction_fee.quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP))
        
        if transaction_type == 'BUY':
            net_amount_with_fee = total_amount_before_fee + final_fee
        elif transaction_type == 'SELL':
            net_amount_with_fee = total_amount_before_fee - final_fee
        else:
            raise InvalidGameActionError(f'未知的交易類型: {transaction_type}')
            
        return (total_amount_before_fee, final_fee, net_amount_with_fee)

    async def _verify_and_update_user_balance(self, conn: asyncpg.Connection, user_id: int, cost_or_proceeds: Decimal, transaction_type: str) -> Decimal:
        """
        Verifies user balance and updates it. For BUY, cost_or_proceeds is positive (deducted).
        For SELL, cost_or_proceeds is positive (added).
        
        Returns:
            Decimal: The new balance after the transaction.
            
        Raises:
            InsufficientFundsError: If the user doesn't have enough balance for BUY.
            InvalidGameActionError: If the transaction type is unknown.
        """
        from gacha.exceptions import InsufficientFundsError, InvalidGameActionError
        
        user_balance_data = await self.user_service.user_repo.get_user_balance_for_update(user_id, connection=conn)
        current_oil_balance = Decimal(user_balance_data if user_balance_data is not None else 0)
        
        if transaction_type == 'BUY':
            if current_oil_balance < cost_or_proceeds:
                raise InsufficientFundsError(
                    required_amount=int(cost_or_proceeds),
                    current_balance=int(current_oil_balance)
                )
            new_oil_balance_decimal = current_oil_balance - cost_or_proceeds
        elif transaction_type == 'SELL':
            new_oil_balance_decimal = current_oil_balance + cost_or_proceeds
        else:
            raise InvalidGameActionError(f'未知的交易類型: {transaction_type} in balance update.')
            
        await self.user_service.user_repo.update_balance(
            user_id, 
            int(new_oil_balance_decimal.to_integral_value(rounding=ROUND_HALF_UP)), 
            connection=conn
        )
        return new_oil_balance_decimal

    async def _update_portfolio_after_buy(self, conn: asyncpg.Connection, user_id: int, asset_id: int, quantity_bought: int, cost_per_unit: Decimal, total_cost_before_fee: Decimal):
        """Updates or creates a portfolio entry after a buy transaction."""
        portfolio_entry = await conn.fetchrow('SELECT id, quantity, average_buy_price FROM player_portfolios WHERE user_id = $1 AND asset_id = $2 FOR UPDATE', user_id, asset_id)
        if portfolio_entry:
            old_quantity = Decimal(portfolio_entry['quantity'])
            old_avg_price = Decimal(portfolio_entry['average_buy_price'])
            old_total_value = old_avg_price * old_quantity
            new_quantity = old_quantity + Decimal(quantity_bought)
            new_total_value = old_total_value + total_cost_before_fee
            new_avg_price = new_total_value / new_quantity if new_quantity > Decimal(0) else Decimal(0)
            await conn.execute('UPDATE player_portfolios SET quantity = $1, average_buy_price = $2, last_transaction_at = CURRENT_TIMESTAMP WHERE id = $3', int(new_quantity), new_avg_price, portfolio_entry['id'])
        else:
            await conn.execute('INSERT INTO player_portfolios (user_id, asset_id, quantity, average_buy_price, last_transaction_at) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)', user_id, asset_id, quantity_bought, cost_per_unit)

    async def _update_portfolio_after_sell(self, conn: asyncpg.Connection, user_id: int, asset_id: int, quantity_sold: int) -> int:
        """
        Updates or deletes a portfolio entry after a sell.
        Returns the quantity owned before this sale.
        
        Raises:
            InsufficientStockQuantityError: If the user doesn't have enough stocks to sell.
        """
        from gacha.exceptions import InsufficientStockQuantityError
        
        portfolio_entry = await conn.fetchrow('SELECT id, quantity FROM player_portfolios WHERE user_id = $1 AND asset_id = $2 FOR UPDATE', user_id, asset_id)
        owned_qty = portfolio_entry['quantity'] if portfolio_entry else 0
        
        if not portfolio_entry or owned_qty < quantity_sold:
            raise InsufficientStockQuantityError(
                f'股票持有數量不足。需要出售: {quantity_sold}, 實際持有: {owned_qty}。',
                required_quantity=quantity_sold,
                available_quantity=owned_qty
            )
            
        new_quantity_in_portfolio = owned_qty - quantity_sold
        if new_quantity_in_portfolio == 0:
            await conn.execute('DELETE FROM player_portfolios WHERE id = $1', portfolio_entry['id'])
        else:
            await conn.execute('UPDATE player_portfolios SET quantity = $1, last_transaction_at = CURRENT_TIMESTAMP WHERE id = $2', new_quantity_in_portfolio, portfolio_entry['id'])
            
        return owned_qty

    async def _record_market_transaction(self, conn: asyncpg.Connection, user_id: int, asset_id: int, transaction_type: str, quantity: int, price_per_unit: Decimal, total_amount_before_fee: Decimal, fee: Decimal):
        """Records the transaction in the market_transactions table."""
        await conn.execute('INSERT INTO market_transactions (user_id, asset_id, transaction_type, quantity, price_per_unit, total_amount, fee) VALUES ($1, $2, $3, $4, $5, $6, $7)', user_id, asset_id, transaction_type.upper(), quantity, price_per_unit, total_amount_before_fee, fee)

    async def _get_stock_volume_24h(self, conn: asyncpg.Connection, asset_id: int) -> Decimal:
        """Calculates the total trading volume for a stock in the last 24 hours."""
        twenty_four_hours_ago = datetime.utcnow() - timedelta(hours=24)
        volume_data = await conn.fetchval('\n            SELECT COALESCE(SUM(quantity), 0)\n            FROM market_transactions\n            WHERE asset_id = $1 AND timestamp >= $2\n            ', asset_id, twenty_four_hours_ago)
        return Decimal(volume_data if volume_data is not None else 0)

    async def get_asset_id_by_symbol(self, asset_symbol: str) -> Optional[int]:
        """Helper to get asset_id from asset_symbol."""
        async with self.pool.acquire() as conn:
            asset_id = await conn.fetchval('SELECT asset_id FROM virtual_assets WHERE asset_symbol = $1', asset_symbol.upper())
            return asset_id

    async def get_stock_details(self, asset_symbol: str) -> Optional[Dict[str, Any]]:
        async with self.pool.acquire() as conn:
            query = 'SELECT asset_id, asset_symbol, asset_name, description, current_price, linked_criteria_type, linked_criteria_value, linked_pool_context, base_volatility, volatility_factor, influence_weight, last_updated FROM virtual_assets WHERE asset_symbol = $1'
            stock_data = await conn.fetchrow(query, asset_symbol.upper())
            if stock_data:
                return dict(stock_data)
            return None

    async def get_all_stocks_paginated(self, page: int=1, per_page: int=10) -> Dict[str, Any]:
        offset = (page - 1) * per_page
        async with self.pool.acquire() as conn:
            total_query = 'SELECT COUNT(*) FROM virtual_assets WHERE current_price > 0 AND lifecycle_status != $1'
            total_stocks = await conn.fetchval(total_query, 'delisted')
            stocks_query = '\n                SELECT asset_id, asset_symbol, asset_name, current_price, description, lifecycle_status\n                FROM virtual_assets\n                WHERE current_price > 0 AND lifecycle_status != $1\n                ORDER BY asset_symbol\n                LIMIT $2 OFFSET $3\n            '
            current_stocks_raw = await conn.fetch(stocks_query, 'delisted', per_page, offset)
            stocks_processed = []
            for stock_raw_dict in current_stocks_raw:
                stock_data = dict(stock_raw_dict)
                asset_id = stock_data['asset_id']
                price_history_for_trend = await conn.fetch('\n                    SELECT price, timestamp\n                    FROM (\n                        SELECT price, timestamp, ROW_NUMBER() OVER (PARTITION BY asset_id ORDER BY timestamp DESC) as rn\n                        FROM asset_price_history\n                        WHERE asset_id = $1\n                    ) ranked_prices\n                    WHERE rn <= 2 -- Get the top 2 most recent records\n                    ORDER BY timestamp DESC;\n                ', asset_id)
                current_price_from_va = Decimal(str(stock_data['current_price']))
                previous_price_for_trend = None
                if len(price_history_for_trend) == 2:
                    previous_price_for_trend = Decimal(str(price_history_for_trend[1]['price']))
                elif len(price_history_for_trend) == 1:
                    single_historical_price = Decimal(str(price_history_for_trend[0]['price']))
                    if single_historical_price != current_price_from_va:
                        previous_price_for_trend = single_historical_price
                if previous_price_for_trend is not None and previous_price_for_trend == current_price_from_va:
                    stock_data['previous_price'] = None
                else:
                    stock_data['previous_price'] = previous_price_for_trend
                stock_data['volume_24h'] = await self._get_stock_volume_24h(conn, asset_id)
                stocks_processed.append(stock_data)
            return {'stocks': stocks_processed, 'total_stocks': total_stocks, 'page': page, 'per_page': per_page, 'total_pages': math.ceil(total_stocks / per_page) if total_stocks > 0 else 1}

    async def buy_stock(self, user_id: int, asset_symbol: str, quantity: int, price_per_unit: Optional[Decimal]=None) -> str:
        """
        Allows a user to buy a specified quantity of a stock by symbol.
        
        Returns:
            str: A success message with transaction details.
            
        Raises:
            InvalidGameActionError: If the quantity is less than or equal to zero.
            MarketStockNotFoundError: If the stock symbol is not found.
            MarketDataUnavailableError: If the stock price is invalid.
            InsufficientFundsError: If the user doesn't have enough balance.
            DatabaseOperationError: If there's an unexpected database error.
        """
        from gacha.exceptions import (
            InvalidGameActionError, 
            MarketStockNotFoundError, 
            MarketDataUnavailableError,
            InsufficientFundsError, 
            DatabaseOperationError
        )
        
        if quantity <= 0:
            raise InvalidGameActionError('購買數量必須大於0')
            
        asset_symbol_upper = asset_symbol.upper()
        async with self.pool.acquire() as conn:
            async with conn.transaction():
                try:
                    asset_id, actual_transaction_price, locked_symbol = await self._get_asset_info_for_transaction(conn, asset_symbol_upper)
                    total_cost_before_fee, fee, total_cost_with_fee = self._calculate_transaction_details(quantity, actual_transaction_price, 'BUY')
                    await self._verify_and_update_user_balance(conn, user_id, total_cost_with_fee, 'BUY')
                    await self._update_portfolio_after_buy(conn, user_id, asset_id, quantity, actual_transaction_price, total_cost_before_fee)
                    await self._record_market_transaction(conn, user_id, asset_id, 'BUY', quantity, actual_transaction_price, total_cost_before_fee, fee)
                    return f'成功購買 {quantity} 股 {locked_symbol}，花費 {total_cost_with_fee:.0f} 油幣 (含 {fee:.0f} 手續費)。'
                except (InvalidGameActionError, MarketStockNotFoundError, MarketDataUnavailableError, InsufficientFundsError):
                    # 讓這些特定的錯誤直接向上傳播
                    raise
                except Exception as e:
                    logger.error('Unexpected error during buy_stock for user %s, symbol %s: %s', user_id, asset_symbol_upper, e, exc_info=True)
                    raise DatabaseOperationError('購買股票時發生未預期的內部錯誤') from e

    async def sell_stock(self, user_id: int, asset_symbol: str, quantity: int, price_per_unit: Optional[Decimal]=None) -> str:
        """Allows a user to sell a specified quantity of a stock by symbol.
        
        Returns:
            str: A success message with transaction details.
            
        Raises:
            InvalidGameActionError: If the quantity is less than or equal to zero.
            MarketStockNotFoundError: If the stock symbol is not found.
            InsufficientStockQuantityError: If the user doesn't have enough stocks to sell.
            DatabaseOperationError: If there's an unexpected database error.
        """
        from gacha.exceptions import InvalidGameActionError, MarketStockNotFoundError, InsufficientStockQuantityError, DatabaseOperationError
        
        if quantity <= 0:
            raise InvalidGameActionError('出售數量必須大於0。')
            
        asset_symbol_upper = asset_symbol.upper()
        async with self.pool.acquire() as conn:
            async with conn.transaction():
                try:
                    asset_id, actual_transaction_price, locked_symbol = await self._get_asset_info_for_transaction(conn, asset_symbol_upper)
                    await self._update_portfolio_after_sell(conn, user_id, asset_id, quantity)
                    total_proceeds_before_fee, fee, total_proceeds_after_fee = self._calculate_transaction_details(quantity, actual_transaction_price, 'SELL')
                    await self._verify_and_update_user_balance(conn, user_id, total_proceeds_after_fee, 'SELL')
                    await self._record_market_transaction(conn, user_id, asset_id, 'SELL', quantity, actual_transaction_price, total_proceeds_before_fee, fee)
                    return f'成功出售 {quantity} 股 {locked_symbol}，獲得 {total_proceeds_after_fee:.0f} 油幣 (已扣除 {fee:.0f} 手續費)。'
                except ValueError as ve:
                    logger.warning('Failed to sell stock %s for user %s: %s', asset_symbol_upper, user_id, ve)
                    # Convert ValueError to appropriate domain exceptions
                    error_msg = str(ve)
                    if "not found" in error_msg.lower():
                        raise MarketStockNotFoundError(f"找不到股票代碼 {asset_symbol_upper}")
                    elif "insufficient" in error_msg.lower() or "not enough" in error_msg.lower():
                        raise InsufficientStockQuantityError(f"持有股數不足，無法出售 {quantity} 股 {asset_symbol_upper}")
                    else:
                        raise InvalidGameActionError(error_msg)
                except Exception as e:
                    logger.error('Unexpected error during sell_stock for user %s, symbol %s: %s', user_id, asset_symbol_upper, e, exc_info=True)
                    raise DatabaseOperationError('出售股票時發生未預期的內部錯誤。') from e

    async def get_user_stock_quantity(self, user_id: int, asset_id: int) -> int:
        """Fetches the quantity of a specific stock a user owns."""
        async with self.pool.acquire() as conn:
            quantity = await conn.fetchval('SELECT quantity FROM player_portfolios WHERE user_id = $1 AND asset_id = $2', user_id, asset_id)
            return quantity if quantity is not None else 0

    async def get_user_portfolio_assets(self, user_id: int) -> List[int]:
        """Fetches a list of asset_ids the user currently holds."""
        async with self.pool.acquire() as conn:
            rows = await conn.fetch('SELECT asset_id FROM player_portfolios WHERE user_id = $1 AND quantity > 0', user_id)
            return [row['asset_id'] for row in rows]

    async def get_user_portfolio(self, user_id: int) -> Dict[str, Any]:
        async with self.pool.acquire() as conn:
            query = '\n            SELECT pp.asset_id, va.asset_symbol, va.asset_name, pp.quantity, pp.average_buy_price,\n                   va.current_price AS current_market_price, va.lifecycle_status\n            FROM player_portfolios pp\n            JOIN virtual_assets va ON pp.asset_id = va.asset_id\n            WHERE pp.user_id = $1 AND pp.quantity > 0\n            ORDER BY va.asset_symbol;\n            '
            portfolio_rows = await conn.fetch(query, user_id)
            holdings = []
            total_portfolio_value_at_current = Decimal(0)
            total_portfolio_cost_basis = Decimal(0)
            for row in portfolio_rows:
                quantity = Decimal(row['quantity'])
                avg_buy_price = Decimal(row['average_buy_price'])
                current_market_price = Decimal(row['current_market_price'])
                cost_basis = quantity * avg_buy_price
                current_value = quantity * current_market_price
                unrealized_pnl = current_value - cost_basis
                total_portfolio_value_at_current += current_value
                total_portfolio_cost_basis += cost_basis
                holdings.append({'asset_id': row['asset_id'], 'asset_symbol': row['asset_symbol'], 'asset_name': row['asset_name'], 'quantity': int(quantity), 'average_buy_price': float(avg_buy_price), 'current_market_price': float(current_market_price), 'cost_basis': float(cost_basis), 'current_value': float(current_value), 'unrealized_pnl': float(unrealized_pnl), 'lifecycle_status': row['lifecycle_status']})
            overall_unrealized_pnl = total_portfolio_value_at_current - total_portfolio_cost_basis
            user_gacha_info = await self.user_service.get_user(user_id, create_if_missing=True)
            user_oil_balance = user_gacha_info.oil_balance if user_gacha_info else 0
            return {'user_oil_balance': user_oil_balance, 'holdings': holdings, 'total_portfolio_value_at_current_market_price': float(total_portfolio_value_at_current), 'total_portfolio_cost_basis': float(total_portfolio_cost_basis), 'overall_unrealized_pnl': float(overall_unrealized_pnl), 'total_asset_value': float(user_oil_balance + total_portfolio_value_at_current)}

    async def get_aggregated_buy_sell_volume_7d(self, asset_id: int) -> Dict[str, int]:
        """
        Fetches the aggregated buy and sell volume for a given asset_id over the last 7 days.
        """
        seven_days_ago = datetime.utcnow() - timedelta(days=7)
        query = "\n            SELECT\n                COALESCE(SUM(CASE WHEN transaction_type = 'BUY' THEN quantity ELSE 0 END), 0) as total_buy_volume,\n                COALESCE(SUM(CASE WHEN transaction_type = 'SELL' THEN quantity ELSE 0 END), 0) as total_sell_volume\n            FROM market_transactions\n            WHERE asset_id = $1 AND timestamp >= $2;\n        "
        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow(query, asset_id, seven_days_ago)
            if row:
                return {'total_buy_volume': int(row['total_buy_volume']), 'total_sell_volume': int(row['total_sell_volume'])}
            return {'total_buy_volume': 0, 'total_sell_volume': 0}
        except Exception as e:
            logger.error('Error fetching aggregated buy/sell volume for asset %s: %s', asset_id, e, exc_info=True)
            return {'total_buy_volume': 0, 'total_sell_volume': 0}

    async def get_price_history_for_chart_7d(self, asset_id: int) -> List[Dict[str, Any]]:
        """
        Fetches and downsamples price history for a given asset_id over the last 7 days,
        aiming for approximately hourly data points.
        """
        seven_days_ago = datetime.utcnow() - timedelta(days=7)
        query = "\n            SELECT\n                date_trunc('hour', timestamp) as hour_bucket,\n                AVG(price) as avg_price\n            FROM asset_price_history\n            WHERE asset_id = $1 AND timestamp >= $2\n            GROUP BY hour_bucket\n            ORDER BY hour_bucket ASC;\n        "
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(query, asset_id, seven_days_ago)
            price_history = []
            for row in rows:
                price_history.append({'timestamp': row['hour_bucket'], 'price': Decimal(row['avg_price'])})
            return price_history
        except Exception as e:
            logger.error('Error fetching price history for chart for asset %s: %s', asset_id, e, exc_info=True)
            return []

    async def get_recent_transactions(self, asset_id: int, limit: int=5) -> List[Dict[str, Any]]:
        """
        Fetches the most recent transactions for a given asset_id, including user display names.
        Prioritizes gacha_users.nickname, then Discord display_name, then gacha_users.username, then user_id.

        Note: Fetching Discord display_name individually for each user (self.bot.get_user)
        can become a performance bottleneck if called frequently for many transactions with diverse users.
        Consider batch fetching user data or a local caching layer for Discord names if performance issues arise.
        """
        query = '\n            SELECT\n                mt.user_id, -- Added user_id for Discord lookup\n                COALESCE(u.nickname, CAST(u.user_id AS VARCHAR)) AS user_nickname_from_db,\n                mt.transaction_type,\n                mt.quantity,\n                mt.price_per_unit,\n                mt.timestamp\n            FROM market_transactions mt\n            JOIN gacha_users u ON mt.user_id = u.user_id\n            WHERE mt.asset_id = $1\n            ORDER BY mt.timestamp DESC\n            LIMIT $2;\n        '
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(query, asset_id, limit)
            recent_transactions = []
            for row_data in rows:
                user_id = row_data['user_id']
                user_nickname = row_data['user_nickname_from_db']
                if user_nickname == str(user_id):
                    try:
                        discord_user = self.bot.get_user(user_id)
                        if discord_user and discord_user.display_name:
                            user_nickname = discord_user.display_name
                    except Exception as e_discord:
                        logger.warning('Could not fetch Discord display_name for user_id %s: %s', user_id, e_discord)
                recent_transactions.append({'user_nickname': user_nickname, 'transaction_type': row_data['transaction_type'], 'quantity': row_data['quantity'], 'price_per_unit': Decimal(row_data['price_per_unit']), 'timestamp': row_data['timestamp']})
            return recent_transactions
        except Exception as e:
            logger.error('Error fetching recent transactions for asset %s: %s', asset_id, e, exc_info=True)
            return []

    def _apply_chart_styles(self, fig: plt.Figure, ax: plt.Axes, asset_symbol: str):
        """設定圖表樣式"""
        # 設定圖表顏色
        fig.set_facecolor(self.CHART_FIG_FACE_COLOR)
        ax.set_facecolor(self.CHART_AX_FACE_COLOR)
        
        # 設定標題和標籤
        days_limit = config_service._settings.gacha_stock_integration.stock_market.price_history_days
        oil_emoji = config_service.get_oil_emoji()
        
        ax.set_title(f'{asset_symbol} 價格趨勢 (最近 {days_limit} 天)', 
                    fontsize=14, color=self.CHART_TEXT_COLOR)
        ax.set_xlabel('日期時間 (UTC)', 
                     fontsize=10, color=self.CHART_TEXT_COLOR)
        ax.set_ylabel(f'價格 ({oil_emoji})', 
                     fontsize=10, color=self.CHART_TEXT_COLOR)
        
        # 設定刻度標籤顏色
        for label in ax.get_xticklabels() + ax.get_yticklabels():
            label.set_color(self.CHART_TEXT_COLOR)
        
        # 設定其他樣式
        ax.tick_params(axis='both', colors=self.CHART_TEXT_COLOR)
        for spine in ax.spines.values():
            spine.set_color(self.CHART_TEXT_COLOR)
        ax.grid(True, linestyle='--', alpha=0.7, color=self.CHART_GRID_COLOR)

    def _do_generate_price_trend_chart(self, asset_symbol: str, price_history_data: List[Dict[str, Any]]) -> Optional[bytes]:
        """
        Internal synchronous method to generate the chart using Matplotlib.
        This will be run in a ThreadPoolExecutor.
        """
        if not price_history_data:
            logger.warning('No price history data provided for chart generation of %s.', asset_symbol)
            return None
        timestamps = [item['timestamp'] for item in price_history_data]
        prices = [float(item['price']) for item in price_history_data]
        if not timestamps or not prices:
            logger.warning('Empty timestamps or prices for chart generation of %s.', asset_symbol)
            return None
        
        # 不使用 plt.rc_context() 以避免重置字體設定
        fig, ax = plt.subplots(figsize=(config_service._settings.gacha_stock_integration.stock_market.chart_figsize_width, config_service._settings.gacha_stock_integration.stock_market.chart_figsize_height))
        self._apply_chart_styles(fig, ax, asset_symbol)
        ax.plot(timestamps, prices, marker='o', linestyle='-', markersize=4, color=self.CHART_LINE_COLOR)
        ax.xaxis.set_major_formatter(mdates.DateFormatter(config_service._settings.gacha_stock_integration.stock_market.chart_date_format))
        ax.xaxis.set_major_locator(mticker.MaxNLocator(nbins=config_service._settings.gacha_stock_integration.stock_market.chart_max_xticks, prune='both'))
        fig.autofmt_xdate()
        ax.yaxis.set_major_formatter(mticker.FormatStrFormatter('%.2f'))
        plt.tight_layout()
        buf = io.BytesIO()
        try:
            plt.savefig(buf, format='png', dpi=config_service._settings.gacha_stock_integration.stock_market.chart_dpi)
            buf.seek(0)
            image_bytes = buf.getvalue()
            return image_bytes
        except Exception as e_save:
            logger.error('Error saving Matplotlib chart for %s to buffer: %s', asset_symbol, e_save, exc_info=True)
            return None
        finally:
            buf.close()
            plt.close(fig)

    async def generate_price_trend_chart_7d(self, asset_id: int, asset_symbol: str, price_history_data: List[Dict[str, Any]]) -> Optional[bytes]:
        """
        Asynchronously generates a 7-day price trend chart image using Matplotlib in a ThreadPoolExecutor.
        """
        if not price_history_data:
            price_history_data = await self.get_price_history_for_chart_7d(asset_id)
            if not price_history_data:
                logger.warning('Still no price history data after fetching for asset %s to generate chart.', asset_id)
                return None
        loop = asyncio.get_running_loop()
        func_to_run = partial(self._do_generate_price_trend_chart, asset_symbol, price_history_data)
        try:
            image_bytes = await loop.run_in_executor(self.executor, func_to_run)
            return image_bytes
        except Exception as e:
            logger.error('Error in executor for chart generation for asset %s: %s', asset_symbol, e, exc_info=True)
            return None

    async def _get_user_specific_stock_details(self, user_id: int, asset_id: int) -> Tuple[Optional[Decimal], Optional[int]]:
        """Fetches user's oil balance and quantity of a specific stock."""
        user_oil_balance: Optional[Decimal] = None
        user_stock_quantity: Optional[int] = None
        try:
            user_gacha_info = await self.user_service.get_user(user_id, create_if_missing=False)
            if user_gacha_info:
                user_oil_balance = Decimal(user_gacha_info.oil_balance)
            user_stock_quantity = await self.get_user_stock_quantity(user_id, asset_id)
        except Exception as e_user_data:
            logger.error('Error fetching user-specific data for user %s, asset %s: %s', user_id, asset_id, e_user_data, exc_info=True)
        return (user_oil_balance, user_stock_quantity)

    async def get_stock_details_for_view(self, asset_symbol_or_id: Union[str, int], user_id: Optional[int]=None) -> Optional[Dict[str, Any]]:
        """
        Fetches all necessary details for the StockDetailView.
        """
        stock_data_dict: Optional[Dict[str, Any]] = None
        async with self.pool.acquire() as conn:
            if isinstance(asset_symbol_or_id, str):
                stock_data_record = await conn.fetchrow('SELECT * FROM virtual_assets WHERE asset_symbol = $1', asset_symbol_or_id.upper())
            else:
                stock_data_record = await conn.fetchrow('SELECT * FROM virtual_assets WHERE asset_id = $1', asset_symbol_or_id)
            if not stock_data_record:
                logger.warning('Could not find stock data for %s in get_stock_details_for_view.', asset_symbol_or_id)
                return None
            stock_data_dict = dict(stock_data_record)
        asset_id = stock_data_dict['asset_id']
        asset_symbol = stock_data_dict['asset_symbol']
        aggregated_volume_7d = await self.get_aggregated_buy_sell_volume_7d(asset_id)
        recent_transactions_5 = await self.get_recent_transactions(asset_id, limit=5)
        price_history_for_chart = await self.get_price_history_for_chart_7d(asset_id)
        chart_image_bytes: Optional[bytes] = None
        if price_history_for_chart:
            chart_image_bytes = await self.generate_price_trend_chart_7d(asset_id, asset_symbol, price_history_for_chart)
        else:
            logger.warning('No price history data to generate chart for asset %s in get_stock_details_for_view.', asset_id)
        user_oil_balance: Optional[Decimal] = None
        user_stock_quantity: Optional[int] = None
        if user_id is not None:
            user_oil_balance, user_stock_quantity = await self._get_user_specific_stock_details(user_id, asset_id)
        return {'stock_data': stock_data_dict, 'aggregated_volume_7d': aggregated_volume_7d, 'recent_transactions_5': recent_transactions_5, 'chart_image_bytes': chart_image_bytes, 'user_oil_balance': user_oil_balance, 'user_stock_quantity': user_stock_quantity}

    async def get_related_news_for_stock(self, asset_id: int, limit: int=5) -> List[Dict[str, Any]]:
        """
        Fetches recent news headlines related to a specific stock asset_id.
        """
        query = '\n            SELECT headline, published_at, news_type, character_archetype, character_name\n            FROM market_news\n            WHERE affected_asset_id = $1\n            ORDER BY published_at DESC\n            LIMIT $2;\n        '
        try:
            async with self.pool.acquire() as conn:
                news_records = await conn.fetch(query, asset_id, limit)
            return [dict(record) for record in news_records]
        except Exception as e:
            logger.error('Error fetching related news for asset %s: %s', asset_id, e, exc_info=True)
            return []

    async def search_asset_symbols(self, search_term: str) -> List[str]:
        """
        Searches for asset symbols in the virtual_assets table for autocomplete.
        Returns a list of matching asset symbols.
        """
        choices = []
        query_limit = 25
        try:
            async with self.pool.acquire() as conn:
                if search_term:
                    sql_query = '\n                        SELECT asset_symbol\n                        FROM public.virtual_assets\n                        WHERE asset_symbol ILIKE $1\n                        ORDER BY asset_symbol\n                        LIMIT $2;\n                    '
                    db_records = await conn.fetch(sql_query, f'{search_term}%', query_limit)
                else:
                    sql_query = '\n                        SELECT asset_symbol\n                        FROM public.virtual_assets\n                        ORDER BY asset_symbol  -- Or some other metric like trading volume if available\n                        LIMIT $1;\n                    '
                    db_records = await conn.fetch(sql_query, query_limit)
                for record in db_records:
                    choices.append(record['asset_symbol'])
        except Exception as e:
            logger.error("Error during search_asset_symbols DB query (term: '%s'): %s", search_term, e, exc_info=True)
            return []
        return choices