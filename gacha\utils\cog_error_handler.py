"""
Gacha 系統 Cog 層統一錯誤處理工具
根據重構文檔實現純異常模式的錯誤處理
"""
import discord
from typing import Optional, Dict, Any, Type
from utils.logger import logger
from gacha.exceptions import (
    MinBetNotMetError,
    InsufficientFundsError,
    UserNotFoundError,
    GameNotFoundError,
    GameAlreadyOverError,
    InvalidGameActionError,
    GameSettlementError,
    DatabaseOperationError,
    GachaSystemError,
    CardNotFoundError,
    InvalidPoolTypeError,
    RecordNotFoundError,
    StoredPriceError,
    DatabaseIntegrityError,
    MarketSystemError,
    CooldownError
)


async def handle_gacha_error(
    interaction: discord.Interaction, 
    error: Exception, 
    context: str,
    ephemeral: bool = True
) -> None:
    """
    統一的 Gacha 系統錯誤處理函數
    
    Args:
        interaction: Discord 交互對象
        error: 捕獲的異常
        context: 錯誤發生的上下文描述
        ephemeral: 是否為私人訊息
    """
    embed = None
    
    # 用戶相關錯誤
    if isinstance(error, UserNotFoundError):
        embed = discord.Embed(
            title="找不到帳戶",
            description="請稍後再試或聯繫管理員",
            color=discord.Color.red()
        )
    
    # 卡片相關錯誤
    elif isinstance(error, CardNotFoundError):
        embed = discord.Embed(
            title="找不到卡片",
            description=f"卡片 ID: {error.card_id if error.card_id else '未知'} 不存在或已被移除",
            color=discord.Color.red()
        )
    
    # 卡池相關錯誤
    elif isinstance(error, InvalidPoolTypeError):
        embed = discord.Embed(
            title="無效卡池",
            description=str(error),
            color=discord.Color.red()
        )
    
    # 經濟相關錯誤
    elif isinstance(error, InsufficientFundsError):
        embed = discord.Embed(
            title="餘額不足",
            description=f"需要 {error.required_amount} 油幣，目前餘額 {error.current_balance} 油幣",
            color=discord.Color.red()
        )
    
    elif isinstance(error, StoredPriceError):
        embed = discord.Embed(
            title="價格錯誤",
            description="無法獲取卡片價格資訊，請稍後再試",
            color=discord.Color.red()
        )
    
    # 冷卻時間錯誤
    elif isinstance(error, CooldownError):
        embed = discord.Embed(
            title="冷卻中",
            description=str(error),
            color=discord.Color.gold()
        )
        
        if error.next_available_time:
            next_time_str = f'<t:{int(error.next_available_time)}:R>'
            embed.add_field(name="下次可用時間", value=next_time_str, inline=False)
            
        if error.cooldown_type == 'daily':
            embed.set_footer(text="每日獎勵每24小時可領取一次")
        elif error.cooldown_type == 'hourly':
            embed.set_footer(text="每小時獎勵每60分鐘可領取一次")
            
        # 添加適當的表情符號，使錯誤訊息更友好
        if interaction.guild and interaction.guild.id:
            embed.set_thumbnail(url="https://cdn.dev.conquest.bot/thumbnails/timer.png")
    
    # 遊戲相關錯誤
    elif isinstance(error, MinBetNotMetError):
        embed = discord.Embed(
            title="下注金額太低",
            description=f"最低下注為 {error.min_bet} 油幣，您下注了 {error.bet_placed} 油幣",
            color=discord.Color.red()
        )
    
    elif isinstance(error, GameNotFoundError):
        embed = discord.Embed(
            title="遊戲不存在",
            description=f"遊戲 {error.game_id} 不存在或已過期",
            color=discord.Color.red()
        )
    
    elif isinstance(error, GameAlreadyOverError):
        embed = discord.Embed(
            title="遊戲已結束",
            description=f"遊戲 {error.game_id} 已經結束",
            color=discord.Color.red()
        )
    
    elif isinstance(error, InvalidGameActionError):
        embed = discord.Embed(
            title="無效操作",
            description=f"無效的 {error.game_type} 遊戲操作：{error.action}",
            color=discord.Color.red()
        )
    
    elif isinstance(error, GameSettlementError):
        embed = discord.Embed(
            title="遊戲結算失敗",
            description=f"遊戲結算時發生錯誤：{error.reason}",
            color=discord.Color.red()
        )
    
    # 市場相關錯誤
    elif isinstance(error, MarketSystemError):
        embed = discord.Embed(
            title="市場系統錯誤",
            description=str(error),
            color=discord.Color.red()
        )
    
    # 資料庫相關錯誤
    elif isinstance(error, DatabaseIntegrityError):
        embed = discord.Embed(
            title="資料完整性錯誤",
            description="資料庫操作違反完整性約束，請稍後再試",
            color=discord.Color.red()
        )
        logger.error(f"資料完整性錯誤在 {context}: {error}", exc_info=True)
    
    elif isinstance(error, DatabaseOperationError):
        embed = discord.Embed(
            title="資料庫錯誤",
            description="資料庫操作失敗，請稍後再試",
            color=discord.Color.red()
        )
        logger.error(f"資料庫操作錯誤在 {context}: {error}", exc_info=True)
    
    elif isinstance(error, RecordNotFoundError):
        embed = discord.Embed(
            title="記錄不存在",
            description="找不到請求的記錄，請確認輸入是否正確",
            color=discord.Color.red()
        )
    
    # 一般 Gacha 系統錯誤
    elif isinstance(error, GachaSystemError):
        embed = discord.Embed(
            title="系統錯誤",
            description=str(error),
            color=discord.Color.red()
        )
    
    # 常見 Python 錯誤
    elif isinstance(error, ValueError):
        embed = discord.Embed(
            title="輸入錯誤",
            description=str(error),
            color=discord.Color.red()
        )
    
    # 未預期的錯誤
    else:
        logger.error(f"未處理的錯誤在 {context}: {type(error).__name__}: {error}", exc_info=True)
        embed = discord.Embed(
            title="系統錯誤",
            description="發生未預期的錯誤，請稍後再試",
            color=discord.Color.red()
        )
    
    try:
        if interaction.response.is_done():
            await interaction.followup.send(embed=embed, ephemeral=ephemeral)
        else:
            await interaction.response.send_message(embed=embed, ephemeral=ephemeral)
    except Exception as send_error:
        logger.error(f"發送錯誤訊息失敗在 {context}: {send_error}")


async def handle_game_error(
    interaction: discord.Interaction, 
    error: Exception, 
    context: str,
    ephemeral: bool = True
) -> None:
    """
    統一的遊戲錯誤處理函數
    
    Args:
        interaction: Discord 交互對象
        error: 捕獲的異常
        context: 錯誤發生的上下文描述
        ephemeral: 是否為私人訊息
    """
    embed = None
    
    if isinstance(error, MinBetNotMetError):
        embed = discord.Embed(
            title="下注金額太低",
            description=f"最低下注為 {error.min_bet} 油幣，您下注了 {error.bet_placed} 油幣",
            color=discord.Color.red()
        )
        
    elif isinstance(error, InsufficientFundsError):
        embed = discord.Embed(
            title="餘額不足",
            description=f"需要 {error.required_amount} 油幣，目前餘額 {error.current_balance} 油幣",
            color=discord.Color.red()
        )
        
    elif isinstance(error, UserNotFoundError):
        embed = discord.Embed(
            title="找不到帳戶",
            description="請稍後再試或聯繫管理員",
            color=discord.Color.red()
        )
        
    elif isinstance(error, GameNotFoundError):
        embed = discord.Embed(
            title="遊戲不存在",
            description=f"遊戲 {error.game_id} 不存在或已過期",
            color=discord.Color.red()
        )
        
    elif isinstance(error, GameAlreadyOverError):
        embed = discord.Embed(
            title="遊戲已結束",
            description=f"遊戲 {error.game_id} 已經結束",
            color=discord.Color.red()
        )
        
    elif isinstance(error, InvalidGameActionError):
        embed = discord.Embed(
            title="無效操作",
            description=f"無效的 {error.game_type} 遊戲操作：{error.action}",
            color=discord.Color.red()
        )
        
    elif isinstance(error, GameSettlementError):
        embed = discord.Embed(
            title="遊戲結算失敗",
            description=f"遊戲結算時發生錯誤：{error.reason}",
            color=discord.Color.red()
        )
        
    elif isinstance(error, DatabaseOperationError):
        embed = discord.Embed(
            title="資料庫錯誤",
            description="資料庫操作失敗，請稍後再試",
            color=discord.Color.red()
        )
        
    elif isinstance(error, GachaSystemError):
        embed = discord.Embed(
            title="系統錯誤",
            description=str(error),
            color=discord.Color.red()
        )
        
    else:
        # 未預期的錯誤
        logger.error(f"未處理的錯誤在 {context}: {type(error).__name__}: {error}", exc_info=True)
        embed = discord.Embed(
            title="系統錯誤",
            description="發生未預期的錯誤，請稍後再試",
            color=discord.Color.red()
        )
    
    try:
        if interaction.response.is_done():
            await interaction.followup.send(embed=embed, ephemeral=ephemeral)
        else:
            await interaction.response.send_message(embed=embed, ephemeral=ephemeral)
    except Exception as send_error:
        logger.error(f"發送錯誤訊息失敗在 {context}: {send_error}")


async def send_error_embed(
    interaction: discord.Interaction,
    title: str,
    description: str,
    ephemeral: bool = True
) -> None:
    """
    發送簡單的錯誤 Embed
    
    Args:
        interaction: Discord 交互對象
        title: 錯誤標題
        description: 錯誤描述
        ephemeral: 是否為私人訊息
    """
    embed = discord.Embed(
        title=title,
        description=description,
        color=discord.Color.red()
    )
    
    try:
        if interaction.response.is_done():
            await interaction.followup.send(embed=embed, ephemeral=ephemeral)
        else:
            await interaction.response.send_message(embed=embed, ephemeral=ephemeral)
    except Exception as send_error:
        logger.error(f"發送錯誤 Embed 失敗: {send_error}")


# 創建一個錯誤處理器工廠，用於生成特定上下文的錯誤處理函數
def create_error_handler(context_name: str, ephemeral: bool = True):
    """
    創建一個特定上下文的錯誤處理函數
    
    Args:
        context_name: 錯誤上下文名稱
        ephemeral: 是否為私人訊息
        
    Returns:
        一個接受 interaction 和 error 參數的錯誤處理函數
    """
    async def error_handler(interaction: discord.Interaction, error: Exception):
        return await handle_gacha_error(interaction, error, context_name, ephemeral)
    return error_handler 