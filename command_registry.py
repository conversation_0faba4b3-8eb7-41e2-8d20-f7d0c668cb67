"""
命令註冊器 - 負責統一管理所有Discord命令的註冊流程
將命令註冊與系統初始化分離，明確職責分工
"""

import logging
import traceback
import inspect # <<< ADDED
from typing import Dict, List, Any, Optional, Callable, Awaitable
import importlib

from discord.ext import commands

from ai_assistant.commands import setup as setup_ai_commands
from cogs.image_processing_cog import setup as setup_image_commands


class CommandRegistry:
    """命令註冊器類 - 負責統一管理所有Discord命令的註冊流程"""

    def __init__(self, bot: commands.Bot, verbose: bool = True):
        """初始化命令註冊器

        Args:
            bot: Discord機器人實例
            verbose: 是否輸出詳細日誌
        """
        self.bot = bot
        self.verbose = verbose
        self.registration_status = {
            "ai_assistant": False,
            "image_processing": False,
            "gacha": False # 新增 Gacha 系統註冊狀態
        }

        # 初始化日誌
        self.log("CommandRegistry 初始化完成")

    def log(self, message: str, level: str = "info") -> None:
        """統一日誌輸出

        Args:
            message: 日誌消息
            level: 日誌級別
        """
        prefix = {
            "info": "➤",
            "success": "✓",
            "error": "❌",
            "warning": "⚠️"
        }.get(level, "➤")

        if self.verbose:
            print(f"{prefix} {message}")

    async def register_all_commands(self) -> bool:
        """註冊所有命令，並在註冊前清理舊命令和擴展

        Returns:
            bool: 是否全部成功
        """
        self.log("\n== 清理舊命令和擴展 ==")
        try:
            # 1. 卸載所有已載入的擴展
            # 獲取當前所有已載入擴展的名稱列表副本，以避免在迭代時修改列表
            loaded_extensions = list(self.bot.extensions.keys())
            if loaded_extensions:
                self.log(f"➤ 準備卸載 {len(loaded_extensions)} 個已載入的擴展...")
                for extension_name in loaded_extensions:
                    try:
                        await self.bot.unload_extension(extension_name)
                        self.log(f"✓ 已卸載擴展: {extension_name}", "success")
                    except Exception as e:
                        # 某些核心擴展可能不允許卸載，或者卸載時可能出錯
                        self.log(f"⚠️ 卸載擴展 {extension_name} 時發生錯誤: {str(e)}", "warning")
            else:
                self.log("➤ 沒有偵測到預先載入的擴展。")

            # 2. 清空全局應用程式命令樹 (Slash Commands)
            # 這樣可以確保所有斜線命令都是重新同步的，避免舊命令殘留
            # 注意: clear_commands 會移除所有全局命令。
            # 如果只想更新現有命令，則此步驟可能不是必需的，sync() 會處理差異。
            # 但為了確保環境乾淨，執行清空是個好習慣，尤其是在開發和重構階段。
            self.log("➤ 正在請求清空全局應用程式命令樹...")
            if self.bot.tree is None:
                self.log("❌ CRITICAL: self.bot.tree is None right before attempting to call clear_commands! Aborting command registration for safety.", "error")
                return False # 提前返回，避免 TypeError
            
            # clear_commands 通常是同步方法，标记命令以供清除。
            # 实际的同步和清除由 await self.bot.tree.sync() 完成。
            self.bot.tree.clear_commands(guild=None) # 清空全局命令 (移除 await)
            # 如果有針對特定伺服器註冊的命令，也需要相應清空它們：
            # for guild_obj in self.bot.guilds:
            #     self.bot.tree.clear_commands(guild=guild_obj) # 同样，这里也不用 await

            self.log("✓ 已請求清空全局應用程式命令樹。", "success")

        except Exception as e:
            self.log(f"❌ 清理舊命令和擴展時發生嚴重錯誤: {str(e)}", "error")
            if self.verbose:
                traceback.print_exc()
            # 根據策略，清理失敗時可以選擇是否繼續執行後續註冊
            # return False # 如果希望清理失敗時中止

        overall_success = True
        self.log("\n== 註冊 CommandRegistry 管理的命令 ==")

        # 1. 註冊AI助手命令
        success = await self._register_ai_commands()
        overall_success = overall_success and success

        # 2. 註冊圖像處理命令
        success = await self._register_image_processing_commands()
        overall_success = overall_success and success

        # 3. 註冊 Gacha 系統命令
        success = await self._register_gacha_commands()
        overall_success = overall_success and success

        # 同步斜線命令
        self.log("\n== 同步斜線命令 ==")
        self.log("➤ 正在同步斜線命令...")
        try:
            # 全局同步命令，確保用戶安裝後可見
            await self.bot.tree.sync(guild=None)
            self.log("✓ 全局斜線命令同步完成", "success")
        except Exception as e:
            self.log(f"❌ 同步斜線命令時發生錯誤: {str(e)}", "error")
            overall_success = False

        return overall_success

    async def _register_ai_commands(self) -> bool:
        """註冊AI助手命令

        Returns:
            bool: 是否成功
        """
        try:
            self.log("➤ 註冊AI助手命令...")
            await setup_ai_commands(self.bot)
            # TimerUtils 日誌使用環境變數配置的日誌級別
            # logging.getLogger("TimerUtils").setLevel(logging.INFO) # 移除硬編碼的設置
            self.log("✓ AI助手命令已註冊", "success")
            self.registration_status["ai_assistant"] = True
            return True
        except Exception as e:
            self.log(f"❌ 註冊AI助手命令時發生錯誤: {str(e)}", "error")
            if self.verbose:
                traceback.print_exc()
            return False

    async def _register_image_processing_commands(self) -> bool:
        """註冊圖像處理命令

        Returns:
            bool: 是否成功
        """
        try:
            self.log("➤ 註冊圖像處理命令...")
            await setup_image_commands(self.bot)
            self.log("✓ 圖像處理命令已註冊", "success")
            self.registration_status["image_processing"] = True
            return True
        except Exception as e:
            self.log(f"❌ 註冊圖像處理命令時發生錯誤: {str(e)}", "error")
            if self.verbose:
                traceback.print_exc()
            return False

    async def _register_gacha_commands(self) -> bool:
        """註冊 Gacha 系統命令

        Returns:
            bool: 是否成功
        """
        # 檢查 GachaSystem 是否已初始化
        if not hasattr(self.bot, 'gacha_system') or not self.bot.gacha_system.is_initialized:
            self.log("❌ GachaSystem 未初始化，無法註冊 Gacha 命令", "error")
            return False
        
        # 這裡列出所有需要由 CommandRegistry 載入的 gacha cogs
        gacha_cogs = [
            "blackjack_cog",
            "collection_cog", 
            "dice_cog",
            "draw_cog",
            "economy_cog",
            "help_cog",
            "leaderboard_cog",
            "market_cog",
            "mines_cog",
            "trading_cog",
            "wish_cog",
            "shop_cog",
            "profile_cog"
            
        ]
        
        success = True
        self.log("\n== 註冊 Gacha 系統命令 ==")
        
        for cog_name in gacha_cogs:
            try:
                self.log(f"➤ 註冊 {cog_name}...")
                
                # 使用 Cog 的 setup 函數來載入 Cog
                module_path = f"gacha.cogs.{cog_name}"
                try:
                    # 嘗試導入模組
                    module = importlib.import_module(module_path)
                    setup_function = getattr(module, 'setup', None)

                    if setup_function and inspect.iscoroutinefunction(setup_function):
                        # 調用 Cog 的 setup 函數
                        # 由於所有服務都通過 GachaSystem 管理，setup 函數可以直接從 bot.gacha_system 獲取需要的服務
                        await setup_function(self.bot)
                        self.log(f"✓ {cog_name} 已註冊", "success")
                    else:
                        self.log(f"❌ {cog_name} 中未找到有效的非同步 'setup' 函數", "error")
                        success = False
                        
                except ImportError:
                    self.log(f"❌ 無法導入模組 {module_path}", "error")
                    if self.verbose:
                        traceback.print_exc()
                    success = False
                    
                except Exception as e:
                    self.log(f"❌ 執行 {cog_name} 的 setup 函數時發生錯誤: {str(e)}", "error")
                    if self.verbose:
                        traceback.print_exc()
                    success = False

            except Exception as e:
                self.log(f"❌ 處理 {cog_name} 註冊時發生未預期錯誤: {str(e)}", "error")
                if self.verbose:
                    traceback.print_exc()
                success = False

        # 更新 gacha 系統整體註冊狀態
        self.registration_status["gacha"] = success
        return success

    def get_registration_report(self) -> Dict[str, Any]:
        """獲取命令註冊狀態報告

        Returns:
            Dict[str, Any]: 註冊狀態報告
        """
        return {
            "status": self.registration_status,
            "overall_success": all(self.registration_status.values())
        }


def get_command_registry(bot: commands.Bot) -> CommandRegistry:
    """獲取命令註冊器實例

    Args:
        bot: Discord機器人實例

    Returns:
        CommandRegistry: 命令註冊器實例
    """
    return CommandRegistry(bot)
