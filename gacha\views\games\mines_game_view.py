# gacha/views/games/mines_game_view.py
import discord
import logging
from typing import (
    Dict,
    Any,
    Optional,
    TYPE_CHECKING,
)  # Import Optional and TYPE_CHECKING

# Import necessary components and constants
from gacha.utils.mines_constants import BOARD_ROWS, BOARD_COLS, TILE_HIDDEN
from gacha.services.games.mines_service import MinesService  # 服務類
from gacha.services.core.economy_service import (
    EconomyService,
)  # Import EconomyService for Handler
from gacha.views.embeds.games.mines_embed_builder import build_mines_game_embed
from gacha.utils import interaction_utils  # 導入 interaction_utils

if TYPE_CHECKING:
    # Import for type checking only to avoid circular dependency
    from gacha.views.handlers.mines_callback_handler import MinesCallbackHandler


class MinesGameView(discord.ui.View):
    """尋寶礦區遊戲進行中的 View (4x5 棋盤 + 提現按鈕)"""

    def __init__(
        self,
        game_state: Dict[str, Any],
        handler: "MinesCallbackHandler",
        player: discord.User | discord.Member,
        timeout=600,
    ):
        # Use local import for type checking within the method or for
        # instantiation if needed here
        from gacha.views.handlers.mines_callback_handler import MinesCallbackHandler

        super().__init__(timeout=timeout)
        self.game_state = game_state
        self.player = player
        self.user_id = game_state["user_id"]
        # Store game_instance_id directly in the view instance
        self.game_instance_id = game_state.get("game_instance_id")
        if not self.game_instance_id:
            # Log an error or raise an exception if game_instance_id is missing
            # This is critical for the view to function correctly
            logging.error(f"MinesGameView initialized without game_instance_id for user {self.user_id}. Game state: {game_state}")
            # Consider raising an exception or handling this case gracefully
            # raise ValueError("game_instance_id is missing in game_state")
        self.interaction_message: Optional[discord.InteractionMessage] = (
            None  # Use Optional
        )

        # Use the passed handler
        self.handler = handler
        if hasattr(
                self.handler,
                "game_view") and self.handler.game_view is None:
            self.handler.game_view = self
        elif hasattr(self.handler, "set_game_view"):  # Or if it has a setter method
            self.handler.set_game_view(self)

        # 動態創建 4x5 方塊按鈕
        board_display = game_state.get(
            "board_display", [[TILE_HIDDEN] * BOARD_COLS] * BOARD_ROWS
        )
        revealed_tiles_set = set(
            tuple(pos) for pos in game_state.get("revealed_tiles", [])
        )

        for r in range(BOARD_ROWS):  # 0-3
            for c in range(BOARD_COLS):  # 0-4
                tile_state = board_display[r][c]
                is_revealed = (r, c) in revealed_tiles_set
                button = discord.ui.Button(
                    label=None,
                    emoji=tile_state,
                    style=(
                        discord.ButtonStyle.secondary
                        if not is_revealed
                        else discord.ButtonStyle.primary
                    ),
                    custom_id=f"mines_tile_{r}_{c}",
                    row=r,
                    disabled=is_revealed or game_state.get("game_over", False),
                )
                # Use lambda to capture r, c, gid, and self (view instance)
                button.callback = (
                    lambda interaction, # Interaction is the first argument passed to callbacks
                           row=r,
                           col=c,
                           gid=self.game_instance_id,
                           view=self: # Capture the view instance (self)
                           self.handler.handle_tile_click(
                               interaction, # Pass interaction
                               row,
                               col,
                               gid, # Pass game_instance_id
                               view # Pass the view instance
                           )
                )
                self.add_item(button)

        # 創建提現按鈕並放在第 5 行 (row=4)
        self.cash_out_button = discord.ui.Button(
            label="💰 提現",
            style=discord.ButtonStyle.success,
            custom_id="mines_cash_out",
            row=4,  # Place cash out button in the last row
            disabled=game_state.get("found_safe_tiles_count", 0) == 0
            or game_state.get("game_over", False),
        )
        # Assign handler's cash out callback, passing game_instance_id and the view instance
        self.cash_out_button.callback = (
            lambda interaction, # Interaction is the first argument
                   gid=self.game_instance_id,
                   view=self: # Capture the view instance (self)
                   self.handler.handle_cash_out(
                       interaction, # Pass interaction
                       gid, # Pass game_instance_id
                       view # Pass the view instance
                   )
        )

        if not game_state.get(
            "game_over", False
        ):  # Only add cash_out if game is not over
            self.add_item(self.cash_out_button)

        if game_state.get("game_over", False):
            # Add "Play Again (Same Settings)" button
            self.play_again_same_button = discord.ui.Button(
                label="再來一局 (相同設定)",
                style=discord.ButtonStyle.primary,
                custom_id="mines_play_again_same",
                row=4,  # Place in the same row as cash_out or below board
            )
            # Use lambda to pass the current game_state to the handler
            self.play_again_same_button.callback = (
                lambda i: self.handler.handle_play_again(i, self.game_state)
            )
            self.add_item(self.play_again_same_button)

            # Add "Play Again (New Settings)" button
            self.play_again_new_button = discord.ui.Button(
                label="調整設定並重玩",
                style=discord.ButtonStyle.secondary,
                custom_id="mines_play_again_new",
                row=4,  # Place in the same row
            )
            # Use lambda to pass the current bet_amount (as old_bet) to the
            # handler
            self.play_again_new_button.callback = (
                lambda i: self.handler.handle_adjust_and_replay(
                    i, self.game_state.get("bet_amount")
                )
            )
            self.add_item(self.play_again_new_button)

    async def interaction_check(
            self, interaction: discord.Interaction) -> bool:
        """
        檢查與此遊戲視圖互動的使用者是否為原始玩家。
        """
        return await interaction_utils.check_user_permission(
            interaction=interaction,
            expected_user_id=self.user_id,
            error_message_on_fail="這不是您的遊戲，無法操作！",
        )

    async def on_timeout(self):
        if self.interaction_message and not self.game_state.get(
                "game_over", False):
            try:
                mines_service: MinesService = (
                    self.handler.mines_service
                )  # Get service via handler
                game_instance_id = self.game_state.get("game_instance_id")
                if game_instance_id:
                    mines_service.end_game(
                        game_instance_id
                    )  # Ensure game state is cleaned up

                embed = build_mines_game_embed(self.game_state, self.player)
                embed.title = "⛏️ 尋寶礦區 - 已超時"
                embed.description = "遊戲因閒置過久已結束。"
                embed.color = discord.Color.orange()
                for item in self.children:
                    item.disabled = True
                await self.interaction_message.edit(
                    embed=embed, view=self
                )  # Edit the message to show timeout
            except discord.NotFound:
                logging.warning(
                    f"Failed to edit timed out mines game message for user {self.user_id} (message not found)."
                )
            except Exception as e:
                logging.error(
                    f"Error handling timeout for mines game user {self.user_id}: {e}"
                )
        self.stop()  # Stop the view regardless
