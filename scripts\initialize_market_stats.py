import asyncio
import asyncpg
import sys
import os
from typing import Dict, Any

# 改進路徑處理，確保能夠找到項目根目錄
current_script_path = os.path.abspath(__file__)
scripts_dir = os.path.dirname(current_script_path)
project_root = os.path.dirname(scripts_dir)  # 獲取項目根目錄

# 添加項目根目錄到Python模塊搜索路徑
sys.path.insert(0, project_root)

# Configure logging
from utils.logger import logger 

from dotenv import load_dotenv

# Load environment variables from .env file
dotenv_path = os.path.join(project_root, '.env')
load_dotenv(dotenv_path=dotenv_path)

# --- Database Configuration ---
DB_USER = os.getenv("PG_USER", "postgres")
DB_PASSWORD = os.getenv("PG_PASSWORD", "postgres")
DB_HOST = os.getenv("PG_HOST", "localhost")
DB_PORT = os.getenv("PG_PORT", "5432")
DB_NAME = os.getenv("GACHA_DB_NAME", "gacha_database") # Ensure this matches your .env for Gacha

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# --- End Database Configuration ---

async def get_db_connection():
    """Establishes and returns an asyncpg database connection."""
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to the database: {e}")
        raise

async def fetch_collection_stats(conn: asyncpg.Connection) -> Dict[int, Dict[str, Any]]:
    """
    Fetches total_owned_quantity, unique_owner_count, and favorite_count
    for each card_id from gacha_user_collections.
    """
    query = """
    SELECT
        card_id,
        SUM(quantity) AS total_owned_quantity,
        COUNT(DISTINCT user_id) AS unique_owner_count,
        SUM(CASE WHEN is_favorite = TRUE THEN 1 ELSE 0 END) AS favorite_count
    FROM
        gacha_user_collections
    GROUP BY
        card_id;
    """
    rows = await conn.fetch(query)
    stats: Dict[int, Dict[str, Any]] = {}
    for row in rows:
        stats[row['card_id']] = {
            'total_owned_quantity': row['total_owned_quantity'],
            'unique_owner_count': row['unique_owner_count'],
            'favorite_count': row['favorite_count']
        }
    return stats

async def fetch_wishlist_stats(conn: asyncpg.Connection) -> Dict[int, int]:
    """Fetches wishlist_count for each card_id from gacha_user_wishes."""
    query = """
    SELECT
        card_id,
        COUNT(user_id) AS wishlist_count
    FROM
        gacha_user_wishes
    GROUP BY
        card_id;
    """
    rows = await conn.fetch(query)
    stats: Dict[int, int] = {row['card_id']: row['wishlist_count'] for row in rows}
    return stats

async def initialize_market_stats(conn: asyncpg.Connection):
    """
    Initializes the gacha_card_market_stats table.
    """

    collection_stats = await fetch_collection_stats(conn)
    wishlist_stats = await fetch_wishlist_stats(conn)

    all_card_ids = set(collection_stats.keys()) | set(wishlist_stats.keys())

    if not all_card_ids:
        return

    insert_count = 0
    update_count = 0

    # Prepare data for batch insertion/update
    records_to_upsert = []
    for card_id in all_card_ids:
        coll_stat = collection_stats.get(card_id, {})
        wish_count = wishlist_stats.get(card_id, 0)

        records_to_upsert.append({
            'card_id': card_id,
            'total_owned_quantity': coll_stat.get('total_owned_quantity', 0),
            'unique_owner_count': coll_stat.get('unique_owner_count', 0),
            'favorite_count': coll_stat.get('favorite_count', 0),
            'wishlist_count': wish_count,
            # supply_demand_modifier and previous_sd_modifier will use default 1.0000
        })

    # Upsert data into gacha_card_market_stats
    # This query will insert if card_id does not exist, or update if it does.
    # For initialization, it's mostly inserts, but ON CONFLICT handles re-runs.
    upsert_query = """
    INSERT INTO gacha_card_market_stats (
        card_id, total_owned_quantity, unique_owner_count, favorite_count, wishlist_count,
        supply_demand_modifier, previous_sd_modifier, last_sd_calculated_at
    ) VALUES (
        $1, $2, $3, $4, $5, 1.0000, 1.0000, CURRENT_TIMESTAMP
    )
    ON CONFLICT (card_id) DO UPDATE SET
        total_owned_quantity = EXCLUDED.total_owned_quantity,
        unique_owner_count = EXCLUDED.unique_owner_count,
        favorite_count = EXCLUDED.favorite_count,
        wishlist_count = EXCLUDED.wishlist_count,
        last_sd_calculated_at = CURRENT_TIMESTAMP;
    """
    # For initialization, we typically want to overwrite with fresh calculations.
    # The default behavior of the ON CONFLICT above is to update with the new values.

    
    # Execute in a transaction
    async with conn.transaction():
        # It's generally more efficient to use executemany for batch operations
        # However, asyncpg's ON CONFLICT DO UPDATE works per row with execute.
        # For a large number of records, consider staging tables or more complex batching if performance is critical.
        # For this initialization script, iterating and executing should be acceptable.
        for record in records_to_upsert:
            try:
                # Check if record exists to count inserts vs updates (optional, for logging)
                
                await conn.execute(
                    upsert_query,
                    record['card_id'],
                    record['total_owned_quantity'],
                    record['unique_owner_count'],
                    record['favorite_count'],
                    record['wishlist_count']
                )
            except Exception as e:
                logger.error(f"Error upserting record for card_id {record['card_id']}: {e}")
                # Decide if one error should stop the whole script or just log and continue
                # For initialization, it might be better to log and continue to process other cards.

    # A more accurate count of inserts/updates would require querying before/after or more complex logic.
    # For simplicity, we'll just log the total number of records processed.


async def main():
    conn = None
    try:
        conn = await get_db_connection()
        await initialize_market_stats(conn)
    except Exception as e:
        logger.error(f"An error occurred during the initialization script: {e}", exc_info=True)
    finally:
        if conn:
            await conn.close()

if __name__ == "__main__":
    # Basic check if essential DB components were loaded from .env (or defaults used)
    # The placeholder check is no longer relevant as we construct from env vars.
    # A more robust check might be to see if DB_HOST is still 'localhost' if that's not your actual dev host.
    if not DB_HOST or (DB_HOST == "localhost" and os.getenv("PG_HOST") is None): # Example check
        logger.warning("DATABASE_URL might be using default values if .env is not configured correctly.")
    
    # Check if any of the core components are default and log a warning if so,
    # as it might indicate .env wasn't loaded or is missing keys.
    if DB_USER == "postgres" and os.getenv("PG_USER") is None:
        logger.warning("PG_USER is using default 'postgres'. Ensure .env is configured if this is not intended.")
    if DB_NAME == "gacha_database" and os.getenv("GACHA_DB_NAME") is None:
        logger.warning("GACHA_DB_NAME is using default 'gacha_database'. Ensure .env is configured if this is not intended.")

    asyncio.run(main())