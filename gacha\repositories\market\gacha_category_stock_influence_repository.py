import asyncpg
from decimal import Decimal
from typing import List, Set, Optional
from utils.logger import logger
import datetime

class GachaCategoryStockInfluenceRepository:

    async def get_distinct_category_keys(self, conn: asyncpg.Connection) -> Set[str]:
        """從 gacha_category_stock_influence 表中獲取所有不重複的 category_key。"""
        rows = await conn.fetch('SELECT DISTINCT category_key FROM gacha_category_stock_influence')
        return {row['category_key'] for row in rows}

    async def upsert_category_influence(self, conn: asyncpg.Connection, category_key: str, base_stock_modifier: Decimal) -> None:
        """插入或更新特定 gacha 類別的基礎庫存調節因子。"""
        query = '\n            INSERT INTO gacha_category_stock_influence (category_key, base_stock_modifier, last_base_calculated_at)\n            VALUES ($1, $2, CURRENT_TIMESTAMP)\n            ON CONFLICT (category_key) DO UPDATE SET\n                base_stock_modifier = EXCLUDED.base_stock_modifier,\n                last_base_calculated_at = CURRENT_TIMESTAMP;\n        '
        try:
            await conn.execute(query, category_key, base_stock_modifier)
            logger.debug("已成功更新或插入 gacha 類別 '%s' 的基礎股票修正值為 %s", category_key, base_stock_modifier)
        except Exception as e:
            logger.error("更新/插入 gacha 類別 '%s' 的庫存影響時出錯: %s", category_key, e, exc_info=True)
            raise

    async def cleanup_expired_news_effects(self, conn: asyncpg.Connection) -> int:
        """清理 gacha_category_stock_influence 表中過期的新聞效果。
        將 temporary_news_modifier 重設為 1.0，並將 news_effect_expiry 設為 NULL。
        返回受影響的行數。
        """
        query = '\n            UPDATE gacha_category_stock_influence\n            SET temporary_news_modifier = 1.0000, news_effect_expiry = NULL\n            WHERE news_effect_expiry IS NOT NULL AND news_effect_expiry < CURRENT_TIMESTAMP;\n        '
        try:
            status = await conn.execute(query)
            if status and status.startswith('UPDATE '):
                return int(status.split()[-1])
            return 0
        except Exception as e:
            logger.error('清理過期的新聞效果時出錯: %s', e, exc_info=True)
            return 0