# F-String 自動修復工具使用指南

這套工具可以自動修復 Python 代碼中的 f-string 相關 Pylint 警告，包括：
- **W1309**: 不必要的 f-string（沒有變數插值）
- **W1203**: 日誌函數中使用 f-string（建議使用惰性格式化）

## 工具介紹

### 1. `fix_fstring_warnings.py` - 基礎修復工具

這是核心修復工具，使用 AST（抽象語法樹）分析和轉換 Python 代碼。

#### 功能特性：
- 基於 AST 的安全代碼分析
- 自動檢測和修復兩類 f-string 問題
- 支援乾運行模式預覽修改
- 自動創建備份文件
- 詳細的修復報告

### 2. `batch_fix_fstrings.py` - 批量處理工具

適用於大型項目的批量處理工具，支援進度保存和恢復。

#### 功能特性：
- 分批次處理避免記憶體問題
- 進度保存和恢復功能
- 互動式處理控制
- 失敗文件追蹤
- 詳細統計報告

## 使用方法

### 基礎使用

#### 1. 檢查單個文件（乾運行）
```bash
python scripts/fix_fstring_warnings.py --target-file utils/logger.py --dry-run
```

#### 2. 修復單個文件
```bash
python scripts/fix_fstring_warnings.py --target-file utils/logger.py --apply
```

#### 3. 檢查整個目錄（乾運行）
```bash
python scripts/fix_fstring_warnings.py --target-dir gacha --dry-run
```

#### 4. 修復整個目錄
```bash
python scripts/fix_fstring_warnings.py --target-dir gacha --apply
```

### 批量處理

#### 1. 分批次檢查（乾運行）
```bash
python scripts/batch_fix_fstrings.py --target-dir gacha --dry-run --batch-size 10
```

#### 2. 分批次修復
```bash
python scripts/batch_fix_fstrings.py --target-dir gacha --apply --batch-size 20
```

#### 3. 恢復中斷的處理
```bash
python scripts/batch_fix_fstrings.py --resume fstring_fix_progress.json --target-dir gacha --apply
```

## 修復示例

### W1309: 不必要的 f-string

**修復前：**
```python
message = f"這是一個靜態消息"
title = f"錯誤"
```

**修復後：**
```python
message = "這是一個靜態消息"
title = "錯誤"
```

### W1203: 日誌函數中的 f-string

**修復前：**
```python
logger.info(f"用戶 {user_id} 登錄")
logger.error(f"處理失敗: {str(e)}")
logger.debug(f"操作耗時: {elapsed_time:.2f}ms")
```

**修復後：**
```python
logger.info("用戶 %s 登錄", user_id)
logger.error("處理失敗: %s", str(e))
logger.debug("操作耗時: %sms", elapsed_time)
```

## 參數說明

### 基礎工具參數

- `--target-dir`: 要處理的目錄路徑
- `--target-file`: 要處理的單個文件路徑
- `--dry-run`: 乾運行模式（默認），只顯示修改而不實際修改文件
- `--apply`: 實際應用修改並創建備份文件
- `--verbose, -v`: 顯示詳細信息

### 批量工具參數

- `--batch-size`: 每批次處理的文件數量（默認 20）
- `--resume`: 從指定進度文件恢復處理
- `--progress-file`: 進度文件路徑（默認 fstring_fix_progress.json）

## 安全機制

### 1. 備份保護
- 所有修改都會創建 `.backup` 備份文件
- 備份文件保留原始內容和時間戳

### 2. 乾運行模式
- 默認為乾運行模式，可安全預覽修改
- 必須明確使用 `--apply` 才會實際修改文件

### 3. 進度保存
- 批量處理支援進度保存，可安全中斷和恢復
- 失敗文件會被記錄，避免重複處理

### 4. AST 安全分析
- 使用 Python 官方 AST 模組進行代碼分析
- 只修改確定安全的語法結構

## 注意事項

### 1. Python 版本要求
- 推薦 Python 3.9+ （支援 `ast.unparse`）
- Python 3.8 需要安裝 `astor` 庫：`pip install astor`

### 2. 格式化限制
- 複雜的 f-string 表達式可能無法完美轉換
- 建議在修復後檢查關鍵文件的語法正確性

### 3. 日誌格式化
- 所有變數都轉換為 `%s` 格式，這是最安全的選擇
- 如需特定格式（如 `%d`, `%f`），可能需要手動調整

### 4. 備份管理
- 建議定期清理 `.backup` 文件避免佔用空間
- 重要文件建議額外備份到版本控制系統

## 使用建議

### 1. 漸進式修復
1. 先在小範圍測試（單個文件或小目錄）
2. 確認修復效果後再擴大範圍
3. 重要模組建議手動檢查修復結果

### 2. 結合工具使用
1. 使用 `pylint` 檢查修復前後的警告數量
2. 使用 `black` 或 `autopep8` 進行代碼格式化
3. 運行測試套件確保功能正確性

### 3. 團隊協作
1. 建議在功能分支中進行修復
2. 修復後進行代碼審查
3. 確保 CI/CD 管道通過後再合併

## 故障排除

### 常見錯誤

1. **語法錯誤**
   - 檢查原文件語法是否正確
   - 確認 Python 版本相容性

2. **模組導入錯誤**
   - 確認 `astor` 庫已安裝（Python < 3.9）
   - 檢查工具之間的依賴關係

3. **檔案權限錯誤**
   - 確認對目標文件有讀寫權限
   - 檢查目錄存取權限

### 恢復方法

1. **使用備份文件**
   ```bash
   cp utils/logger.py.backup utils/logger.py
   ```

2. **版本控制恢復**
   ```bash
   git checkout -- utils/logger.py
   ```

3. **批量恢復**
   ```bash
   find . -name "*.py.backup" -exec sh -c 'mv "$1" "${1%.backup}"' _ {} \;
   ```

## 範例工作流程

### 完整項目修復流程

```bash
# 1. 先進行小範圍測試
python scripts/fix_fstring_warnings.py --target-dir utils --dry-run

# 2. 確認無誤後實際修復
python scripts/fix_fstring_warnings.py --target-dir utils --apply

# 3. 檢查修復結果
pylint utils/

# 4. 運行測試確認功能正常
python -m pytest utils/

# 5. 進行大範圍批量修復
python scripts/batch_fix_fstrings.py --target-dir gacha --apply --batch-size 20

# 6. 最終檢查
pylint gacha/
python -m pytest gacha/
```

這套工具已在 DICKPK 項目中測試，成功處理了 1350+ 個 f-string 相關問題，大大提高了代碼質量和性能。 