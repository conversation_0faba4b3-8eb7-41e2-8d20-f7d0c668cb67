"""
CollectionView 狀態管理類
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
from gacha.models.models import UserCard


@dataclass
class CollectionViewState:
    """集中管理 CollectionView 的狀態"""

    # 顯示模式狀態
    show_function_mode: bool = False
    in_sort_mode: bool = False

    # 卡片狀態
    current_card: Optional[UserCard] = None
    has_duplicates: bool = False
    current_card_dynamic_price_details: Optional[Dict[str, Any]] = None

    # 用戶信息
    user_nickname: str = ""
    user_avatar_url: Optional[str] = None

    def update_current_card(self, card: Optional[UserCard]):
        """更新當前卡片並重置相關狀態"""
        self.current_card = card
        self.current_card_dynamic_price_details = None

    def enter_function_mode(self):
        """進入功能模式"""
        self.show_function_mode = True

    def enter_sort_mode(self):
        """進入排序模式"""
        self.show_function_mode = True
        self.in_sort_mode = True

    def exit_to_main_mode(self):
        """退出到主模式"""
        self.show_function_mode = False
        self.in_sort_mode = False

    def exit_to_function_mode(self):
        """從排序模式退出到功能模式"""
        self.in_sort_mode = False
        # show_function_mode 保持 True
