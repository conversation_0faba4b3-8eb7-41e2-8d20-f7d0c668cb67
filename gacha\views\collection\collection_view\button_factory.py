"""
按鈕工廠模塊
提供卡冊視圖中所有按鈕的創建和配置功能
"""

from typing import TYPE_CHECKING, Any, Callable, Dict, List, Optional

import discord
from discord.ui import Button


# 避免循環引用
if TYPE_CHECKING:
    from .card_view import CollectionView


class ButtonFactory:
    """按鈕工廠類，負責創建和配置按鈕"""

    @staticmethod
    def create_button(
        label: str,
        style: discord.ButtonStyle,
        row: int = 0,
        disabled: bool = False,
        emoji: Optional[str] = None,
        custom_id: Optional[str] = None,
    ) -> Button:
        """創建基本按鈕

        參數:
            label: 按鈕標籤
            style: 按鈕樣式
            row: 按鈕所在行
            disabled: 是否禁用
            emoji: 按鈕表情符號
            custom_id: 按鈕自定義ID

        返回:
            Button: 創建的按鈕
        """
        button_params = {
            "label": label,
            "style": style,
            "row": row,
            "disabled": disabled,
        }

        if emoji:
            button_params["emoji"] = emoji

        if custom_id:
            button_params["custom_id"] = custom_id

        return Button(**button_params)

    @staticmethod
    def create_favorite_button(
        is_favorite: bool, row: int = 1, disabled: bool = False
    ) -> Button:
        """創建最愛按鈕

        參數:
            is_favorite: 是否已是最愛
            row: 按鈕所在行
            disabled: 是否禁用

        返回:
            Button: 創建的最愛按鈕
        """
        if is_favorite:
            # 已收藏狀態
            return ButtonFactory.create_button(
                label="已收藏",
                style=discord.ButtonStyle.success,
                row=row,
                disabled=disabled,
                emoji="❤️",
                custom_id="toggle_favorite",
            )
        else:
            # 未收藏狀態 (統一使用 success 樣式)
            return ButtonFactory.create_button(
                label="加入最愛",
                style=discord.ButtonStyle.success, # <<< 從 secondary 改為 success
                row=row,
                disabled=disabled,
                emoji="🤍",
                custom_id="toggle_favorite",
            )

    @staticmethod
    def create_sell_button(has_cards: bool, row: int = 1) -> Button:
        """創建賣卡按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的賣卡按鈕
        """
        return ButtonFactory.create_button(
            label="賣1張",
            style=discord.ButtonStyle.danger,
            row=row,
            disabled=not has_cards,
            emoji="<:sellall:1366135697941205163>",
            custom_id="sell_one_card",
        )

    @staticmethod
    def create_sell_all_button(has_cards: bool, row: int = 1) -> Button:
        """創建賣出所有按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的賣出所有按鈕
        """
        return ButtonFactory.create_button(
            label="賣全部",
            style=discord.ButtonStyle.danger,
            row=row,
            disabled=not has_cards,
            emoji="<:sellall:1366135697941205163>",
            custom_id="sell_all_current_card",
        )

    @staticmethod
    def create_function_button(has_cards: bool, row: int = 1) -> Button:
        """創建功能按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的功能按鈕
        """
        return ButtonFactory.create_button(
            label="功能",
            style=discord.ButtonStyle.primary,
            row=row,
            disabled=not has_cards,
            custom_id="function_button",
        )

    @staticmethod
    def create_enhance_button(
        has_cards: bool, has_duplicates: bool, row: int = 2
    ) -> Button:
        """創建升星按鈕

        參數:
            has_cards: 是否有卡片
            has_duplicates: 是否有重複卡片
            row: 按鈕所在行

        返回:
            Button: 創建的升星按鈕
        """
        return ButtonFactory.create_button(
            label="升星",
            style=discord.ButtonStyle.primary,
            row=row,
            disabled=not (has_cards and has_duplicates),
            custom_id="enhance_card",
        )

    @staticmethod
    def create_sort_mode_button(
        has_cards: bool, is_favorite: bool, row: int = 2
    ) -> Button:
        """創建排序模式按鈕

        參數:
            has_cards: 是否有卡片
            is_favorite: 是否為最愛卡片
            row: 按鈕所在行

        返回:
            Button: 創建的排序模式按鈕
        """
        label = "排序模式" if is_favorite else "先加入最愛才能排序"
        style = (
            discord.ButtonStyle.success
            if is_favorite
            else discord.ButtonStyle.secondary
        )

        return ButtonFactory.create_button(
            label=label,
            style=style,
            row=row,
            disabled=not (has_cards and is_favorite),
            custom_id="sort_mode",
        )

    @staticmethod
    def create_description_button(has_cards: bool, row: int = 2) -> Button:
        """創建設置描述按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的設置描述按鈕
        """
        return ButtonFactory.create_button(
            label="設置描述",
            style=discord.ButtonStyle.success,
            row=row,
            disabled=not has_cards,  # 移除星級檢查，僅檢查是否有卡
            emoji="📝",
            custom_id="set_description",
        )

    @staticmethod
    def create_back_button(row: int = 2) -> Button:
        """創建返回按鈕

        參數:
            row: 按鈕所在行

        返回:
            Button: 創建的返回按鈕
        """
        return ButtonFactory.create_button(
            label="返回",
            style=discord.ButtonStyle.secondary,
            row=row,
            custom_id="back_to_main",
        )

    @staticmethod
    def create_batch_favorite_button(has_cards: bool, row: int = 1) -> Button:
        """創建批次加入最愛按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的批次加入最愛按鈕
        """
        return ButtonFactory.create_button(
            label="批次加入最愛",
            style=discord.ButtonStyle.success,
            row=row,
            disabled=not has_cards,
            emoji="<a:sr:1357714854244515936>",
            custom_id="batch_favorite",
        )

    @staticmethod
    def create_batch_unfavorite_button(
            has_cards: bool, row: int = 1) -> Button:
        """創建批次取消最愛按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的批次取消最愛按鈕
        """
        return ButtonFactory.create_button(
            label="批次取消最愛",
            style=discord.ButtonStyle.danger,
            row=row,
            disabled=not has_cards,
            emoji="<a:sw:1365447243863429273>",
            custom_id="batch_unfavorite",
        )

    # 自定義排序模式按鈕

    @staticmethod
    def create_move_to_top_button(has_cards: bool, row: int = 2) -> Button:
        """創建置頂按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的置頂按鈕
        """
        return ButtonFactory.create_button(
            label="置頂",
            style=discord.ButtonStyle.success,
            row=row,
            disabled=not has_cards,
            custom_id="move_to_top",
        )

    @staticmethod
    def create_move_up_button(has_cards: bool, row: int = 2) -> Button:
        """創建上移按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的上移按鈕
        """
        return ButtonFactory.create_button(
            label="上移",
            style=discord.ButtonStyle.success,
            row=row,
            disabled=not has_cards,
            custom_id="move_up",
        )

    @staticmethod
    def create_move_down_button(has_cards: bool, row: int = 2) -> Button:
        """創建下移按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的下移按鈕
        """
        return ButtonFactory.create_button(
            label="下移",
            style=discord.ButtonStyle.success,
            row=row,
            disabled=not has_cards,
            custom_id="move_down",
        )

    @staticmethod
    def create_move_to_bottom_button(has_cards: bool, row: int = 2) -> Button:
        """創建置底按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的置底按鈕
        """
        return ButtonFactory.create_button(
            label="置底",
            style=discord.ButtonStyle.success,
            row=row,
            disabled=not has_cards,
            custom_id="move_to_bottom",
        )

    @staticmethod
    def create_move_to_position_button(
            has_cards: bool, row: int = 3) -> Button:
        """創建移至指定位置按鈕

        參數:
            has_cards: 是否有卡片
            row: 按鈕所在行

        返回:
            Button: 創建的移至指定位置按鈕
        """
        return ButtonFactory.create_button(
            label="移至位置...",
            style=discord.ButtonStyle.success,
            row=row,
            disabled=not has_cards,
            custom_id="move_to_position",
        )

    @staticmethod
    def create_back_to_function_button(row: int = 3) -> Button:
        """創建返回功能列表按鈕

        參數:
            row: 按鈕所在行

        返回:
            Button: 創建的返回功能列表按鈕
        """
        return ButtonFactory.create_button(
            label="返回功能列表",
            style=discord.ButtonStyle.secondary,
            row=row,
            custom_id="back_to_function",
        )

    @staticmethod
    def update_sort_mode_button_state(button: discord.ui.Button, is_available: bool, is_favorite: bool):
        """根據卡片狀態更新排序模式按鈕的 label, style, 和 disabled 狀態"""
        if is_available:
            button.label = "排序模式" if is_favorite else "先加入最愛才能排序"
            button.style = discord.ButtonStyle.success if is_favorite else discord.ButtonStyle.secondary
            button.disabled = not is_favorite
        else:
            button.label = "排序模式"
            button.style = discord.ButtonStyle.secondary
            button.disabled = True
