"""
Gacha系統21點遊戲視圖
創建並格式化用於21點遊戲的Discord Embed和交互組件
"""
from typing import Any, Dict, Optional, TYPE_CHECKING, Callable, Coroutine
import functools
import discord
from discord.ui import <PERSON><PERSON>, View
from utils.logger import logger
from gacha.services.games.blackjack_service import BlackjackGameService
from gacha.services.core.economy_service import EconomyService
from gacha.services.ui.game_display_service import GameDisplayService
from gacha.views.collection.collection_view.interaction_manager import InteractionManager
from gacha.views.embeds.games.blackjack_embed_builder import BlackjackEmbedBuilder
from gacha.views.handlers.blackjack_callback_handler import BlackjackCallbackHandler
if TYPE_CHECKING:
    pass

class BlackjackView(View):
    """21點遊戲視圖"""

    def __init__(self, user: discord.User, game_state: Dict[str, Any], blackjack_service: BlackjackGameService, economy_service: EconomyService, game_display_service: GameDisplayService, original_bet: int):
        """初始化21點遊戲視圖

        參數:
            user: Discord用戶對象
            game_state: 遊戲狀態
            blackjack_service: Blackjack遊戲服務實例
            economy_service: 經濟服務實例
            game_display_service: 遊戲顯示服務實例
            original_bet: 遊戲的原始下注金額
            
        Raises:
            ValueError: 當 game_state 中缺少 game_id 時
        """
        super().__init__(timeout=180)
        self.user = user
        self.user_id = user.id
        self.game_state = game_state
        self.game_id = game_state.get('game_id')
        if not self.game_id:
            raise ValueError('BlackjackView 初始化時缺少 game_id')
        self._blackjack_service = blackjack_service
        self._economy_service = economy_service
        self._game_display_service = game_display_service
        self._original_bet = original_bet
        self.message: Optional[discord.Message] = None
        self.is_processing = False
        self.interaction_manager = InteractionManager()
        # Pass the view instance (self) to the handler
        self.callback_handler = BlackjackCallbackHandler(blackjack_service=self._blackjack_service, economy_service=self._economy_service, game_display_service=self._game_display_service, interaction_manager=self.interaction_manager, user_id=self.user_id, view=self, game_id=self.game_id)

    async def init_buttons(self):
        """初始化並添加遊戲按鈕

        注意：此方法必須在視圖被發送/編輯後等待調用。
        """
        self.clear_items()
        logger.debug('BlackjackView.init_buttons called. game_over state: %s', self.game_state.get('game_over', False))
        if self.game_state.get('game_over', False):
            user_id = self.user_id
            latest_balance = 0
            try:
                balance_info = await self._economy_service.get_balance(user_id)
                latest_balance = balance_info.get('balance', 0)
                logger.debug('User %s latest balance for replay options: %s', user_id, latest_balance)
            except Exception as e:
                logger.error('Error fetching latest balance for user %s in BlackjackView: %s', user_id, e)
            current_bet = self._original_bet
            min_bet = self._blackjack_service.MIN_BET
            play_again_same_button = Button(label=f'再來一局 ({current_bet} 油幣)', style=discord.ButtonStyle.green, custom_id=f'blackjack:replay:same:{current_bet}', disabled=latest_balance < current_bet)
            play_again_same_button.callback = self.callback_handler.handle_replay_callback
            self.add_item(play_again_same_button)
            double_bet = current_bet * 2
            double_button = Button(label=f'雙倍下注 ({double_bet} 油幣)', style=discord.ButtonStyle.primary, custom_id=f'blackjack:replay:double:{current_bet}', disabled=latest_balance < double_bet)
            double_button.callback = self.callback_handler.handle_replay_callback
            self.add_item(double_button)
            half_bet = max(min_bet, current_bet // 2)
            half_button = Button(label=f'減半下注 ({half_bet} 油幣)', style=discord.ButtonStyle.secondary, custom_id=f'blackjack:replay:half:{current_bet}', disabled=latest_balance < half_bet or current_bet <= min_bet)
            half_button.callback = self.callback_handler.handle_replay_callback
            self.add_item(half_button)
            min_bet_button = Button(label=f'最低下注 ({min_bet} 油幣)', style=discord.ButtonStyle.secondary, custom_id=f'blackjack:replay:min:{current_bet}', disabled=latest_balance < min_bet or current_bet == min_bet)
            min_bet_button.callback = self.callback_handler.handle_replay_callback
            self.add_item(min_bet_button)
            return
        hit_button = Button(label='要牌', style=discord.ButtonStyle.blurple, custom_id='blackjack:action:hit')
        hit_button.callback = functools.partial(self.callback_handler.handle_action_callback, action='hit')
        self.add_item(hit_button)
        stand_button = Button(label='停牌', style=discord.ButtonStyle.red, custom_id='blackjack:action:stand')
        stand_button.callback = functools.partial(self.callback_handler.handle_action_callback, action='stand')
        self.add_item(stand_button)

    async def on_timeout(self):
        """超時處理"""
        for item in self.children:
            item.disabled = True
        if self.message:
            try:
                await self.message.edit(view=self)
            except discord.NotFound:
                logger.warning(f"BlackjackView: Message {self.message.id} not found when trying to edit on timeout. It might have been deleted.")
            except Exception as e:
                logger.error(f"BlackjackView: An unexpected error occurred while editing message {self.message.id} on timeout: {e}", exc_info=True)