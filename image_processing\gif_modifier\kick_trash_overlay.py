"""
踢飛頭像進垃圾桶 GIF 疊加模塊
"""
import os
from PIL import Image, ImageSequence, ImageDraw
import math

# 假設 fetch_avatar 和 get_gif_config 已經在其他地方定義或將被正確導入
# from .config import get_gif_config
# async def fetch_avatar(user): ...

def _calculate_paste_position_after_rotation(original_size, new_size, original_center_paste_pos):
    """
    計算旋轉後的圖像(其邊界框可能已改變)應該粘貼在原背景的哪個位置，
    以使其看起來像是圍繞原始未旋轉圖像的中心點進行了旋轉。
    original_size: (寬, 高) 未旋轉圖像的尺寸
    new_size: (寬, 高) 旋轉後圖像的尺寸 (PIL rotate expand=True 後的尺寸)
    original_center_paste_pos: (x, y) 未旋轉圖像的中心點原本應該粘貼在背景上的座標
    返回: (x, y) 旋轉後的圖像左上角應該粘貼在背景上的座標
    """
    original_width, original_height = original_size
    new_width, new_height = new_size
    
    # 未旋轉圖像的左上角粘貼位置
    # original_top_left_x = original_center_paste_pos[0] - original_width / 2
    # original_top_left_y = original_center_paste_pos[1] - original_height / 2
    
    # 旋轉後圖像的中心點相對於其自身左上角的偏移
    new_center_offset_x = new_width / 2
    new_center_offset_y = new_height / 2
    
    # 目標是讓旋轉後圖像的中心點與 original_center_paste_pos 對齊
    paste_x = original_center_paste_pos[0] - new_center_offset_x
    paste_y = original_center_paste_pos[1] - new_center_offset_y
    
    return int(paste_x), int(paste_y)

def generate_kick_trash_frames(user_avatar_img, config):
    """
    生成"踢飛頭像進垃圾桶"動畫的所有影格。

    參數:
        user_avatar_img (PIL.Image.Image): 使用者頭像的 PIL Image 物件 (RGBA)。
        config (dict): "kick_trash.gif" 的特定設定物件。

    返回:
        list: 一個包含所有處理後影格 (PIL.Image.Image objects) 的列表。
    """
    all_final_frames = []
    kick_phase_config = config.get("kick_phase", {})
    trash_phase_config = config.get("trash_phase", {})
    output_gif_size = config.get("output_gif_size", (300, 250)) # 獲取輸出尺寸
    
    # --- 初始化 ---
    base_avatar_size = config.get("avatar_size", (64, 64))
    avatar_pil = user_avatar_img.resize(base_avatar_size, Image.LANCZOS)

    # --- 預先載入踢擊動畫影格 ---
    total_source_frames = config.get("total_source_frames", 30)
    source_path_pattern = config.get("source_path_pattern", "")
    kick_trigger_frame = kick_phase_config.get("kick_trigger_frame", 17)
    preloaded_kick_frames = []
    
    # 預先載入所有踢擊動畫影格
    for frame_index in range(total_source_frames):
        frame_path = source_path_pattern.format(frame_index)
        if os.path.exists(frame_path):
            kick_frame = Image.open(frame_path).convert("RGBA")
            # 直接將影格調整為最終輸出尺寸
            if kick_frame.size != output_gif_size:
                kick_frame = kick_frame.resize(output_gif_size, Image.LANCZOS)
            preloaded_kick_frames.append(kick_frame)
        else:
            print(f"警告: 踢擊動畫影格 {frame_path} 不存在。")
            # 如果有前一幀，複製前一幀；否則創建空白影格
            if preloaded_kick_frames:
                preloaded_kick_frames.append(preloaded_kick_frames[-1].copy())
            else:
                preloaded_kick_frames.append(Image.new("RGBA", output_gif_size, (0,0,0,0)))

    # --- 階段一：踢擊動畫 ---
    avatar_initial_offset_kick = kick_phase_config.get("avatar_initial_offset_kick", (50, 180))
    avatar_deformation_scale = kick_phase_config.get("avatar_deformation_scale", (1.0, 0.8))
    kick_fly_frames_in_kick_scene = kick_phase_config.get("kick_fly_frames", 5) # 在踢擊場景中飛行的影格數
    kick_fly_velocity = list(kick_phase_config.get("kick_fly_velocity", (15, -25))) 
    kick_fly_rotation_speed = kick_phase_config.get("kick_fly_rotation_speed", 15)

    current_avatar_center_pos = list(avatar_initial_offset_kick) # 使用中心點追蹤位置
    current_avatar_angle = 0.0
    is_kicked = False
    
    # 實際踢飛並在原場景中持續的最後一幀索引
    last_kick_scene_frame_for_avatar = kick_trigger_frame + kick_fly_frames_in_kick_scene -1

    for frame_index in range(total_source_frames):
        if is_kicked and frame_index > last_kick_scene_frame_for_avatar:
            # 頭像已經飛出踢擊場景，不再處理踢擊場景的剩餘原始影格
            break 

        # 使用預載入的影格而不是每次都從檔案讀取
        if frame_index < len(preloaded_kick_frames):
            current_background = preloaded_kick_frames[frame_index].copy()
        else:
            print(f"警告: 預載入影格索引 {frame_index} 超出範圍。")
            if all_final_frames: 
                all_final_frames.append(all_final_frames[-1].copy())
            else:
                all_final_frames.append(Image.new("RGBA", output_gif_size, (0,0,0,0)))
            continue

        # 複製基礎頭像用於當前幀處理
        processed_avatar_for_frame = avatar_pil.copy() 
        avatar_current_size_for_paste = base_avatar_size # 除非形變，否則保持原大小

        if frame_index < kick_trigger_frame:
            # 被踢之前，頭像在腳邊 (current_avatar_center_pos 是中心點)
            paste_pos = (int(current_avatar_center_pos[0] - base_avatar_size[0]/2), 
                         int(current_avatar_center_pos[1] - base_avatar_size[1]/2))
            current_background.paste(processed_avatar_for_frame, paste_pos, processed_avatar_for_frame)
        
        elif frame_index >= kick_trigger_frame and frame_index <= last_kick_scene_frame_for_avatar:
            is_kicked = True
            # 形變
            deformed_size = (
                int(base_avatar_size[0] * avatar_deformation_scale[0]),
                int(base_avatar_size[1] * avatar_deformation_scale[1])
            )
            processed_avatar_for_frame = processed_avatar_for_frame.resize(deformed_size, Image.LANCZOS)
            avatar_current_size_for_paste = deformed_size # 更新用於粘貼計算的尺寸

            if frame_index > kick_trigger_frame: # 從踢中後的下一幀開始移動和旋轉
                current_avatar_center_pos[0] += kick_fly_velocity[0]
                current_avatar_center_pos[1] += kick_fly_velocity[1]
                current_avatar_angle += kick_fly_rotation_speed
            
            # 旋轉
            if current_avatar_angle != 0:
                # expand=True確保旋轉後的圖像完整，但尺寸會改變
                rotated_avatar = processed_avatar_for_frame.rotate(current_avatar_angle, resample=Image.BICUBIC, expand=True)
                # 計算粘貼位置，以保持視覺上的中心點旋轉
                paste_pos = _calculate_paste_position_after_rotation(
                    avatar_current_size_for_paste, # 形變後、旋轉前的尺寸
                    rotated_avatar.size,           # 旋轉後的實際尺寸
                    current_avatar_center_pos
                )
                current_background.paste(rotated_avatar, paste_pos, rotated_avatar)
            else:
                # 未旋轉，直接粘貼
                paste_pos = (int(current_avatar_center_pos[0] - avatar_current_size_for_paste[0]/2), 
                             int(current_avatar_center_pos[1] - avatar_current_size_for_paste[1]/2))
                current_background.paste(processed_avatar_for_frame, paste_pos, processed_avatar_for_frame)
        
        all_final_frames.append(current_background)

    # --- 階段二：飛入垃圾桶 ---
    trash_scene_frames = trash_phase_config.get("trash_scene_frames", 15)
    trash_bin_opening_rect = trash_phase_config.get("trash_bin_opening_rect", (100, 80, 200, 150)) # x1,y1,x2,y2
    avatar_final_pos_in_trash_offset = trash_phase_config.get("avatar_final_pos_in_trash_offset", (0, 20))

    # 載入垃圾桶相關圖片
    trash_can_back_path = "image_processing/gifs/trash_can_back.png" # 從設定檔讀取更佳
    trash_can_front_lip_path = "image_processing/gifs/trash_can_front_lip.png" # 從設定檔讀取更佳

    if not (os.path.exists(trash_can_back_path) and os.path.exists(trash_can_front_lip_path)):
        print("錯誤: 垃圾桶圖片資源缺失。")
        # 可以選擇返回已有的踢擊動畫影格，或拋出錯誤
        # 為了繼續，我們假設如果缺失則此階段不執行，但這不是好做法
        if not all_final_frames: # 如果連踢擊動畫都沒有，返回空
            return []
    else:
        # 預先載入並縮放垃圾桶圖片到最終輸出尺寸
        trash_back_img = Image.open(trash_can_back_path).convert("RGBA")
        trash_front_lip_img = Image.open(trash_can_front_lip_path).convert("RGBA")
        
        # 如果垃圾桶背景與最終輸出尺寸不一致，預先縮放
        if trash_back_img.size != output_gif_size:
            trash_back_img = trash_back_img.resize(output_gif_size, Image.LANCZOS)
        
        # 如果垃圾桶前景與最終輸出尺寸不一致，預先縮放
        if trash_front_lip_img.size != output_gif_size:
            trash_front_lip_img = trash_front_lip_img.resize(output_gif_size, Image.LANCZOS)

        # 讀取新的軌跡參數
        avatar_entry_style = trash_phase_config.get("avatar_entry_style", "linear") # 默認為線性
        arc_start_y_offset_above_bin_top = trash_phase_config.get("arc_start_y_offset_above_bin_top", 0)
        arc_peak_y_offset_from_linear = trash_phase_config.get("arc_peak_y_offset_from_linear", 0)

        # 頭像在垃圾桶場景的X起始位置：從左邊緣外開始
        avatar_trash_start_x = -base_avatar_size[0]
        
        # 計算Y軸的起始點和目標點
        trash_bin_top_y = trash_bin_opening_rect[1]
        
        # 新的Y起始點：在垃圾桶開口頂部上方指定偏移處
        avatar_trash_start_y = trash_bin_top_y - arc_start_y_offset_above_bin_top
        
        # 垃圾桶開口中心 + 設定的最終落點偏移 (這是Y的目標點)
        trash_bin_center_x = (trash_bin_opening_rect[0] + trash_bin_opening_rect[2]) / 2
        trash_bin_center_y = (trash_bin_opening_rect[1] + trash_bin_opening_rect[3]) / 2
        
        avatar_trash_target_x = trash_bin_center_x + avatar_final_pos_in_trash_offset[0]
        # Y的目標點：垃圾桶開口中心Y + 最終落點偏移
        avatar_trash_target_y = trash_bin_center_y + avatar_final_pos_in_trash_offset[1]

        # 讀取垃圾桶階段頭像縮放比例
        avatar_scale_in_trash = trash_phase_config.get("avatar_scale_in_trash", 1.0) # 默認為不縮放
        
        current_trash_avatar_size = base_avatar_size
        if avatar_scale_in_trash != 1.0:
            current_trash_avatar_size = (
                int(base_avatar_size[0] * avatar_scale_in_trash),
                int(base_avatar_size[1] * avatar_scale_in_trash)
            )
            avatar_for_trash_scene = avatar_pil.resize(current_trash_avatar_size, Image.LANCZOS)
        else:
            avatar_for_trash_scene = avatar_pil.copy() # 使用原始調整大小後的頭像


        # 設定實際用於掉落動畫的影格數
        actual_fall_frames = 10
        # 確保不會超過總影格數，並且在總影格數很少時有合理的值
        if trash_scene_frames == 0:
            actual_fall_frames = 0
        elif trash_scene_frames < actual_fall_frames: # 如果總影格數少於15，則用滿所有影格掉落
            actual_fall_frames = trash_scene_frames
        
        # 確保 actual_fall_frames 至少為1，如果 trash_scene_frames 大於0
        if actual_fall_frames < 1 and trash_scene_frames > 0:
            actual_fall_frames = 1


        for i in range(trash_scene_frames):
            trash_scene_bg = trash_back_img.copy()
            
            current_x = 0
            current_y = 0

            if trash_scene_frames == 0: # 理論上不會執行到這裡，因為上面會返回空列表
                # 但為了防禦性程式設計，如果真的進來了，就用一個空白背景
                pass # current_x, current_y 保持 (0,0) 或其他預設值，頭像可能不會被正確放置

            elif i < actual_fall_frames:
                # 在前半段時間內完成掉落
                progress_for_movement = 0.0
                if actual_fall_frames == 1: # 如果只有一幀用於掉落 (例如總共1或2幀)
                    progress_for_movement = 1.0 # 直接到達終點
                elif actual_fall_frames > 1:
                    progress_for_movement = i / (actual_fall_frames - 1)
                
                current_x = avatar_trash_start_x + (avatar_trash_target_x - avatar_trash_start_x) * progress_for_movement
                
                effective_y_progress = progress_for_movement # 改回線性，移除加速度
                linear_y = avatar_trash_start_y + (avatar_trash_target_y - avatar_trash_start_y) * effective_y_progress
                
                if avatar_entry_style == "arc_top_down":
                    # 拋物線偏移仍然基於 progress_for_movement 計算
                    parabolic_offset = 4 * arc_peak_y_offset_from_linear * progress_for_movement * (1 - progress_for_movement)
                    current_y = linear_y - parabolic_offset
                else:
                    current_y = linear_y
            else:
                # 後半段時間，頭像保持在底部
                current_x = avatar_trash_target_x
                current_y = avatar_trash_target_y
            
            paste_x = int(current_x - current_trash_avatar_size[0]/2)
            paste_y = int(current_y - current_trash_avatar_size[1]/2)

            trash_scene_bg.paste(avatar_for_trash_scene, (paste_x, paste_y), avatar_for_trash_scene)
            trash_scene_bg.paste(trash_front_lip_img, (0,0), trash_front_lip_img)
            
            all_final_frames.append(trash_scene_bg)

    # 檢查是否生成了任何影格
    if not all_final_frames:
        print("警告: 未生成任何影格。")
        return []
        
    # 所有影格已經是最終尺寸，直接返回
    return all_final_frames

# if __name__ == '__main__': 及其後的內容應該在函式定義之外，保持原樣和原縮排
# (以下內容是為了確保 diff 的完整性，實際上 SEARCH 區塊不應包含這麼多，
#  但為了修復上次的錯誤，這裡包含了直到檔案末尾的結構)

if __name__ == '__main__':
    # 簡易測試代碼 (需要自行準備 avatar.png 和相關 config)
    print("執行簡易測試...")
    try:
        # 創建一個假的 RGBA 頭像圖像用於測試
        test_avatar = Image.new("RGBA", (128, 128), (255, 0, 0, 128)) # 半透明紅色方塊
        
        mock_config = {
            "gif_source_type": "frames_sequence",
            "source_path_pattern": "../gifs/kick/frame_{:02d}_delay-0.05s.gif",
            "total_source_frames": 30,
            "avatar_size": (64, 64),
            "output_gif_size": (211, 254),
            "frame_duration": 50,
            "kick_phase": {
                "kick_trigger_frame": 17,
                "avatar_initial_offset_kick": (110, 230),
                "avatar_deformation_scale": (1.0, 0.8),
                "kick_fly_frames": 5,
                "kick_fly_velocity": (35, -35),
                "kick_fly_rotation_speed": 15,
            },
            "trash_phase": {
                "trash_scene_frames": 20,
                "trash_bin_opening_rect": (100, 80, 200, 150),
                "avatar_final_pos_in_trash_offset": (-35, 45),
                "avatar_entry_style": "arc_top_down",
                "arc_start_y_offset_above_bin_top": 70,
                "arc_peak_y_offset_from_linear": 50,
                "avatar_scale_in_trash": 0.65
            }
        }

        print(f"使用設定: {mock_config}")
        frames = generate_kick_trash_frames(test_avatar, mock_config)
        
        if frames:
            print(f"成功生成 {len(frames)} 個影格。")
            output_gif_path = "test_kick_trash_final.gif"
            frames[0].save(
                output_gif_path,
                save_all=True,
                append_images=frames[1:],
                duration=mock_config["frame_duration"],
                loop=0,
                disposal=2,
            )
            print(f"測試GIF已保存到: {output_gif_path}")
        else:
            print("未能生成影格。")
            
    except FileNotFoundError as e:
        print(f"測試時發生檔案未找到錯誤: {e}")
        print("請確保測試所需的GIF影格和垃圾桶圖片位於正確的路徑。")
    except Exception as e:
        print(f"測試時發生錯誤: {e}")
        import traceback
        traceback.print_exc()