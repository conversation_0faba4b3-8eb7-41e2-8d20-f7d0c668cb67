# 專案系統架構文檔

## 1. 總體架構概述 (Overall Architecture Overview)

### 1.1. 專案目標與定位
本專案旨在創建一個功能豐富的 Discord 抽卡遊戲機器人。其核心特色包括但不限於：
*   多樣化的抽卡機制與卡牌收集系統。
*   完善的卡牌管理功能，如圖鑑、強化、最愛標記等。
*   模擬經濟系統，包含卡牌市場交易。
*   一個獨立且動態的虛擬資產/股票市場模擬系統。
*   增加用戶互動性的多種小遊戲。
*   社群功能，如排行榜等。

### 1.2. 主要技術棧
*   **核心語言與框架：** Python (推斷基於常見的 Discord 機器人框架，如 Discord.py)
*   **資料庫：** PostgreSQL (用於持久化存儲核心數據)
*   **緩存/消息隊列：** Redis (用於市場系統中的生命週期計數器、新聞影響隊列等)
*   **圖表生成：** Matplotlib (用於生成股票價格趨勢圖)
*   **AI輔助：** 內部或外部 AI服務 (用於生成新股票公司的描述等內容)

### 1.3. 高層次組件圖
```mermaid
graph TD
    A[使用者 (Discord)] --> B(核心應用程式 `bot.py`);
    B -- 命令請求 --> C{抽卡系統模組 (`gacha/`)};
    B -- 圖像請求 --> D[圖像處理模組 (`image_processing/`)];
    B -- AI功能請求 --> E[AI 助理模組 (`ai_assistant/`)];
    B -- 市場操作 --> F{虛擬資產/股票市場模組};
    C -- 數據操作 --> G[資料庫模組 (PostgreSQL & `schema.sql`)];
    F -- 數據操作 --> G;
    C -- 日誌記錄 --> H[日誌模組 (`logs/`)];
    F -- 日誌記錄 --> H;
    B -- 日誌記錄 --> H;
    I[腳本與工具 (`scripts/`, `utils/`)] -.-> B;
    I -.-> G;
    B -- 加載配置 --> J{配置 (`gacha/app_config.py`)};
    F -- 依賴 --> K[Redis];
    F -- AI內容生成 --> E;
```

## 2. 模組詳解 (Module Breakdown)

### 2.1. 核心應用程式 (Core Application)
*   **涉及文件：** [`bot.py`](bot.py:1) (主入口), [`gacha/app_config.py`](gacha/app_config.py:1) (配置中心), [`requirements.txt`](requirements.txt:1)。
*   **主要職責：**
    *   應用程式的啟動和作為 Discord Bot 的主入口點。
    *   處理與 Discord API 的所有交互，包括接收事件 (如消息、指令) 和發送回應。
    *   實現命令路由和分派機制，將用戶指令轉發到相應的業務模組進行處理。
    *   從 [`gacha/app_config.py`](gacha/app_config.py:1) 加載和管理全局應用程式配置，如 Bot Token、資料庫連接參數、各模組的特定參數等。
    *   管理應用程式級別的依賴項 (記錄在 [`requirements.txt`](requirements.txt:1) 中)。
*   **關鍵組件交互：**
    *   [`bot.py`](bot.py:1) 作為中央協調器，初始化並持有各服務實例的引用。
    *   [`gacha/app_config.py`](gacha/app_config.py:1) 作為所有運行時配置的統一來源。
*   **與其他模組的依賴關係：**
    *   調用 `gacha` 模組的命令處理器和服務。
    *   調用 `image_processing` 模組的功能。
    *   調用 `ai_assistant` 模組的功能。
    *   調用 `StockMarketCommands` 等與股票市場相關的命令處理器。
    *   使用 `database` 模組進行初始化或全局數據庫連接管理（如果有的話）。
    *   使用 `logs` 模組記錄應用程式級別的日誌。

### 2.2. 抽卡系統模組 (Gacha System - [`gacha/`](gacha/:1))
*   **主要職責：** 實現專案的抽卡核心玩法，包括多樣化的卡池、單抽與多抽機制、許願系統、卡牌圖鑑、用戶卡牌收藏管理（強化、最愛、排序篩選）、卡牌相關的經濟功能（如將卡牌出售換取遊戲內貨幣）以及多種互動小遊戲。
*   **關鍵子模組/組件及其交互方式：**
    *   **[`gacha/commands/`](gacha/commands/:1):** 用戶命令接口層。定義用戶可通過 Discord 輸入的斜線命令 (app_commands)。這些命令類負責解析用戶輸入，進行初步驗證，然後調用相應的 `services` 來執行業務邏輯。它們通常不包含複雜的業務邏輯本身。
    *   **[`gacha/services/`](gacha/services/:1):** 核心業務邏輯層。封裝了抽卡、卡牌管理、經濟操作等具體業務流程。`Services` 負責協調一個或多個 `Repositories` 來完成一個完整的業務操作。它們處理業務規則、數據驗證（更複雜的驗證）、計算、以及與其他服務的交互。服務層的目標是保持 `commands` 層的簡潔，並使業務邏輯可重用和可測試。`Services` 調用 `Repositories` 的方法來請求數據或持久化數據，不直接執行 SQL 查詢。
    *   **[`gacha/repositories/`](gacha/repositories/:1):** 數據訪問層 (DAL)。提供一個抽象接口來訪問持久化存儲（PostgreSQL 資料庫）。每個 `Repository` 通常對應一個或一組相關的資料庫表（或數據實體）。它們封裝了數據的查詢 (SELECT)、創建 (INSERT)、更新 (UPDATE) 和刪除 (DELETE) 操作，即 CRUD 操作。`Repositories` 被 `Services` 調用，它們不應該包含複雜的業務邏輯，其職責是高效、準確地執行數據操作，並將資料庫返回的原始數據轉換為應用程式內部使用的數據模型/對象，或者接收這些模型對象並將其持久化。
    *   **[`gacha/models/`](gacha/models/:1):** 數據模型層。定義應用程式內部使用的數據結構和對象 (通常是 Pydantic 模型或簡單的 Python 類)。這些模型作為 `Services` 和 `Repositories` 之間以及應用程式各層之間傳遞數據的載體。它們通常代表了業務領域中的核心實體（如卡牌、用戶收藏、卡池配置、用戶信息等），並可能包含一些基礎的數據驗證規則或格式化邏輯。
    *   **[`gacha/views/`](gacha/views/:1):** 用戶界面呈現層。負責將業務邏輯處理後的數據格式化並呈現給用戶。在 Discord Bot 的上下文中，這通常意味著生成和管理 Discord Embeds、按鈕 (Buttons)、選擇菜單 (Select Menus) 和模態框 (Modals) 等交互式 UI 組件。
*   **與其他主要模組的依賴關係：**
    *   強依賴 `database` 模組（通過 `repositories`）進行數據存儲和檢索。
    *   可能調用 `image_processing` 模組生成卡牌相關的圖像或展示效果。
    *   日誌記錄到 `logs` 模組。

### 2.3. 圖像處理模組 (Image Processing - [`image_processing/`](image_processing/:1))
*   **主要職責：** 提供圖像生成、修改和處理功能，特別是針對 GIF 動圖的處理，例如將用戶頭像疊加到預設的 GIF 動畫上，或生成其他個性化圖像效果。
*   **關鍵子模組/組件：**
    *   [`image_processing/gif_modifier/`](image_processing/gif_modifier/:1): 包含具體的 GIF 修改邏輯，如 [`avatar_gif_overlay.py`](image_processing/gif_modifier/avatar_gif_overlay.py:1) 和 [`punch_overlay.py`](image_processing/gif_modifier/punch_overlay.py:1)。
*   **與其他主要模組的依賴關係：**
    *   可能被 `gacha` 模組用於生成個性化的卡牌展示圖像或用戶互動時的趣味圖像。
    *   可能被核心應用程式用於其他需要動態圖像生成的場景。

### 2.4. AI 助理模組 (AI Assistant - [`ai_assistant/`](ai_assistant/:1))
*   **主要職責：** 提供 AI 輔助功能或自動化工具。目前已知的功能包括為股票市場系統中的新上市公司生成公司名稱、股票代碼和描述。也可能包含如計時器工具 ([`ai_assistant/timer_utils.py`](ai_assistant/timer_utils.py:1)) 等其他輔助功能。
*   **關鍵組件：**
    *   [`ai_assistant/ai_service_base.py`](ai_assistant/ai_service_base.py:1): AI 服務的基礎類或接口。
    *   [`ai_assistant/prompt_service.py`](gacha/../ai_assistant/prompt_service.py:1) (推斷): 管理和格式化發送給 AI 模型的提示 (Prompts)。
*   **與其他主要模組的依賴關係：**
    *   被 `StockLifecycleService` (虛擬資產/股票市場模組的一部分) 調用，用於在創建新股票公司時生成相關文本內容。
    *   其他模組也可能按需調用此模組提供的 AI 功能。

### 2.5. 資料庫模組 (Database - [`database/`](database/:1), [`schema.sql`](schema.sql:1))
*   **主要職責：**
    *   定義專案所使用的 PostgreSQL 資料庫的表結構、索引、約束和關係，詳見 [`schema.sql`](schema.sql:1)。
    *   管理資料庫連接。連接參數從 [`gacha/app_config.py`](gacha/app_config.py:1) 中讀取。
    *   (可能) 包含一些底層的資料庫操作輔助函數或配置，但主要的數據庫交互由各模組的 `repositories` 子模組負責。
*   **關鍵組件：**
    *   [`schema.sql`](schema.sql:1): 包含完整的資料庫定義語言 (DDL)，是理解數據持久化層的關鍵。
    *   [`database/config.py`](database/config.py:1): (如果仍在使用) 可能包含部分資料庫連接配置邏輯，但主要配置源是 [`gacha/app_config.py`](gacha/app_config.py:1)。
*   **與其他主要模組的依賴關係：**
    *   被專案中所有需要數據持久化的模組（主要是通過它們的 `repositories`）廣泛使用。
    *   核心應用程式在啟動時可能需要使用此模組來初始化資料庫連接池。

### 2.6. 腳本與工具 (Scripts & Utilities - [`scripts/`](scripts/:1), [`utils/`](utils/:1))
*   **主要職責：**
    *   **[`scripts/`](scripts/:1):** 包含一次性或輔助性的腳本，用於開發、維護、數據遷移、數據分析等任務。例如：[`scripts/analyze_card_rarity.py`](scripts/analyze_card_rarity.py:1) (分析卡牌稀有度), [`scripts/export_cards_to_json.py`](scripts/export_cards_to_json.py:1) (導出卡牌數據)。
    *   **[`utils/`](utils/:1):** 包含項目範圍內可重用的通用工具函數或類，例如日誌記錄器封裝 ([`utils/logger.py`](utils/logger.py:1))、日期時間處理、字符串操作等，以避免代碼重複。
*   **與其他主要模組的依賴關係：**
    *   `scripts/` 中的腳本通常獨立運行，但可能會導入和使用其他模組的服務或倉庫來與應用程式數據交互。
    *   `utils/` 中的工具被專案中幾乎所有其他模組按需導入和使用。

### 2.7. 日誌模組 (Logging - [`logs/`](logs/:1))
*   **主要職責：** 負責記錄應用程式運行時的各種日誌信息，用於調試問題、監控系統狀態和分析用戶行為或系統事件。
*   **關鍵組件/產出：**
    *   [`logs/stock_fluctuation_factors.jsonl`](logs/stock_fluctuation_factors.jsonl:1): 一個結構化的 JSON Lines 文件，詳細記錄了股票市場中每次股票價格變動的影響因子和計算過程。由 `FluctuationLogger` 服務寫入。
    *   (推斷) 可能還有其他通用的應用程式日誌文件，記錄常規操作、警告和錯誤信息，其格式和級別由 [`utils/logger.py`](utils/logger.py:1) 和相關配置定義。
*   **與其他主要模組的依賴關係：**
    *   被專案中幾乎所有其他模組使用，用於輸出不同級別的日誌信息。

### 2.8. 虛擬資產/股票市場模組 (Virtual Asset/Stock Market System)
*   **主要職責：** 實現一個獨立於卡牌系統的、功能完善的模擬股票交易市場。這包括股票資產 (`virtual_assets`) 的創建與定義、股票的完整生命週期管理 (Active, ST - Special Treatment, Delisted - 已退市)、基於多種因素的動態價格更新機制、處理用戶的買賣交易請求、管理用戶的股票投資組合，以及通過新聞和圖表向用戶展示市場信息。
*   **核心服務及其職責：**
    *   **`StockTradingService` ([`gacha/services/market/stock_trading_service.py`](gacha/services/market/stock_trading_service.py:1))**:
        *   處理用戶的股票買賣請求（包括驗證、手續費計算、更新用戶油幣餘額和投資組合）。
        *   提供多種查詢接口，用於獲取市場股票列表、用戶投資組合詳情、單個股票的詳細數據（包括價格歷史、近期交易、7日交易量統計等）。
        *   異步生成股票的7日價格趨勢圖 (使用 Matplotlib)。
        *   記錄市場交易到 `market_transactions` 表。
    *   **`StockPriceUpdateEngine` ([`gacha/services/market/stock_price_update_engine.py`](gacha/services/market/stock_price_update_engine.py:1))**:
        *   由定時任務觸發，負責定期更新市場上所有未退市股票的價格。
        *   價格更新邏輯是一個複雜模型，綜合考慮：
            *   股票的有效錨定價格 (由 `AnchorPriceService` 提供)。
            *   基於股票自身 `base_volatility` 的隨機漫步。
            *   從 Redis 隊列中讀取的待處理新聞事件的量化影響。
            *   股票自身的 `volatility_factor` 對總變動的放大作用。
            *   系統配置的最大漲跌幅限制。
            *   當股價接近全局最低價時觸發的特殊「底部抑制邏輯」（對 ST 股票可能有不同參數）。
        *   更新後的價格會寫入 `virtual_assets` 表和 `asset_price_history` 表。
        *   詳細的價格波動計算因子會由 `FluctuationLogger` 記錄到 [`logs/stock_fluctuation_factors.jsonl`](logs/stock_fluctuation_factors.jsonl:1)。
    *   **`StockLifecycleService` ([`gacha/services/market/stock_lifecycle_service.py`](gacha/services/market/stock_lifecycle_service.py:1))**:
        *   由定時任務觸發，管理股票的生命週期狀態 (ACTIVE, ST, DELISTED)。
        *   定期檢查股票是否滿足進入 ST 狀態的條件（例如，價格持續過低、市值持續過低）或從 ST 恢復為 ACTIVE 的條件（價格和市值達標）。
        *   檢查 ST 狀態的股票是否滿足進入 DELISTED 狀態的條件（例如，處於 ST 狀態時間過長、市值進一步惡化）。
        *   狀態轉換的連續檢查次數記錄在 Redis 中。
        *   處理股票退市時的資產清算：根據動態計算的回購價格向所有持有該股票的玩家返還油幣，並清除其持倉。
        *   當市場上活躍公司數量低於設定的最低值時（通常在有公司退市後），觸發創建新的股票公司。新公司的名稱、股票代碼、描述等信息優先嘗試由 AI (`AIServiceBase`, `PromptService`) 根據隨機選擇的「關聯標準」（如特定卡池、稀有度或全局性）生成，若 AI 生成失敗則使用後備方案。新公司的初始股本、價格等參數基於系統配置。
        *   在股票狀態變更（進入ST、從ST恢復、退市）或新股上市時，會調用 `StockNewsService` 發布相關新聞。
    *   **`AnchorPriceService` ([`gacha/services/market/anchor_price_service.py`](gacha/services/market/anchor_price_service.py:1))**:
        *   負責計算和提供股票價格波動所圍繞的「有效錨定價格」，供 `StockPriceUpdateEngine` 使用。
    *   **`FluctuationLogger` ([`gacha/services/scheduled/fluctuation_logger.py`](gacha/services/scheduled/fluctuation_logger.py:1))**:
        *   負責將 `StockPriceUpdateEngine` 在更新股價時計算出的詳細價格波動因子，以結構化 JSON 格式記錄到日誌文件 [`logs/stock_fluctuation_factors.jsonl`](logs/stock_fluctuation_factors.jsonl:1)。
    *   **`StockNewsService` ([`gacha/services/scheduled/stock_news_service.py`](gacha/services/scheduled/stock_news_service.py:1))**:
        *   負責生成和發布與股票市場事件相關的新聞（例如新股上市、ST警告、退市、股價大幅波動等）。
        *   生成的新聞可能包含對股價的直接影響因子，這些影響會被推送到 Redis 隊列中，供 `StockPriceUpdateEngine` 在下次價格更新時處理。
*   **與其他主要模組的依賴關係：**
    *   強依賴 `database` 模組 (通過各自的 Repository，如 `StockAssetRepository`, `PlayerPortfolioRepository`)。
    *   依賴 `UserService` (核心應用程式或抽卡系統模組提供) 來管理用戶的油幣餘額。
    *   依賴 `AIServiceBase` 和 `PromptService` (AI 助理模組提供) 進行新公司相關內容的生成。
    *   依賴 Redis 存儲生命週期檢查計數器和待處理的新聞價格影響隊列。
    *   日誌記錄到 `logs` 模組 (包括詳細的波動因子日誌)。
    *   用戶交互命令定義在 `gacha/commands/market/` 目錄下，由核心應用程式路由。

## 3. 數據模型概覽 (Data Model Overview)

本專案的數據持久化依賴 PostgreSQL 資料庫，其詳細結構定義在 [`schema.sql`](schema.sql:1) 文件中。以下是主要的數據實體及其關係：

### 3.1. 核心抽卡相關實體
*   **`gacha_users`**: 存儲用戶的基本信息、遊戲內貨幣（`oil_balance`）、總抽卡次數、每日簽到狀態、許願槽數量 (`wish_slots`) 和許願力量等級 (`wish_power_level`) 等。
*   **`gacha_master_cards`**: 存儲所有卡牌的元數據，包括卡牌 ID、名稱、系列、圖像 URL、描述、基礎售價、稀有度、卡池類型、市場價格配置 (`price_config`) 以及由卡牌市場系統更新的當前市場售價 (`current_market_sell_price`)。
*   **`gacha_user_collections`**: 記錄用戶擁有的卡牌實例，關聯 `gacha_users` 和 `gacha_master_cards`，包含持有數量、首次/末次獲取時間、是否標記為最愛、卡牌星級以及自定義排序索引。
*   **`gacha_user_wishes`**: 存儲用戶在許願池中設定的目標卡牌，關聯 `gacha_users` 和 `gacha_master_cards`。
*   **`gacha_card_market_stats`**: 存儲每張卡牌的市場統計數據，如總擁有量、唯一擁有者數量、被許願次數、被收藏次數、供需調整因子等，用於卡牌市場的價格計算。
*   **`gacha_category_stock_influence`**: (此表名可能與卡牌市場相關，而非股票市場) 存儲卡牌分類（如系列）對其市場價格的基礎影響因子和臨時新聞影響因子。

### 3.2. 核心股票市場相關實體
*   **`virtual_assets`**: 存儲股票市場中所有可交易的虛擬資產（即「股票」）的定義。包含資產 ID (`asset_id`)、股票代碼 (`asset_symbol`)、股票名稱 (`asset_name`)、描述、當前價格 (`current_price`)、初始錨定價格 (`initial_anchor_price`)、當前錨定價格 (`current_anchor_price`)、總股本 (`total_shares`)、基礎波動率 (`base_volatility`)、波動因子 (`volatility_factor`)、影響力權重 (`influence_weight`)、生命週期狀態 (`lifecycle_status` 如 ACTIVE, ST, DELISTED) 以及與特定遊戲內標準的關聯 (`linked_criteria_type`, `linked_criteria_value`, `linked_pool_context`)。
*   **`asset_price_history`**: 記錄 `virtual_assets` 中每支股票的歷史價格變動，包含資產 ID、價格和時間戳。
*   **`player_portfolios`**: 記錄玩家（關聯 `gacha_users`）持有的股票資產組合。包含用戶 ID、資產 ID、持有數量 (`quantity`) 和平均買入價格 (`average_buy_price`)。
*   **`market_transactions`**: 記錄股票市場中發生的所有買賣交易。包含用戶 ID、資產 ID、交易類型 (BUY/SELL)、數量、單位價格、交易總額和手續費。
*   **`market_news`**: 存儲影響市場（包括股票市場和可能的卡牌市場）的新聞事件。包含新聞標題、內容、可能受影響的資產 ID (`affected_asset_id`，可關聯 `virtual_assets` 或 `gacha_master_cards`)、新聞情緒 (`sentiment`)、發布時間、新聞類型等。

### 3.3. 實體關係圖 (Simplified ER Diagram)
```mermaid
erDiagram
    gacha_users {
        bigint user_id PK
        int oil_balance
        int wish_slots
        varchar nickname
    }
    gacha_master_cards {
        int card_id PK
        varchar name
        varchar series
        int rarity
        decimal current_market_sell_price
    }
    gacha_user_collections {
        int id PK
        bigint user_id FK
        int card_id FK
        int quantity
        boolean is_favorite
    }
    gacha_user_wishes {
        int id PK
        bigint user_id FK
        int card_id FK
    }
    gacha_card_market_stats {
        int card_id PK, FK
        decimal supply_demand_modifier
    }
    virtual_assets {
        int asset_id PK
        varchar asset_symbol
        varchar asset_name
        decimal current_price
        varchar lifecycle_status
        varchar linked_criteria_type
    }
    asset_price_history {
        bigint id PK
        int asset_id FK
        decimal price
        timestamp timestamp
    }
    player_portfolios {
        int id PK
        bigint user_id FK
        int asset_id FK
        int quantity
        decimal average_buy_price
    }
    market_transactions {
        bigint id PK
        bigint user_id FK
        int asset_id FK
        varchar transaction_type
        int quantity
        decimal price_per_unit
        decimal fee
    }
    market_news {
        int id PK
        varchar headline
        text content
        int affected_asset_id FK "Optional"
        varchar sentiment
    }

    gacha_users ||--o{ gacha_user_collections : "擁有卡牌"
    gacha_master_cards ||--o{ gacha_user_collections : "對應卡牌"
    gacha_users ||--o{ gacha_user_wishes : "許願卡牌"
    gacha_master_cards ||--o{ gacha_user_wishes : "被許願"
    gacha_master_cards ||--|| gacha_card_market_stats : "擁有市場統計"

    gacha_users ||--o{ player_portfolios : "持有股票組合"
    virtual_assets ||--o{ player_portfolios : "被持有"
    gacha_users ||--o{ market_transactions : "進行股票交易"
    virtual_assets ||--o{ market_transactions : "被交易"
    virtual_assets ||--o{ asset_price_history : "有價格歷史"
    virtual_assets ||--o{ market_news : "可能影響"
```

## 4. 核心工作流程 (Core Workflows)

### 4.1. 用戶執行抽卡命令的流程
```mermaid
sequenceDiagram
    participant User as 使用者 (Discord)
    participant Bot as 核心應用程式 (`bot.py`)
    participant DrawCmd as DrawCommand (`gacha/commands/`)
    participant GachaSvc as GachaService (`gacha/services/`)
    participant UserRepo as UserRepository (`gacha/repositories/`)
    participant UserCollRepo as UserCollectionRepository (`gacha/repositories/`)
    participant MasterRepo as MasterCardRepository (`gacha/repositories/`)
    participant WishSvc as WishService (`gacha/services/`)
    participant DrawView as DrawView (`gacha/views/`)

    User->>Bot: /draw (抽卡命令 + 參數)
    Bot->>DrawCmd: 路由命令
    DrawCmd->>UserRepo: 檢查用戶餘額 (oil_balance)
    UserRepo-->>DrawCmd: 返回餘額信息
    alt 餘額不足
        DrawCmd-->>Bot: 回覆餘額不足
        Bot-->>User: 顯示餘額不足消息
    else 餘額充足
        DrawCmd->>GachaSvc: perform_draw(user_id, pool_type)
        GachaSvc->>MasterRepo: 獲取指定卡池信息
        MasterRepo-->>GachaSvc: 返回卡池數據 (卡牌列表、概率)
        GachaSvc->>WishSvc: 檢查用戶許願池及加成
        WishSvc-->>GachaSvc: 返回許願加成信息
        GachaSvc->>GachaSvc: 執行抽卡算法 (結合概率和許願)
        GachaSvc->>UserRepo: 扣除用戶 oil_balance
        UserRepo-->>GachaSvc: 確認扣款
        GachaSvc->>UserCollRepo: 添加/更新用戶抽中的卡牌到收藏
        UserCollRepo-->>GachaSvc: 確認添加/更新
        GachaSvc->>UserRepo: (可選) 更新用戶總抽卡次數
        GachaSvc-->>DrawCmd: 返回抽卡結果 (抽中的卡牌列表)
        DrawCmd->>DrawView: 格式化抽卡結果
        DrawView-->>DrawCmd: 返回 Embed 內容
        DrawCmd-->>Bot: 回覆抽卡結果
        Bot-->>User: 顯示抽卡結果 Embed
    end
```

### 4.2. 市場卡牌價格更新流程 (針對 `gacha_master_cards.current_market_sell_price`)
此流程與虛擬資產股票的價格更新機制不同，主要依賴於卡牌自身的市場統計數據。
```mermaid
graph TD
    subgraph 卡牌市場價格更新 (推測流程)
        A[定時任務或事件觸發] --> B{卡牌市場價格服務 (推測)};
        B --> C[遍歷 `gacha_master_cards`];
        C -- 對每張卡牌 --> D{計算新市場售價};
        D -- 讀取 --> E1[`gacha_master_cards` (基礎售價, 價格配置)];
        D -- 讀取 --> E2[`gacha_card_market_stats` (供需因子, 收藏熱度等)];
        D -- 讀取 --> E3[`gacha_category_stock_influence` (分類影響)];
        D -- (可選) 讀取 --> E4[`market_news` (相關新聞影響)];
        D --> F[更新 `gacha_master_cards.current_market_sell_price`];
        F -- 寫入 --> G[MasterCardRepository];
    end
```
*注意：此流程基於分析摘要和表結構推斷，具體實現需查閱卡牌市場相關的服務代碼。*

### 4.3. 用戶購買/出售虛擬資產 (股票) 的流程
```mermaid
sequenceDiagram
    participant User as 使用者 (Discord)
    participant Bot as 核心應用程式 (`bot.py`)
    participant StockCmd as StockMarketCommand (`gacha/commands/market/`)
    participant TradingSvc as StockTradingService (`gacha/services/market/`)
    participant UserRepo as UserRepository
    participant PortfolioRepo as PlayerPortfolioRepository
    participant TransRepo as MarketTransactionRepository
    participant AssetRepo as StockAssetRepository

    User->>Bot: (通過 PortfolioView 按鈕觸發) 買入/賣出股票請求
    Bot->>StockCmd: (間接) 路由到 Modal 提交處理
    StockCmd->>TradingSvc: buy_stock/sell_stock(user_id, symbol, qty)
    TradingSvc->>AssetRepo: (在事務中) 獲取股票信息 (asset_id, current_price) FOR UPDATE
    AssetRepo-->>TradingSvc: 返回股票信息
    alt 買入股票
        TradingSvc->>TradingSvc: 計算總成本 (含手續費)
        TradingSvc->>UserRepo: (在事務中) 檢查並扣除用戶油幣餘額
        UserRepo-->>TradingSvc: 確認餘額操作
        TradingSvc->>PortfolioRepo: (在事務中) 更新/創建用戶持倉記錄
        PortfolioRepo-->>TradingSvc: 確認持倉更新
    else 賣出股票
        TradingSvc->>PortfolioRepo: (在事務中) 檢查用戶持股數量 FOR UPDATE
        PortfolioRepo-->>TradingSvc: 返回持股數量
        alt 持股不足
            TradingSvc-->>StockCmd: 返回錯誤信息 (持股不足)
        else 持股充足
            TradingSvc->>TradingSvc: 計算總收益 (扣除手續費)
            TradingSvc->>UserRepo: (在事務中) 增加用戶油幣餘額
            UserRepo-->>TradingSvc: 確認餘額操作
            TradingSvc->>PortfolioRepo: (在事務中) 更新/刪除用戶持倉記錄
            PortfolioRepo-->>TradingSvc: 確認持倉更新
        end
    end
    TradingSvc->>TransRepo: (在事務中) 記錄市場交易
    TransRepo-->>TradingSvc: 確認交易記錄
    TradingSvc-->>StockCmd: 返回交易成功信息
    StockCmd->>Bot: (通過 Modal 回調) 更新 PortfolioView
    Bot-->>User: 顯示更新後的投資組合視圖
```

### 4.4. 虛擬資產 (股票) 價格定時更新流程
```mermaid
sequenceDiagram
    participant Scheduler as 定時任務調度器
    participant Engine as StockPriceUpdateEngine
    participant AnchorSvc as AnchorPriceService
    participant Redis
    participant FluctuationLog as FluctuationLogger
    participant DB as 資料庫 (`virtual_assets`, `asset_price_history`)

    Scheduler->>Engine: update_stock_prices()
    Engine->>DB: 查詢所有未退市且錨定價有效的股票
    DB-->>Engine: 返回股票列表
    loop 遍歷每支股票 (asset)
        Engine->>AnchorSvc: get_effective_anchor_price(asset.asset_id)
        AnchorSvc-->>Engine: 返回有效錨定價
        Engine->>Redis: lrange(pending_news_impacts:{asset.asset_id}) (獲取新聞影響)
        Redis-->>Engine: 返回待處理新聞影響事件列表
        Engine->>Engine: 處理新聞影響，累加 total_queued_news_impact_amount
        Engine->>Redis: ltrim(pending_news_impacts:{asset.asset_id}) (移除已處理新聞)
        Engine->>Engine: 計算 random_walk_amount (基於 base_volatility 和錨定價)
        Engine->>Engine: total_change_amount_raw = random_walk + news_impact
        Engine->>Engine: total_change_amount_amplified_unlimited = raw_change * volatility_factor
        Engine->>Engine: 計算 final_max_abs_change_amount (漲跌幅限制)
        Engine->>Engine: total_change_amount_amplified = cap(unlimited_change, final_max_abs_change)
        alt 股價低於底部抑制閾值
            Engine->>Engine: 應用底部抑制邏輯 (抑制上漲，概率性施加下跌壓力)
            Engine->>Engine: 調整 total_change_amount_amplified
        end
        Engine->>Engine: new_price_raw = current_price + total_change_amount_amplified
        Engine->>Engine: final_new_price = cap(new_price_raw, global_min_price, global_max_price)
        alt final_new_price 與 current_price 不同
            Engine->>FluctuationLog: log_fluctuation_factor(詳細計算因子)
            Engine->>DB: (事務中) UPDATE virtual_assets SET current_price = final_new_price
            Engine->>DB: (事務中) INSERT INTO asset_price_history (asset_id, final_new_price)
        end
    end
    Engine->>Scheduler: (日誌) 更新完成
```

### 4.5. 股票生命週期狀態檢查與更新流程
```mermaid
sequenceDiagram
    participant Scheduler as 定時任務調度器
    participant LifecycleSvc as StockLifecycleService
    participant Redis
    participant NewsSvc as StockNewsService
    participant DB as 資料庫 (`virtual_assets`, `player_portfolios`, `gacha_users`)
    participant AISvc as AI服務 (可選)

    Scheduler->>LifecycleSvc: check_and_update_all_stocks_lifecycle()
    LifecycleSvc->>DB: 查詢所有 ACTIVE 或 ST 狀態的股票 (獲取 asset_id, current_price, current_status, total_shares)
    DB-->>LifecycleSvc: 返回股票列表
    loop 遍歷每支股票
        LifecycleSvc->>LifecycleSvc: 計算當前市值 (current_price * total_shares)
        alt 股票狀態為 ACTIVE
            LifecycleSvc->>Redis: 讀/寫 ST條件A (價格過低) 的連續檢查計數器
            LifecycleSvc->>Redis: 讀/寫 ST條件B (市值過低) 的連續檢查計數器
            alt 滿足任一ST條件達到連續次數
                LifecycleSvc->>LifecycleSvc: set_stock_to_st(asset_id)
                Note right of LifecycleSvc: set_stock_to_st 內部:
                Note right of LifecycleSvc: 1. DB: 更新 virtual_assets.lifecycle_status 為 ST
                Note right of LifecycleSvc: 2. Redis: 重置ST相關計數器
                Note right of LifecycleSvc: 3. NewsSvc: 發布ST警告新聞
            end
        else 股票狀態為 ST
            LifecycleSvc->>LifecycleSvc: 檢查是否滿足從ST恢復為ACTIVE的條件 (價格和市值均達標)
            alt 滿足恢復條件
                LifecycleSvc->>LifecycleSvc: set_stock_to_active(asset_id)
                Note right of LifecycleSvc: set_stock_to_active 內部:
                Note right of LifecycleSvc: 1. DB: 更新 virtual_assets.lifecycle_status 為 ACTIVE
                Note right of LifecycleSvc: 2. Redis: 重置ST和退市相關計數器
                Note right of LifecycleSvc: 3. NewsSvc: 發布ST恢復新聞
            else 不滿足恢復條件 (繼續檢查退市)
                LifecycleSvc->>Redis: 讀/寫 退市條件C (ST時間過長) 的連續檢查計數器
                LifecycleSvc->>Redis: 讀/寫 退市條件D (ST下市值過低) 的連續檢查計數器
                alt 滿足任一退市條件達到連續次數
                    LifecycleSvc->>LifecycleSvc: set_stock_to_delisted(asset_id)
                    Note right of LifecycleSvc: set_stock_to_delisted 內部:
                    Note right of LifecycleSvc: 1. DB: 更新 virtual_assets.lifecycle_status 為 DELISTED
                    Note right of LifecycleSvc: 2. Redis: 重置所有相關計數器
                    Note right of LifecycleSvc: 3. LifecycleSvc: _get_dynamic_buyback_price() (計算回購價)
                    Note right of LifecycleSvc: 4. LifecycleSvc: process_delisted_stock_assets_internal() (清算股東資產: 更新 player_portfolios, gacha_users.oil_balance)
                    Note right of LifecycleSvc: 5. NewsSvc: 發布退市新聞
                    Note right of LifecycleSvc: 6. LifecycleSvc: trigger_new_company_creation_if_needed()
                end
            end
        end
    end
    LifecycleSvc->>Scheduler: (日誌) 檢查完成

    %% trigger_new_company_creation_if_needed 的簡化流程
    LifecycleSvc->>DB: 獲取活躍公司數量
    DB-->>LifecycleSvc: 返回活躍公司數
    alt 活躍公司數 < 最低要求
        loop 需要創建的新公司數量
            LifecycleSvc->>LifecycleSvc: create_new_stock_company()
            Note right of LifecycleSvc: create_new_stock_company 內部:
            Note right of LifecycleSvc: 1. (可選) AISvc: 生成公司名稱、代碼、描述
            Note right of LifecycleSvc: 2. DB: 插入新公司到 virtual_assets
            Note right of LifecycleSvc: 3. NewsSvc: 發布新公司上市新聞
        end
    end
```

## 5. 部署與依賴簡述 (Deployment & Dependencies Overview)
*   **部署方式：** 專案預期作為一個常駐的後台應用程式運行。核心的 Discord Bot ([`bot.py`](bot.py:1)) 會持續監聽並回應 Discord 事件。同時，系統包含多個需要定期執行的後台任務（通過 `services/scheduled/` 下的服務實現），例如：
    *   股票價格更新 (`StockPriceUpdateEngine`)。
    *   股票生命週期狀態檢查 (`StockLifecycleService`)。
    *   市場新聞生成與發布 (`StockNewsService`)。
    *   錨定價格更新 (`AnchorPriceService`，如果其包含定時更新邏輯)。
    *   這些定時任務可能由一個統一的調度器（如 APScheduler，集成在 [`bot.py`](bot.py:1) 或獨立的調度服務如 [`gacha/services/scheduled/scheduled_task_orchestrator.py`](gacha/services/scheduled/scheduled_task_orchestrator.py:1)）管理。
*   **主要外部依賴：**
    *   **PostgreSQL：** 作為主數據庫，存儲所有持久化數據。
    *   **Redis：** 用於緩存、消息隊列（如待處理的新聞價格影響）、以及存儲股票生命週期相關的連續檢查計數器。
    *   **Discord API：** 專案的核心交互平台。
    *   **(可選) 外部 AI 服務：** 如果AI內容生成依賴外部 API。

## 6. 未來展望與潛在改進 (Future Considerations & Potential Improvements)
*   **性能優化：** 隨著用戶量和數據量的增長，對資料庫查詢、頻繁的 Redis 操作以及定時任務的性能進行監控和優化。
*   **更複雜的市場機制：** 考慮引入更複雜的市場機制，如限價單、訂單簿、做市商機制等，以增加股票市場的真實感和深度。
*   **卡牌市場與股票市場的聯動：** 探索卡牌市場的表現（如特定卡牌的熱度、價格）如何間接影響相關聯的股票資產價格，或反之。
*   **用戶行為分析與反饋：** 收集用戶在抽卡和市場交易中的行為數據，用於平衡性調整和新功能設計。
*   **可擴展性：** 確保架構在未來新增遊戲模組或擴展現有功能時具有良好的可擴展性。
*   **監控與告警：** 建立完善的系統運行狀態監控和異常告警機制。

```