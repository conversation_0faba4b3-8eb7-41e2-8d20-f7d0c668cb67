services:
  # Bot 正式環境配置（連接到主機現有服務）
  bot-prod:
    build: .
    container_name: dickpk_bot_prod
    env_file: []  # 禁止讀取 .env 文件，避免被本機設定覆蓋
    volumes:
      - ./logs:/app/logs
      - ./database/gacha_waifu:/app/database/gacha_waifu
      - ./fonts:/app/fonts
      - ./image_processing:/app/image_processing
    environment:
      - DISCORD_TOKEN=MTIyMTIzMDczNDYwMjE0MTcyNw.G5uxa8.nCy4baPoCd7YD_qe1BTfsuptJzz_hzj9UeLGI8
      - GACHA_DB_NAME=gacha_database
      - LOG_LEVEL=INFO
      - PG_HOST=host.docker.internal  # 連接到主機的 PostgreSQL
      - PG_PORT=5432
      - PG_USER=postgres
      - PG_PASSWORD=26015792
      - PVE_DB_TYPE=postgresql
      - LADDER_DB_TYPE=postgresql
      - LADDER_DATABASE=ladder_database
      - DEV_MODE=false
      - PVE_DATABASE=pve_database
      - ENABLE_PVE_SYSTEM=false
      - ENABLE_LADDER_SYSTEM=true
      - ENABLE_GACHA_SYSTEM=true
      - AI_API_KEY=26015792
      - REDIS_HOST=host.docker.internal  # 連接到主機的 Redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - REDIS_PASSWORD=
      - REDIS_ENABLED=True
    restart: unless-stopped

volumes:
  postgres_prod_data:
  redis_prod_data: 