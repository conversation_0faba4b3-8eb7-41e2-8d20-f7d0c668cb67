"""
包含 Gacha 系統中使用的 Modals
"""
from typing import TYPE_CHECKING
from decimal import Decimal
import discord
import logging
from typing import TYPE_CHECKING, Optional, Callable, Awaitable
from utils.logger import logger
from gacha.utils import interaction_utils
from gacha.utils.mines_constants import MIN_BET, MAX_BET
if TYPE_CHECKING:
    from gacha.views.handlers.mines_callback_handler import MinesCallbackHandler

class MinesCustomBetModal(discord.ui.Modal, title='自訂金額'):
    """尋寶礦區自訂下注金額模態框"""
    bet_amount = discord.ui.TextInput(label=f'請輸入下注金額 ({MIN_BET}-{MAX_BET})', placeholder=f'請輸入{MIN_BET}-{MAX_BET}之間的整數', min_length=len(str(MIN_BET)), max_length=len(str(MAX_BET)), required=True)

    def __init__(self, handler: 'MinesCallbackHandler'):
        super().__init__()
        self.handler = handler

    async def on_submit(self, interaction: discord.Interaction):
        try:
            bet_val_str = self.bet_amount.value
            bet_amount = int(bet_val_str)
            if bet_amount < MIN_BET or bet_amount > MAX_BET:
                await interaction.response.send_message(f'下注金額必須在 {MIN_BET:,} 至 {MAX_BET:,} 之間。', ephemeral=True)
                return
            if hasattr(self.handler, 'handle_custom_bet_submit'):
                await self.handler.handle_custom_bet_submit(interaction, self, bet_val_str)
            else:
                logger.error("MinesCustomBetModal: Handler does not have 'handle_custom_bet_submit' method.")
                await interaction_utils.safe_send_message(interaction, '處理提交時發生內部錯誤 (Handler method missing)。', ephemeral=True)
        except ValueError:
            await interaction.response.send_message('請輸入有效的數字金額。', ephemeral=True)
        except Exception as e:
            logging.error(f'Error during MinesCustomBetModal on_submit (calling handler): {e}', exc_info=True)
            await interaction_utils.safe_send_message(interaction, '處理自訂金額時發生未知錯誤。', ephemeral=True)
if TYPE_CHECKING:
    from gacha.services.market.stock_trading_service import StockTradingService

class BuyStockModal(discord.ui.Modal, title='買入股票'):
    quantity_input = discord.ui.TextInput(label='購買數量', placeholder='請輸入要購買的股數', min_length=1, max_length=10, required=True)

    def __init__(self, stock_symbol: str, current_price: Decimal, stock_trading_service: 'StockTradingService', on_trade_complete_callback: Optional[Callable[[discord.Interaction], Awaitable[None]]]=None):
        super().__init__()
        self.stock_symbol = stock_symbol
        self.current_price = current_price
        self.stock_trading_service = stock_trading_service
        self.on_trade_complete_callback = on_trade_complete_callback
        self.add_item(discord.ui.TextInput(label='股票代碼', default=self.stock_symbol, style=discord.TextStyle.short, required=False))
        self.add_item(discord.ui.TextInput(label='當前每股價格 (油幣)', default=f'{self.current_price:.2f}', style=discord.TextStyle.short, required=False))

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True, thinking=True)
        try:
            quantity_str = self.quantity_input.value
            quantity = int(quantity_str)
            if quantity <= 0:
                await interaction.followup.send('購買數量必須是正整數。', ephemeral=True)
                return
            if not self.stock_trading_service:
                await interaction.followup.send('交易服務暫不可用，請稍後再試。', ephemeral=True)
                return
                
            # 直接調用，不檢查返回值
            message = await self.stock_trading_service.buy_stock(
                user_id=interaction.user.id, 
                asset_symbol=self.stock_symbol, 
                quantity=quantity, 
                price_per_unit=self.current_price
            )
            
            # 處理成功情況
            total_cost = self.current_price * quantity
            await interaction.followup.send(
                f'✅ 成功買入 {quantity} 股 {self.stock_symbol}！\n'
                f'總花費: {total_cost:.2f} 油幣。\n{message}', 
                ephemeral=False
            )
            
            if self.on_trade_complete_callback:
                try:
                    await self.on_trade_complete_callback(interaction)
                except Exception as cb_err:
                    logger.error('Error executing on_trade_complete_callback after buying stock: %s', cb_err, exc_info=True)
                    
        except ValueError:
            await interaction.followup.send('請輸入有效的數字作為購買數量。', ephemeral=True)
        except ImportError:
            # 防止導入異常類別失敗
            logger.error('Failed to import exception classes in BuyStockModal')
            await interaction.followup.send('買入股票時發生系統錯誤。', ephemeral=True)
        except Exception as e:
            # 導入所有可能的異常類別
            from gacha.exceptions import (
                InvalidGameActionError, 
                MarketStockNotFoundError, 
                MarketDataUnavailableError,
                InsufficientFundsError,
                DatabaseOperationError,
                MarketSystemError
            )
            
            # 根據異常類型提供特定的用戶友好訊息
            if isinstance(e, InvalidGameActionError):
                await interaction.followup.send(f'❌ 操作無效：{str(e)}', ephemeral=True)
            elif isinstance(e, MarketStockNotFoundError):
                await interaction.followup.send(f'❌ 找不到股票：{str(e)}', ephemeral=True)
            elif isinstance(e, MarketDataUnavailableError):
                await interaction.followup.send(f'❌ 股票資料無效：{str(e)}', ephemeral=True)
            elif isinstance(e, InsufficientFundsError):
                await interaction.followup.send(f'❌ 餘額不足：{str(e)}', ephemeral=True)
            elif isinstance(e, DatabaseOperationError) or isinstance(e, MarketSystemError):
                await interaction.followup.send('❌ 系統錯誤：買入股票時發生資料庫操作問題。', ephemeral=True)
                logger.error('Database error in BuyStockModal on_submit for %s by %s: %s', 
                            self.stock_symbol, interaction.user.id, e, exc_info=True)
            else:
                logger.error('Unexpected error in BuyStockModal on_submit for %s by %s: %s', 
                            self.stock_symbol, interaction.user.id, e, exc_info=True)
                await interaction.followup.send('❌ 買入股票時發生未知錯誤。', ephemeral=True)

class SellStockModal(discord.ui.Modal, title='賣出股票'):
    quantity_input = discord.ui.TextInput(label='賣出數量', placeholder='請輸入要賣出的股數', min_length=1, max_length=10, required=True)

    def __init__(self, stock_symbol: str, current_price: Decimal, user_id: int, stock_trading_service: 'StockTradingService', max_quantity: int, on_trade_complete_callback: Optional[Callable[[discord.Interaction], Awaitable[None]]]=None):
        super().__init__()
        self.stock_symbol = stock_symbol
        self.current_price = current_price
        self.user_id = user_id
        self.stock_trading_service = stock_trading_service
        self.max_quantity = max_quantity
        self.on_trade_complete_callback = on_trade_complete_callback
        self.add_item(discord.ui.TextInput(label='股票代碼', default=self.stock_symbol, style=discord.TextStyle.short, required=False))
        self.add_item(discord.ui.TextInput(label='當前每股價格 (油幣)', default=f'{self.current_price:.2f}', style=discord.TextStyle.short, required=False))
        self.add_item(discord.ui.TextInput(label='您目前持有股數', default=str(self.max_quantity), style=discord.TextStyle.short, required=False))

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True, thinking=True)
        try:
            quantity_str = self.quantity_input.value
            quantity = int(quantity_str)
            if quantity <= 0:
                await interaction.followup.send('賣出數量必須是正整數。', ephemeral=True)
                return
            if quantity > self.max_quantity:
                await interaction.followup.send(f'賣出數量 ({quantity}) 不能超過您持有的股數 ({self.max_quantity})。', ephemeral=True)
                return
            if not self.stock_trading_service:
                await interaction.followup.send('交易服務暫不可用，請稍後再試。', ephemeral=True)
                return
                
            # 直接調用，不檢查返回值
            message = await self.stock_trading_service.sell_stock(
                user_id=self.user_id, 
                asset_symbol=self.stock_symbol, 
                quantity=quantity, 
                price_per_unit=self.current_price
            )
            
            # 處理成功情況
            total_revenue = self.current_price * quantity
            await interaction.followup.send(
                f'✅ 成功賣出 {quantity} 股 {self.stock_symbol}！\n'
                f'預計收入: {total_revenue:.2f} 油幣。\n{message}', 
                ephemeral=False
            )
            
            if self.on_trade_complete_callback:
                try:
                    await self.on_trade_complete_callback(interaction)
                except Exception as cb_err:
                    logger.error('Error executing on_trade_complete_callback after selling stock: %s', cb_err, exc_info=True)
                    
        except ValueError:
            await interaction.followup.send('請輸入有效的數字作為賣出數量。', ephemeral=True)
        except ImportError:
            # 防止導入異常類別失敗
            logger.error('Failed to import exception classes in SellStockModal')
            await interaction.followup.send('賣出股票時發生系統錯誤。', ephemeral=True)
        except Exception as e:
            # 導入所有可能的異常類別
            from gacha.exceptions import (
                InvalidGameActionError, 
                MarketStockNotFoundError, 
                InsufficientStockQuantityError, 
                DatabaseOperationError,
                MarketSystemError
            )
            
            # 根據異常類型提供特定的用戶友好訊息
            if isinstance(e, InvalidGameActionError):
                await interaction.followup.send(f'❌ 操作無效：{str(e)}', ephemeral=True)
            elif isinstance(e, MarketStockNotFoundError):
                await interaction.followup.send(f'❌ 找不到股票：{str(e)}', ephemeral=True)
            elif isinstance(e, InsufficientStockQuantityError):
                await interaction.followup.send(f'❌ 持有股數不足：{str(e)}', ephemeral=True)
            elif isinstance(e, DatabaseOperationError) or isinstance(e, MarketSystemError):
                await interaction.followup.send('❌ 系統錯誤：賣出股票時發生資料庫操作問題。', ephemeral=True)
                logger.error('Database error in SellStockModal on_submit for %s by %s: %s', 
                            self.stock_symbol, interaction.user.id, e, exc_info=True)
            else:
                logger.error('Unexpected error in SellStockModal on_submit for %s by %s: %s', 
                            self.stock_symbol, interaction.user.id, e, exc_info=True)
                await interaction.followup.send('❌ 賣出股票時發生未知錯誤。', ephemeral=True)