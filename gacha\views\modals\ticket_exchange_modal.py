import discord
from discord.ext import commands
from discord.ui import Modal, TextInput
from utils.logger import logger
from gacha.services.shop.shop_service import ShopService
from gacha.views.shop.random_ticket_confirmation_view import RandomTicketConfirmationView
from gacha.views.embeds.shop.random_ticket_confirmation_embed_builder import build_random_ticket_confirmation_embed
from gacha.views.shop.specific_ticket_selection_view import SpecificTicketSelectionView
from gacha.views.embeds.shop.specific_ticket_selection_embed_builder import build_specific_ticket_selection_embed
from gacha.exceptions import (
    InvalidQuantityError, InvalidShopItemError, InsufficientOilTicketsError,
    SessionCreationError, InvalidSessionError, ShopSystemError
)

class TicketExchangeModal(Modal, title='輸入兌換數量'):

    def __init__(self, cog: commands.Cog, shop_item_id: str, original_message_to_edit: discord.Message, ticket_name: str=None, timeout: int=300):
        super().__init__(timeout=timeout)
        self.cog = cog
        self.shop_item_id = shop_item_id
        self.original_message_to_edit = original_message_to_edit
        self.ticket_name = ticket_name
        self.quantity_input = TextInput(label='請輸入兌換數量', placeholder='例如: 1', default='1', min_length=1, max_length=3, required=True)
        self.add_item(self.quantity_input)

    async def on_submit(self, interaction: discord.Interaction):
        """處理 Modal 提交"""
        quantity_str = self.quantity_input.value.strip()
        user_id = interaction.user.id
        
        try:
            quantity = int(quantity_str)
            if quantity <= 0:
                await interaction.response.send_message('數量必須是正整數。', ephemeral=True)
                return
        except ValueError:
            await interaction.response.send_message('數量必須是一個有效的數字。', ephemeral=True)
            return
            
        await interaction.response.defer(ephemeral=True)
        
        try:
            shop_service: ShopService = self.cog.shop_service
            
            # 啟動兌換流程
            exchange_result = await shop_service.initiate_ticket_exchange(
                user_id=user_id, 
                shop_item_id=self.shop_item_id, 
                quantity=quantity
            )
            
            # 獲取會話ID和下一步操作
            session_id = exchange_result.get('session_id')
            next_step = exchange_result.get('next_step')
            message = exchange_result.get('message', '')
            
            # 如果沒有會話ID，直接返回結果
            if not session_id:
                await interaction.followup.send(message)
                return
                
            # 獲取會話數據
            session_data_model = shop_service.get_session_data(session_id)
            current_ticket_name = self.ticket_name or session_data_model.ticket_definition.display_name
            
            # 根據下一步操作處理不同的兌換流程
            if next_step == 'confirm_random':
                # 隨機票券確認流程
                confirm_embed = build_random_ticket_confirmation_embed(interaction, session_data_model)
                confirm_view = RandomTicketConfirmationView(cog=self.cog, session_id=session_id)
                
                if self.original_message_to_edit:
                    await self.original_message_to_edit.edit(embed=confirm_embed, view=confirm_view)
                else:
                    logger.error('TicketExchangeModal session %s: original_message_to_edit was None for confirm_random. Sending new public message via followup as fallback.', session_id)
                    await interaction.followup.send(embed=confirm_embed, view=confirm_view, ephemeral=False)
                    
            elif next_step == 'select_specific_card':
                # 指定票券選擇流程
                specific_selection_view = SpecificTicketSelectionView(
                    original_interaction=interaction, 
                    cog=self.cog, 
                    shop_service=shop_service, 
                    session_id=session_id, 
                    initial_session_data=session_data_model
                )
                
                initial_embed, configured_view = await specific_selection_view.prepare_initial_message_payload(interaction)
                
                if self.original_message_to_edit:
                    await self.original_message_to_edit.edit(embed=initial_embed, view=configured_view)
                    if hasattr(configured_view, 'message') and configured_view.message is None:
                        configured_view.message = self.original_message_to_edit
                    elif not hasattr(configured_view, 'message'):
                        logger.warning('TicketExchangeModal: configured_view %s does not have .message attribute to set after edit.', type(configured_view))
                else:
                    logger.error('TicketExchangeModal session %s: original_message_to_edit was None. Sending new message instead.', session_id)
                    new_message = await interaction.channel.send(embed=initial_embed, view=configured_view)
                    if hasattr(configured_view, 'message') and configured_view.message is None:
                        configured_view.message = new_message
                    elif not hasattr(configured_view, 'message'):
                        logger.warning('TicketExchangeModal: configured_view %s does not have .message attribute to set after new send.', type(configured_view))
            else:
                # 其他情況
                response_message = f'成功初始化 {current_ticket_name} 兌換！\n{message}'
                if session_id:
                    response_message += f'\n會話 ID: `{session_id}`'
                response_message += '\n請按指示完成後續操作。'
                await interaction.followup.send(response_message, ephemeral=False)
                
        except InvalidQuantityError as e:
            await interaction.followup.send(f'兌換數量無效: {str(e)}', ephemeral=True)
        except InvalidShopItemError as e:
            await interaction.followup.send(f'商品項目無效: {str(e)}', ephemeral=True)
        except InsufficientOilTicketsError as e:
            await interaction.followup.send(f'油票不足: {str(e)}', ephemeral=True)
        except SessionCreationError as e:
            await interaction.followup.send(f'創建兌換會話失敗: {str(e)}', ephemeral=True)
        except InvalidSessionError as e:
            await interaction.followup.send(f'無效的會話: {str(e)}', ephemeral=True)
        except ShopSystemError as e:
            await interaction.followup.send(f'商店系統錯誤: {str(e)}', ephemeral=True)
        except Exception as e:
            logger.error('在處理票券兌換 Modal 提交時發生錯誤 (user: %s, shop_item_id: %s): %s', interaction.user.id, self.shop_item_id, e, exc_info=True)
            if not interaction.response.is_done():
                await interaction.response.send_message('處理您的兌換請求時發生內部錯誤，請稍後再試或聯繫管理員。', ephemeral=True)
            else:
                await interaction.followup.send('處理您的兌換請求時發生內部錯誤，請稍後再試或聯繫管理員。', ephemeral=True)

    async def on_error(self, interaction: discord.Interaction, error: Exception) -> None:
        """處理 Modal 中的錯誤"""
        logger.error('在 Modal %s 中發生錯誤 (user: %s, shop_item_id: %s): %s', self.title, interaction.user.id, self.shop_item_id, error, exc_info=True)
        if not interaction.response.is_done():
            await interaction.response.send_message('處理您的請求時發生錯誤。', ephemeral=True)
        else:
            await interaction.followup.send('處理您的請求時發生錯誤。', ephemeral=True)