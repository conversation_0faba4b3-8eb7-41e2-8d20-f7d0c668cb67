"""
Gacha系統全圖鑑視圖
處理全圖鑑指令的視圖和交互
"""
import asyncio
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
import discord
import asyncpg
from discord.ui import But<PERSON>, View
from utils.logger import logger
from database.postgresql.async_manager import AsyncPgManager
from gacha.models.filters import CollectionFilters
from gacha.models.models import Card
from gacha.services.core.encyclopedia_service import EncyclopediaService
from gacha.views.collection.collection_view.base_pagination import BasePaginationView
from gacha.views.collection.collection_view.interaction_manager import InteractionManager
from gacha.views.embeds.collection.encyclopedia_embed_builder import EncyclopediaEmbedBuilder
from gacha.exceptions import (
    CardPageNotFoundException,
    DatabaseOperationError,
    EncyclopediaError
)

class EncyclopediaView(BasePaginationView):
    """全圖鑑視圖"""

    def __init__(self, interaction: discord.Interaction, encyclopedia_service: EncyclopediaService, page: int=1, sort_by: str='rarity', sort_order: str='desc', pool_type: Optional[str]=None, rarity: Optional[Union[int, List[int]]]=None, series_name: Optional[str]=None, card_id: Optional[int]=None, card_name: Optional[str]=None, initial_data: dict=None):
        """初始化全圖鑑視圖 (Modified for async factory)

        參數:
            interaction: 觸發指令的交互對象
            encyclopedia_service: 已初始化的 EncyclopediaService 實例
            page: 當前頁碼
            sort_by: 排序字段
            sort_order: 排序順序
            pool_type: 卡池類型
            rarity: 稀有度 (數字)，可以是單一數字或數字列表
            series_name: 系列名稱
            card_id: 卡片ID篩選
            card_name: 卡片名稱篩選
            initial_data: 預先載入的初始頁面數據
        """
        user = interaction.user
        total_pages = initial_data.get('total_pages', 1) if initial_data else 1
        super().__init__(user=user, current_page=page, total_pages=total_pages, timeout=60)
        self.interaction = interaction
        self.encyclopedia_service = encyclopedia_service
        self.filters = CollectionFilters()
        self.filters.pool_type = None if pool_type == 'all' else pool_type
        if rarity is not None:
            if isinstance(rarity, list):
                self.filters.rarity_in = [int(r) for r in rarity if isinstance(r, (int, str)) and str(r).isdigit()]
            elif isinstance(rarity, (int, str)) and str(rarity).isdigit():
                self.filters.rarity_in = [int(rarity)]
            else:
                self.filters.rarity_in = None
        else:
            self.filters.rarity_in = None
        self.filters.series = series_name
        self.filters.card_id = card_id
        self.filters.card_name = card_name
        self.sort_by = sort_by
        self.sort_order = sort_order
        self.has_card = initial_data.get('has_card', False) if initial_data else False
        self.card_data = initial_data.get('card_data', {}) if initial_data else {}
        self.owner_count = initial_data.get('owner_count', 0) if initial_data else 0
        self.total_cards = initial_data.get('total_cards', 0) if initial_data else 0
        self.page = page

    @classmethod
    async def create(cls, interaction: discord.Interaction, page: int=1, sort_by: str='rarity', sort_order: str='desc', pool_type: Optional[str]=None, rarity: Optional[Union[int, List[int]]]=None, series_name: Optional[str]=None, card_id: Optional[int]=None, card_name: Optional[str]=None):
        """非同步創建並初始化 EncyclopediaView 實例"""
        try:
            encyclopedia_service: EncyclopediaService = interaction.client.encyclopedia_service
            if encyclopedia_service is None:
                raise AttributeError('Required service (EncyclopediaService) not found on bot instance.')
        except AttributeError:
            logger.error('[EncyclopediaView] Failed to get EncyclopediaService from interaction.client. Bot might not have initialized the service correctly.')
            await interaction.response.send_message('無法載入圖鑑資料，服務未正確初始化。', ephemeral=True)
            return None
        filters = CollectionFilters()
        filters.pool_type = None if pool_type == 'all' else pool_type
        if rarity is not None:
            if isinstance(rarity, list):
                filters.rarity_in = [int(r) for r in rarity if isinstance(r, (int, str)) and str(r).isdigit()]
            elif isinstance(rarity, (int, str)) and str(rarity).isdigit():
                filters.rarity_in = [int(rarity)]
            else:
                filters.rarity_in = None
        else:
            filters.rarity_in = None
        filters.series = series_name
        filters.card_id = card_id
        filters.card_name = card_name
        
        try:
            initial_result = await encyclopedia_service.get_card_for_page(
                page=page, 
                sort_by=sort_by, 
                sort_order=sort_order, 
                pool_type=filters.pool_type, 
                rarity=filters.rarity_in, 
                series_name=filters.series, 
                card_id=filters.card_id, 
                card_name=filters.card_name
            )
            total_pages = initial_result.get('total_pages', 1)
            valid_page = max(1, min(page, total_pages)) if total_pages > 0 else 1
            
            if valid_page != page:
                initial_result = await encyclopedia_service.get_card_for_page(
                    page=valid_page, 
                    sort_by=sort_by, 
                    sort_order=sort_order, 
                    pool_type=filters.pool_type, 
                    rarity=filters.rarity_in, 
                    series_name=filters.series, 
                    card_id=filters.card_id, 
                    card_name=filters.card_name
                )
                page = valid_page
                
            instance = cls(
                interaction=interaction, 
                encyclopedia_service=encyclopedia_service, 
                page=page, 
                sort_by=sort_by, 
                sort_order=sort_order, 
                pool_type=pool_type, 
                rarity=rarity, 
                series_name=series_name, 
                card_id=card_id, 
                card_name=card_name, 
                initial_data=initial_result
            )
            return instance
            
        except CardPageNotFoundException as e:
            await interaction.response.send_message(f"找不到頁面：{str(e)}", ephemeral=True)
            return None
        except DatabaseOperationError as e:
            await interaction.response.send_message(f"資料庫操作錯誤：{str(e)}", ephemeral=True)
            return None
        except Exception as e:
            logger.error('[EncyclopediaView] 創建視圖時發生錯誤: %s', str(e), exc_info=True)
            await interaction.response.send_message('載入圖鑑資料時發生未知錯誤。', ephemeral=True)
            return None

    async def get_current_page_embed(self) -> discord.Embed:
        """實現父類的抽象方法，返回當前頁面的 Embed

        返回:
            discord.Embed: 當前頁面的 Embed 對象
        """
        builder = EncyclopediaEmbedBuilder(card_data=self.card_data if self.has_card else None, page=self.page, total_pages=self.total_pages, total_cards=self.total_cards, owner_count=self.owner_count)
        return builder.build_embed()

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """實現父類的抽象方法，更新頁面數據

        參數:
            page: 新的頁碼
            interaction: 交互對象
        """
        start_time = time.time()
        try:
            self.page = page
            self.current_page = page
            result = await self.encyclopedia_service.get_card_for_page(
                page=page, 
                sort_by=self.sort_by, 
                sort_order=self.sort_order, 
                pool_type=self.filters.pool_type, 
                rarity=self.filters.rarity_in, 
                series_name=self.filters.series, 
                card_id=self.filters.card_id, 
                card_name=self.filters.card_name
            )
            
            self.has_card = result.get('has_card', False)
            self.card_data = result.get('card_data', {})
            self.owner_count = result.get('owner_count', 0)
            self.total_cards = result.get('total_cards', 0)
            self.total_pages = result.get('total_pages', 1)
            
            self._refresh_button_states()
            
            embed = await self.get_current_page_embed()
            await interaction.followup.edit_message(message_id=interaction.message.id, embed=embed, view=self)
            
        except CardPageNotFoundException as e:
            end_time = time.time()
            logger.error('頁面不存在: 用戶ID=%s, 頁碼=%s, 錯誤=%s, 耗時=%ss', self.user_id, page, str(e), end_time - start_time)
            await interaction.followup.send(f"找不到頁面：{str(e)}", ephemeral=True)
            
        except DatabaseOperationError as e:
            end_time = time.time()
            logger.error('數據庫操作錯誤: 用戶ID=%s, 頁碼=%s, 錯誤=%s, 耗時=%ss', self.user_id, page, str(e), end_time - start_time)
            await interaction.followup.send(f"資料庫操作錯誤：{str(e)}", ephemeral=True)
            
        except Exception as e:
            end_time = time.time()
            logger.error('頁面切換失敗: 用戶ID=%s, 頁碼=%s, 錯誤=%s, 耗時=%ss', self.user_id, page, str(e), end_time - start_time, exc_info=True)
            try:
                await interaction.followup.send('載入頁面時發生錯誤，請重試。', ephemeral=True)
            except Exception:
                pass