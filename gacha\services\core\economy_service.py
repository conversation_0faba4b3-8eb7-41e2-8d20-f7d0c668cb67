from __future__ import annotations
import asyncpg
import asyncio
from typing import Dict, List, Optional, Tuple, Any, Literal
from gacha.repositories.collection.user_collection_repository import CardSellOperation, SellOperationDetailType
import time
from datetime import datetime
import json
from decimal import Decimal, ROUND_HALF_UP
import typing
if typing.TYPE_CHECKING:
    from gacha.services.common.validation_service import ValidationService
from utils.logger import logger
from database.redis.manager import RedisManager
from gacha.utils.redis_publisher import RedisEventPublisher
from gacha.services.market.market_price_service import MarketPriceService
from gacha.constants import MarketStatsEventType
from gacha.app_config import config_service
from database.postgresql.async_manager import AsyncPgManager
from gacha.models.models import Card, GachaUser, UserCard
from gacha.repositories.card.master_card_repository import MasterCardRepository
from gacha.repositories.collection.user_collection_repository import UserCollectionRepository
from gacha.services.core.user_service import UserService
from gacha.services.core.collection_service import CollectionService
from gacha.models.filters import CollectionFilters
from gacha.exceptions import UserNotFoundError, StoredPriceError, CooldownError

class InvalidPriceError(Exception):
    """當卡片價格無效或無法獲取時拋出此異常。"""
    pass

SellOperationType = Literal['ONE', 'LEAVE_ONE', 'ALL']

class EconomyService:
    """經濟服務 (Asyncpg 版本)，處理油幣和交易相關邏輯"""

    def __init__(self, pool: asyncpg.Pool, user_service: UserService, card_repo: MasterCardRepository, collection_repo: UserCollectionRepository, collection_service: CollectionService, market_price_service: MarketPriceService, validation_service: ValidationService, redis_publisher: Optional[RedisEventPublisher]=None, redis_manager_legacy_hourly: Optional[RedisManager]=None):
        if pool is None:
            err_msg = 'EconomyService 初始化失敗：必須提供 asyncpg 連接池。'
            logger.error(err_msg)
            raise ValueError(err_msg)
        if market_price_service is None or validation_service is None:
            err_msg = 'EconomyService 初始化失敗：必須提供 MarketPriceService 和 ValidationService 實例。'
            logger.error(err_msg)
            raise ValueError(err_msg)
        if redis_manager_legacy_hourly and (not redis_publisher):
            logger.warning('EconomyService 初始化：提供了 legacy RedisManager 但未提供 RedisEventPublisher。事件發布將不可用。')
        self.pool = pool
        self.user_service = user_service
        self.card_repo = card_repo
        self.collection_repo = collection_repo
        self.collection_service = collection_service
        self.validation_service = validation_service
        self.redis = redis_manager_legacy_hourly
        self.redis_publisher = redis_publisher
        self.market_price_service = market_price_service
        self.DAILY_REWARD = config_service.get_config('gacha_core_settings.economy_daily_reward', 5000)
        self.HOURLY_REWARD = config_service.get_config('gacha_core_settings.economy_hourly_reward', 2000)
        self.HOURLY_CLAIM_KEY = config_service.get_config('gacha_core_settings.economy_hourly_claim_key_prefix', 'gacha:hourly_claim:')

    async def get_balance(self, user_id: int) -> Dict[str, int]:
        """
        獲取用戶油幣餘額和相關資訊（自動創建用戶）
        
        Args:
            user_id: 用戶ID
            
        Returns:
            包含餘額和抽卡次數的字典
            
        Raises:
            UserNotFoundError: 如果用戶不存在且無法創建
            DatabaseOperationError: 如果資料庫操作失敗
        """
        user = await self.validation_service.ensure_user_exists(user_id, create_if_missing=True)
        return {
            'balance': user.oil_balance,
            'total_draws': user.total_draws
        }

    async def claim_daily_reward(self, user_id: int) -> Dict[str, Any]:
        """
        領取每日獎勵
        
        Args:
            user_id: 用戶ID
            
        Returns:
            包含新餘額和獎勵金額的字典
            
        Raises:
            UserNotFoundError: 如果用戶不存在且無法創建
            CooldownError: 如果用戶今天已經領取過獎勵
            DatabaseOperationError: 如果資料庫操作失敗
        """
        async with self.pool.acquire() as conn:
            async with conn.transaction():
                user_repo = self.user_service.user_repo
                await self.validation_service.ensure_user_exists(user_id, create_if_missing=True, connection=conn)
                user = await user_repo.get_user_for_update(user_id, connection=conn)
                
                if not user.can_claim_daily:
                    # 計算下次可領取時間（台灣時間明天的午夜）
                    import datetime as dt
                    from datetime import timezone
                    import pytz
                    
                    # 獲取台灣時區
                    tw_timezone = pytz.timezone('Asia/Taipei')
                    
                    # 獲取當前台灣時間
                    current_time_utc = dt.datetime.now(timezone.utc)
                    current_time_tw = current_time_utc.astimezone(tw_timezone)
                    
                    # 計算台灣時間下一個日期的午夜時間
                    next_day_tw = current_time_tw.date() + dt.timedelta(days=1)
                    next_midnight_tw = dt.datetime.combine(next_day_tw, dt.time.min)
                    next_midnight_tw = tw_timezone.localize(next_midnight_tw)
                    
                    # 轉換回 UTC 時間戳
                    next_claim_timestamp = int(next_midnight_tw.timestamp())
                    
                    # 計算距離下次可領取的時間
                    seconds_until_reset = (next_midnight_tw - current_time_tw).total_seconds()
                    hours_remaining = int(seconds_until_reset // 3600)
                    minutes_remaining = int((seconds_until_reset % 3600) // 60)
                    
                    # 根據剩餘時間格式化訊息
                    if hours_remaining > 0:
                        time_msg = f"{hours_remaining} 小時"
                        if minutes_remaining > 0:
                            time_msg += f" {minutes_remaining} 分鐘"
                    else:
                        time_msg = f"{minutes_remaining} 分鐘"
                    
                    # 直接拋出 CooldownError 異常，由 Cog 層捕獲並處理
                    raise CooldownError(
                        message=f"今天已經領取過了，請在 {time_msg} 後再來！", 
                        next_available_time=next_claim_timestamp,
                        cooldown_type="daily"
                    )
                
                new_balance = user.oil_balance + self.DAILY_REWARD
                await user_repo.update_balance(user_id, new_balance, connection=conn)
                await user_repo.update_daily_claim(user_id, connection=conn)
                
        return {
            'new_balance': new_balance,
            'reward': self.DAILY_REWARD
        }

    async def claim_hourly_reward(self, user_id: int) -> Dict[str, Any]:
        """
        領取每小時獎勵
        
        Args:
            user_id: 用戶ID
            
        Returns:
            包含新餘額、獎勵金額和下次可領取時間的字典
            
        Raises:
            CooldownError: 如果用戶處於冷卻時間內
            ValueError: 如果Redis不可用
            UserNotFoundError: 如果用戶不存在且無法創建
            DatabaseOperationError: 如果資料庫操作失敗
        """
        logger.debug('[GACHA][HOURLY] Entering claim_hourly_reward for user %s. self.redis is: %s (type: %s)', user_id, self.redis, type(self.redis))
        hourly_key = f'{self.HOURLY_CLAIM_KEY}{user_id}'
        current_time = int(time.time())
        
        # 檢查Redis是否可用
        if not self.redis:
            logger.error('[GACHA][HOURLY] RedisManager for hourly claims not provided to EconomyService. User %s', user_id)
            # 直接拋出 ValueError 異常，由 Cog 層捕獲並處理
            raise ValueError('每小時獎勵功能暫時不可用。')
        
        # 檢查冷卻時間
        last_claim_val = await self.redis.get(hourly_key)
        if last_claim_val is not None:
            last_claim = int(last_claim_val)
            time_diff = current_time - last_claim
            if time_diff < 3600:
                remaining_seconds = 3600 - time_diff
                next_claim_time = current_time + remaining_seconds
                
                # 計算剩餘分鐘和秒數，用於顯示
                remaining_minutes = remaining_seconds // 60
                remaining_seconds_display = remaining_seconds % 60
                
                # 直接拋出 CooldownError 異常，由 Cog 層捕獲並處理
                raise CooldownError(
                    message=f"冷卻中！請在 {remaining_minutes} 分 {remaining_seconds_display} 秒後再試", 
                    next_available_time=next_claim_time,
                    cooldown_type="hourly"
                )
        
        # 更新用戶餘額
        new_balance = 0
        async with self.pool.acquire() as conn:
            async with conn.transaction():
                user = await self.validation_service.ensure_user_exists(user_id, create_if_missing=True, connection=conn)
                new_balance = user.oil_balance + self.HOURLY_REWARD
                await self.user_service.user_repo.update_balance(user_id, new_balance, connection=conn)
        
        # 設置冷卻時間
        await self.redis.set(hourly_key, current_time, ttl=3600)
        next_claim_time = current_time + 3600
        
        return {
            'new_balance': new_balance,
            'reward': self.HOURLY_REWARD,
            'next_claim_time': next_claim_time
        }

    async def sell_cards_universal(self, user_id: int, filters: CollectionFilters, operation_type: SellOperationType, force_sell: bool=False) -> Dict[str, Any]:
        """
        通用的卡片售賣接口 (基於預存價格)。

        參數:
            user_id (int): 用戶ID。
            filters (CollectionFilters): 卡片篩選條件。
            operation_type (SellOperationType): 售賣操作類型 ('ONE', 'LEAVE_ONE', 'ALL')。
            force_sell (bool): 是否強制售賣最愛卡片。

        返回:
            包含詳細售賣結果的字典。
            
        拋出:
            UserNotFoundError: 如果用戶不存在
            StoredPriceError: 如果卡片預存價格無效
            ValueError: 如果沒有符合條件的卡片可售賣
            RuntimeError: 如果數據庫操作失敗
        """
        current_balance_initial: Optional[int] = None
        try:
            user_obj = await self.user_service.get_user(user_id)
            if user_obj:
                current_balance_initial = user_obj.oil_balance
            else:
                logger.warning('[SELL_UNIVERSAL_B] User %s: User object not found initially.', user_id)
                raise UserNotFoundError(user_id=user_id)
        except Exception as e_user_fetch:
            logger.error('[SELL_UNIVERSAL_B] User %s: Failed to fetch initial user balance: %s', user_id, e_user_fetch)
            raise

        candidates_with_stored_prices = await self._gather_sell_candidates_and_stored_prices(user_id, filters)
        if not candidates_with_stored_prices:
            raise ValueError('沒有找到符合篩選條件的卡片可供售賣。')

        sell_plan = self._prepare_sell_operations(candidates_with_stored_prices, operation_type, force_sell)
        
        if not sell_plan.get('cards_to_update', []) and (not sell_plan.get('cards_to_delete', [])):
            raise ValueError('沒有符合售賣條件的卡片（可能由於數量、最愛設置或操作類型限制）。')
        
        sell_tx_result = await self._execute_sell_in_transaction(user_id=user_id, sell_operations_for_repo=sell_plan['sell_operations_for_repo'], precise_total_revenue=sell_plan['total_revenue'], sold_cards_details_for_response=sell_plan['sold_cards_details_for_response'])
        final_new_balance = sell_tx_result['new_balance']
        rounded_total_revenue_decimal = sell_tx_result['total_revenue']
        logger.debug('[SELL_UNIVERSAL_C] User %s: Sell transaction successful. New balance: %s, Rounded revenue: %s', user_id, final_new_balance, rounded_total_revenue_decimal)
        
        # 移除重複的訊息生成，使用空字串作為 message
        logger.debug('[SELL_UNIVERSAL_C] User %s: Sold %s cards for %s oil', user_id, sell_plan['total_quantity_sold'], int(rounded_total_revenue_decimal))
        
        return self._format_sell_response(
            message='', 
            total_cards_sold=sell_plan['total_quantity_sold'], 
            total_revenue=int(rounded_total_revenue_decimal), 
            new_balance=final_new_balance, 
            sold_cards_summary=sell_plan['sold_cards_details_for_response']
        )

    async def _gather_sell_candidates_and_stored_prices(self, user_id: int, filters: CollectionFilters) -> List[Dict[str, Any]]:
        """
        (事務外) 獲取符合篩選條件的用戶卡片快照，這些快照需包含預存的市場價格。
        如果任何卡片的預存價格無效 (例如 NULL 或 <=0)，則拋出 StoredPriceError。
        """
        logger.debug('[SELL_GATHER_B] User %s: Gathering sell candidates with stored prices. Filters: %s', user_id, filters)
        card_snapshots = await self.collection_repo.get_sellable_card_snapshots_with_stored_price(user_id, filters)
        if not card_snapshots:
            logger.debug('[SELL_GATHER_B] User %s: No snapshots found.', user_id)
            return []
        valid_candidates = []
        for snapshot in card_snapshots:
            card_id = snapshot.get('card_id')
            if card_id is None:
                logger.warning('[SELL_GATHER_B] User %s: Snapshot missing card_id: %s', user_id, snapshot)
                continue
            stored_price = snapshot.get('current_market_sell_price')
            if stored_price is None or not isinstance(stored_price, Decimal) or stored_price <= Decimal(0):
                err_msg = f"卡片 {card_id} (名稱: {snapshot.get('name', 'N/A')}) 的預存市場價格無效 ({stored_price})。售賣操作已中止。"
                logger.error('[SELL_GATHER_B] User %s: %s', user_id, err_msg)
                raise StoredPriceError(err_msg)
            valid_candidates.append({'card_id': card_id, 'quantity': snapshot['quantity'], 'is_favorite': snapshot['is_favorite'], 'name': snapshot.get('name', 'N/A'), 'rarity': snapshot.get('rarity'), 'pool_type': snapshot.get('pool_type'), 'actual_sell_price': stored_price})
        logger.debug('[SELL_GATHER_B] User %s: Found %s valid candidates with stored prices.', user_id, len(valid_candidates))
        return valid_candidates

    def _prepare_sell_operations(self, candidates_with_stored_prices: List[Dict[str, Any]], operation_type: SellOperationType, force_sell: bool) -> Dict[str, Any]:
        """
        (事務外) 根據候選卡片、其預存價格和操作類型，計算實際要售賣的卡片和數量。
        返回一個包含 CardSellOperation 列表以及其他摘要信息的字典。
        """
        logger.debug('[SELL_PREPARE_C] Preparing operations for %s candidates. Operation: %s, Force: %s', len(candidates_with_stored_prices), operation_type, force_sell)
        sell_operations_for_repo: List[CardSellOperation] = []
        sold_cards_details_for_response: List[Dict[str, Any]] = []
        total_revenue = Decimal(0)
        total_quantity_sold = 0
        _cards_to_update_debug: Dict[int, int] = {}
        _cards_to_delete_debug: List[int] = []
        for candidate in candidates_with_stored_prices:
            card_id = candidate['card_id']
            current_quantity_owned = candidate['quantity']
            is_favorite = candidate['is_favorite']
            actual_sell_price = candidate['actual_sell_price']
            card_name = candidate.get('name', 'N/A')
            rarity = candidate.get('rarity')
            pool_type = candidate.get('pool_type')
            if not force_sell and is_favorite:
                logger.debug('[SELL_PREPARE_C] Skipping card_id %s (favorite and not force_sell).', card_id)
                continue
            quantity_to_sell_for_this_card = 0
            repo_operation_type: SellOperationDetailType
            if operation_type == 'ONE':
                if current_quantity_owned >= 1:
                    quantity_to_sell_for_this_card = 1
                    repo_operation_type = 'sell_specific_quantity_of_card'
                elif current_quantity_owned == 0:
                    quantity_to_sell_for_this_card = 0
                    repo_operation_type = 'sell_all_for_card'
                else:
                    continue
            elif operation_type == 'LEAVE_ONE':
                if current_quantity_owned > 1:
                    quantity_to_sell_for_this_card = current_quantity_owned - 1
                    repo_operation_type = 'sell_leaving_one_for_card'
                elif current_quantity_owned == 0:
                    quantity_to_sell_for_this_card = 0
                    repo_operation_type = 'sell_all_for_card'
                else:
                    continue
            elif operation_type == 'ALL':
                quantity_to_sell_for_this_card = current_quantity_owned
                repo_operation_type = 'sell_all_for_card'
            else:
                logger.warning('[SELL_PREPARE_C] Unknown operation_type: %s for card %s. Skipping.', operation_type, card_id)
                continue
            logger.debug('[SELL_PREPARE_C] Card %s: current_qty=%s, op_type=%s -> repo_op_type=%s, to_sell=%s', card_id, current_quantity_owned, operation_type, repo_operation_type, quantity_to_sell_for_this_card)
            if quantity_to_sell_for_this_card > 0 or (current_quantity_owned == 0 and repo_operation_type == 'sell_all_for_card'):
                card_revenue_for_this_item = Decimal(0)
                if quantity_to_sell_for_this_card > 0:
                    card_revenue_for_this_item = actual_sell_price * quantity_to_sell_for_this_card
                    total_revenue += card_revenue_for_this_item
                    total_quantity_sold += quantity_to_sell_for_this_card
                op_detail: CardSellOperation = {'card_id': card_id, 'operation_type': repo_operation_type, 'current_quantity_owned': current_quantity_owned, 'quantity_to_sell': quantity_to_sell_for_this_card if repo_operation_type == 'sell_specific_quantity_of_card' else None}
                sell_operations_for_repo.append(op_detail)
                sold_cards_details_for_response.append({'card_id': card_id, 'name': card_name, 'rarity': rarity, 'pool_type': pool_type, 'quantity_sold': quantity_to_sell_for_this_card, 'price_per_unit': int(actual_sell_price), 'total_value_sold_for_card': float(card_revenue_for_this_item.quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)), 'is_favorite': is_favorite})
                remaining_qty_debug = current_quantity_owned - quantity_to_sell_for_this_card
                if remaining_qty_debug > 0:
                    _cards_to_update_debug[card_id] = remaining_qty_debug
                else:
                    _cards_to_delete_debug.append(card_id)
        logger.debug('[SELL_PREPARE_C] Plan: Repo Operations: %s, Updates (debug): %s, Deletes (debug): %s, Revenue: %s, Qty Sold: %s', len(sell_operations_for_repo), len(_cards_to_update_debug), len(_cards_to_delete_debug), total_revenue, total_quantity_sold)
        return {'sell_operations_for_repo': sell_operations_for_repo, 'total_revenue': total_revenue, 'total_quantity_sold': total_quantity_sold, 'sold_cards_details_for_response': sold_cards_details_for_response, 'cards_to_update': list(_cards_to_update_debug.keys()), 'cards_to_delete': _cards_to_delete_debug}

    async def _execute_sell_in_transaction(self, user_id: int, sell_operations_for_repo: List[CardSellOperation], precise_total_revenue: Decimal, sold_cards_details_for_response: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        (事務內) 執行實際的數據庫售賣操作，調用 UserCollectionRepository 中的通用批量方法。
        此方法接收精確的總收益 (Decimal)，並在其內部進行四捨五入以更新用戶餘額。
        返回包含四捨五入總收益 (Decimal) 和新餘額 (int) 的字典。
        """
        new_balance_value: int = 0
        rounded_revenue_for_balance_update: Decimal = Decimal(0)
        card_ids_to_invalidate: List[int] = []
        transaction_successful = False
        repo_result_data_for_stream: Optional[Dict[str, Any]] = None
        async with self.pool.acquire() as conn:
            async with conn.transaction():
                current_balance_in_db = await self.user_service.user_repo.get_user_balance_for_update(user_id, connection=conn)
                if current_balance_in_db is None:
                    logger.error('[SELL_EXECUTE_C] User %s: User not found or balance could not be retrieved in transaction.', user_id)
                    raise RuntimeError(f'執行售賣事務失敗：找不到用戶 {user_id} 或無法獲取其鎖定餘額')
                logger.debug('[SELL_EXECUTE_C] User %s: Current balance in DB (locked): %s', user_id, current_balance_in_db)
                repo_result_data = None
                if sell_operations_for_repo:
                    repo_result_data = await self.collection_repo.process_sell_operations_batch(user_id=user_id, operations=sell_operations_for_repo, connection=conn)
                    repo_result_data_for_stream = repo_result_data
                    card_ids_to_invalidate = repo_result_data.get('card_ids_for_cache_invalidation', [])
                rounded_revenue_for_balance_update = precise_total_revenue.quantize(Decimal('1'), rounding=ROUND_HALF_UP)
                new_balance_value = current_balance_in_db + int(rounded_revenue_for_balance_update)
                logger.debug('[SELL_EXECUTE_C] User %s: Precise Revenue: %s, Rounded Revenue for Balance Update: %s, Calculated new balance: %s', user_id, precise_total_revenue, rounded_revenue_for_balance_update, new_balance_value)
                # 更新用戶餘額
                await self.user_service.user_repo.update_balance(user_id, new_balance_value, connection=conn)
                transaction_successful = True
            if transaction_successful and card_ids_to_invalidate:
                try:
                    await self.collection_repo._invalidate_owner_count_cache_batch(card_ids_to_invalidate)
                except Exception as e_cache:
                    logger.error('[SELL_EXECUTE_C_CACHE] User %s: Failed to invalidate owner count cache for card_ids %s after transaction. Error: %s', user_id, card_ids_to_invalidate, e_cache, exc_info=True)
        if transaction_successful and self.redis_publisher:
            total_owned_updates_payload = [(item['card_id'], -item['quantity_sold']) for item in sold_cards_details_for_response if item.get('card_id') and item.get('quantity_sold') is not None]
            unique_owner_updates_payload = []
            if repo_result_data_for_stream and repo_result_data_for_stream.get('deleted_cards'):
                for deleted_card_info in repo_result_data_for_stream.get('deleted_cards', []):
                    card_id_to_decrement = deleted_card_info.get('card_id')
                    if card_id_to_decrement is not None:
                        unique_owner_updates_payload.append((card_id_to_decrement, -1))
            if total_owned_updates_payload:
                await self.redis_publisher.publish(event_type=MarketStatsEventType.TOTAL_OWNED_UPDATE, payload=total_owned_updates_payload, batch_payload=False, user_id_for_log=user_id)
            if unique_owner_updates_payload:
                await self.redis_publisher.publish(event_type=MarketStatsEventType.UNIQUE_OWNER_UPDATE, payload=unique_owner_updates_payload, batch_payload=False, user_id_for_log=user_id)
        elif transaction_successful and (not self.redis_publisher):
            logger.warning('[SELL_REDIS_STREAM] User %s: Redis client not available, skipping market stats event publishing for sell operation.', user_id)
        return {'total_revenue': rounded_revenue_for_balance_update, 'new_balance': new_balance_value}

    def _format_sell_response(self, message: str, total_cards_sold: int=0, total_revenue: int=0, new_balance: Optional[int]=None, sold_cards_summary: Optional[List[Dict]]=None, failed_reason: Optional[str]=None) -> Dict[str, Any]:
        """
        格式化售賣操作的響應。
        
        參數:
            message: 響應消息
            total_cards_sold: 售賣的卡片總數
            total_revenue: 總收益
            new_balance: 新餘額
            sold_cards_summary: 售賣卡片摘要
            failed_reason: 失敗原因 (如有)
            
        返回:
            格式化的響應字典
        """
        actual_sold_cards_summary = sold_cards_summary if sold_cards_summary is not None else []
        return {'message': message, 'total_cards_sold': total_cards_sold, 'total_revenue': total_revenue, 'new_balance': new_balance, 'sold_cards_summary': actual_sold_cards_summary, 'sold_cards_for_examples': actual_sold_cards_summary}

    async def award_oil(self, user_id: int, amount: int, reason: str='遊戲獎勵', connection: Optional[asyncpg.Connection]=None) -> int:
        """
        獎勵用戶油幣（用於遊戲獲勝等情況）
        
        Args:
            user_id: 用戶ID
            amount: 獎勵金額（可為負數，表示扣除）
            reason: 獎勵原因
            connection: 可選的資料庫連接
            
        Returns:
            int: 更新後的餘額
            
        Raises:
            UserNotFoundError: 如果用戶不存在
            InsufficientBalanceError: 如果扣除金額大於用戶餘額
            DatabaseOperationError: 如果資料庫操作失敗
        """
        return await self.user_service.award_balance(user_id, amount, connection=connection)