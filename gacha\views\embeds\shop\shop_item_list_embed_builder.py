import discord
import math
from typing import List
from decimal import Decimal  # For oil_ticket_balance type hint
from utils.logger import logger

# 假設 ShopItemDefinition 和 OilTicketBalance 的路徑
# 請根據實際情況調整
from gacha.models.shop_models import ShopItemDefinition


class ShopItemListEmbedBuilder:
    def __init__(self, items_per_page: int = 4):
        self.items_per_page = items_per_page

    def build_embed(
        self,
        items: list[ShopItemDefinition],
        current_page: int,
        total_items: int,
        oil_ticket_balance_value: Decimal,
        category_name: str = "商店商品列表",
    ) -> discord.Embed:
        """
        建立顯示商店商品列表的 Embed。

        Args:
            items: 當前頁碼的商品列表。
            current_page: 當前頁碼 (1-based)。
            total_items: 所有商品的總數。
            oil_ticket_balance_value: 玩家的油票餘額 (Decimal value)。
            category_name: 商品分類名稱。

        Returns:
            一個 discord.Embed 物件。
        """
        total_pages = math.ceil(total_items / self.items_per_page)

        # 使用更漂亮的顏色 - 金色
        embed = discord.Embed(color=discord.Color.gold())

        # 設置作者欄位和圖示
        embed.set_author(
            name=f"🏪 {category_name} 🏪",
            icon_url="https://cdn.discordapp.com/attachments/1079179758069366945/1364902124588109835/151906-20250319-232405-000.gif?ex=682f9ce9&is=682e4b69&hm=4c0eb6b418eaed804fe07b7cba125dd1daeee0212508d71f598be3c19294b647&",
        )

        # 設置右上角圖片
        embed.set_thumbnail(
            url="https://cdn.discordapp.com/attachments/1336020673730187334/1374870032210722927/kanna-anime.gif?ex=682f9eff&is=682e4d7f&hm=4b319eb2ed278aed37d73b23c79929a43d382233f4069306064d5bc14bc353d5&"
        )

        # 添加頁數信息作為頁腳
        embed.set_footer(text=f"頁數 {current_page}/{total_pages}")

        # 添加玩家油票信息
        available_tickets = math.floor(oil_ticket_balance_value)

        # 使用 tc 自訂 emoji 作為油票圖標
        oil_ticket_emoji = "<:tc:1374853428823588955>"

        embed.description = (
            f"## 💰 您的油票餘額\n"
            f"> {oil_ticket_emoji} **可用油票:** {available_tickets} 張\n\n"
            f"## 📋 商品列表"
        )

        # 添加商品列表
        if not items:
            embed.add_field(
                name="❌ 暫無商品", value="此分類暫無可兌換的商品。", inline=False
            )
        else:
            for item in items:
                # 添加商品序號和更美觀的格式
                embed.add_field(
                    name=f"{item.emoji} **{item.display_name}**",
                    value=(
                        f"<:ReplyCont:1357534065841930290> **描述:** {item.description}\n"
                        f"<:Reply:1357534074830590143> **價格:** {item.cost_oil_tickets} {oil_ticket_emoji}"
                    ),
                    inline=False,
                )

        # 添加時間戳
        embed.timestamp = discord.utils.utcnow()

        return embed

    def _build_progress_bar(self, percentage: int) -> str:
        """
        建立一個文本進度條。

        Args:
            percentage: 進度百分比 (0-100)。

        Returns:
            進度條字符串。
        """
        full_blocks = percentage // 10
        empty_blocks = 10 - full_blocks
        return f"[{'▇' * full_blocks}{'-' * empty_blocks}]"


# 假設 OilTicketBalance 模型，如果實際路徑不同，請調整
# from path.to.your.models import OilTicketBalance

# # 假設 ShopItemDefinition 模型，如果實際路徑不同，請調整
# # from path.to.your.shop_service import ShopItemDefinition
