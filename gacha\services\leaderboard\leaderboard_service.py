import asyncpg
import asyncio
from typing import Dict, List, Tuple, Optional, Any, Callable
import discord
from database.postgresql.async_manager import AsyncPgManager
from gacha.repositories.leaderboard.leaderboard_repository import LeaderboardRepository
from gacha.models.models import Card
from utils.logger import logger
from gacha.constants import RarityLevel

class LeaderboardService:
    """處理排行榜相關邏輯的服務類 (Asyncpg 版本)"""

    def __init__(self, bot, pool: Optional[asyncpg.Pool]=None):
        """初始化排行榜服務 (Asyncpg 版本)

        參數:
            bot: Discord機器人實例
            pool: asyncpg 連接池實例 (可選)
        """
        from gacha.app_config import config_service
        
        # 設置數據庫連接池
        if pool is None:
            try:
                self.pool = AsyncPgManager.get_pool()
            except RuntimeError as e:
                logger.error('無法初始化 LeaderboardService：asyncpg 連接池未提供也無法從管理器獲取。')
                raise e
        else:
            self.pool = pool
        
        # 設置其他屬性
        self.bot = bot
        self.leaderboard_repo = LeaderboardRepository(self.pool)
        self.items_per_page = config_service.get_config('gacha_core_settings.leaderboard_items_per_page')


        asyncio.create_task(self._preheat_cache())

    async def _preheat_cache(self):
        """預熱排行榜緩存"""
        try:
            if hasattr(self.leaderboard_repo, 'preheat_rankings') and callable(getattr(self.leaderboard_repo, 'preheat_rankings')):
                await self.leaderboard_repo.preheat_rankings()
            else:
                logger.warning("'self.leaderboard_repo' (type: %s) 可能缺少 'preheat_rankings' 方法或該方法不可調用。", type(self.leaderboard_repo))
        except Exception as e:
            logger.error('排行榜緩存預熱失敗: %s', str(e), exc_info=True)

    async def get_leaderboard(self, leaderboard_type: str, page: int=1, asset_symbol: Optional[str]=None) -> Tuple[List[Dict[str, Any]], int, int]:
        """(Async) 獲取指定類型的排行榜數據"""
        offset = (page - 1) * self.items_per_page
        results_list, total_users_for_this_query = await self.leaderboard_repo.get_leaderboard(leaderboard_type=leaderboard_type, limit=self.items_per_page, offset=offset, asset_symbol=asset_symbol)
        total_pages = (total_users_for_this_query + self.items_per_page - 1) // self.items_per_page if total_users_for_this_query > 0 else 0
        page = max(1, min(page, total_pages)) if total_pages > 0 else 1
        current_offset_for_rank = (page - 1) * self.items_per_page
        if leaderboard_type == 'collection_unique':
            logger.debug('[LeaderboardService.get_leaderboard] Raw data for collection_unique (page %s): %s', page, results_list)
        elif leaderboard_type == 'stock_holding':
            logger.debug('[LeaderboardService.get_leaderboard] Raw data for stock_holding (asset: %s, page %s): %s', asset_symbol, page, results_list)
        for i, entry in enumerate(results_list):
            entry['rank'] = current_offset_for_rank + i + 1
            await self._add_discord_username(entry)
        return (results_list, total_pages, total_users_for_this_query)

    async def _add_discord_username(self, entry: Dict[str, Any]) -> None:
        """為排行榜項添加Discord用戶名稱 (保持 async)"""
        user_id = entry.get('user_id')
        user_name = entry.get('user_name')
        if user_id and user_name == str(user_id):
            try:
                user = self.bot.get_user(int(user_id))
                if user:
                    entry['user_name'] = user.display_name
            except ValueError:
                logger.error('Invalid user_id format in leaderboard entry: %s', user_id)
            except Exception as e:
                logger.error('Error fetching Discord username for %s: %s', user_id, e, exc_info=True)

    async def search_player_in_leaderboard(self, leaderboard_type: str, player_name: str, asset_symbol: Optional[str]=None) -> Optional[Dict[str, Any]]:
        """(Async) 在指定類型的排行榜中搜索玩家"""
        result = await self.leaderboard_repo.search_player_by_name(leaderboard_type, player_name, asset_symbol)
        if result:
            await self._add_discord_username(result)
        return result

    async def get_all_market_assets(self) -> List[Dict[str, str]]:
        """(Async) 獲取所有市場上可交易資產的列表 (symbol 和 name)"""
        query = f'SELECT asset_symbol, asset_name FROM {self.leaderboard_repo.virtual_assets_table} ORDER BY asset_name'
        try:
            records = await self.leaderboard_repo._fetch(query)
            return [{'symbol': r['asset_symbol'], 'name': r['asset_name']} for r in records]
        except Exception as e:
            logger.error('獲取市場資產列表失敗: %s', e, exc_info=True)
            return []

    def format_leaderboard_entry(self, entry: Dict[str, Any], leaderboard_type: str) -> Dict[str, str]:
        """統一格式化排行榜項目"""
        name = f"#{entry.get('rank', '?')} {entry.get('user_name', '未知用戶')}"
        value = '數據格式錯誤'
        try:
            if leaderboard_type == 'rarity':
                value = LeaderboardService._format_rarity_entry(entry)
            elif leaderboard_type == 'completion':
                percentage = entry.get('completion_percentage', 0)
                value = f"完成度: {entry.get('collected_cards', 0)}/{entry.get('total_cards', 0)} ({percentage:.2f}%)"
            elif leaderboard_type == 'draws':
                value = f"抽卡次數: {entry.get('total_draws', 0)} | 油幣餘額: {entry.get('oil_balance', 0)}"
            elif leaderboard_type == 'oil':
                value = f"<:oil1:1368446294594424872> 餘額:`{entry.get('oil_balance', 0)}` | 抽卡次數: {entry.get('total_draws', 0)}"
            elif leaderboard_type == 'collection_unique':
                value = LeaderboardService._format_collection_entry(entry)
            elif leaderboard_type == 'portfolio_value':
                total_value = entry.get('total_portfolio_value', 0)
                value = f'總資產價值: <:oil1:1368446294594424872> {total_value:,.2f}'
            elif leaderboard_type == 'stock_holding':
                stock_quantity = entry.get('stock_quantity', 0)
                asset_name = entry.get('asset_name', '未知股票')
                value = f'{asset_name} 持有: {stock_quantity} 股'
            elif leaderboard_type == 'trade_volume':
                total_volume = entry.get('total_trade_volume', 0)
                value = f'總交易額: <:oil1:1368446294594424872> {total_volume:,.2f}'
            elif leaderboard_type == 'trade_count':
                total_trades = entry.get('total_trade_count', 0)
                value = f'總交易次數: {total_trades} 次'
            else:
                value = '未知的排行榜類型'
        except Exception as e:
            logger.error('格式化排行榜條目時出錯 (type=%s): %s', leaderboard_type, e, exc_info=True)
            value = f'格式化錯誤: {e}'
        return {'name': name, 'value': value}

    def get_leaderboard_config(self) -> Dict[str, Any]:
        """獲取所有排行榜的配置"""
        from gacha.app_config import config_service
        return config_service.get_config('gacha_core_settings.leaderboard_config')

    @staticmethod
    def _calculate_percentage(value: int, total: int) -> float:
        """計算百分比，避免除以零錯誤"""
        if not total:
            return 0.0
        value_float = float(value)
        total_float = float(total)
        return round(value_float * 100.0 / total_float, 1)

    @staticmethod
    def _format_collection_entry(entry):
        """格式化收集排行榜的條目，動態顯示所有卡池"""
        from gacha.app_config import config_service
        result = f"獨立卡片: {entry.get('total_unique_cards', 0)}"
        try:
            pool_parts = []
            for key in entry:
                if key.endswith('_pool_total_cards') and (not key.startswith('total_')):
                    pool_type = key.replace('_pool_total_cards', '')
                    collected_key = f'{pool_type}_pool_unique_cards'
                    collected = int(entry.get(collected_key, 0))
                    total = int(entry.get(key, 0))
                    if total > 0:
                        original_pool_type = pool_type.replace('_', '-')
                        pool_display_name = config_service.get_pool_type_names().get(original_pool_type, original_pool_type.capitalize())
                        pool_percent = LeaderboardService._calculate_percentage(collected, total)
                        pool_first_char = pool_display_name[0] if pool_display_name else original_pool_type[0]
                        pool_parts.append(f'({pool_first_char}{pool_percent}%)')
            pool_parts.sort()
            if pool_parts:
                pool_percentages = '<:ReplyCont:1357534065841930290>' + ''.join(pool_parts)
            else:
                pool_percentages = ''
            merged_rarities: Dict[int, Dict[str, int]] = {}
            rarity_fields = []
            for key in entry.keys():
                if key.startswith('rarity_') and key.endswith('_unique_cards'):
                    try:
                        rarity_num_str = key.split('_')[1]
                        rarity_num = int(rarity_num_str)
                        collected = entry.get(key, 0)
                        total_key = f'rarity_{rarity_num_str}_total_cards'
                        total = entry.get(total_key, 0)
                        rarity_fields.append((rarity_num, collected, total))
                    except (IndexError, ValueError) as e_parse:
                        logger.warning("無法從鍵 '%s' 解析數字稀有度: %s", key, e_parse)
                        continue
            for rarity, collected, total in rarity_fields:
                if total > 0:
                    if rarity not in merged_rarities:
                        merged_rarities[rarity] = {'collected': 0, 'total': 0}
                    merged_rarities[rarity]['collected'] += collected
                    merged_rarities[rarity]['total'] += total
            sorted_rarities = sorted([(r, d['collected'], d['total']) for r, d in merged_rarities.items()], key=lambda x: x[0], reverse=True)
            from gacha.views import utils as view_utils
            rarity_strings = []
            for r_val, c_val, t_val in sorted_rarities:
                rarity_enum = RarityLevel(r_val) if isinstance(r_val, int) else None
                emoji = view_utils.get_rarity_display_code(rarity_enum) if rarity_enum else '❓'
                formatted_str = f'{emoji} {c_val}/{t_val}'
                rarity_strings.append(formatted_str)
            rarity_lines = []
            for i in range(0, len(rarity_strings), 4):
                chunk = rarity_strings[i:i + 4]
                line_prefix = '<:Reply:1357534074830590143>' if i == len(rarity_strings) - len(chunk) else '<:ReplyCont:1357534065841930290>'
                rarity_lines.append(f'{line_prefix}' + ' | '.join(chunk))
            rarity_display = '\n'.join(rarity_lines)
            final_output = f'{pool_percentages}\n{rarity_display}'
            return final_output
        except Exception as e:
            logger.error('Error formatting collection entry: %s', e, exc_info=True)
            return f'格式化錯誤: {e}'

    @staticmethod
    def _format_rarity_entry(entry):
        """格式化稀有度排行榜條目 (使用數字稀有度鍵)，只顯示有卡片的稀有度"""
        rarities_ordered = [7, 6, 5, 4, 3, 2, 1]
        rarity_parts = []
        for rarity_num in rarities_ordered:
            count_key = f'rarity_{rarity_num}_count'
            count = int(entry.get(count_key, 0))
            if count > 0:
                from gacha.views import utils as view_utils
                rarity_enum = RarityLevel(rarity_num) if isinstance(rarity_num, int) else None
                emoji = view_utils.get_rarity_display_code(rarity_enum) if rarity_enum else '❓'
                rarity_parts.append(f'{emoji} x{count}')
        return ' | '.join(rarity_parts) if rarity_parts else '尚未收集任何卡片'