"""
評分歷史管理模塊 - 負責存儲和檢索用戶評分歷史
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import asyncio
import time


# 設置日誌
logger = logging.getLogger("RatingHistory")

# 定義用戶歷史記錄的基礎目錄
USER_HISTORY_DIR = os.path.join(os.path.dirname(__file__), "user_history")

class RatingHistory:
    """處理用戶評分歷史的類"""
    
    def __init__(self): # 移除 history_file_path 參數
        """
        初始化評分歷史管理器
        """
        # 不再預加載所有歷史記錄
        # self.history_file = history_file_path or os.path.join(os.path.dirname(__file__), "data", "history.json")
        # self.history_data = self._load_history()
        self._ensure_user_history_dir() # 更改為確保 user_history 目錄
        
    def _ensure_user_history_dir(self): # 重命名並修改
        """確保用戶歷史記錄目錄存在"""
        # data_dir = os.path.dirname(self.history_file) # 移除對 self.history_file 的依賴
        if not os.path.exists(USER_HISTORY_DIR):
            os.makedirs(USER_HISTORY_DIR, exist_ok=True)
            logger.info(f"創建用戶歷史記錄目錄: {USER_HISTORY_DIR}")

    def _get_user_history_file_path(self, user_id: str) -> str:
        """獲取特定用戶的歷史文件路徑"""
        return os.path.join(USER_HISTORY_DIR, f"{user_id}.json")

    def _load_history(self, user_id: str) -> List[Dict[str, Any]]: # 修改簽名並調整邏輯
        """
        從文件加載特定用戶的歷史記錄
        
        參數:
            user_id (str): 用戶ID
            
        返回:
            List[Dict[str, Any]]: 該用戶的評分記錄列表
        """
        user_file = self._get_user_history_file_path(user_id)
        try:
            if os.path.exists(user_file):
                with open(user_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 徹底移除舊格式兼容邏輯
                    if isinstance(data, list):
                        return data # 新的每用戶文件格式
                    else:
                        logger.warning(f"用戶 {user_id} 的歷史文件 {user_file} 格式不正確（期望列表，實際為 {type(data)}），將返回空列表。")
                        # 可選：如果文件格式錯誤，可以考慮將其重命名或刪除，以避免後續問題
                        # os.rename(user_file, user_file + ".corrupted") 
                        return []
            else:
                # logger.info(f"用戶 {user_id} 的歷史記錄文件不存在，將返回空列表: {user_file}")
                return []
        except json.JSONDecodeError as e:
            logger.error(f"解析用戶 {user_id} 的歷史記錄文件 {user_file} 時出錯: {str(e)}，將返回空列表。")
            # 可選：處理損壞的JSON文件
            # os.rename(user_file, user_file + ".json_decode_error")
            return []
        except Exception as e:
            logger.error(f"加載用戶 {user_id} 的歷史記錄時發生未知錯誤 ({user_file}): {str(e)}")
            return []
    
    def _save_history(self, user_id: str, user_history_data: List[Dict[str, Any]]): # 修改簽名並調整邏輯
        """將特定用戶的歷史記錄保存到文件"""
        user_file = self._get_user_history_file_path(user_id)
        try:
            with open(user_file, 'w', encoding='utf-8') as f:
                json.dump(user_history_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存用戶 {user_id} 的歷史記錄時出錯 ({user_file}): {str(e)}")
    
    def add_rating(self, user_id: str, rating_result: Dict[str, Any]):
        """
        添加一條評分記錄
        
        參數:
            user_id (str): 用戶ID
            rating_result (Dict[str, Any]): 評分結果
        """
        user_history = self._load_history(user_id) # 加載特定用戶的歷史
        
        # 添加時間戳
        rating_with_timestamp = {
            **rating_result,
            "timestamp": time.time() 
        }
        
        # 添加到用戶歷史記錄
        user_history.append(rating_with_timestamp)
        
        # 限制每個用戶最多保存5條記錄
        if len(user_history) > 5:
            user_history = user_history[-5:]
        
        # 保存更新後的歷史記錄
        self._save_history(user_id, user_history) # 保存特定用戶的歷史
        logger.info(f"已為用戶 {user_id} 添加新的評分記錄到 {self._get_user_history_file_path(user_id)}")
    
    def get_user_history(self, user_id: str) -> List[Dict[str, Any]]:
        """
        獲取用戶的評分歷史
        
        參數:
            user_id (str): 用戶ID
            
        返回:
            List[Dict[str, Any]]: 評分記錄列表
        """
        return self._load_history(user_id) # 直接從特定用戶文件加載
    
    def clear_user_history(self, user_id: str) -> bool:
        """
        清除用戶的評分歷史
        
        參數:
            user_id (str): 用戶ID
            
        返回:
            bool: 是否成功清除
        """
        user_file = self._get_user_history_file_path(user_id)
        if os.path.exists(user_file):
            try:
                self._save_history(user_id, []) # 保存空列表以清除
                logger.info(f"已清除用戶 {user_id} 的評分歷史: {user_file}")
                return True
            except Exception as e:
                logger.error(f"清除用戶 {user_id} 的歷史記錄時出錯 ({user_file}): {str(e)}")
                return False
        else:
            logger.info(f"用戶 {user_id} 的歷史文件不存在，無需清除: {user_file}")
            return True # 或者 False，取決於期望行為，此處認為不存在即已清除
    
    def format_history_for_prompt(self, user_id: str) -> str:
        """
        將用戶歷史記錄格式化為提示詞
        
        參數:
            user_id (str): 用戶ID
            
        返回:
            str: 格式化的歷史記錄提示詞
        """
        history = self.get_user_history(user_id)
        
        if not history:
            return ""
        
        history_entries = []
        
        # 按時間排序，最新的排在前面
        # 確保 timestamp 存在且是數字
        sorted_history = sorted(
            [entry for entry in history if isinstance(entry.get("timestamp"), (int, float))],
            key=lambda x: x["timestamp"],
            reverse=True
        )
        
        # 只取最近的三條記錄
        recent_history = sorted_history[:3]
        
        for i, entry in enumerate(recent_history):
            # 格式化每條歷史記錄
            entry_str = (
                f"歷史評分 {i+1}:\\n"
                f"評分: {entry.get('score', 'N/A')}\\n"
                f"評價: {entry.get('review', '無評價')}\\n"
                f"建議: {entry.get('suggestion', '無建議')}\\n"
                f"結論: {entry.get('conclusion', '無結論')}\\n"
            )
            history_entries.append(entry_str)
        
        # 直接返回歷史記錄內容
        return "\\n\\n".join(history_entries)
    
    # def clear_cache(self): # 移除此方法
    #     """清除內存緩存"""
    #     self.history_data.clear()
    #     logger.info("已清除評分歷史緩存")
        
    async def save_rating(self, user_id: str, rating_result: Dict[str, Any]):
        """
        異步保存評分結果
        
        參數:
            user_id (str): 用戶ID
            rating_result (Dict[str, Any]): 評分結果
        """
        # 調用同步方法添加評分
        # 在實際應用中，IO密集型操作（如文件讀寫）應該異步執行或在線程池中執行
        # 為簡化，此處保持同步調用，但標記為異步方法以匹配 OutfitRater 中的調用
        # 如果需要真正的異步文件操作，可以使用 aiofiles 庫
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self.add_rating, user_id, rating_result)
        return True 