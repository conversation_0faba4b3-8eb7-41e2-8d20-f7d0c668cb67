# RPG 任務 11：Discord 整合 (Cogs 與 Views)

本文檔詳細列出了將 RPG 系統與 Discord Bot 集成的任務，包括創建命令處理 Cogs、用戶交互的 Views 以及信息展示的 Embeds。

**參考設計文檔：**
*   `RPG_01_System_Architecture.md` (II. 層次結構 - Presentation Layer)
*   `RPG_11_Battle_Presentation.md` (戰鬥展示流程和 Embed 設計)
*   `RPG_TASK_01_Project_Setup_and_Config_System.md` (已創建的 Cogs 和 Views 文件存根)
*   `RPG_TASK_09_Application_Services.md` (應用服務將被 Cogs 調用)

**目標位置：**
*   Cogs: `rpg_system/cogs/`
*   Embeds: `rpg_system/views/embeds/`
*   Views (discord.ui.View): 可能在 `rpg_system/views/` 或與 Cogs 緊密相關的位置。
*   Formatters: `rpg_system/views/formatters/`

## 1. `BattleCog`

在 `rpg_system/cogs/battle_cog.py` 中實現。處理與PVE戰鬥相關的 Discord 命令。

- [ ] **定義 `BattleCog` 類 (繼承 `commands.Cog`)：**
    - [ ] **依賴注入 (`__init__`)：**
        - [ ] `bot: commands.Bot`
        - [ ] `battle_coordinator_service: BattleCoordinatorService`
        - [ ] `config_loader: ConfigLoader` (用於獲取樓層名等顯示信息)
        - [ ] (可能需要 `BattleEmbedGenerator` - 見下文)
    - [ ] **PVE 相關命令：**
        - [ ] `/pve_start [floor_id: str]` 命令：
            *   調用 `battle_coordinator_service.prepare_pve_battle(ctx.author.id, floor_id)`。
            *   如果準備失敗，回復錯誤信息。
            *   如果成功，調用 `battle_coordinator_service.start_battle(battle_instance)`。
            *   創建 `BattleView` (包含行動按鈕) 和初始的戰鬥 Embed。
            *   發送包含 Embed 和 View 的消息。
            *   存儲活動的戰鬥會話 (e.g., `Dict[int, Battle]` or `Dict[int, BattleView]` keyed by `user_id` or `message_id` to prevent concurrent battles per user or manage multiple battles)。
        - [ ] `/pve_team_select` 命令 (如果允許戰前選隊伍，而非固定使用 `rpg_user_progress.current_team_formation`)。
        - [ ] `/pve_status` 命令 (查看當前 PVE 層數和進度)。

## 2. `SkillManagementCog`

在 `rpg_system/cogs/skill_management_cog.py` 中實現。處理卡牌技能裝備、全局技能學習/升級等命令。

- [ ] **定義 `SkillManagementCog` 類 (繼承 `commands.Cog`)：**
    - [ ] **依賴注入 (`__init__`)：**
        - [ ] `bot: commands.Bot`
        - [ ] `player_card_management_service: PlayerCardManagementService`
        - [ ] `player_skill_management_service: PlayerSkillManagementService`
        - [ ] `config_loader: ConfigLoader`
    - [ ] **卡牌技能管理命令：**
        - [ ] `/card_equip_active [collection_id: int] [skill_id: str] [slot: int]`
        - [ ] `/card_unequip_active [collection_id: int] [slot: int]`
        - [ ] `/card_equip_passive [collection_id: int] [skill_id: str] [slot_key: str]`
        - [ ] `/card_unequip_passive [collection_id: int] [slot_key: str]`
        - [ ] `/card_skills_show [collection_id: int]` (展示卡牌當前技能配置和可用槽位)
    - [ ] **全局技能管理命令：**
        - [ ] `/gskill_learn [skill_id: str] [type: Literal["active", "passive"]]`
        - [ ] `/gskill_upgrade [skill_id: str] [type: Literal["active", "passive"]]`
        - [ ] `/gskill_list [type: Optional[Literal["active", "passive"]]]` (展示已學習的全局技能及其等級/XP)

## 3.戰鬥界面 (`BattleView` 和 Embeds)

- [ ] **`BattleEmbedGenerator` (在 `rpg_system/views/embeds/battle_embeds.py`)**
    - [ ] 創建一個類或一組函數，負責根據 `Battle` 對象的當前狀態生成美觀的 Discord Embeds。
    - [ ] `generate_battle_state_embed(battle: Battle, config_loader: ConfigLoader, message_log: Optional[List[str]] = None) -> discord.Embed`:
        *   **內容 (參考 `RPG_11_Battle_Presentation.md`)：**
            *   回合數。
            *   玩家隊伍狀態 (HP, MP, 狀態效果圖標)。
            *   怪物隊伍狀態 (HP, MP, 狀態效果圖標)。
            *   (可選) 最近的戰鬥日誌消息 (`message_log`)。
            *   當前行動者提示。
            *   使用 Emoji 和 Markdown 增強可讀性。
    - [ ] `generate_battle_action_selection_embed(battle: Battle, acting_combatant: Combatant, config_loader: ConfigLoader) -> discord.Embed`:
        *   提示玩家選擇技能。
        *   列出 `acting_combatant` 可用的主動技能 (包括普攻)，顯示其 MP 消耗和冷卻狀態。
    - [ ] `generate_battle_target_selection_embed(battle: Battle, acting_combatant: Combatant, skill_id: str, config_loader: ConfigLoader) -> discord.Embed`:
        *   提示玩家選擇目標。
        *   根據技能的目標邏輯預選或高亮可能的目標。
    - [ ] `generate_battle_end_embed(battle: Battle, config_loader: ConfigLoader, battle_results: Dict[str, Any]) -> discord.Embed`:
        *   顯示戰鬥結果 (勝利/失敗/平局)。
        *   (如果勝利) 顯示獲得的獎勵和進度更新。
- [ ] **`BattleView` (在 `battle_cog.py` 或 `rpg_system/views/battle_view.py`)**
    - [ ] 創建一個繼承 `discord.ui.View` 的類。
    - [ ] `__init__(self, battle_instance: Battle, battle_cog_instance: BattleCog, user_id: int)`。
    - [ ] **包含按鈕：**
        - [ ] **技能按鈕：**
            *   為當前行動玩家的每個可用主動技能（包括普攻）創建按鈕 (`discord.ui.Button`)。
            *   按鈕標籤可以是技能名，`custom_id` 可以是 `skill_action:{skill_id}`。
            *   按鈕的回調 (`async def skill_button_callback(self, interaction: discord.Interaction, button: discord.ui.Button)`)：
                *   解析 `skill_id`。
                *   更新 Embed/View 以進入目標選擇階段，或如果技能是單目標/無目標，則直接處理。
                *   禁用舊的技能按鈕。
        - [ ] **目標按鈕 (動態添加或在目標選擇階段顯示)：**
            *   為每個潛在目標創建按鈕。
            *   按鈕標籤可以是目標名，`custom_id` 可以是 `target_selection:{combatant_instance_id}`。
            *   按鈕的回調：
                *   獲取選擇的 `skill_id` 和 `target_id`。
                *   調用 `battle_cog_instance.battle_coordinator_service.process_player_action(...)`。
                *   調用 `battle_cog_instance.battle_coordinator_service.advance_battle_turn(...)` 處理後續AI回合。
                *   獲取更新後的 `Battle` 狀態。
                *   生成新的戰鬥狀態 Embed。
                *   更新 View 上的按鈕 (為下一個玩家回合準備技能按鈕，或顯示戰鬥結束信息)。
                *   編輯原始消息 (`interaction.message.edit(...)`)。
        - [ ] (可選) **放棄戰鬥按鈕。**
    - [ ] **超時處理 (`on_timeout`)：** 自動判負或結束交互。
    - [ ] **狀態管理：** View 需要知道當前是技能選擇階段還是目標選擇階段，以便正確顯示/禁用按鈕。

## 4. 信息格式化 (`Formatters`)

在 `rpg_system/views/formatters/` 目錄下創建格式化工具函數/類。

- [ ] **`skill_formatters.py`:**
    - [ ] `format_skill_for_display(skill_instance: SkillInstance, skill_config: Dict, config_loader: ConfigLoader) -> str`:
        *   生成技能的文本描述，包括等級、MP消耗、冷卻、效果描述等。
- [ ] **`combatant_formatters.py`:**
    - [ ] `format_combatant_status_short(combatant: Combatant, config_loader: ConfigLoader) -> str`:
        *   生成戰鬥單位狀態的簡短文本 (e.g., "名稱 HP: X/Y MP: A/B [狀態圖標]")。
- [ ] **`reward_formatters.py`:**
    - [ ] `format_rewards(rewards_list: List[RewardItem], config_loader: ConfigLoader) -> str`:
        *   將獎勵列表格式化為用戶友好的文本。

## 5. 錯誤處理與用戶反饋

- [ ] 在 Cogs 中，對服務層返回的錯誤進行捕獲，並向用戶發送易於理解的錯誤提示 (e.g., ephemeral messages)。
- [ ] 對於耗時操作 (如戰鬥準備)，可以考慮使用 `ctx.defer()` 或發送一個"處理中"的初始消息。

## 6. Cog 加載

- [ ] 確保在主 bot 文件中有邏輯來加載這些 RPG 相關的 Cogs。
    ```python
    # In your main bot file
    async def setup_hook():
        # ... other cogs
        await bot.load_extension("rpg_system.cogs.battle_cog")
        await bot.load_extension("rpg_system.cogs.skill_management_cog")
        # ...
    ```

## 7. 單元測試與集成測試 (重點)

- [ ] **測試 Cogs：**
    - [ ] Mock 依賴的服務。
    - [ ] 使用 `discord.ext.test` 或類似庫 (如果適用) 或手動 mock `Context` 和 `Interaction` 對象。
    - [ ] 驗證命令是否能被正確調用。
    - [ ] 驗證服務方法是否被正確參數調用。
    - [ ] 驗證是否發送了預期的 Embed 和 View (檢查其內容和組件)。
- [ ] **測試 Views：**
    - [ ] Mock `Interaction`。
    - [ ] 測試按鈕回調邏輯。
    - [ ] 驗證按鈕點擊後服務調用和消息編輯是否正確。
    - [ ] 測試 View 的狀態轉換。
- [ ] **測試 Embed 生成器和 Formatters：**
    - [ ] 提供 mock數據，驗證生成的 Embed 內容和格式化字符串是否符合預期。
- [ ] **手動集成測試：** 在實際的 Discord Bot 環境中運行和測試命令流，確保交互符合預期。

---
完成所有上述任務後，請勾選並更新 `RPG_IMPLEMENTATION_PLAN.md` 中的相應條目。 