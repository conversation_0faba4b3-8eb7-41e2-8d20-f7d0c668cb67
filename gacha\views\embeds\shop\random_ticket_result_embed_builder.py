import discord
from typing import List, Optional, TYPE_CHECKING, TypedDict
from gacha.models.shop_models import ShopItemDefinition
from utils.logger import logger
from gacha.constants import RarityLevel
from gacha.views import utils as view_utils
from gacha.app_config import config_service
if TYPE_CHECKING:
    from gacha.models.models import Card
ENCYCLOPEDIA_ICON_URL = 'https://cdn.discordapp.com/attachments/1079179758069366945/1364902124588109835/151906-20250319-232405-000.gif'

class _CardDisplayInfo(TypedDict):
    name: str
    card_id: Optional[int]
    series: str
    image_url: Optional[str]
    rarity_enum: Optional[RarityLevel]
    pool_type: str

def _extract_card_display_info(card: 'Card', ticket_definition_pool_type: Optional[str]=None) -> _CardDisplayInfo:
    """Helper to extract and process card details for display."""
    card_id_val = getattr(card, 'card_id', None)
    rarity_attribute = getattr(card, 'rarity', None)
    rarity_enum_val: Optional[RarityLevel] = None
    if isinstance(rarity_attribute, RarityLevel):
        rarity_enum_val = rarity_attribute
    elif rarity_attribute is not None:
        try:
            rarity_enum_val = RarityLevel(int(rarity_attribute))
        except (ValueError, TypeError):
            logger.warning("無法將卡片稀有度值 '%s' 轉換為 RarityLevel Enum。卡片ID: %s", rarity_attribute, card_id_val)
    pool_type_val = getattr(card, 'pool_type', None)
    if pool_type_val is None:
        pool_type_val = ticket_definition_pool_type
    if pool_type_val is None:
        pool_type_val = 'main'
    return _CardDisplayInfo(name=getattr(card, 'name', '未知卡片'), card_id=card_id_val, series=getattr(card, 'series', '未知系列'), image_url=getattr(card, 'image_url', None), rarity_enum=rarity_enum_val, pool_type=pool_type_val)

def build_random_ticket_result_embed(interaction: discord.Interaction, ticket_name: str, quantity_used: int, displayed_card: Optional['Card'], current_page: int, total_pages: int, total_cards_drawn: int, ticket_definition_pool_type: Optional[str]=None) -> discord.Embed:
    """
    建立隨機券兌換結果的 Embed。
    支持分頁顯示，每頁一張卡片。
    """
    user_name = interaction.user.display_name
    author_text = f'{user_name} 的【{ticket_name}】'
    embed_color = discord.Color.light_grey()
    card_info: Optional[_CardDisplayInfo] = None
    if displayed_card:
        card_info = _extract_card_display_info(displayed_card, ticket_definition_pool_type)
        if card_info['rarity_enum']:
            embed_color = view_utils.get_rarity_color(card_info['rarity_enum'], card_info['pool_type'])
    embed = discord.Embed(title='隨機券兌換結果', color=embed_color)
    embed.set_author(name=author_text, icon_url=interaction.user.display_avatar.url or ENCYCLOPEDIA_ICON_URL)
    description_lines = [f'成功兌換 **{ticket_name} x {quantity_used}** 張。']
    if total_cards_drawn > 0:
        if total_cards_drawn == 1:
            description_lines.append(f'您使用 {quantity_used} 張 {ticket_name} 兌換了以下卡片：')
        else:
            description_lines.append(f'您使用 {quantity_used} 張 {ticket_name} 兌換了 **{total_cards_drawn}** 張卡片 (第 {current_page}/{total_pages} 張)：')
    else:
        description_lines.append(f'您使用了 {quantity_used} 張 {ticket_name}，但本次未獲得任何卡片。')
    current_embed_description = '\n'.join(description_lines)
    if not card_info and total_cards_drawn > 0:
        embed.description = current_embed_description
        embed.add_field(name='錯誤', value='無法顯示卡片資訊。', inline=False)
    elif not card_info and total_cards_drawn == 0:
        embed.description = current_embed_description
    elif card_info:
        rarity_emoji = '❓'
        display_rarity_name = '未知'
        if card_info['rarity_enum']:
            rarity_emoji = view_utils.get_encyclopedia_rarity_emoji(card_info['rarity_enum'], card_info['pool_type'])
            display_rarity_name = view_utils.get_user_friendly_rarity_name(card_info['rarity_enum'])
        pool_display_text = config_service.get_config('gacha_core_settings.pool_type_prefixes').get(card_info['pool_type'], '')
        temp_card_name = card_info['name']
        temp_card_series = card_info['series']
        card_detail_lines_for_desc = [f'{rarity_emoji} **{temp_card_name}**', f'<:ReplyCont:1357534065841930290> *{temp_card_series}*', f'<:ReplyCont:1357534065841930290>{display_rarity_name} | {pool_display_text}']
        embed.description = current_embed_description + '\n\n' + '\n'.join(card_detail_lines_for_desc)
        if card_info['image_url']:
            embed.set_image(url=card_info['image_url'])
        if card_info['rarity_enum']:
            rarity_images_config = config_service.get_rarity_images_url()
            all_pools_images = rarity_images_config.get('all_pools', {})
            temp_rarity_enum_value = card_info['rarity_enum'].value
            thumbnail_url = all_pools_images.get(temp_rarity_enum_value)
            if thumbnail_url is None:
                thumbnail_url = all_pools_images.get(str(temp_rarity_enum_value))
            if thumbnail_url:
                embed.set_thumbnail(url=thumbnail_url)
            else:
                temp_rarity_enum_name = card_info['rarity_enum'].name
                temp_pool_type = card_info['pool_type']
                temp_card_id = card_info['card_id']
                logger.warning("無法獲取稀有度 '%s' (值: %s, 池: %s) 的縮圖 URL。卡片ID: %s", temp_rarity_enum_name, temp_rarity_enum_value, temp_pool_type, temp_card_id)
    else:
        embed.description = current_embed_description
    footer_parts = []
    if total_cards_drawn > 1:
        footer_parts.append(f'第 {current_page} 頁 / 共 {total_pages} 頁')
    if card_info and card_info['card_id'] is not None:
        footer_parts.append(f"Card ID: {card_info['card_id']}")
    if not footer_parts and total_cards_drawn > 0:
        footer_parts.append('恭喜您！')
    elif not footer_parts and total_cards_drawn == 0:
        footer_parts.append('下次好運！')
    if footer_parts:
        embed.set_footer(text=' • '.join(footer_parts))
    elif not embed.footer:
        embed.set_footer(text='隨機券兌換')
    return embed