import discord
from typing import List, Optional, TYPE_CHECKING
from gacha.views.collection.collection_view.base_pagination import BasePaginationView, BasePaginationJumpModal
from gacha.views.embeds.shop.random_ticket_result_embed_builder import build_random_ticket_result_embed
from utils.logger import logger
if TYPE_CHECKING:
    from gacha.models.models import Card

class RandomTicketResultView(BasePaginationView):
    """
    顯示隨機券兌換結果的 View, 使用 BasePaginationView 實現分頁。
    """

    def __init__(self, original_interaction: discord.Interaction, ticket_name: str, quantity_used: int, drawn_cards: List['Card'], ticket_definition_pool_type: Optional[str]=None, timeout: Optional[float]=180.0):
        self.original_interaction = original_interaction
        self.user_id = original_interaction.user.id
        self.ticket_name = ticket_name
        self.quantity_used = quantity_used
        self.drawn_cards = drawn_cards
        self.ticket_definition_pool_type = ticket_definition_pool_type
        self.items_per_page = 1
        self.total_items = len(self.drawn_cards)
        total_pages = self.total_items if self.total_items > 0 else 1
        super().__init__(user=original_interaction.user, current_page=1, total_pages=total_pages, timeout=timeout)

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """更新視圖到指定頁面並編輯原始消息。"""
        self.current_page = page
        embed = self.get_current_page_embed()
        try:
            if self.message:
                await interaction.edit_original_response(embed=embed, view=self)
            else:
                await interaction.edit_original_response(embed=embed, view=self)
        except discord.HTTPException as e:
            logger.error('RandomTicketResultView: Failed to edit message: %s', e, exc_info=True)
            if isinstance(e, discord.NotFound):
                logger.warning('Original message for RandomTicketResultView not found, possibly deleted.')
            elif isinstance(e, discord.InteractionResponded) and (not interaction.response.is_done()):
                logger.warning('InteractionResponded error during edit_original_response. This might indicate a logic issue or unexpected state.')

    def get_current_page_embed(self) -> discord.Embed:
        """為當前頁面生成 discord.Embed。"""
        card_index = self.current_page - 1
        current_card_to_display = self.drawn_cards[card_index] if 0 <= card_index < self.total_items else None
        return build_random_ticket_result_embed(interaction=self.original_interaction, ticket_name=self.ticket_name, quantity_used=self.quantity_used, displayed_card=current_card_to_display, current_page=self.current_page, total_pages=self.total_pages, total_cards_drawn=self.total_items, ticket_definition_pool_type=self.ticket_definition_pool_type)

    async def send_initial_message(self, interaction: discord.Interaction):
        """發送初始消息。這個方法會被外部調用來啟動視圖。"""
        embed = self.get_current_page_embed()
        return (embed, self)

    async def prepare_initial_message_payload(self, interaction: Optional[discord.Interaction]=None) -> tuple[discord.Embed, 'RandomTicketResultView']:
        """準備初始消息的 Embed 和 View 實例。"""
        embed = self.get_current_page_embed()
        return (embed, self)

    async def on_timeout(self):
        logger.info('RandomTicketResultView for user %s (original interaction: %s) timed out.', self.user_id, self.original_interaction.id)
        for item in self.children:
            if isinstance(item, discord.ui.Button) or isinstance(item, discord.ui.Select):
                item.disabled = True
        if self.message:
            try:
                await self.message.edit(content=f'{self.message.content}\n*（此結果顯示已超時）*', view=self)
            except discord.HTTPException as e:
                logger.warning('Failed to edit message on RandomTicketResultView timeout: %s', e)
        self.stop()