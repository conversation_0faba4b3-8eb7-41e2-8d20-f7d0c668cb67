"""
GIF配置模塊
為不同的GIF提供配置參數，定義頭像位置、大小和動畫效果
"""

# GIF配置字典 - 針對不同GIF的特殊配置
GIF_CONFIGS = {
    "frog.gif": {
        "avatar_position": "left_bottom",  # 左下方
        "avatar_size": (70, 70),           # 頭像尺寸
        "offset_x": 5,                    # X軸位置偏移 (從左邊緣開始)
        "offset_y": -10,                   # Y軸位置偏移 (相對於底部)
        "animation": {                     # 動畫效果
            "x_amplitude": 5,              # X軸抖動幅度
            "y_amplitude": 3,              # Y軸抖動幅度
            "phase_shift": 0.33,           # 相位差 (0-1)
            "frequency": 1.0               # 震動頻率
        }
    },
    "uncle_abe.gif": {
        "avatar_position": "right_bottom", # 右下方
        "avatar_size": (120, 120),         # 更大的頭像尺寸
        "offset_x": 140,                   # X軸位置偏移 (從右邊緣開始)
        "offset_y": -20,                   # Y軸位置偏移 (相對於底部)
        "animation": {                     # 動畫效果
            "x_amplitude": 12,             # X軸抖動幅度 (提高了幅度)
            "y_amplitude": 10,             # Y軸抖動幅度 (提高了幅度)
            "phase_shift": 0.25,           # 相位差 (0-1) (調整為更快的節奏)
            "frequency": 2.0               # 新增頻率參數，用於控制震動速度
        }
    },
    "punching.gif": {
        "avatar_position": "center",       # 中間位置
        "avatar_size": (210, 210),         # 頭像尺寸
        "offset_x": 0,                     # X軸位置偏移
        "offset_y": 40,                    # Y軸位置偏移 (相對於中心)
        "animation": {                     # 動畫效果
            "x_amplitude": 0,             # X軸抖動幅度
            "y_amplitude": 0,             # Y軸抖動幅度
            "phase_shift": 0,            # 相位差 (0-1)
            "frequency": 0               # 震動頻率
        }
    },
    "kick_trash.gif": {
        "gif_source_type": "frames_sequence",
        "source_path_pattern": "image_processing/gifs/kick/frame_{:02d}_delay-0.05s.gif",
        "total_source_frames": 30,
        "avatar_size": (64, 64),
        "output_gif_size": (211, 254), # 最終輸出GIF的尺寸 - 已修改為原始踢擊動畫尺寸
        "frame_duration": 50, # 每一影格的持續時間 (毫秒) - 已修改

        # --- 階段一：踢擊動畫設定 ---
        "kick_phase": {
            "kick_trigger_frame": 17, # 索引從0開始，所以第18幀是索引17
            "avatar_initial_offset_kick": (110, 230), # 頭像在腳邊的 (x,y) 偏移 (相對於GIF左上角，需微調) - 已修改
            "avatar_deformation_scale": (1, 1), # 被踢中時的形變 - 增強衝擊感
            "kick_fly_frames": 5, # 踢飛動作在原場景持續的影格數
            "kick_fly_velocity": (40, -40), # 頭像被踢飛的初始每幀 (dx, dy) 速度 - 大幅提高
            "kick_fly_rotation_speed": 45, # 被踢飛時的每幀旋轉角度 (可選) - 大幅提高
        },

        # --- 階段二：飛入垃圾桶設定 ---
        "trash_phase": {
            "trash_scene_frames": 20, # 飛入垃圾桶動畫的影格數 - 拉長時間
            "trash_bin_opening_rect": (100, 80, 200, 150), # (x1,y1,x2,y2) 垃圾桶開口座標
            "avatar_final_pos_in_trash_offset": (-25, 45), # 頭像落點相對於開口中心的偏移 - Y掉更深
            "avatar_entry_style": "arc_top_down",
            "arc_start_y_offset_above_bin_top": 60,
            "arc_peak_y_offset_from_linear": 40,
            "avatar_scale_in_trash": 0.55 # 垃圾桶階段頭像縮放比例
        }
    }
    # 可以添加更多GIF配置
    # "其他GIF.gif": { ... }ˇㄧ
}

# 默認配置
DEFAULT_GIF_CONFIG = {
    "avatar_position": "center_bottom",    # 底部中央
    "avatar_size": (80, 80),               # 頭像尺寸
    "offset_x": 0,                         # X軸位置偏移
    "offset_y": -10,                       # Y軸位置偏移 (相對於底部)
    "animation": {                         # 動畫效果
        "x_amplitude": 0,                  # X軸抖動幅度
        "y_amplitude": 0,                  # Y軸抖動幅度
        "phase_shift": 0,                  # 相位差 (0-1)
        "frequency": 1.0                   # 震動頻率
    }
}

def get_gif_config(gif_path):
    """根據GIF路徑獲取配置"""
    import os
    gif_name = os.path.basename(gif_path)
    return GIF_CONFIGS.get(gif_name, DEFAULT_GIF_CONFIG)

def register_gif_config(gif_name, config):
    """註冊新的GIF配置"""
    GIF_CONFIGS[gif_name] = config
    return True

def list_available_gifs():
    """列出所有已配置的GIF名稱"""
    return list(GIF_CONFIGS.keys()) 