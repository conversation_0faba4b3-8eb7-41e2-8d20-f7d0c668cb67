"""
Profile Settings View - 處理檔案設定相關的 Discord UI 互動
"""
import discord
from discord import ui
from typing import Optional

from utils.logger import logger
from gacha.services.core.profile_service import ProfileService
from gacha.exceptions import (
    ProfileCardSetError, ProfileBackgroundError, UserDoesNotOwnCardError,
    UserNotFoundError, DatabaseOperationError
)


class SetMainCardModal(ui.Modal, title="設定主展示卡片"):
    """設定主展示卡片的模態框"""
    
    card_id = ui.TextInput(
        label="卡片ID", 
        placeholder="請輸入您擁有的卡片ID",
        required=True,
        min_length=1,
        max_length=20
    )
    
    def __init__(self, view: "ProfileSettingsView"):
        super().__init__()
        self.view = view
    
    async def on_submit(self, interaction: discord.Interaction):
        """提交時的處理邏輯"""
        await interaction.response.defer(ephemeral=True)
        
        try:
            card_id_input = self.card_id.value.strip()
            
            # 校驗卡片ID是否為數字
            try:
                card_id = int(card_id_input)
            except ValueError:
                await interaction.followup.send("❌ 卡片ID必須是有效的數字。", ephemeral=True)
                return
                
            # 調用設定主卡片的服務
            await self.view.profile_service.set_main_card(self.view.user_id, card_id)
            
            # 設定成功，清除緩存
            await self.view.profile_service.invalidate_profile_cache(self.view.user_id)
            
            # 更新原始嵌入消息
            await self.view.update_settings_embed(interaction)
            
        except UserDoesNotOwnCardError as e:
            await interaction.followup.send(f"❌ 設定主卡片失敗: {e}", ephemeral=True)
        except ProfileCardSetError as e:
            await interaction.followup.send(f"❌ 設定主卡片失敗: {e}", ephemeral=True)
        except Exception as e:
            logger.error(f"設定主卡片時發生錯誤: {e}", exc_info=True)
            await interaction.followup.send("❌ 設定主卡片時發生錯誤，請稍後再試。", ephemeral=True)


class SetSubCardsModal(ui.Modal, title="設定副展示卡片"):
    """設定副展示卡片的模態框，使用多個獨立輸入框"""
    
    sub_card1 = ui.TextInput(
        label="副卡片槽位 1",
        placeholder="請輸入卡片ID",
        required=False,
        max_length=20
    )
    
    sub_card2 = ui.TextInput(
        label="副卡片槽位 2",
        placeholder="請輸入卡片ID",
        required=False,
        max_length=20
    )
    
    sub_card3 = ui.TextInput(
        label="副卡片槽位 3",
        placeholder="請輸入卡片ID",
        required=False,
        max_length=20
    )
    
    sub_card4 = ui.TextInput(
        label="副卡片槽位 4",
        placeholder="請輸入卡片ID",
        required=False,
        max_length=20
    )
    
    def __init__(self, view: "ProfileSettingsView"):
        super().__init__()
        self.view = view
    
    async def on_submit(self, interaction: discord.Interaction):
        """提交時的處理邏輯"""
        await interaction.response.defer(ephemeral=True)
        
        try:
            # 直接获取对应UI槽位的输入值
            raw_inputs = [
                self.sub_card1.value.strip(),
                self.sub_card2.value.strip(),
                self.sub_card3.value.strip(),
                self.sub_card4.value.strip(),
            ]

            # 收集所有非空输入的卡片ID，用于后续的重复性检查和主卡检查
            all_entered_card_ids_for_validation = []
            # 存储 slot_index (1-based) -> card_id
            parsed_card_ids_for_setting: dict[int, int] = {} 

            for ui_slot_index_zero_based, card_id_str in enumerate(raw_inputs):
                actual_slot_number = ui_slot_index_zero_based + 1 # UI槽位号是1-based

                if card_id_str: # 如果用户在这个槽位输入了内容
                    try:
                        card_id = int(card_id_str)
                        all_entered_card_ids_for_validation.append(card_id)
                        # 存储实际UI槽位号和对应的卡片ID
                        parsed_card_ids_for_setting[actual_slot_number] = card_id
                    except ValueError:
                        await interaction.followup.send(f"❌ 副卡片槽位 {actual_slot_number} 的ID '{card_id_str}' 不是有效的數字。", ephemeral=True)
                        return
            
            # 如果用户没有在任何一个输入框中提供有效的卡片ID
            if not parsed_card_ids_for_setting:
                # 根据之前的讨论，如果所有输入都为空或无效，我们不做任何操作，
                # 用户可能只是打开了模态框然后关闭，或者输入了非数字。
                # 错误信息已在上面处理，这里可以直接返回。
                # 如果需要，可以发送一个通用提示，但目前行为是无操作返回。
                # await interaction.followup.send("🤔 您沒有輸入任何有效的副卡片ID進行設定。", ephemeral=True)
                return

            # 确保没有重复的卡片ID (在所有用户明确输入的卡片中)
            if len(all_entered_card_ids_for_validation) != len(set(all_entered_card_ids_for_validation)):
                await interaction.followup.send("❌ 您輸入的副展示卡片ID中存在重複。", ephemeral=True)
                return
            
            # 确保用户没有尝试设定主卡为副卡
            profile_data = await self.view.profile_service.get_profile_data(
                self.view.user_id, 
                interaction.user.display_name, 
                str(interaction.user.avatar.url) if interaction.user.avatar else None
            )
            main_card_id = profile_data.main_card.card_id if profile_data.main_card else None

            if main_card_id:
                for slot, sub_card_id_to_set in parsed_card_ids_for_setting.items():
                    if sub_card_id_to_set == main_card_id:
                        await interaction.followup.send(f"❌ 卡片ID {main_card_id} (在副卡槽位 {slot}) 已被設定為主展示卡，不能同時設定為副展示卡。", ephemeral=True)
                        return
            
            # 逐個設定用戶實際輸入的副卡
            # operations_done = False # 用于跟踪是否至少执行了一次设置
            for slot_to_set, card_id_to_set in parsed_card_ids_for_setting.items():
                await self.view.profile_service.set_sub_card(self.view.user_id, slot_to_set, card_id_to_set)
                # operations_done = True
            
            # 只有在实际进行了某些卡片设置时才刷新缓存和UI
            # (parsed_card_ids_for_setting 非空已在前面检查过，确保了至少有一个有效输入)
            await self.view.profile_service.invalidate_profile_cache(self.view.user_id)
            await self.view.update_settings_embed(interaction)

        except UserDoesNotOwnCardError as e:
            await interaction.followup.send(f"❌ 設定副卡片失敗: {e}", ephemeral=True)
        except ProfileCardSetError as e:
            await interaction.followup.send(f"❌ 設定副卡片失敗: {e}", ephemeral=True)
        except Exception as e:
            logger.error(f"設定副卡片時發生錯誤: {e}", exc_info=True)
            await interaction.followup.send("❌ 設定副卡片時發生錯誤，請稍後再試。", ephemeral=True)


class ClearSubCardSlotModal(ui.Modal, title="清除副展示卡片"):
    """清除副展示卡片槽位的模態框"""
    
    slot_number = ui.TextInput(
        label="要清除的副卡片位置 (1-4)",
        placeholder="請輸入1到4的數字",
        required=True,
        min_length=1,
        max_length=1
    )
    
    def __init__(self, view: "ProfileSettingsView"):
        super().__init__()
        self.view = view
    
    async def on_submit(self, interaction: discord.Interaction):
        """提交時的處理邏輯"""
        await interaction.response.defer(ephemeral=True)
        
        try:
            slot_to_clear_str = self.slot_number.value.strip()
            
            if not slot_to_clear_str: # 如果用户没有输入任何东西
                await self.view.profile_service.clear_all_sub_cards(self.view.user_id)
                await self.view.profile_service.invalidate_profile_cache(self.view.user_id)
                await self.view.update_settings_embed(interaction) # 更新嵌入消息
                return

            try:
                slot_to_clear = int(slot_to_clear_str)
                if not 1 <= slot_to_clear <= 4:
                    await interaction.followup.send("❌ 副卡片位置必須是 1 到 4 之間的數字。", ephemeral=True)
                    return
            except ValueError:
                await interaction.followup.send("❌ 副卡片位置必須是有效的數字。", ephemeral=True)
                return
                
            await self.view.profile_service.clear_sub_card_slot(self.view.user_id, slot_to_clear)
            await self.view.profile_service.invalidate_profile_cache(self.view.user_id)
            await self.view.update_settings_embed(interaction) # 更新嵌入消息
            
        except ProfileCardSetError as e:
            await interaction.followup.send(f"❌ 清除副卡片失敗: {e}", ephemeral=True)
        except Exception as e:
            logger.error(f"清除副卡片時發生錯誤: {e}", exc_info=True)
            await interaction.followup.send("❌ 清除副卡片時發生錯誤，請稍後再試。", ephemeral=True)


class SetStatusModal(ui.Modal, title="設定個性簽名"):
    """設定個性簽名的模態框"""
    
    status_text = ui.TextInput(
        label="個性簽名 (最多150字)",
        placeholder="請輸入您的個性簽名...",
        required=False,
        max_length=150,
        style=discord.TextStyle.paragraph
    )
    
    def __init__(self, view: "ProfileSettingsView"):
        super().__init__()
        self.view = view
    
    async def on_submit(self, interaction: discord.Interaction):
        """提交時的處理邏輯"""
        await interaction.response.defer(ephemeral=True)
        
        try:
            status_text = self.status_text.value
            
            # 驗證字符長度
            if len(status_text) > 150:
                await interaction.followup.send("❌ 個性簽名不能超過150個字符。", ephemeral=True)
                return
                
            # 調用設定個性簽名的服務
            await self.view.profile_service.set_user_status(self.view.user_id, status_text)
            
            # 設定成功，清除緩存
            await self.view.profile_service.invalidate_profile_cache(self.view.user_id)
            
            # 更新原始嵌入消息
            await self.view.update_settings_embed(interaction)
            
        except ProfileBackgroundError as e: # ProfileService.set_user_status 可能会抛出这个
            await interaction.followup.send(f"❌ 設定個性簽名失敗: {e}", ephemeral=True)
        except Exception as e:
            logger.error(f"設定個性簽名時發生錯誤: {e}", exc_info=True)
            await interaction.followup.send("❌ 設定個性簽名時發生錯誤，請稍後再試。", ephemeral=True)


class ProfileSettingsView(ui.View):
    """個人資料設定視圖"""
    
    def __init__(self, bot: discord.Client, profile_service: ProfileService, user_id: int):
        super().__init__(timeout=180)  # 3分鐘超時
        self.bot = bot
        self.profile_service = profile_service
        self.user_id = user_id
    
    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """確保只有發起命令的用戶可以與這些按鈕互動"""
        if interaction.user.id != self.user_id:
            await interaction.response.send_message("❌ 您不能使用其他人的設定介面。", ephemeral=True)
            return False
        return True
    
    @ui.button(label="設定主展示卡片", style=discord.ButtonStyle.primary, row=0)
    async def set_main_card_button(self, interaction: discord.Interaction, button: ui.Button):
        """打開設定主展示卡片的模態框"""
        modal = SetMainCardModal(self)
        await interaction.response.send_modal(modal)
    
    @ui.button(label="設定副展示卡片", style=discord.ButtonStyle.primary, row=0)
    async def set_sub_cards_button(self, interaction: discord.Interaction, button: ui.Button):
        """打開設定副展示卡片的模態框"""
        modal = SetSubCardsModal(self)
        await interaction.response.send_modal(modal)
    
    @ui.button(label="清除副展示卡片", style=discord.ButtonStyle.secondary, row=1)
    async def clear_sub_card_button(self, interaction: discord.Interaction, button: ui.Button):
        """打開清除副展示卡片的模態框"""
        modal = ClearSubCardSlotModal(self)
        await interaction.response.send_modal(modal)
    
    @ui.button(label="設定個性簽名", style=discord.ButtonStyle.primary, row=1)
    async def set_status_button(self, interaction: discord.Interaction, button: ui.Button):
        """打開設定個性簽名的模態框"""
        modal = SetStatusModal(self)
        await interaction.response.send_modal(modal)
    
    @ui.button(label="重置背景圖片", style=discord.ButtonStyle.secondary, row=2)
    async def reset_background_button(self, interaction: discord.Interaction, button: ui.Button):
        """處理重置背景圖片的邏輯"""
        await interaction.response.defer(ephemeral=True)
        
        try:
            # 調用重置背景的服務
            await self.profile_service.reset_background(self.user_id)
            
            # 重置成功，清除緩存
            await self.profile_service.invalidate_profile_cache(self.user_id)
            
            # 更新原始嵌入消息
            await self.update_settings_embed(interaction)
            
        except ProfileBackgroundError as e:
            await interaction.followup.send(f"❌ 重置背景失敗: {e}", ephemeral=True)
        except Exception as e:
            logger.error(f"重置背景時發生錯誤: {e}", exc_info=True)
            await interaction.followup.send("❌ 重置背景時發生錯誤，請稍後再試。", ephemeral=True)

    async def update_settings_embed(self, interaction: discord.Interaction):
        """更新設定嵌入消息"""
        try:
            new_embed = await self.create_settings_embed()
            await interaction.edit_original_response(embed=new_embed, view=self)
        except Exception as e:
            logger.error(f"更新設定嵌入消息失敗: {e}", exc_info=True)
            # 如果更新失败，尝试发送一个错误消息，但不要覆盖原始的 view，以便用户可以继续尝试
            try:
                await interaction.followup.send("❌ 更新設定顯示時發生錯誤，但您的更改可能已保存。请尝试重新打开设定界面。", ephemeral=True)
            except discord.HTTPException:
                pass # 如果连 followup 都失败，就没办法了

    async def create_settings_embed(self) -> discord.Embed:
        """創建顯示當前設定信息的嵌入消息"""
        embed = discord.Embed(
            title="📝 個人檔案設定",
            description="以下是您當前的個人檔案設定情況，您可以通過下方按鈕進行修改。",
            color=discord.Color.blue()
        )
        
        try:
            # 獲取用戶 Discord 信息
            user = await self.bot.fetch_user(self.user_id)
            user_display_name = user.display_name if user else "未知用戶"
            user_avatar_url = str(user.display_avatar.url) if user and user.display_avatar else None
            
            # 獲取用戶檔案數據
            user_profile = await self.profile_service.get_profile_data(
                self.user_id,
                discord_display_name=user_display_name,
                avatar_url=user_avatar_url
            )
            
            # 主卡信息
            main_card = getattr(user_profile, 'main_card', None)
            if main_card:
                main_card_value = f"**{main_card.name}** (ID: {main_card.card_id})"
                embed.add_field(name="🎴 主展示卡片", value=main_card_value, inline=False)
            else:
                embed.add_field(name="🎴 主展示卡片", value="未設定", inline=False)
            
            # 副卡信息
            sub_cards_dict = getattr(user_profile, 'sub_cards', {})
            sub_cards_value = ""
            
            if sub_cards_dict:
                for i in range(1, 5):
                    card = sub_cards_dict.get(i)
                    if card:
                        sub_cards_value += f"槽位 {i}: **{card.name}** (ID: {card.card_id})\n"
                    else:
                        sub_cards_value += f"槽位 {i}: 未設定\n"
                if not sub_cards_value:
                    sub_cards_value = "副卡片信息格式不正确或槽位错误\n"
            else:
                sub_cards_value = "未設定任何副卡片"
                
            embed.add_field(name="🃏 副展示卡片", value=sub_cards_value, inline=False)
            
            # 背景信息
            background_url = getattr(user_profile, 'background_image_url', None)
            if background_url and background_url != 'default':
                background_value = "已設定自定義背景"
                embed.set_thumbnail(url=background_url)
            else:
                background_value = "使用預設背景"
                
            embed.add_field(name="🖼️ 背景圖片", value=background_value, inline=False)
            
            # 個性簽名
            status = getattr(user_profile, 'user_status', None)
            if status:
                embed.add_field(name="📝 個性簽名", value=f"「{status}」", inline=False)
            else:
                embed.add_field(name="📝 個性簽名", value="未設定", inline=False)
                
            # 點讚數
            like_count_value = getattr(user_profile, 'like_count', 0)
            embed.add_field(name="❤️ 收到的點讚", value=str(like_count_value), inline=True)
            
        except Exception as e:
            logger.error(f"創建設定信息嵌入消息時發生錯誤: {e}", exc_info=True)
            embed.description = "⚠️ 獲取設定信息時發生錯誤，但您仍可使用下方按鈕進行設定。"
        
        embed.set_footer(text="使用下方按鈕修改設定 • 設定完成後可使用 /profile 查看效果")
        return embed 