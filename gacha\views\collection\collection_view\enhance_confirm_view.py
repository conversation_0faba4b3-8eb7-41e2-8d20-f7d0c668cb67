"""
Gacha系統卡片升星確認視圖
提供卡片升星確認和連續升星功能

重構後採用統一的服務訪問模式，保持系統架構的一致性
"""
import asyncio
import time
from typing import TYPE_CHECKING, Any, Dict, Optional
import discord
from utils.logger import logger
from gacha.models.models import Card
from gacha.services.core.star_enhancement_service import StarEnhancementService
from gacha.utils import interaction_utils
from .interaction_manager import InteractionManager
from gacha.views import utils as view_utils
from gacha.constants import RarityLevel
from gacha.exceptions import CardNotFoundError
if TYPE_CHECKING:
    from .card_view import CollectionView

class EnhanceConfirmView(discord.ui.View):
    """用於升星確認的臨時視圖

    採用統一的服務訪問模式，通過 collection_view.services 統一訪問所有服務，
    保持系統架構的一致性。
    """

    def __init__(self, original_interaction: discord.Interaction, user_id: int, card_id: int, collection_view: 'CollectionView', initial_enhancement_info: Dict[str, Any], timeout: int=120):
        """初始化升星確認視圖"""
        super().__init__(timeout=timeout)
        self.original_interaction = original_interaction
        self.user_id = user_id
        self.card_id = card_id
        self.collection_view = collection_view
        self.current_enhancement_info = initial_enhancement_info
        self.interaction_manager = InteractionManager()

    @property
    def enhance_service(self) -> 'StarEnhancementService':
        """統一的升星服務訪問接口

        遵循系統的服務容器模式，通過 collection_view.services 訪問服務
        """
        return self.collection_view.services.star_enhancement

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """
        檢查與此升星確認視圖互動的使用者是否為原始擁有者。
        """
        return await interaction_utils.check_user_permission(interaction=interaction, expected_user_id=self.user_id, error_message_on_fail='您無法操作他人的升星確認介面！')

    @discord.ui.button(label='確認升星', style=discord.ButtonStyle.danger, custom_id='confirm_enhance_action_v5')
    async def confirm_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """確認升星按鈕回調"""
        if not await self.interaction_manager.try_defer(interaction):
            return
            
        try:
            # 執行升星操作 - 如果不可能會拋出異常
            result = await self.enhance_service.enhance_card(self.user_id, self.card_id)
            
            try:
                # 檢查下一次升星的可能性 - 如果不可能會拋出異常，但我們捕獲它以顯示結果
                next_enhancement_info = await self.enhance_service.check_enhancement_possibility(self.user_id, self.card_id)
                can_enhance_again = True
            except Exception as e:
                # 如果下一次升星不可能，記錄錯誤並設置can_enhance_again為False
                logger.debug('下一次升星檢查失敗: %s', str(e))
                next_enhancement_info = {'card_data': self.current_enhancement_info['card_data']}
                can_enhance_again = False
                
            card_embed = self._create_card_embed(self.current_enhancement_info['card_data'], result, next_enhancement_info)
            continue_view = EnhanceContinueView(self.user_id, self.card_id, self.collection_view, next_enhancement_info, can_enhance_again)
            await interaction.edit_original_response(embed=card_embed, view=continue_view)
            asyncio.create_task(self._update_collection_view())
            
        except Exception as e:
            # 處理升星過程中的所有異常
            error_embed = discord.Embed(
                title="升星失敗",
                description=f"升星過程中發生錯誤: {str(e)}",
                color=discord.Color.red()
            )
            await interaction.edit_original_response(embed=error_embed, view=None)
            logger.error('升星操作失敗: %s', str(e), exc_info=True)

    def _create_card_embed(self, card_data: Any, result: Dict[str, Any], next_enhancement_info: Dict[str, Any]) -> discord.Embed:
        """創建卡片嵌入"""
        star_enhanced = result.get('star_enhanced', False)
        card_name = card_data.card.name
        old_star_level = result.get('old_star_level', 0)
        new_star_level = result.get('new_star_level', old_star_level)
        color = discord.Color.brand_green() if star_enhanced else discord.Color.brand_red()
        star_emoji = '<a:sw:1365447243863429273>'
        message = result.get('message', '')
        if '消耗了' in message:
            message = message.replace('消耗了', '\n消耗了')
        embed = discord.Embed(title=f'{star_emoji} {card_name} 升星結果 {star_emoji}', description=message, color=color)
        if hasattr(card_data.card, 'image_url') and card_data.card.image_url:
            embed.set_image(url=card_data.card.image_url)
        card_rarity_enum = None
        if hasattr(card_data, 'card') and hasattr(card_data.card, 'rarity'):
            try:
                if isinstance(card_data.card.rarity, int):
                    card_rarity_enum = RarityLevel(card_data.card.rarity)
                elif isinstance(card_data.card.rarity, RarityLevel):
                    card_rarity_enum = card_data.card.rarity
            except ValueError:
                pass
        rarity_image_url = view_utils.get_rarity_image(card_rarity_enum)
        if rarity_image_url:
            embed.set_thumbnail(url=rarity_image_url)
        old_star_emoji = view_utils.get_star_emoji_string(old_star_level)
        new_star_emoji = view_utils.get_star_emoji_string(new_star_level)
        if star_enhanced:
            embed.add_field(name='⭐ 星級提升 ⭐', value=f'**{old_star_level}** {old_star_emoji} → **{new_star_level}** {new_star_emoji}', inline=False)
        else:
            embed.add_field(name='⭐ 當前星級 ⭐', value=f'**{old_star_level}** {old_star_emoji}', inline=False)
        can_enhance_next = next_enhancement_info.get('can_enhance', False)
        if can_enhance_next:
            current_new_level = result.get('new_star_level', result.get('old_star_level', 0))
            next_level = current_new_level + 1
            next_costs = next_enhancement_info.get('costs', {})
            success_rate = next_enhancement_info.get('success_rate', 0.0)
            embed.add_field(name=f'✨ 下一級（{next_level}星）升星資源 ✨', value=f"💰 所需油幣: **{next_costs.get('oil', 'N/A')}** <:oil1:1368446294594424872>\n🎴 所需重複卡: **{next_costs.get('duplicates', 'N/A')}** 張\n📊 成功率: **{success_rate:.1f}%**", inline=False)
        if hasattr(card_data.card, 'series') and card_data.card.series:
            embed.set_footer(text=f'系列: {card_data.card.series}')
        return embed

    async def _update_collection_view(self):
        """更新原始的CollectionView"""
        try:
            await self.collection_view._update_page(self.collection_view.current_page, self.original_interaction)
        except Exception as e:
            logger.error('更新原始卡冊視圖時出錯: %s', e)

    @discord.ui.button(label='取消', style=discord.ButtonStyle.secondary, custom_id='cancel_enhance_action_v5')
    async def cancel_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """取消升星按鈕回調"""
        cancel_embed = discord.Embed(title='操作取消', description='升星操作已取消。', color=discord.Color.greyple())
        await interaction.response.edit_message(embed=cancel_embed, view=None)
        self.interaction_manager.mark_responded(interaction.id)

    async def on_timeout(self):
        """視圖超時處理"""
        for item in self.children:
            item.disabled = True

class EnhanceContinueView(discord.ui.View):
    """升星後的繼續升星視圖

    採用統一的服務訪問模式，保持與其他組件的一致性
    """

    def __init__(self, user_id: int, card_id: int, collection_view: 'CollectionView', enhancement_info: Dict[str, Any], can_enhance: bool=True, timeout: int=300):
        """初始化繼續升星視圖"""
        super().__init__(timeout=timeout)
        self.user_id = user_id
        self.card_id = card_id
        self.collection_view = collection_view
        self.enhancement_info = enhancement_info
        self.original_interaction = getattr(collection_view, 'original_interaction', None)
        self.interaction_manager = InteractionManager()
        if not can_enhance:
            self.continue_button.disabled = True
            self.continue_button.label = '無法繼續升星'

    @property
    def enhance_service(self) -> 'StarEnhancementService':
        """統一的升星服務訪問接口

        遵循系統的服務容器模式，通過 collection_view.services 訪問服務
        """
        return self.collection_view.services.star_enhancement

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """
        檢查與此繼續升星視圖互動的使用者是否為原始擁有者。
        """
        return await interaction_utils.check_user_permission(interaction=interaction, expected_user_id=self.user_id, error_message_on_fail='您無法操作他人的繼續升星介面！')

    @discord.ui.button(label='繼續升星', style=discord.ButtonStyle.primary, custom_id='continue_enhance_v5')
    async def continue_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """繼續升星按鈕回調"""
        if not await self.interaction_manager.try_defer(interaction):
            return
            
        try:
            # 檢查升星可能性 - 如果不可能會拋出異常
            await self.enhance_service.check_enhancement_possibility(self.user_id, self.card_id)
            
            # 執行升星操作 - 如果不可能會拋出異常
            result = await self.enhance_service.enhance_card(self.user_id, self.card_id)
            updated_card_data = await self.enhance_service.collection_repo.get_user_card(self.user_id, self.card_id)
            
            if not updated_card_data:
                raise CardNotFoundError(f"找不到卡片 {self.card_id}", card_id=self.card_id)
                
            try:
                # 檢查下一次升星的可能性 - 如果不可能會拋出異常，但我們捕獲它以顯示結果
                next_enhancement_info = await self.enhance_service.check_enhancement_possibility(self.user_id, self.card_id)
                can_enhance_again = True
            except Exception as e:
                # 如果下一次升星不可能，記錄錯誤並設置can_enhance_again為False
                logger.debug('下一次升星檢查失敗: %s', str(e))
                next_enhancement_info = {'card_data': updated_card_data}
                can_enhance_again = False
                
            card_embed = self._create_card_embed(updated_card_data, result, next_enhancement_info)
            new_continue_view = EnhanceContinueView(self.user_id, self.card_id, self.collection_view, next_enhancement_info, can_enhance_again)
            await interaction.edit_original_response(embed=card_embed, view=new_continue_view)
            asyncio.create_task(self._update_collection_view())
                
        except Exception as e:
            # 處理升星過程中的所有異常
            error_embed = discord.Embed(
                title="升星失敗",
                description=f"升星過程中發生錯誤: {str(e)}",
                color=discord.Color.red()
            )
            await interaction.edit_original_response(embed=error_embed, view=None)
            logger.error('升星操作失敗: %s', str(e), exc_info=True)

    async def _update_collection_view(self):
        """更新原始的CollectionView"""
        try:
            if self.collection_view and hasattr(self.collection_view, '_update_page') and self.original_interaction:
                await self.collection_view._update_page(self.collection_view.current_page, self.original_interaction)
        except Exception as e:
            logger.error('更新原始卡冊視圖時出錯: %s', e)

    def _create_card_embed(self, card_data: Any, result: Dict[str, Any], next_enhancement_info: Dict[str, Any]) -> discord.Embed:
        """創建卡片嵌入"""
        star_enhanced = result.get('star_enhanced', False)
        card_name = card_data.card.name
        old_star_level = result.get('old_star_level', 0)
        new_star_level = result.get('new_star_level', old_star_level)
        color = discord.Color.brand_green() if star_enhanced else discord.Color.brand_red()
        star_emoji = '<a:sw:1365447243863429273>'
        message = result.get('message', '')
        if '消耗了' in message:
            message = message.replace('消耗了', '\n消耗了')
        embed = discord.Embed(title=f'{star_emoji} {card_name} 升星結果 {star_emoji}', description=message, color=color)
        if hasattr(card_data.card, 'image_url') and card_data.card.image_url:
            embed.set_image(url=card_data.card.image_url)
        card_rarity_enum_continue = None
        if hasattr(card_data, 'card') and hasattr(card_data.card, 'rarity'):
            try:
                if isinstance(card_data.card.rarity, int):
                    card_rarity_enum_continue = RarityLevel(card_data.card.rarity)
                elif isinstance(card_data.card.rarity, RarityLevel):
                    card_rarity_enum_continue = card_data.card.rarity
            except ValueError:
                pass
        rarity_image_url = view_utils.get_rarity_image(card_rarity_enum_continue)
        if rarity_image_url:
            embed.set_thumbnail(url=rarity_image_url)
        old_star_emoji = view_utils.get_star_emoji_string(old_star_level)
        new_star_emoji = view_utils.get_star_emoji_string(new_star_level)
        if star_enhanced:
            embed.add_field(name='⭐ 星級提升 ⭐', value=f'**{old_star_level}** {old_star_emoji} → **{new_star_level}** {new_star_emoji}', inline=False)
        else:
            embed.add_field(name='⭐ 當前星級 ⭐', value=f'**{old_star_level}** {old_star_emoji}', inline=False)
        can_enhance_next = next_enhancement_info.get('can_enhance', False)
        if can_enhance_next:
            current_new_level = result.get('new_star_level', result.get('old_star_level', 0))
            next_level = current_new_level + 1
            next_costs = next_enhancement_info.get('costs', {})
            success_rate = next_enhancement_info.get('success_rate', 0.0)
            embed.add_field(name=f'✨ 下一級（{next_level}星）升星資源 ✨', value=f"💰 所需油幣: **{next_costs.get('oil', 'N/A')}** <:oil1:1368446294594424872>\n🎴 所需重複卡: **{next_costs.get('duplicates', 'N/A')}** 張\n📊 成功率: **{success_rate:.1f}%**", inline=False)
        if hasattr(card_data.card, 'series') and card_data.card.series:
            embed.set_footer(text=f'系列: {card_data.card.series}')
        return embed

    async def on_timeout(self):
        """視圖超時處理"""
        for item in self.children:
            item.disabled = True