from __future__ import annotations
import typing
import asyncpg
import random
from typing import Dict, List, Optional, Tuple, Any
from database.postgresql.async_manager import AsyncPgManager
from gacha.services.games.base_game_service import BaseGameService
import discord
import functools
from utils.logger import logger
from gacha.exceptions import MinBetNotMetError, GameSettlementError, GameNotFoundError, InvalidGameActionError

if typing.TYPE_CHECKING:
    from gacha.services.core.economy_service import EconomyService
    from gacha.services.ui.game_display_service import GameDisplayService

CARD_EMOJIS = {
    ('diamonds', 'A'): '<:D1:1367810486250508299>', ('diamonds', '2'): '<:D2:1367810491153383496>',
    ('diamonds', '3'): '<:D3:1367810497176404049>', ('diamonds', '4'): '<:D4:1367810508203360256>',
    ('diamonds', '5'): '<:D5:1367810514591154216>', ('diamonds', '6'): '<:D6:1367810519951478815>',
    ('diamonds', '7'): '<:D7:1367810525056208896>', ('diamonds', '8'): '<:D8:1367810530500149359>',
    ('diamonds', '9'): '<:D9:1367810536758317097>', ('diamonds', '10'): '<:D10:1367810543020281926>',
    ('diamonds', 'J'): '<:D11:1367810548686655639>', ('diamonds', 'Q'): '<:D12:1367810555687079986>',
    ('diamonds', 'K'): '<:D13:1367810561579946075>', ('spades', 'A'): '<:S1:1367810413135269922>',
    ('spades', '2'): '<:S2:1367810419330252921>', ('spades', '3'): '<:S3:1367810424145313916>',
    ('spades', '4'): '<:S4:1367810429509697639>', ('spades', '5'): '<:S5:1367810434492797008>',
    ('spades', '6'): '<:S6:1367810439353864252>', ('spades', '7'): '<:S7:1367810444596875284>',
    ('spades', '8'): '<:S8:1367810449650880542>', ('spades', '9'): '<:S9:1367810454902276192>',
    ('spades', '10'): '<:S10:1367810459662684259>', ('spades', 'J'): '<:S11:1367810466344079450>',
    ('spades', 'Q'): '<:S12:1367810475378872400>', ('spades', 'K'): '<:S13:1367810480999235675>',
    ('clubs', 'A'): '<:C1:1367810267848773673>', ('clubs', '2'): '<:C2:1367810277525033061>',
    ('clubs', '3'): '<:C3:1367810294914748426>', ('clubs', '4'): '<:C4:1367810299985661993>',
    ('clubs', '5'): '<:C5:1367810304695730196>', ('clubs', '6'): '<:C6:1367810310202720366>',
    ('clubs', '7'): '<:C7:1367810314564931645>', ('clubs', '8'): '<:C8:1367810319610675272>',
    ('clubs', '9'): '<:C9:1367810324706623598>', ('clubs', '10'): '<:C10:1367810329895243868>',
    ('clubs', 'J'): '<:C11:1367810333892280320>', ('clubs', 'Q'): '<:C12:1367810337805701230>',
    ('clubs', 'K'): '<:C13:1367810342515769385>', ('hearts', 'A'): '<:H1:1367810346802348143>',
    ('hearts', '2'): '<:H2:1367810351818870896>', ('hearts', '3'): '<:H3:1367810356247920670>',
    ('hearts', '4'): '<:H4:1367810361448726649>', ('hearts', '5'): '<:H5:1367810366444142634>',
    ('hearts', '6'): '<:H6:1367810372156915782>', ('hearts', '7'): '<:H7:1367810377202532392>',
    ('hearts', '8'): '<:H8:1367810382135033896>', ('hearts', '9'): '<:H9:1367810387818450975>',
    ('hearts', '10'): '<:H10:1367810391257907200>', ('hearts', 'J'): '<:H11:1367810396844462104>',
    ('hearts', 'Q'): '<:H12:1367810402255110188>', ('hearts', 'K'): '<:H13:1367810407791722516>',
    ('unknown', '?'): '❓'
}
HIDDEN_CARD_EMOJI = '<:hd:1367815959645130762>'


class PlayingCard:
    """撲克牌"""

    def __init__(self, suit: str, value: str):
        """
        初始化撲克牌

        參數:
            suit: 花色（hearts, spades, diamonds, clubs）
            value: 牌面值（A, 2-10, J, Q, K）
        """
        self.suit = suit
        self.value = value

    def __str__(self) -> str:
        """返回牌的 Emoji 字符串表示"""
        return CARD_EMOJIS.get((self.suit, self.value), CARD_EMOJIS['unknown', '?'])

    @property
    def blackjack_value(self) -> int:
        """獲取牌在Blackjack中的點數"""
        if self.value in ['J', 'Q', 'K']:
            return 10
        elif self.value == 'A':
            return 11
        else:
            try:
                return int(self.value)
            except ValueError:
                logger.warning("無法轉換牌值'%s'為整數", self.value)
                return 0


class Deck:
    """牌組"""

    def __init__(self):
        """初始化牌組"""
        suits = ['hearts', 'spades', 'diamonds', 'clubs']
        values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
        self.cards = [PlayingCard(suit, value) for suit in suits for value in values]
        self.shuffle()

    def shuffle(self):
        """洗牌"""
        random.shuffle(self.cards)

    def deal(self) -> Optional[PlayingCard]:
        """發牌"""
        if not self.cards:
            return None
        return self.cards.pop()


class Hand:
    """玩家或莊家的手牌"""

    def __init__(self):
        """初始化手牌"""
        self.cards = []
        self.soft_value = 0
        self.hard_value = 0

    def add_card(self, card: PlayingCard):
        """添加一張牌到手牌

        參數:
            card: 要添加的牌
        """
        self.cards.append(card)
        self._recalculate_value()

    def _recalculate_value(self):
        """重新計算手牌值"""
        non_ace_sum = sum(card.blackjack_value for card in self.cards if card.value != 'A')
        ace_count = sum(1 for card in self.cards if card.value == 'A')
        max_value = non_ace_sum + ace_count * 11
        adjusted_value = max_value
        for _ in range(ace_count):
            if adjusted_value > 21:
                adjusted_value -= 10
            else:
                break
        self.hard_value = adjusted_value
        self.soft_value = adjusted_value if adjusted_value <= 21 else 0

    def is_blackjack(self) -> bool:
        """檢查是否為BlackJack（一張A和一張10點牌）"""
        return len(self.cards) == 2 and self.hard_value == 21

    def is_busted(self) -> bool:
        """檢查是否爆牌（點數超過21）"""
        return self.hard_value > 21

    def __str__(self) -> str:
        """返回手牌的字符串表示"""
        return ' '.join(str(card) for card in self.cards)


class BlackjackGame:
    """21點遊戲"""

    def __init__(self, user_id: int, bet: int):
        """
        初始化遊戲

        參數:
            user_id: 玩家ID
            bet: 下注金額
        """
        self.user_id = user_id
        self.bet = bet
        self.deck = Deck()
        self.player_hand = Hand()
        self.dealer_hand = Hand()
        self.game_over = False
        self.result = None
        self.payout = 0
        
        # 發初始牌
        self.player_hand.add_card(self.deck.deal())
        self.dealer_hand.add_card(self.deck.deal())
        self.player_hand.add_card(self.deck.deal())
        self.dealer_hand.add_card(self.deck.deal())
        self._check_initial_blackjack()

    def _check_initial_blackjack(self):
        """檢查初始發牌是否有Blackjack"""
        player_bj = self.player_hand.is_blackjack()
        dealer_bj = self.dealer_hand.is_blackjack()
        
        if player_bj and dealer_bj:
            self.game_over = True
            self.result = 'push'
            self.payout = self.bet
        elif player_bj:
            self.game_over = True
            self.result = 'blackjack'
            self.payout = int(self.bet * 2.5)
        elif dealer_bj:
            self.game_over = True
            self.result = 'dealer_blackjack'
            self.payout = 0

    def player_hit(self) -> Dict[str, Any]:
        """玩家要牌

        返回:
            遊戲狀態信息
        """
        if self.game_over:
            return self.get_game_state()
            
        self.player_hand.add_card(self.deck.deal())
        if self.player_hand.is_busted():
            self.game_over = True
            self.result = 'bust'
            self.payout = 0
        return self.get_game_state()

    def player_stand(self) -> Dict[str, Any]:
        """玩家停牌

        返回:
            遊戲狀態信息
        """
        if self.game_over:
            return self.get_game_state()
            
        # 莊家要牌直到17點或以上
        while self.dealer_hand.hard_value < 17:
            self.dealer_hand.add_card(self.deck.deal())
            
        self.game_over = True
        
        if self.dealer_hand.is_busted():
            self.result = 'dealer_bust'
            self.payout = self.bet * 2
        elif self.dealer_hand.hard_value > self.player_hand.hard_value:
            self.result = 'dealer_win'
            self.payout = 0
        elif self.dealer_hand.hard_value < self.player_hand.hard_value:
            self.result = 'player_win'
            self.payout = self.bet * 2
        else:
            self.result = 'push'
            self.payout = self.bet
            
        return self.get_game_state()

    def get_game_state(self) -> Dict[str, Any]:
        """獲取當前遊戲狀態

        返回:
            遊戲狀態信息
        """
        player_hand_state = {
            'cards': [str(card) for card in self.player_hand.cards],
            'hard_value': self.player_hand.hard_value,
            'soft_value': self.player_hand.soft_value,
            'is_busted': self.player_hand.is_busted(),
            'is_blackjack': self.player_hand.is_blackjack()
        }
        
        dealer_hand_state = {
            'cards': [str(card) for card in self.dealer_hand.cards],
            'hard_value': self.dealer_hand.hard_value,
            'soft_value': self.dealer_hand.soft_value,
            'is_busted': self.dealer_hand.is_busted(),
            'is_blackjack': self.dealer_hand.is_blackjack(),
            'show_first_only': not self.game_over
        }
        
        return {
            'user_id': self.user_id,
            'game_over': self.game_over,
            'result': self.result,
            'payout': self.payout,
            'bet': self.bet,
            'player_hand': player_hand_state,
            'dealer_hand': dealer_hand_state
        }


class BlackjackGameService(BaseGameService):
    """
    遊戲服務類，處理 21 點遊戲的邏輯和結算 (繼承自 BaseGameService)
    使用純異常模式進行錯誤處理
    """
    MIN_BET = 10

    def __init__(self, pool: asyncpg.Pool, economy_service: 'EconomyService', game_display_service: 'GameDisplayService'):
        """初始化遊戲服務 (Asyncpg 版本)"""
        super().__init__(economy_service=economy_service, pool=pool)
        self._game_display_service = game_display_service
        self.active_games: Dict[str, BlackjackGame] = {}

    async def start_new_game(self, interaction: discord.Interaction, user_id: int, bet: int, game_id: str) -> Dict[str, Any]:
        """開始新的21點遊戲，使用純異常模式
        
        Args:
            interaction: Discord 交互對象
            user_id: 用戶 ID
            bet: 下注金額
            game_id: 遊戲 ID
            
        Returns:
            Dict[str, Any]: 遊戲狀態
            
        Raises:
            MinBetNotMetError: 當下注金額低於最低要求時
            InsufficientFundsError: 當用戶餘額不足時
            GameSettlementError: 當遊戲創建或結算過程中發生錯誤時
        """
        
        # 檢查最低下注要求
        if bet < self.MIN_BET:
            raise MinBetNotMetError(bet_placed=bet, min_bet=self.MIN_BET)
        
        # 檢查並扣除下注金額
        new_balance_after_bet = await self._check_and_deduct_bet(user_id, bet, '21點', self.MIN_BET)
        
        # 創建新遊戲
        game = BlackjackGame(user_id, bet)
        self.active_games[game_id] = game
        game_state = game.get_game_state()
        game_state['game_id'] = game_id
        
        if game_state.get('game_over', False):
            # 遊戲立即結束（如雙方都是BlackJack）
            final_game_state = await self._settle_game(game_id, game_state)
            await self.handle_game_outcome(interaction, final_game_state, bet)
            return final_game_state
        else:
            game_state['new_balance'] = new_balance_after_bet
            game_state['original_bet'] = bet
            return game_state

    async def player_action(self, interaction: discord.Interaction, game_id: str, action: str) -> Dict[str, Any]:
        """處理玩家動作，使用純異常模式
        
        Args:
            interaction: Discord 交互對象
            game_id: 遊戲 ID
            action: 玩家動作 ('hit' 或 'stand')
            
        Returns:
            Dict[str, Any]: 更新後的遊戲狀態
            
        Raises:
            GameNotFoundError: 當找不到指定的遊戲時
            InvalidGameActionError: 當動作無效時
        """
        
        if game_id not in self.active_games:
            raise GameNotFoundError(game_id=game_id)
            
        game = self.active_games[game_id]
        
        if action == 'hit':
            game_state = game.player_hit()
        elif action == 'stand':
            game_state = game.player_stand()
        else:
            raise InvalidGameActionError(action=action, game_type='21點')
            
        game_state['game_id'] = game_id
        
        if game_state.get('game_over', False):
            final_game_state = await self._settle_game(game_id, game_state)
            await self.handle_game_outcome(interaction, final_game_state, game.bet, interaction.message)
            return final_game_state
        else:
            game_state['original_bet'] = game.bet
            return game_state

    async def handle_game_outcome(self, interaction: discord.Interaction, game_state: Dict[str, Any], original_bet: int, message_to_edit: Optional[discord.Message] = None) -> None:
        """處理遊戲結束時的顯示邏輯"""
        from gacha.views.embeds.games.blackjack_embed_builder import BlackjackEmbedBuilder
        from gacha.views.games.blackjack_view import BlackjackView
        
        user = interaction.user
        new_balance = game_state.get('new_balance')

        async def async_view_provider():
            view_instance = BlackjackView(
                user=user, 
                game_state=game_state, 
                blackjack_service=self, 
                economy_service=self.economy_service, 
                game_display_service=self._game_display_service, 
                original_bet=original_bet
            )
            await view_instance.init_buttons()
            return view_instance

        await self._game_display_service.display_game_end(
            interaction=interaction,
            game_name='Blackjack',
            game_result=game_state,
            original_bet=original_bet,
            view_provider=async_view_provider,
            game_specific_embed_builder=lambda state, bet: BlackjackEmbedBuilder(state, user, state.get('new_balance')).build_embed(),
            send_new_message=True if message_to_edit is None else False
        )

    async def _settle_game(self, game_id: str, game_state: Dict[str, Any]) -> Dict[str, Any]:
        """結算遊戲，使用純異常模式
        
        Args:
            game_id: 遊戲 ID
            game_state: 遊戲狀態
            
        Returns:
            Dict[str, Any]: 更新後的遊戲狀態，包含結算結果
            
        Raises:
            GameSettlementError: 當結算過程中發生錯誤時
        """
        user_id = game_state.get('user_id')
        if not user_id:
            raise GameSettlementError(game_id=game_id, reason='缺少用戶識別信息')
            
        if game_id not in self.active_games and not game_state.get('game_over', False):
            raise GameSettlementError(game_id=game_id, reason='遊戲不存在或尚未結束')
            
        if game_id not in self.active_games and game_state.get('game_over', False):
            # 遊戲已經結算過
            if 'new_balance' not in game_state:
                balance_info = await self.economy_service.get_balance(user_id)
                game_state['new_balance'] = balance_info['balance']
            return game_state
            
        if not game_state.get('game_over', False):
            raise GameSettlementError(game_id=game_id, reason='遊戲尚未結束')
            
        payout = game_state.get('payout', 0.0)
        bet = game_state.get('bet', 0)
        
        # 執行結算
        if payout > 0:
            # 使用 BaseGameService 中的 _settle_winnings 方法
            settle_result = await self._settle_winnings(user_id=user_id, payout=float(payout), bet=bet, game_name='21點')
            game_state['new_balance'] = settle_result.get('new_balance', 0)
        else:
            # 玩家輸了，只更新餘額顯示
            balance_info = await self.economy_service.get_balance(user_id)
            game_state['new_balance'] = balance_info.get('balance', 0)
            
        # 從活動遊戲中移除
        if game_id in self.active_games:
            del self.active_games[game_id]
            
        return game_state 