"""
Gacha系統十連抽視圖的按鈕工廠
"""
import discord

class MultiDrawButtonFactory:
    """為 MultiDrawView 創建標準化按鈕的工廠類。"""

    @staticmethod
    def create_continue_button(callback: callable) -> discord.ui.Button:
        """創建 '繼續十連' 按鈕。"""
        button = discord.ui.Button(
            label='繼續十連', 
            style=discord.ButtonStyle.primary, 
            emoji='🎴', 
            row=0,
            custom_id='md_continue_multi_draw' # md for multi_draw
        )
        button.callback = callback
        return button

    @staticmethod
    def create_prev_page_button(callback: callable) -> discord.ui.Button:
        """創建 '上一張' 按鈕。"""
        button = discord.ui.Button(
            emoji='⬅️', 
            style=discord.ButtonStyle.secondary, 
            row=0,
            custom_id='md_prev_card'
        )
        button.callback = callback
        return button

    @staticmethod
    def create_next_page_button(callback: callable) -> discord.ui.Button:
        """創建 '下一張' 按鈕。"""
        button = discord.ui.Button(
            emoji='➡️', 
            style=discord.ButtonStyle.secondary, 
            row=0,
            custom_id='md_next_card'
        )
        button.callback = callback
        return button 