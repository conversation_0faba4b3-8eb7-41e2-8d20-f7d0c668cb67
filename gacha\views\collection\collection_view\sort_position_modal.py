"""
Gacha系統卡片排序模態框
提供自定義卡片排序的輸入界面
"""
from typing import Any, Callable, Dict, Optional, Union
import discord
from utils.operation_logger import OperationLogger
from utils.logger import logger
from gacha.exceptions import (
    InvalidPositionError, 
    CardNotMarkedAsFavoriteError,
    GachaSystemError
)

class SortPositionModal(discord.ui.Modal, title='移動卡片至指定位置'):
    """移動卡片至指定位置的模態框"""

    def __init__(self, user_id: int, card_id: int, callback_func: Callable[[int, int, int], Dict[str, Any]], update_view_func: Callable[[Dict[str, Any], Optional[discord.Interaction], Optional[Exception]], Any], max_position: int=9999):
        """初始化位置輸入模態框

        參數:
            user_id: 用戶ID
            card_id: 卡片ID
            callback_func: 回調函數，接收用戶ID、卡片ID和目標位置，返回操作結果
            update_view_func: 更新視圖的函數，接收結果、交互對象和可能的異常
            max_position: 最大允許的位置值
        """
        super().__init__()
        self.user_id = user_id
        self.card_id = card_id
        self.callback_func = callback_func
        self.update_view_func = update_view_func
        self.max_position = max_position
        self.position_input = discord.ui.TextInput(label='請輸入目標位置', placeholder=f'輸入 1 到 {max_position} 之間的數字', required=True, min_length=1, max_length=5, style=discord.TextStyle.short)
        self.add_item(self.position_input)

    async def on_submit(self, interaction: discord.Interaction):
        """提交表單時的回調"""
        try:
            try:
                target_position = int(self.position_input.value)
            except ValueError:
                await interaction.response.send_message(content='請輸入有效的數字', ephemeral=True)
                return
            if target_position <= 0 or target_position > self.max_position:
                await interaction.response.send_message(content=f'位置必須在 1 到 {self.max_position} 之間', ephemeral=True)
                return
            await interaction.response.defer()
            
            try:
                result = await self.callback_func(self.user_id, self.card_id, target_position)
                await self.update_view_func(result, interaction)
            except (InvalidPositionError, CardNotMarkedAsFavoriteError, GachaSystemError) as e:
                # 傳遞異常給更新視圖函數處理
                await self.update_view_func({}, interaction, e)
            except Exception as e:
                logger.error('[GACHA] 執行排序回調函數失敗: %s', str(e), exc_info=True)
                await self.update_view_func({}, interaction, GachaSystemError(f'處理位置輸入時發生未知錯誤: {str(e)}'))
                
        except Exception as e:
            logger.error('[GACHA] 處理位置輸入失敗: %s', str(e), exc_info=True)
            if not interaction.response.is_done():
                await interaction.response.send_message(content=f'處理位置輸入時發生錯誤: {str(e)}', ephemeral=True)
            else:
                await OperationLogger.log_operation(interaction, content=f'處理位置輸入時發生錯誤: {str(e)}', ephemeral=True)

    async def on_error(self, interaction: discord.Interaction, error: Exception):
        """發生錯誤時的回調"""
        logger.error('[GACHA] 排序模態框錯誤: %s', str(error), exc_info=True)
        await OperationLogger.log_operation(interaction, content='處理位置輸入時發生錯誤', ephemeral=True)