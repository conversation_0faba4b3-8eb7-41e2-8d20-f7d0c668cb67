"""
提供卡片最愛處理程序

這個模組處理與最愛卡片相關的操作，包括加入/取消最愛、批量操作等。
"""
from typing import TYPE_CHECKING, Any, Callable, Dict, List, Optional, Tuple
import discord
from discord.ui import Button
from utils.logger import logger
from gacha.views.ui_components.confirmation import ConfirmationFactory
from gacha.views.collection.favorite_component import FavoriteComponent
from .base_handler import BaseHandler
from gacha.exceptions import CardNotFoundError, DatabaseOperationError
from gacha.views.collection.collection_view.utils import CardStateInfo # 移出 TYPE_CHECKING 條件塊

if TYPE_CHECKING:
    from gacha.services.core.favorite_service import FavoriteService
    from gacha.views.collection.collection_view.card_view import CollectionView

class CardFavoriteHandler(BaseHandler):
    """卡片最愛功能處理器

    採用統一的服務訪問模式，所有服務都通過 view.services 統一訪問，
    保持系統架構的一致性和可維護性。
    """

    def __init__(self, view: 'CollectionView'):
        super().__init__(view)

    @property
    def favorite_service(self) -> 'FavoriteService':
        """統一的最愛服務訪問接口

        遵循系統的服務容器模式，通過 view.services 訪問服務
        """
        return self.view.services.favorite

    # create_toggle_favorite_button 方法不再需要由 Handler 提供

    async def handle_single_favorite_toggle(self, interaction: discord.Interaction, button: discord.ui.Button):
        """處理單個卡片加入/取消最愛按鈕的回調
        
        Args:
            interaction: Discord 互動對象
            button: 被點擊的按鈕實例
        """
        # 權限檢查
        if not await self.check_interaction_permission(interaction):
             return
        # 卡片可用性檢查
        if not await self.check_cards_available(interaction):
            return

        # 獲取當前卡片和用戶ID
        current_user_card = self.current_card # 使用 BaseHandler 的屬性
        if not current_user_card or not hasattr(current_user_card, 'card') or not current_user_card.card:
            await self._send_error_message(interaction, "無法獲取當前卡片信息。")
            return
        card_id = current_user_card.card.card_id
        user_id = self.user_id # 使用 BaseHandler 的屬性

        # 調用 FavoriteComponent 的核心邏輯
        # 將 view, user_id, card_id, button 傳遞給它
        await FavoriteComponent.handle_favorite_toggle(
            interaction=interaction,
            view=self.view, # 傳遞 CollectionView 實例
            user_id=user_id,
            card_id=card_id,
            button=button
        )

    # --- _create_wish_button 和 _create_wish_callback 已移除 ---

    async def batch_favorite_callback(self, interaction: discord.Interaction):
        """批量加入最愛回調 (遷移自 Mixin)"""
        if not await self.check_interaction_permission(interaction):
            return
        await self._handle_batch_operation(interaction, '加入最愛', self._confirm_batch_favorite_action)

    async def batch_unfavorite_callback(self, interaction: discord.Interaction):
        """批量取消最愛回調 (遷移自 Mixin)"""
        if not await self.check_interaction_permission(interaction):
            return
        await self._handle_batch_operation(interaction, '取消最愛', self._confirm_batch_unfavorite_action)

    async def _handle_batch_operation(self, interaction: discord.Interaction, operation_type: str, confirm_callback_action: Callable):
        """處理批量最愛操作的通用邏輯 (遷移自 Mixin)"""
        target_card_ids = await self.view.services.collection.get_filtered_card_ids(self.view.user_id, self.view.filters)
        if not target_card_ids:
            if not interaction.response.is_done():
                await interaction.response.send_message('目前篩選條件下沒有卡片可操作', ephemeral=True)
            else:
                await interaction.followup.send('目前篩選條件下沒有卡片可操作', ephemeral=True)
            return
        num_cards = len(target_card_ids)
        filter_desc = self.favorite_service.get_filtered_cards_description(self.view.filters.pool_type, self.view.filters.rarity_in, self.view.filters.series)
        confirm_message = f'你確定要將 **{num_cards}** 張符合目前篩選條件 ({filter_desc}) 的卡片 **全部{operation_type}** 嗎？\n'
        if operation_type == '加入最愛':
            confirm_message += '這可能會影響部分卡片的自定義排序。'
        self.view._target_card_ids_for_batch = target_card_ids
        await ConfirmationFactory.create_confirmation(interaction=interaction, title=f'確認批量{operation_type}', description=confirm_message, user_id=self.view.user_id, callback=lambda inter, conf: confirm_callback_action(inter, conf), confirm_label='確認', cancel_label='取消', ephemeral=True)

    async def _confirm_batch_favorite_action(self, interaction: discord.Interaction, confirmed: bool):
        """確認批量加入最愛的操作 (遷移自 Mixin, renamed for clarity)"""
        if not confirmed:
            await self._handle_batch_operation_cancel(interaction, '加入最愛')
            return
        card_ids = getattr(self.view, '_target_card_ids_for_batch', [])
        await self._execute_batch_favorite(interaction, card_ids)

    async def _confirm_batch_unfavorite_action(self, interaction: discord.Interaction, confirmed: bool):
        """確認批量取消最愛的操作 (遷移自 Mixin, renamed for clarity)"""
        if not confirmed:
            await self._handle_batch_operation_cancel(interaction, '取消最愛')
            return
        card_ids = getattr(self.view, '_target_card_ids_for_batch', [])
        await self._execute_batch_unfavorite(interaction, card_ids)

    async def _handle_batch_operation_cancel(self, interaction: discord.Interaction, operation_type: str):
        """處理批量操作取消 (遷移自 Mixin)"""
        cancel_embed = discord.Embed(title=f'批量{operation_type}已取消', description='已取消批量操作。', color=discord.Color.light_grey())
        if not interaction.response.is_done():
            await interaction.response.edit_message(embed=cancel_embed, view=None)
        else:
            await interaction.edit_original_response(embed=cancel_embed, view=None)

    async def _execute_batch_db_operation(
        self,
        interaction: discord.Interaction,
        card_ids: List[int],
        service_method: Callable[[int, List[int]], int], # 接收 user_id 和 card_ids, 返回 updated_count
        operation_type: str
    ):
        """通用批量資料庫操作執行器"""
        if not card_ids:
            err_embed = discord.Embed(title='錯誤', description='找不到要操作的卡片 (ID列表為空)', color=discord.Color.red())
            if not interaction.response.is_done():
                await interaction.response.edit_message(embed=err_embed, view=None)
            else:
                await interaction.edit_original_response(embed=err_embed, view=None)
            return

        try:
            updated_count = await service_method(self.view.user_id, card_ids)
            await self._show_batch_operation_success(interaction, updated_count, operation_type)
        except (ValueError, CardNotFoundError, DatabaseOperationError) as e: # 特定業務異常
            await self._show_batch_operation_error(interaction, str(e), operation_type)
        except Exception as e:
            logger.error(f'[GACHA_HANDLER] 批量{operation_type}失敗: %s', e, exc_info=True)
            await self._show_batch_operation_error(interaction, '發生未知錯誤', operation_type)

    async def _execute_batch_favorite(self, interaction: discord.Interaction, card_ids: List[int]):
        """執行批量加入最愛"""
        await self._execute_batch_db_operation(
            interaction,
            card_ids,
            self.favorite_service.batch_favorite_cards,
            '加入最愛'
        )

    async def _execute_batch_unfavorite(self, interaction: discord.Interaction, card_ids: List[int]):
        """執行批量取消最愛"""
        await self._execute_batch_db_operation(
            interaction,
            card_ids,
            self.favorite_service.batch_unfavorite_cards,
            '取消最愛'
        )

    async def _show_batch_operation_success(self, interaction: discord.Interaction, updated_count: int, operation_type: str):
        """顯示批量操作成功結果"""
        embed_title = f"批量{operation_type}成功"
        embed_color = discord.Color.green()
        message = f"已成功{operation_type} {updated_count} 張卡片"
        embed = discord.Embed(title=embed_title, description=message, color=embed_color)
        
        if not interaction.response.is_done():
            await interaction.response.edit_message(embed=embed, view=None)
        else:
            await interaction.edit_original_response(embed=embed, view=None)
        
        # 更新視圖顯示
        if hasattr(self.view, 'data_manager') and self.view.data_manager:
            self.view.data_manager.clear_cache(f'批量{operation_type}後清空緩存')
        if hasattr(self.view, 'current_page') and hasattr(self.view, '_update_page'):
            try:
                if hasattr(self.view, 'interaction') and self.view.interaction is not None:
                    await self.view._update_page(self.view.current_page, self.view.interaction)
                else:
                    logger.warning('[GACHA_HANDLER] Original interaction not found on view for page update after batch op.')
                    await self.view._update_page(self.view.current_page, interaction)
            except Exception as e:
                logger.error('[GACHA_HANDLER] Error updating main view after batch op: %s', e, exc_info=True)

    async def _show_batch_operation_error(self, interaction: discord.Interaction, error_message: str, operation_type: str):
        """顯示批量操作錯誤結果"""
        embed_title = f"批量{operation_type}失敗"
        embed_color = discord.Color.red()
        embed = discord.Embed(title=embed_title, description=error_message, color=embed_color)
        
        if not interaction.response.is_done():
            await interaction.response.edit_message(embed=embed, view=None)
        else:
            await interaction.edit_original_response(embed=embed, view=None)