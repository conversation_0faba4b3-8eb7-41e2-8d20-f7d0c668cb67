import uuid
import datetime
import math
import asyncio
from typing import List, Dict, Any, Optional, Tuple, Union, TypeVar, Callable
from decimal import Decimal
from enum import Enum
from functools import wraps
from pydantic import ValidationError
from gacha.services.core.user_service import UserService
from gacha.repositories.card.master_card_repository import MasterCardRepository
from gacha.repositories.collection.user_collection_repository import UserCollectionRepository
from gacha.app_config import config_service
from gacha.models.shop_models import ShopItemDefinition, ExchangeSessionData
from utils.logger import logger
from gacha.constants import RarityLevel
from gacha.views import utils as view_utils
from gacha.models.models import Card
from gacha.exceptions import (
    ShopSystemError, InvalidQuantityError, InvalidShopItemError, InsufficientOilTicketsError,
    SessionCreationError, InvalidSessionError, MissingTicketDefinitionError, InvalidTicketTypeError,
    NoCardsSelectedError, IncompleteSelectionError, MaxSelectionReachedError, CardNotInSelectionError
)
from .base_ticket_service import BaseTicketService
from .random_ticket_service import RandomTicketService
from .specific_ticket_service import SpecificTicketService

class TicketType(Enum):
    RANDOM = 'random'
    SPECIFIC = 'specific'
T = TypeVar('T')

class ShopService(BaseTicketService):
    """
    商店服務管理器，負責會話管理和協調，
    將具體的實現委託給專門的服務類
    """

    def __init__(self, user_service: UserService, master_card_repo: MasterCardRepository, user_collection_repo: UserCollectionRepository, pool: Any):
        super().__init__(user_service, master_card_repo, user_collection_repo, pool)
        self.active_sessions: Dict[str, ExchangeSessionData] = {}
        self.random_ticket_service = RandomTicketService(user_service, master_card_repo, user_collection_repo, pool)
        self.specific_ticket_service = SpecificTicketService(user_service, master_card_repo, user_collection_repo, pool)

    def get_session_data(self, session_id: str) -> ExchangeSessionData:
        """
        獲取會話數據
        
        Raises:
            InvalidSessionError: 會話無效或已過期
        """
        session_data = self.active_sessions.get(session_id)
        if not session_data:
            raise InvalidSessionError(session_id=session_id)
        return session_data

    def get_available_oil_tickets(self, decimal_balance: Optional[Decimal]) -> int:
        """將小數油票餘額轉換為可用整數油票"""
        return math.floor(decimal_balance) if decimal_balance is not None else 0

    @staticmethod
    def with_valid_session(func: Callable[..., T]) -> Callable[..., T]:
        """裝飾器：驗證會話有效性"""

        @wraps(func)
        async def wrapper(self, session_id: str, *args, **kwargs):
            try:
                session_data = self.get_session_data(session_id)
                return await func(self, session_id, session_data, *args, **kwargs)
            except InvalidSessionError as e:
                logger.warning("Invalid session: %s", e)
                raise
        return wrapper



    async def cleanup_expired_sessions(self, max_age_minutes: int=30) -> int:
        """清理過期的兌換會話，返回清理的會話數量"""
        now = datetime.datetime.now()
        expired_sessions = []
        for session_id, session_data in list(self.active_sessions.items()):
            if session_data.created_at:
                if session_data.created_at.tzinfo is None and now.tzinfo is not None:
                    comparison_now = now.replace(tzinfo=None)
                elif session_data.created_at.tzinfo is not None and now.tzinfo is None:
                    logger.warning('Timezone mismatch between session created_at (%s) and current time (%s) for session %s. Proceeding with naive comparison.', session_data.created_at.tzinfo, now.tzinfo, session_id)
                    comparison_now = now
                else:
                    comparison_now = now
                age = (comparison_now - session_data.created_at).total_seconds() / 60
                if age > max_age_minutes:
                    expired_sessions.append(session_id)
            else:
                logger.warning('Session %s has no created_at timestamp. Cannot determine age.', session_id)
                
        for session_id in expired_sessions:
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
                logger.info('Cleaned up expired exchange session: %s', session_id)
                
        if expired_sessions:
            logger.info('Cleaned up %s expired exchange sessions.', len(expired_sessions))
            
        return len(expired_sessions)

    async def cancel_exchange_session(self, session_id: str) -> None:
        """
        取消兌換會話
        
        Raises:
            InvalidSessionError: 找不到指定的兌換會話
        """
        if session_id in self.active_sessions:
            logger.info('Cancelling session: %s', session_id)
            del self.active_sessions[session_id]
        else:
            logger.warning('Attempted to cancel non-existent or already cancelled session: %s', session_id)
            raise InvalidSessionError(session_id=session_id)

    async def process_ticket_exchange(self, session_id: str, selected_card_ids: Optional[List[int]]=None) -> Dict[str, Any]:
        """
        統一處理票券兌換流程，根據會話類型委託給相應的服務

        Args:
            session_id: 會話ID
            selected_card_ids: 指定模式下選擇的卡片ID列表，隨機模式為None

        Returns:
            包含處理結果的字典
            
        Raises:
            InvalidSessionError: 會話無效或已過期
            MissingTicketDefinitionError: 會話中缺少票券定義
            InvalidTicketTypeError: 票券類型無效
            NoCardsSelectedError: 沒有選擇任何卡片
            IncompleteSelectionError: 選擇尚未完成
        """
        # 獲取並驗證會話
        session_data = self.get_session_data(session_id)
        ticket_definition = session_data.ticket_definition
        
        if not ticket_definition:
            logger.error('No ticket_definition in session_data for session %s', session_id)
            del self.active_sessions[session_id]
            raise MissingTicketDefinitionError(session_id=session_id)
            
        user_id = session_data.user_id
        total_quantity_to_redeem = session_data.total_quantity_to_redeem
        
        try:
            ticket_type = TicketType(ticket_definition.ticket_type)
        except ValueError:
            logger.error('Invalid ticket_type in ticket_definition for session %s: %s', session_id, ticket_definition.ticket_type)
            del self.active_sessions[session_id]
            raise InvalidTicketTypeError(ticket_type=ticket_definition.ticket_type)
            
        result = None
        
        if ticket_type == TicketType.RANDOM:
            result = await self.random_ticket_service.process_random_ticket_exchange(
                user_id=user_id, 
                ticket_definition=ticket_definition, 
                quantity=total_quantity_to_redeem, 
                session_id=session_id
            )
        elif ticket_type == TicketType.SPECIFIC:
            cards_to_process = selected_card_ids
            
            if not cards_to_process:
                if not session_data.pending_selected_cards:
                    raise NoCardsSelectedError()
                    
                if len(session_data.pending_selected_cards) != total_quantity_to_redeem:
                    needed = total_quantity_to_redeem - len(session_data.pending_selected_cards)
                    raise IncompleteSelectionError(needed=needed, total=total_quantity_to_redeem)
                    
                cards_to_process = session_data.pending_selected_cards
                
            result = await self.specific_ticket_service.process_specific_ticket_exchange(
                user_id=user_id, 
                ticket_definition=ticket_definition, 
                selected_card_ids=cards_to_process, 
                session_id=session_id
            )
            
        # 交易成功，清理會話
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
            
        return result

    async def process_random_ticket_confirmation(self, session_id: str) -> Dict[str, Any]:
        """處理隨機券的確認兌換 - 委託給統一處理方法"""
        logger.info('ShopService: Processing random ticket confirmation for session %s', session_id)
        return await self.process_ticket_exchange(session_id)

    async def finalize_specific_ticket_exchange(self, session_id: str) -> Dict[str, Any]:
        """處理指定券的確認兌換 - 委託給統一處理方法"""
        logger.info('ShopService: Finalizing specific ticket exchange for session %s', session_id)
        return await self.process_ticket_exchange(session_id)

    @with_valid_session
    async def find_target_page_for_specific_exchange(self, session_id: str, session_data: ExchangeSessionData, search_query: str) -> Dict[str, Any]:
        """根據搜索查詢找到特定票券兌換會話的目標頁碼"""
        return await self.specific_ticket_service.find_target_page_for_specific_exchange(
            ticket_definition=session_data.ticket_definition, 
            search_query=search_query
        )

    @with_valid_session
    async def get_specific_ticket_card_page(self, session_id: str, session_data: ExchangeSessionData, page_number: int=1) -> Dict[str, Any]:
        """獲取特定兌換券在指定頁碼的卡片列表"""
        items_per_page = 1
        return await self.specific_ticket_service.get_specific_ticket_card_page(
            ticket_definition=session_data.ticket_definition, 
            page_number=page_number, 
            items_per_page=items_per_page
        )

    @with_valid_session
    async def process_specific_ticket_selection(self, session_id: str, session_data: ExchangeSessionData, selected_card_id: int) -> Dict[str, Any]:
        """
        處理指定券中特定卡片的選擇
        
        Returns:
            包含會話狀態的字典
            
        Raises:
            MaxSelectionReachedError: 已達到最大選擇數量
        """
        user_id = session_data.user_id
        ticket_def = session_data.ticket_definition
        total_to_select = session_data.total_quantity_to_redeem
        
        if len(session_data.pending_selected_cards) >= total_to_select:
            raise MaxSelectionReachedError(max_count=total_to_select)
            
        # 驗證選擇的卡片
        await self.specific_ticket_service._validate_selected_cards(
            card_ids=[selected_card_id], 
            ticket_definition=ticket_def
        )
        
        # 添加到待選列表
        session_data.pending_selected_cards.append(selected_card_id)
        session_data.last_updated_at = datetime.datetime.utcnow()
        
        remaining = total_to_select - len(session_data.pending_selected_cards)
        session_data_summary = {
            'pending_count': len(session_data.pending_selected_cards), 
            'total_to_redeem': total_to_select
        }
        
        if remaining > 0:
            return {
                'message': f'已選擇卡片。還需選擇 {remaining} 張。',
                'next_step': 'continue_selection',
                'session_data_summary': session_data_summary
            }
        else:
            return {
                'message': f'已選擇所有 {total_to_select} 張卡片。請點擊「確認兌換」。',
                'next_step': 'selection_complete_ready_for_finalize',
                'session_data_summary': session_data_summary
            }

    @with_valid_session
    async def unselect_card_for_exchange(self, session_id: str, session_data: ExchangeSessionData, card_id_to_unselect: int) -> Dict[str, Any]:
        """
        從當前兌換會話中取消選擇一張指定的卡片
        
        Raises:
            CardNotInSelectionError: 要取消選擇的卡片不在當前已選列表中
        """
        if card_id_to_unselect in session_data.pending_selected_cards:
            session_data.pending_selected_cards.remove(card_id_to_unselect)
            session_data.last_updated_at = datetime.datetime.utcnow()
            
            remaining = len(session_data.pending_selected_cards)
            total = session_data.total_quantity_to_redeem
            needed = total - remaining
            
            return {
                'message': f'已取消選擇卡片。還需選擇 {needed} 張卡片。',
                'pending_count': remaining,
                'total_count': total
            }
        else:
            raise CardNotInSelectionError(card_id=card_id_to_unselect)

    async def initiate_ticket_exchange(self, user_id: int, shop_item_id: str, quantity: int) -> Dict[str, Any]:
        """
        啟動兌換券兌換流程
        
        Returns:
            包含會話信息的字典
            
        Raises:
            InvalidQuantityError: 數量無效
            InvalidShopItemError: 商品項目無效
            InsufficientOilTicketsError: 油票餘額不足
            SessionCreationError: 創建會話時發生錯誤
        """
        # 驗證數量
        if not isinstance(quantity, int) or quantity <= 0:
            raise InvalidQuantityError(quantity=quantity)
            
        # 驗證商品項目
        ticket_definition: Optional[ShopItemDefinition] = self.config_service.gacha_core.ticket_shop_items.get(shop_item_id)
        if not ticket_definition:
            raise InvalidShopItemError(item_id=shop_item_id)
            
        # 驗證餘額
        current_balance_decimal = await self.user_service.get_oil_ticket_balance(user_id)
        current_balance = self.get_available_oil_tickets(current_balance_decimal)
        total_cost = ticket_definition.cost_oil_tickets * quantity
        
        if current_balance < total_cost:
            raise InsufficientOilTicketsError(required_amount=total_cost, current_balance=current_balance)
            
        # 創建會話
        session_id = str(uuid.uuid4())
        try:
            session_data = ExchangeSessionData(
                session_id=session_id,
                user_id=user_id,
                ticket_item_id=shop_item_id,
                ticket_definition=ticket_definition,
                total_quantity_to_redeem=quantity,
                pending_selected_cards=[],
                created_at=datetime.datetime.utcnow(),
                last_updated_at=datetime.datetime.utcnow()
            )
            self.active_sessions[session_id] = session_data
        except Exception as e:
            logger.error('Error creating ExchangeSessionData for ticket %s: %s', shop_item_id, e, exc_info=True)
            raise SessionCreationError(f'創建兌換會話時發生內部錯誤: {str(e)}')
            
        # 根據票券類型返回相應信息
        ticket_type = TicketType(ticket_definition.ticket_type)
        
        if ticket_type == TicketType.RANDOM:
            return {
                'message': f'已啟動 {ticket_definition.display_name} x {quantity} 的兌換。請確認。',
                'session_id': session_id,
                'next_step': 'confirm_random',
                'ticket_type': ticket_type.value
            }
        elif ticket_type == TicketType.SPECIFIC:
            return {
                'message': f'已啟動 {ticket_definition.display_name} x {quantity} 的兌換。請選擇要兌換的卡片。',
                'session_id': session_id,
                'next_step': 'select_specific_card',
                'ticket_type': ticket_type.value
            }
        else:
            raise InvalidTicketTypeError(ticket_type=ticket_definition.ticket_type)