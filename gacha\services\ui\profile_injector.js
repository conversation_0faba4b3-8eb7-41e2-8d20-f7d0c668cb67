function injectProfileData(payload) {
    const { texts, attributes, inner_htmls, styles, display_toggles } = payload;

    for (const selector in texts) {
        const element = document.querySelector(selector);
        if (element) {
            element.innerText = texts[selector];
        } // else console.warn(`[Injector] Selector not found for text: ${selector}`);
    }

    for (const selector in attributes) {
        const element = document.querySelector(selector);
        if (element) {
            for (const attr in attributes[selector]) {
                element.setAttribute(attr, attributes[selector][attr]);
            }
            // Add error handler for images loaded via data URI
            if (element.tagName === 'IMG' && attributes[selector].src && attributes[selector].src.startsWith('data:image')) {
                element.onerror = () => {
                    console.warn(`[Injector] Error loading image data URI for ${selector}. Alt: ${element.alt}`);
                    // Optionally, set a fallback image or hide the element
                    // element.src = 'path/to/fallback/image.png';
                    // element.style.display = 'none';
                };
            }
        } // else console.warn(`[Injector] Selector not found for attributes: ${selector}`);
    }

    for (const selector in inner_htmls) {
        const element = document.querySelector(selector);
        if (element) {
            element.innerHTML = inner_htmls[selector];
        } // else console.warn(`[Injector] Selector not found for innerHTML: ${selector}`);
    }

    for (const selector in styles) {
        const element = document.querySelector(selector);
        if (element) {
            for (const prop in styles[selector]) {
                element.style[prop] = styles[selector][prop];
            }
        } // else console.warn(`[Injector] Selector not found for styles: ${selector}`);
    }
    
    for (const selector in display_toggles) {
        const element = document.querySelector(selector);
        if (element) {
            element.style.display = display_toggles[selector];
        } // else console.warn(`[Injector] Selector not found for display_toggle: ${selector}`);
    }
    // console.log("[Injector] Profile data injection complete via external script.");
} 