--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: fuzzystrmatch; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS fuzzystrmatch WITH SCHEMA public;


--
-- Name: EXTENSION fuzzystrmatch; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION fuzzystrmatch IS 'determine similarities and distance between strings';


--
-- Name: pg_stat_statements; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_stat_statements WITH SCHEMA public;


--
-- Name: EXTENSION pg_stat_statements; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pg_stat_statements IS 'track planning and execution statistics of all SQL statements executed';


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: asset_price_history; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.asset_price_history (
    id bigint NOT NULL,
    asset_id integer NOT NULL,
    price numeric(18,4) NOT NULL,
    "timestamp" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: asset_price_history_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.asset_price_history_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: asset_price_history_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.asset_price_history_id_seq OWNED BY public.asset_price_history.id;


--
-- Name: card_trade_history; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.card_trade_history (
    id integer NOT NULL,
    initiator_user_id bigint NOT NULL,
    receiver_user_id bigint NOT NULL,
    offered_master_card_id integer NOT NULL,
    offered_quantity integer DEFAULT 1 NOT NULL,
    requested_master_card_id integer,
    requested_quantity integer DEFAULT 1,
    price_amount integer,
    trade_type character varying(20) NOT NULL,
    fee_charged integer DEFAULT 0,
    completed_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT card_trade_history_trade_type_check CHECK (((trade_type)::text = ANY (ARRAY[('CARD_FOR_OIL'::character varying)::text, ('CARD_FOR_CARD'::character varying)::text, ('GIFT_CARD'::character varying)::text])))
);


--
-- Name: COLUMN card_trade_history.offered_master_card_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.card_trade_history.offered_master_card_id IS 'FK to gacha_master_cards.card_id for the card offered.';


--
-- Name: COLUMN card_trade_history.offered_quantity; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.card_trade_history.offered_quantity IS 'Quantity of the card offered by the initiator.';


--
-- Name: COLUMN card_trade_history.requested_master_card_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.card_trade_history.requested_master_card_id IS 'FK to gacha_master_cards.card_id for the card requested from receiver (if card-for-card).';


--
-- Name: COLUMN card_trade_history.requested_quantity; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.card_trade_history.requested_quantity IS 'Quantity of the card requested from receiver (if card-for-card).';


--
-- Name: COLUMN card_trade_history.price_amount; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.card_trade_history.price_amount IS 'Oil amount paid by receiver to initiator (if card-for-oil). Null for card-for-card or gift.';


--
-- Name: COLUMN card_trade_history.trade_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.card_trade_history.trade_type IS 'Type of P2P trade: CARD_FOR_OIL, CARD_FOR_CARD, GIFT_CARD.';


--
-- Name: COLUMN card_trade_history.fee_charged; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.card_trade_history.fee_charged IS 'Fee amount deducted from initiator in CARD_FOR_OIL trades.';


--
-- Name: card_trade_history_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.card_trade_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: card_trade_history_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.card_trade_history_id_seq OWNED BY public.card_trade_history.id;


--
-- Name: gacha_card_descriptions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.gacha_card_descriptions (
    id integer NOT NULL,
    user_id bigint NOT NULL,
    card_id integer NOT NULL,
    description character varying(150) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: gacha_card_descriptions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.gacha_card_descriptions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: gacha_card_descriptions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.gacha_card_descriptions_id_seq OWNED BY public.gacha_card_descriptions.id;


--
-- Name: gacha_card_highest_star; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.gacha_card_highest_star (
    card_id integer NOT NULL,
    user_id bigint,
    star_level integer DEFAULT 0 NOT NULL,
    achieved_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: gacha_card_market_stats; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.gacha_card_market_stats (
    card_id integer NOT NULL,
    total_owned_quantity bigint DEFAULT 0 NOT NULL,
    unique_owner_count integer DEFAULT 0 NOT NULL,
    wishlist_count integer DEFAULT 0 NOT NULL,
    favorite_count integer DEFAULT 0 NOT NULL,
    supply_demand_modifier numeric(10,4) DEFAULT 1.0000 NOT NULL,
    previous_sd_modifier numeric(10,4) DEFAULT 1.0000 NOT NULL,
    last_sd_calculated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: gacha_category_stock_influence; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.gacha_category_stock_influence (
    category_key character varying(150) NOT NULL,
    base_stock_modifier numeric(10,4) DEFAULT 1.0000 NOT NULL,
    temporary_news_modifier numeric(10,4) DEFAULT 1.0000 NOT NULL,
    news_effect_expiry timestamp with time zone,
    last_base_calculated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: gacha_master_cards; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.gacha_master_cards (
    card_id integer NOT NULL,
    original_id character varying(100),
    name character varying(100) NOT NULL,
    series character varying(100) NOT NULL,
    image_url character varying(255) NOT NULL,
    description text,
    sell_price integer DEFAULT 10 NOT NULL,
    creation_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    is_active boolean DEFAULT true,
    pool_type character varying(50) DEFAULT 'main'::character varying,
    rarity integer NOT NULL,
    price_config jsonb DEFAULT '{"max_modifier": 5.0, "min_modifier": 0.4, "stock_influence_weight": 1.0}'::jsonb,
    current_market_sell_price numeric(12,4) DEFAULT 0.00,
    last_price_update_at timestamp with time zone
);


--
-- Name: COLUMN gacha_master_cards.current_market_sell_price; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.gacha_master_cards.current_market_sell_price IS '預存的當前市場售價，由後台機制更新';


--
-- Name: COLUMN gacha_master_cards.last_price_update_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.gacha_master_cards.last_price_update_at IS '市場售價最後更新時間';


--
-- Name: gacha_master_cards_card_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.gacha_master_cards_card_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: gacha_master_cards_card_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.gacha_master_cards_card_id_seq OWNED BY public.gacha_master_cards.card_id;


--
-- Name: gacha_user_collections; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.gacha_user_collections (
    id integer NOT NULL,
    user_id bigint NOT NULL,
    card_id integer NOT NULL,
    quantity integer DEFAULT 1 NOT NULL,
    first_acquired timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    last_acquired timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    is_favorite boolean DEFAULT false NOT NULL,
    star_level integer DEFAULT 0 NOT NULL,
    custom_sort_index bigint
);


--
-- Name: gacha_user_collections_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.gacha_user_collections_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: gacha_user_collections_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.gacha_user_collections_id_seq OWNED BY public.gacha_user_collections.id;


--
-- Name: gacha_user_wishes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.gacha_user_wishes (
    id integer NOT NULL,
    user_id bigint NOT NULL,
    card_id integer NOT NULL,
    slot_index integer NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


--
-- Name: TABLE gacha_user_wishes; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.gacha_user_wishes IS '保存用戶的願望卡片資訊';


--
-- Name: COLUMN gacha_user_wishes.user_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.gacha_user_wishes.user_id IS '用戶ID，引用gacha_users表';


--
-- Name: COLUMN gacha_user_wishes.card_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.gacha_user_wishes.card_id IS '卡片ID，引用gacha_master_cards表';


--
-- Name: COLUMN gacha_user_wishes.slot_index; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.gacha_user_wishes.slot_index IS '願望槽位置，從1開始';


--
-- Name: COLUMN gacha_user_wishes.created_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.gacha_user_wishes.created_at IS '創建時間';


--
-- Name: gacha_user_wishes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.gacha_user_wishes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: gacha_user_wishes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.gacha_user_wishes_id_seq OWNED BY public.gacha_user_wishes.id;


--
-- Name: gacha_users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.gacha_users (
    user_id bigint NOT NULL,
    oil_balance integer DEFAULT 0 NOT NULL,
    total_draws integer DEFAULT 0 NOT NULL,
    last_daily_claim timestamp with time zone,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    nickname character varying(100),
    wish_slots integer DEFAULT 1 NOT NULL,
    wish_power_level integer DEFAULT 1 NOT NULL,
    oil_ticket_balance numeric(10,2) DEFAULT 0.00 NOT NULL,
    CONSTRAINT check_wish_power_level_range CHECK (((wish_power_level >= 1) AND (wish_power_level <= 10))),
    CONSTRAINT check_wish_slots_range CHECK (((wish_slots >= 1) AND (wish_slots <= 10)))
);


--
-- Name: COLUMN gacha_users.wish_slots; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.gacha_users.wish_slots IS '用戶已解鎖的願望槽數量，範圍1-10';


--
-- Name: COLUMN gacha_users.wish_power_level; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.gacha_users.wish_power_level IS '用戶的願望力量等級，範圍1-10';


--
-- Name: market_news; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.market_news (
    id integer NOT NULL,
    headline character varying(150) NOT NULL,
    content text NOT NULL,
    affected_asset_id integer,
    sentiment character varying(10) NOT NULL,
    published_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    source character varying(50) DEFAULT 'AI Analyst'::character varying,
    news_type text,
    character_archetype text,
    character_name text,
    impacts_price boolean,
    raw_data_input jsonb
);


--
-- Name: COLUMN market_news.source; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.market_news.source IS '代表新聞的「發布渠道」或「原始生成系統」。對於AI生成的新聞，可以統一為一個值，例如 "AI_NEWS_GENERATOR"。';


--
-- Name: market_news_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.market_news_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: market_news_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.market_news_id_seq OWNED BY public.market_news.id;


--
-- Name: market_transactions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.market_transactions (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    asset_id integer NOT NULL,
    transaction_type character varying(4) NOT NULL,
    quantity integer NOT NULL,
    price_per_unit numeric(18,4) NOT NULL,
    total_amount numeric(18,4) NOT NULL,
    fee numeric(18,4) DEFAULT 0 NOT NULL,
    "timestamp" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: market_transactions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.market_transactions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: market_transactions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.market_transactions_id_seq OWNED BY public.market_transactions.id;


--
-- Name: news_type_generation_log; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.news_type_generation_log (
    news_type text NOT NULL,
    last_generated_at timestamp with time zone NOT NULL
);


--
-- Name: player_portfolios; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.player_portfolios (
    id integer NOT NULL,
    user_id bigint NOT NULL,
    asset_id integer NOT NULL,
    quantity integer NOT NULL,
    average_buy_price numeric(18,4) NOT NULL,
    last_transaction_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: player_portfolios_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.player_portfolios_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: player_portfolios_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.player_portfolios_id_seq OWNED BY public.player_portfolios.id;


--
-- Name: user_profiles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_profiles (
    user_id bigint NOT NULL,
    showcased_card_collection_id integer,
    sub_card_1_collection_id integer,
    sub_card_2_collection_id integer,
    sub_card_3_collection_id integer,
    sub_card_4_collection_id integer,
    background_image_url text,
    like_count integer DEFAULT 0 NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    user_status character varying(150)
);


--
-- Name: TABLE user_profiles; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.user_profiles IS '儲存使用者的個人檔案設定';


--
-- Name: COLUMN user_profiles.user_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.user_profiles.user_id IS '使用者 ID，關聯 gacha_users.user_id';


--
-- Name: COLUMN user_profiles.showcased_card_collection_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.user_profiles.showcased_card_collection_id IS '主展示卡片在 gacha_user_collections 中的 ID';


--
-- Name: COLUMN user_profiles.sub_card_1_collection_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.user_profiles.sub_card_1_collection_id IS '副展示卡片 1 在 gacha_user_collections 中的 ID';


--
-- Name: COLUMN user_profiles.sub_card_2_collection_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.user_profiles.sub_card_2_collection_id IS '副展示卡片 2 在 gacha_user_collections 中的 ID';


--
-- Name: COLUMN user_profiles.sub_card_3_collection_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.user_profiles.sub_card_3_collection_id IS '副展示卡片 3 在 gacha_user_collections 中的 ID';


--
-- Name: COLUMN user_profiles.sub_card_4_collection_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.user_profiles.sub_card_4_collection_id IS '副展示卡片 4 在 gacha_user_collections 中的 ID';


--
-- Name: COLUMN user_profiles.background_image_url; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.user_profiles.background_image_url IS '自訂背景圖片的 URL';


--
-- Name: COLUMN user_profiles.like_count; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.user_profiles.like_count IS '此 Profile 收到的按讚總數';


--
-- Name: COLUMN user_profiles.created_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.user_profiles.created_at IS '記錄創建時間';


--
-- Name: COLUMN user_profiles.updated_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.user_profiles.updated_at IS '記錄最後更新時間';


--
-- Name: virtual_assets; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.virtual_assets (
    asset_id integer NOT NULL,
    asset_symbol character varying(10) NOT NULL,
    asset_name character varying(100) NOT NULL,
    description text,
    current_price numeric(18,4) DEFAULT 100.0000 NOT NULL,
    linked_criteria_type character varying(20),
    linked_criteria_value character varying(100),
    linked_pool_context character varying(50),
    base_volatility numeric(5,3) DEFAULT 0.010 NOT NULL,
    volatility_factor numeric(5,2) DEFAULT 1.5 NOT NULL,
    influence_weight numeric(5,2) DEFAULT 1.0 NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    last_updated timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    initial_anchor_price numeric DEFAULT 0.0 NOT NULL,
    current_anchor_price numeric,
    anchor_price_updated_at timestamp with time zone,
    lifecycle_status character varying(10) DEFAULT 'active'::character varying NOT NULL,
    total_shares bigint
);


--
-- Name: virtual_assets_asset_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.virtual_assets_asset_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: virtual_assets_asset_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.virtual_assets_asset_id_seq OWNED BY public.virtual_assets.asset_id;


--
-- Name: asset_price_history id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.asset_price_history ALTER COLUMN id SET DEFAULT nextval('public.asset_price_history_id_seq'::regclass);


--
-- Name: card_trade_history id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.card_trade_history ALTER COLUMN id SET DEFAULT nextval('public.card_trade_history_id_seq'::regclass);


--
-- Name: gacha_card_descriptions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_card_descriptions ALTER COLUMN id SET DEFAULT nextval('public.gacha_card_descriptions_id_seq'::regclass);


--
-- Name: gacha_master_cards card_id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_master_cards ALTER COLUMN card_id SET DEFAULT nextval('public.gacha_master_cards_card_id_seq'::regclass);


--
-- Name: gacha_user_collections id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_user_collections ALTER COLUMN id SET DEFAULT nextval('public.gacha_user_collections_id_seq'::regclass);


--
-- Name: gacha_user_wishes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_user_wishes ALTER COLUMN id SET DEFAULT nextval('public.gacha_user_wishes_id_seq'::regclass);


--
-- Name: market_news id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.market_news ALTER COLUMN id SET DEFAULT nextval('public.market_news_id_seq'::regclass);


--
-- Name: market_transactions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.market_transactions ALTER COLUMN id SET DEFAULT nextval('public.market_transactions_id_seq'::regclass);


--
-- Name: player_portfolios id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.player_portfolios ALTER COLUMN id SET DEFAULT nextval('public.player_portfolios_id_seq'::regclass);


--
-- Name: virtual_assets asset_id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.virtual_assets ALTER COLUMN asset_id SET DEFAULT nextval('public.virtual_assets_asset_id_seq'::regclass);


--
-- Name: asset_price_history asset_price_history_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.asset_price_history
    ADD CONSTRAINT asset_price_history_pkey PRIMARY KEY (id);


--
-- Name: card_trade_history card_trade_history_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.card_trade_history
    ADD CONSTRAINT card_trade_history_pkey PRIMARY KEY (id);


--
-- Name: gacha_card_descriptions gacha_card_descriptions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_card_descriptions
    ADD CONSTRAINT gacha_card_descriptions_pkey PRIMARY KEY (id);


--
-- Name: gacha_card_descriptions gacha_card_descriptions_user_id_card_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_card_descriptions
    ADD CONSTRAINT gacha_card_descriptions_user_id_card_id_key UNIQUE (user_id, card_id);


--
-- Name: gacha_card_highest_star gacha_card_highest_star_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_card_highest_star
    ADD CONSTRAINT gacha_card_highest_star_pkey PRIMARY KEY (card_id);


--
-- Name: gacha_card_market_stats gacha_card_market_stats_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_card_market_stats
    ADD CONSTRAINT gacha_card_market_stats_pkey PRIMARY KEY (card_id);


--
-- Name: gacha_category_stock_influence gacha_category_stock_influence_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_category_stock_influence
    ADD CONSTRAINT gacha_category_stock_influence_pkey PRIMARY KEY (category_key);


--
-- Name: gacha_master_cards gacha_master_cards_original_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_master_cards
    ADD CONSTRAINT gacha_master_cards_original_id_key UNIQUE (original_id);


--
-- Name: gacha_master_cards gacha_master_cards_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_master_cards
    ADD CONSTRAINT gacha_master_cards_pkey PRIMARY KEY (card_id);


--
-- Name: gacha_user_collections gacha_user_collections_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_user_collections
    ADD CONSTRAINT gacha_user_collections_pkey PRIMARY KEY (id);


--
-- Name: gacha_user_collections gacha_user_collections_user_id_card_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_user_collections
    ADD CONSTRAINT gacha_user_collections_user_id_card_id_key UNIQUE (user_id, card_id);


--
-- Name: gacha_user_wishes gacha_user_wishes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_user_wishes
    ADD CONSTRAINT gacha_user_wishes_pkey PRIMARY KEY (id);


--
-- Name: gacha_users gacha_users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_users
    ADD CONSTRAINT gacha_users_pkey PRIMARY KEY (user_id);


--
-- Name: market_news market_news_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.market_news
    ADD CONSTRAINT market_news_pkey PRIMARY KEY (id);


--
-- Name: market_transactions market_transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.market_transactions
    ADD CONSTRAINT market_transactions_pkey PRIMARY KEY (id);


--
-- Name: news_type_generation_log news_type_generation_log_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.news_type_generation_log
    ADD CONSTRAINT news_type_generation_log_pkey PRIMARY KEY (news_type);


--
-- Name: player_portfolios player_portfolios_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.player_portfolios
    ADD CONSTRAINT player_portfolios_pkey PRIMARY KEY (id);


--
-- Name: player_portfolios player_portfolios_user_id_asset_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.player_portfolios
    ADD CONSTRAINT player_portfolios_user_id_asset_id_key UNIQUE (user_id, asset_id);


--
-- Name: gacha_user_wishes unique_user_card; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_user_wishes
    ADD CONSTRAINT unique_user_card UNIQUE (user_id, card_id);


--
-- Name: gacha_user_wishes unique_user_slot; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_user_wishes
    ADD CONSTRAINT unique_user_slot UNIQUE (user_id, slot_index);


--
-- Name: user_profiles user_profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT user_profiles_pkey PRIMARY KEY (user_id);


--
-- Name: virtual_assets virtual_assets_asset_symbol_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.virtual_assets
    ADD CONSTRAINT virtual_assets_asset_symbol_key UNIQUE (asset_symbol);


--
-- Name: virtual_assets virtual_assets_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.virtual_assets
    ADD CONSTRAINT virtual_assets_pkey PRIMARY KEY (asset_id);


--
-- Name: idx_aph_asset_timestamp; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_aph_asset_timestamp ON public.asset_price_history USING btree (asset_id, "timestamp" DESC);


--
-- Name: idx_cth_completed_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_cth_completed_at ON public.card_trade_history USING btree (completed_at DESC);


--
-- Name: idx_cth_initiator; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_cth_initiator ON public.card_trade_history USING btree (initiator_user_id);


--
-- Name: idx_cth_offered_card; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_cth_offered_card ON public.card_trade_history USING btree (offered_master_card_id);


--
-- Name: idx_cth_receiver; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_cth_receiver ON public.card_trade_history USING btree (receiver_user_id);


--
-- Name: idx_cth_requested_card; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_cth_requested_card ON public.card_trade_history USING btree (requested_master_card_id);


--
-- Name: idx_cth_trade_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_cth_trade_type ON public.card_trade_history USING btree (trade_type);


--
-- Name: idx_gacha_card_highest_star_user; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_gacha_card_highest_star_user ON public.gacha_card_highest_star USING btree (user_id);


--
-- Name: idx_gacha_collections_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_gacha_collections_user_id ON public.gacha_user_collections USING btree (user_id);


--
-- Name: idx_gacha_master_cards_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_gacha_master_cards_active ON public.gacha_master_cards USING btree (is_active);


--
-- Name: idx_gacha_master_cards_rarity; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_gacha_master_cards_rarity ON public.gacha_master_cards USING btree (rarity);


--
-- Name: idx_gacha_master_cards_series; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_gacha_master_cards_series ON public.gacha_master_cards USING btree (series);


--
-- Name: idx_gacha_master_cards_series_pool; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_gacha_master_cards_series_pool ON public.gacha_master_cards USING btree (series, pool_type);


--
-- Name: idx_gacha_user_cards_group; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_gacha_user_cards_group ON public.gacha_user_collections USING btree (user_id, card_id, quantity);


--
-- Name: idx_gacha_user_collections_card_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_gacha_user_collections_card_id ON public.gacha_user_collections USING btree (card_id);


--
-- Name: idx_gacha_user_collections_favorite; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_gacha_user_collections_favorite ON public.gacha_user_collections USING btree (user_id, is_favorite);


--
-- Name: idx_gacha_user_collections_last_acquired; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_gacha_user_collections_last_acquired ON public.gacha_user_collections USING btree (user_id, last_acquired DESC);


--
-- Name: idx_gacha_user_collections_star_level; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_gacha_user_collections_star_level ON public.gacha_user_collections USING btree (user_id, star_level);


--
-- Name: idx_gacha_users_oil_ticket_balance; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_gacha_users_oil_ticket_balance ON public.gacha_users USING btree (oil_ticket_balance);


--
-- Name: idx_gcsi_last_base_calculated; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_gcsi_last_base_calculated ON public.gacha_category_stock_influence USING btree (last_base_calculated_at);


--
-- Name: idx_gcsi_news_effect_expiry; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_gcsi_news_effect_expiry ON public.gacha_category_stock_influence USING btree (news_effect_expiry) WHERE (news_effect_expiry IS NOT NULL);


--
-- Name: idx_gmc_is_active_rarity_card_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_gmc_is_active_rarity_card_id ON public.gacha_master_cards USING btree (is_active, rarity, card_id);


--
-- Name: idx_guc_for_join_agg; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_guc_for_join_agg ON public.gacha_user_collections USING btree (card_id, user_id, quantity);


--
-- Name: idx_market_news_character_archetype; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_market_news_character_archetype ON public.market_news USING btree (character_archetype);


--
-- Name: idx_market_news_news_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_market_news_news_type ON public.market_news USING btree (news_type);


--
-- Name: idx_market_transactions_user; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_market_transactions_user ON public.market_transactions USING btree (user_id);


--
-- Name: idx_mn_affected_asset; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_mn_affected_asset ON public.market_news USING btree (affected_asset_id);


--
-- Name: idx_mn_published_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_mn_published_at ON public.market_news USING btree (published_at DESC);


--
-- Name: idx_mt_asset_timestamp; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_mt_asset_timestamp ON public.market_transactions USING btree (asset_id, "timestamp" DESC);


--
-- Name: idx_mt_user_asset; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_mt_user_asset ON public.market_transactions USING btree (user_id, asset_id);


--
-- Name: idx_player_portfolios_asset_quantity; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_player_portfolios_asset_quantity ON public.player_portfolios USING btree (asset_id, quantity DESC);


--
-- Name: idx_user_card_sort; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_card_sort ON public.gacha_user_collections USING btree (user_id, card_id, is_favorite);


--
-- Name: idx_user_cards_sort; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_cards_sort ON public.gacha_user_collections USING btree (user_id, custom_sort_index);


--
-- Name: idx_user_profiles_showcased_card; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_profiles_showcased_card ON public.user_profiles USING btree (showcased_card_collection_id);


--
-- Name: idx_user_profiles_sub_card_1; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_profiles_sub_card_1 ON public.user_profiles USING btree (sub_card_1_collection_id);


--
-- Name: idx_user_profiles_sub_card_2; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_profiles_sub_card_2 ON public.user_profiles USING btree (sub_card_2_collection_id);


--
-- Name: idx_user_profiles_sub_card_3; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_profiles_sub_card_3 ON public.user_profiles USING btree (sub_card_3_collection_id);


--
-- Name: idx_user_profiles_sub_card_4; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_profiles_sub_card_4 ON public.user_profiles USING btree (sub_card_4_collection_id);


--
-- Name: idx_user_wishes_card_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_wishes_card_id ON public.gacha_user_wishes USING btree (card_id);


--
-- Name: idx_user_wishes_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_wishes_user_id ON public.gacha_user_wishes USING btree (user_id);


--
-- Name: idx_va_linked_criteria; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_va_linked_criteria ON public.virtual_assets USING btree (linked_criteria_type, linked_criteria_value, linked_pool_context);


--
-- Name: user_profiles update_user_profiles_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON public.user_profiles FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: asset_price_history asset_price_history_asset_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.asset_price_history
    ADD CONSTRAINT asset_price_history_asset_id_fkey FOREIGN KEY (asset_id) REFERENCES public.virtual_assets(asset_id) ON DELETE CASCADE;


--
-- Name: user_profiles fk_showcased_card; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT fk_showcased_card FOREIGN KEY (showcased_card_collection_id) REFERENCES public.gacha_user_collections(id) ON DELETE SET NULL;


--
-- Name: user_profiles fk_sub_card_1; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT fk_sub_card_1 FOREIGN KEY (sub_card_1_collection_id) REFERENCES public.gacha_user_collections(id) ON DELETE SET NULL;


--
-- Name: user_profiles fk_sub_card_2; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT fk_sub_card_2 FOREIGN KEY (sub_card_2_collection_id) REFERENCES public.gacha_user_collections(id) ON DELETE SET NULL;


--
-- Name: user_profiles fk_sub_card_3; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT fk_sub_card_3 FOREIGN KEY (sub_card_3_collection_id) REFERENCES public.gacha_user_collections(id) ON DELETE SET NULL;


--
-- Name: user_profiles fk_sub_card_4; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT fk_sub_card_4 FOREIGN KEY (sub_card_4_collection_id) REFERENCES public.gacha_user_collections(id) ON DELETE SET NULL;


--
-- Name: card_trade_history fk_trade_history_initiator_user; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.card_trade_history
    ADD CONSTRAINT fk_trade_history_initiator_user FOREIGN KEY (initiator_user_id) REFERENCES public.gacha_users(user_id) ON DELETE SET NULL;


--
-- Name: card_trade_history fk_trade_history_offered_card; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.card_trade_history
    ADD CONSTRAINT fk_trade_history_offered_card FOREIGN KEY (offered_master_card_id) REFERENCES public.gacha_master_cards(card_id) ON DELETE RESTRICT;


--
-- Name: card_trade_history fk_trade_history_receiver_user; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.card_trade_history
    ADD CONSTRAINT fk_trade_history_receiver_user FOREIGN KEY (receiver_user_id) REFERENCES public.gacha_users(user_id) ON DELETE SET NULL;


--
-- Name: card_trade_history fk_trade_history_requested_card; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.card_trade_history
    ADD CONSTRAINT fk_trade_history_requested_card FOREIGN KEY (requested_master_card_id) REFERENCES public.gacha_master_cards(card_id) ON DELETE RESTRICT;


--
-- Name: user_profiles fk_user; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES public.gacha_users(user_id) ON DELETE CASCADE;


--
-- Name: gacha_card_descriptions gacha_card_descriptions_card_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_card_descriptions
    ADD CONSTRAINT gacha_card_descriptions_card_id_fkey FOREIGN KEY (card_id) REFERENCES public.gacha_master_cards(card_id) ON DELETE CASCADE;


--
-- Name: gacha_card_descriptions gacha_card_descriptions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_card_descriptions
    ADD CONSTRAINT gacha_card_descriptions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.gacha_users(user_id) ON DELETE CASCADE;


--
-- Name: gacha_card_highest_star gacha_card_highest_star_card_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_card_highest_star
    ADD CONSTRAINT gacha_card_highest_star_card_id_fkey FOREIGN KEY (card_id) REFERENCES public.gacha_master_cards(card_id) ON DELETE CASCADE;


--
-- Name: gacha_card_highest_star gacha_card_highest_star_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_card_highest_star
    ADD CONSTRAINT gacha_card_highest_star_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.gacha_users(user_id) ON DELETE SET NULL;


--
-- Name: gacha_card_market_stats gacha_card_market_stats_card_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_card_market_stats
    ADD CONSTRAINT gacha_card_market_stats_card_id_fkey FOREIGN KEY (card_id) REFERENCES public.gacha_master_cards(card_id) ON DELETE CASCADE;


--
-- Name: gacha_user_collections gacha_user_collections_card_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_user_collections
    ADD CONSTRAINT gacha_user_collections_card_id_fkey FOREIGN KEY (card_id) REFERENCES public.gacha_master_cards(card_id) ON DELETE CASCADE;


--
-- Name: gacha_user_collections gacha_user_collections_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_user_collections
    ADD CONSTRAINT gacha_user_collections_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.gacha_users(user_id) ON DELETE CASCADE;


--
-- Name: gacha_user_wishes gacha_user_wishes_card_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_user_wishes
    ADD CONSTRAINT gacha_user_wishes_card_id_fkey FOREIGN KEY (card_id) REFERENCES public.gacha_master_cards(card_id);


--
-- Name: gacha_user_wishes gacha_user_wishes_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.gacha_user_wishes
    ADD CONSTRAINT gacha_user_wishes_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.gacha_users(user_id);


--
-- Name: market_news market_news_affected_asset_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.market_news
    ADD CONSTRAINT market_news_affected_asset_id_fkey FOREIGN KEY (affected_asset_id) REFERENCES public.virtual_assets(asset_id);


--
-- Name: market_transactions market_transactions_asset_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.market_transactions
    ADD CONSTRAINT market_transactions_asset_id_fkey FOREIGN KEY (asset_id) REFERENCES public.virtual_assets(asset_id);


--
-- Name: market_transactions market_transactions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.market_transactions
    ADD CONSTRAINT market_transactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.gacha_users(user_id);


--
-- Name: player_portfolios player_portfolios_asset_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.player_portfolios
    ADD CONSTRAINT player_portfolios_asset_id_fkey FOREIGN KEY (asset_id) REFERENCES public.virtual_assets(asset_id) ON DELETE CASCADE;


--
-- Name: player_portfolios player_portfolios_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.player_portfolios
    ADD CONSTRAINT player_portfolios_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.gacha_users(user_id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

