"""
Gacha 系統尋寶礦區遊戲回調處理器
"""
from typing import TYPE_CHECKING, Optional, Dict, Any, Callable
import discord
import logging
from utils.logger import logger
from gacha.utils import interaction_utils
from .base_game_handler import BaseGameCallbackHandler
from gacha.views.embeds.games.mines_embed_builder import build_mines_game_embed
from gacha.views.embeds.games.mines_mode_select_embed_builder import build_mode_selection_embed
from gacha.utils.mines_constants import MIN_BET, MAX_BET
from gacha.exceptions import (
    MinBetNotMetError, 
    InsufficientFundsError, 
    UserNotFoundError,
    GameSettlementError
)
from ..modals import MinesCustomBetModal
if TYPE_CHECKING:
    from gacha.services.games.mines_service import MinesService
    from gacha.services.core.economy_service import EconomyService
    from gacha.services.ui.game_display_service import GameDisplayService
    from gacha.views.collection.collection_view import InteractionManager
    from gacha.views.games.mines_view import MinesModeSelectView
    from gacha.views.games.mines_game_view import MinesGameView
from gacha.views.games.mines_game_view import MinesGameView
from gacha.services.ui.game_display_service import GameDisplayService

class MinesCallbackHandler(BaseGameCallbackHandler):
    """處理尋寶礦區遊戲模式選擇、遊戲進行和結束回調的類"""

    def __init__(self, interaction_manager: 'InteractionManager', user_id: int, mines_service: 'MinesService', economy_service: 'EconomyService', game_display_service: 'GameDisplayService', mode_view: Optional['MinesModeSelectView']=None, game_view: Optional['MinesGameView']=None):
        """
        初始化尋寶礦區回調處理器

        參數:
            interaction_manager: InteractionManager 實例
            user_id: 觸發遊戲的原始用戶 ID
            mines_service: MinesService 實例
            economy_service: EconomyService 實例
            game_display_service: GameDisplayService 實例
            mode_view: 可選的 MinesModeSelectView 實例
            game_view: 可選的 MinesGameView 實例
        """
        super().__init__(interaction_manager, user_id)
        self.mines_service = mines_service
        self.economy_service = economy_service
        self.game_display_service = game_display_service
        self.mode_view = mode_view
        self.game_view = game_view

    async def handle_mode_selection(self, interaction: discord.Interaction, mine_count: int):
        """處理模式選擇按鈕點擊 (來自 MinesModeSelectView)"""
        if not await interaction_utils.check_user_permission(interaction, self.user_id):
            return
        if not self.mode_view:
            logger.error('[MinesModeSelect] Mode view is not set in handler.')
            await interaction_utils.safe_send_message(interaction, '處理模式選擇時發生內部錯誤 (View 未設置)。', ephemeral=True)
            return
        final_bet = self.mode_view.current_bet
        if not interaction.response.is_done():
            # Defer with thinking=False because we will edit the original message
            await interaction_utils.safe_defer(interaction, ephemeral=False, thinking=False) 

        # Disable buttons on the mode selection view as we are transitioning
        for item in self.mode_view.children:
            if isinstance(item, discord.ui.Button):
                item.disabled = True
        
        try:
            # MinesService.create_game 現在返回 MinesGame 實例或拋出異常
            new_game_instance = await self.mines_service.create_game(user_id=self.user_id, bet_amount=final_bet, mine_count=mine_count)
            
            game_state = new_game_instance.get_state_summary()
            
            # 消息與原始服務層GAME_CREATED一致
            message = f'尋寶礦區遊戲已開始！您的賭注是 {final_bet:,.0f} 油幣，共有 {mine_count} 個地雷。祝您好運！' 
            
            game_view = MinesGameView(handler=self, game_state=game_state, player=interaction.user)
            self.game_view = game_view # Store the new game view instance
            
            # 獲取當前餘額 (假設 economy_service.get_balance 返回 int)
            balance_data = await self.economy_service.get_balance(self.user_id)
            current_balance = balance_data.get('balance', 0)
            
            embed = build_mines_game_embed(game_state=game_state, player=interaction.user, current_balance=current_balance)
            
            # 編輯原始互動訊息以顯示遊戲界面
            await interaction_utils.safe_edit_message(interaction=interaction, content=message, embed=embed, view=game_view)
            
            if self.mode_view:
                self.mode_view.stop() # Stop the old mode selection view
                
        except (InsufficientFundsError, MinBetNotMetError, UserNotFoundError, GameSettlementError, ValueError) as specific_error: # Added ValueError for MinesGame init
            # 處理特定的遊戲相關異常
            from gacha.utils.cog_error_handler import handle_gacha_error
            # 傳遞 interaction 而不是 ctx
            await handle_gacha_error(interaction, specific_error, '啟動尋寶礦區遊戲時')
            if self.mode_view: # 確保即使出錯也停止舊視圖
                # Re-enable buttons on error so the user can try again or change settings
                for item in self.mode_view.children:
                    if isinstance(item, discord.ui.Button):
                        item.disabled = False
                # Update the view with the re-enabled buttons
                # Check if interaction is original or followup
                if interaction.message:
                    await interaction_utils.safe_edit_message(interaction=interaction, view=self.mode_view)
                # else: This case (no interaction.message) should ideally not happen if we deferred an original interaction
                # self.mode_view.stop() # Or stop it if we don't want them to retry from the same view
        except Exception as e:
            logger.error('處理模式選擇並啟動遊戲時發生未知異常: %s', e, exc_info=True)
            from gacha.utils.cog_error_handler import handle_gacha_error
            await handle_gacha_error(interaction, e, '啟動尋寶礦區遊戲時')
            if self.mode_view: # 確保即使出錯也停止舊視圖
                 # Re-enable buttons on error
                for item in self.mode_view.children:
                    if isinstance(item, discord.ui.Button):
                        item.disabled = False
                if interaction.message:
                    await interaction_utils.safe_edit_message(interaction=interaction, view=self.mode_view)

    async def handle_custom_bet_modal(self, interaction: discord.Interaction):
        """處理 '自訂金額' 按鈕點擊，彈出模態框 (來自 MinesModeSelectView)"""
        if not await interaction_utils.check_user_permission(interaction, self.user_id):
            return
        if not self.mode_view:
            logger.error('[MinesCustomBet] Mode view is not set in handler.')
            await interaction_utils.safe_send_message(interaction, '打開自訂金額時發生內部錯誤 (View 未設置)。', ephemeral=True)
            return
        try:
            modal = MinesCustomBetModal(handler=self)
            await interaction.response.send_modal(modal)
        except Exception as e:
            logger.error('處理自訂金額模態框時發生異常: %s', e, exc_info=True)
            from gacha.utils.cog_error_handler import handle_gacha_error
            await handle_gacha_error(interaction, e, '打開自訂金額模態框時')

    async def handle_custom_bet_submit(self, interaction: discord.Interaction, modal: MinesCustomBetModal, bet_amount_str: str):
        """處理自訂金額模態框提交"""
        if not self.mode_view:
            logger.error('[MinesCustomBetSubmit] Mode view is not set in handler during modal submit.')
            return
        try:
            bet_amount = int(bet_amount_str)
            if bet_amount < MIN_BET or bet_amount > MAX_BET:
                # 這裡不使用異常，因為這是預期的業務邏輯檢查
                await interaction_utils.safe_send_message(interaction, f'下注金額必須在 {MIN_BET} 到 {MAX_BET} 之間。', ephemeral=True)
                return
                
            self.mode_view.current_bet = bet_amount
            balance_data = await self.economy_service.get_balance(self.user_id)
            current_balance = balance_data.get('balance', 0)
            embed = build_mode_selection_embed(current_bet=bet_amount, current_balance=current_balance, player=interaction.user)
            await interaction.response.edit_message(embed=embed, view=self.mode_view)
        except ValueError:
            # 數值轉換錯誤，使用友好的錯誤訊息
            await interaction_utils.safe_send_message(interaction, '請輸入有效的數字金額。', ephemeral=True)
        except Exception as e:
            logger.error('處理自訂金額提交時發生異常: %s', e, exc_info=True)
            from gacha.utils.cog_error_handler import handle_gacha_error
            await handle_gacha_error(interaction, e, '處理自訂金額提交時')

    async def handle_tile_click(self, interaction: discord.Interaction, row: int, col: int, game_instance_id: str, view: MinesGameView):
        """
        處理方塊按鈕點擊 (來自 MinesGameView)

        參數:
            interaction: discord.Interaction
            row: 點擊的行
            col: 點擊的列
            game_instance_id: 由 View 傳遞的遊戲實例 ID
            view: 觸發此互動的 MinesGameView 實例
        """
        if not await interaction_utils.check_user_permission(interaction, self.user_id):
            return
        # No longer need to check self.game_view here as game_instance_id is passed directly
        if not game_instance_id:
             logger.error('[MinesTileClick] Received empty game_instance_id for user %s interaction %s.', self.user_id, interaction.id)
             await interaction_utils.safe_send_message(interaction, '處理點擊時發生內部錯誤 (遊戲實例ID丟失)。', ephemeral=True)
             return
        # Defer interaction
        await interaction_utils.safe_defer(interaction)
        
        try:
            # Use the passed game_instance_id directly
            revealed_game_instance, raw_reveal_outcome, updated_balance_after_reveal = \
                await self.mines_service.reveal_tile(interaction, game_instance_id, row, col)
            current_game_state = revealed_game_instance.get_state_summary()
            self.game_view.game_state = current_game_state 
            service_message = '' 
            final_display_message_for_embed = '' 

            if revealed_game_instance.game_over:
                awarded_amount = revealed_game_instance.final_winnings if revealed_game_instance.final_winnings is not None else 0.0
                bet_amount_for_display = current_game_state.get('bet_amount', 0)
                game_name_for_display = '尋寶礦區'

                # 根據 revealed_game_instance.last_result 構建訊息
                if revealed_game_instance.last_result == 'LOSS': # MINE_HIT
                    service_message = '哎呀！您踩到了地雷 💥！遊戲結束，本次未能獲得任何油幣。'
                elif revealed_game_instance.last_result == 'WIN': # MAX_WIN_REACHED
                    service_message = f'恭喜！您已達到最大贏利上限！自動為您提現 {awarded_amount:,.0f} 油幣！'
                # 其他遊戲結束情況 (例如錯誤，但服務層應拋出異常)
                # 或者如果 last_result 未明確設置，但 game.game_over 為 True (例如 cash_out 後的狀態)
                # 這裡我們專注於 reveal_tile 導致的遊戲結束
                else:
                    service_message = f'遊戲結束。最終獲得 {awarded_amount:,.0f} 油幣。'
                final_display_message_for_embed = service_message # 通常情況下，這兩個消息可以相同
                
                display_balance_val = updated_balance_after_reveal
                if display_balance_val is None:
                    balance_data = await self.economy_service.get_balance(self.user_id)
                    display_balance_val = balance_data.get('balance', 0)

                def adapted_embed_builder(gs_param: Dict[str, Any], bet_param: int) -> discord.Embed:
                    return build_mines_game_embed(game_state=gs_param, player=interaction.user, current_balance=display_balance_val, custom_message_on_end=final_display_message_for_embed)
                
                async def adapted_view_provider() -> MinesGameView:
                    return MinesGameView(handler=self, game_state=current_game_state, player=interaction.user)
                    
                await self.game_display_service.display_game_end(
                    interaction=interaction, 
                    game_name=game_name_for_display, 
                    game_result=current_game_state, 
                    original_bet=bet_amount_for_display, 
                    view_provider=adapted_view_provider, 
                    game_specific_embed_builder=adapted_embed_builder,
                    awarded_amount=awarded_amount 
                )
                if self.game_view:
                    self.game_view.stop()
                return
            
            # 遊戲未結束, 根據 raw_reveal_outcome 构建 service_message
            result_type = raw_reveal_outcome.get('result_type')
            current_multiplier = current_game_state.get('current_multiplier', 1.0)
            immediate_reward = raw_reveal_outcome.get('immediate_reward', 0)

            if result_type == 'SAFE':
                if raw_reveal_outcome.get('is_special_coin'):
                    service_message = f'幸運！您找到了一個特殊金幣 💰，額外獲得 {immediate_reward:,.0f} 油幣！目前乘數: {current_multiplier:.2f}x。'
                elif raw_reveal_outcome.get('is_special_star'):
                    service_message = f'太棒了！您找到了一個特殊星星 ⭐，額外獲得 {immediate_reward:,.0f} 油幣！目前乘數: {current_multiplier:.2f}x。'
                else: # SAFE_TILE_NORMAL
                    service_message = f'安全！您找到了一個安全格 💎。目前乘數: {current_multiplier:.2f}x。'
            elif result_type == 'ALREADY_REVEALED':
                service_message = '這個格子已經開過了。'
            # revealed_game_instance.game_over 應該已經處理了 'MINE_HIT' 和 'MAX_WIN_REACHED'
            # 所以這裡不需要再處理 'GAME_OVER' 類型的 raw_reveal_outcome
            else: 
                service_message = '點擊處理時發生未知情況。' # Fallback
                logger.warning(f"[MinesTileClick] Unexpected raw_reveal_outcome type: {result_type} for game {game_instance_id} when game not over.")

            # Update view buttons based on the new game_state using the passed view instance
            if isinstance(view, MinesGameView): # Use the passed 'view'
                revealed_tiles_set = set((tuple(pos) for pos in current_game_state.get('revealed_tiles', [])))
                board_display = current_game_state.get('board_display', [])
                for item in view.children: # Use 'view.children'
                    if isinstance(item, discord.ui.Button) and item.custom_id.startswith('mines_tile_'):
                        try:
                            r_str, c_str = item.custom_id.split('_')[2:]
                            r_int, c_int = (int(r_str), int(c_str))
                            if (r_int, c_int) in revealed_tiles_set:
                                item.disabled = True
                                if r_int < len(board_display) and c_int < len(board_display[r_int]):
                                    item.emoji = board_display[r_int][c_int]
                                item.style = discord.ButtonStyle.primary
                        except (IndexError, ValueError):
                            pass
                    elif isinstance(item, discord.ui.Button) and item.custom_id == 'mines_cash_out':
                        item.disabled = current_game_state.get('found_safe_tiles_count', 0) == 0 or current_game_state.get('game_over', False)

                display_balance_val_non_over = updated_balance_after_reveal
                if display_balance_val_non_over is None:
                    balance_data = await self.economy_service.get_balance(self.user_id)
                    display_balance_val_non_over = balance_data.get('balance', 0)

                embed = build_mines_game_embed(current_game_state, interaction.user, current_balance=display_balance_val_non_over)
                await interaction_utils.safe_edit_message(interaction, content=service_message, embed=embed, view=view) # Pass 'view' here
            else:
                # This else block might be less likely now since 'view' is passed directly,
                # but kept for safety. Log that the passed view was not the expected type.
                logger.warning(f"[MinesTileClick] Passed view object was not a MinesGameView instance for game {game_instance_id}. View Type: {type(view)}")
                # Fallback: Edit message without view update
                display_balance_val_non_over = updated_balance_after_reveal
                if display_balance_val_non_over is None:
                    balance_data = await self.economy_service.get_balance(self.user_id)
                    display_balance_val_non_over = balance_data.get('balance', 0)
                embed = build_mines_game_embed(current_game_state, interaction.user, current_balance=display_balance_val_non_over)
                await interaction_utils.safe_edit_message(interaction, content=service_message, embed=embed, view=None)

        except (GameSettlementError, UserNotFoundError, InsufficientFundsError) as specific_error:
            logger.error('處理方塊點擊時發生已知遊戲異常: %s', specific_error, exc_info=True)
            from gacha.utils.cog_error_handler import handle_gacha_error
            await handle_gacha_error(interaction, specific_error, '處理尋寶礦區方塊點擊時')
            # Stop the specific view associated with this interaction on error using the passed 'view'
            if isinstance(view, MinesGameView) and view.game_instance_id == game_instance_id:
                view.stop()

        except Exception as e:
            logger.error('處理方塊點擊時發生未知異常: %s', e, exc_info=True)
            from gacha.utils.cog_error_handler import handle_gacha_error
            await handle_gacha_error(interaction, e, '處理尋寶礦區方塊點擊時')
            # Stop the specific view associated with this interaction on unknown error using the passed 'view'
            if isinstance(view, MinesGameView) and view.game_instance_id == game_instance_id:
                view.stop()

    async def handle_cash_out(self, interaction: discord.Interaction, game_instance_id: str, view: MinesGameView):
        """
        處理提現按鈕點擊 (來自 MinesGameView)

        參數:
            interaction: discord.Interaction
            game_instance_id: 由 View 傳遞的遊戲實例 ID
            view: 觸發此互動的 MinesGameView 實例
        """
        if not await interaction_utils.check_user_permission(interaction, self.user_id):
            return
        # No longer need to check self.game_view here as game_instance_id is passed directly
        if not game_instance_id:
            logger.error('[MinesCashOut] Received empty game_instance_id for user %s interaction %s.', self.user_id, interaction.id)
            await interaction_utils.safe_send_message(interaction, '處理提現時發生內部錯誤 (遊戲實例ID丟失)。', ephemeral=True)
            return
        # Defer interaction
        await interaction_utils.safe_defer(interaction)

        try:
            # Use the passed game_instance_id directly
            cashed_out_game_instance, final_winnings_amount, final_balance_after_cashout = \
                await self.mines_service.cash_out(interaction, game_instance_id)
            game_state_after_cashout = cashed_out_game_instance.get_state_summary()
            message_on_end = ''

            # 根據 cashed_out_game_instance.last_result 和 final_winnings_amount 構建訊息
            if cashed_out_game_instance.last_result == 'CASH_OUT_ZERO':
                message_on_end = '您尚未找到任何寶藏，無法提現。遊戲已結束。'
            elif final_winnings_amount > 0: # Corresponds to CASH_OUT_SUCCESS in old service message builder
                message_on_end = f'提現成功！您成功帶走了 {final_winnings_amount:,.0f} 油幣！'
            # MinesService.cash_out 在其他情況下 (例如 game_over) 或結算失敗時會拋異常，所以這裡的 else 應該不常觸發
            else:
                message_on_end = '提現處理完成。遊戲已結束。' # Fallback message
                logger.info(f'[MinesCashOut] Cash out for game {game_instance_id} resulted in {final_winnings_amount} winnings, last_result: {cashed_out_game_instance.last_result}')

            awarded_amount = final_winnings_amount
            bet_amount_for_display = game_state_after_cashout.get('bet_amount', 0)
            game_name_for_display = '尋寶礦區'
            display_balance_val_cashout = final_balance_after_cashout
            if display_balance_val_cashout is None:
                balance_data = await self.economy_service.get_balance(self.user_id)
                display_balance_val_cashout = balance_data.get('balance', 0)

            def adapted_embed_builder(gs_param: Dict[str, Any], bet_param: int) -> discord.Embed:
                return build_mines_game_embed(game_state=gs_param, player=interaction.user, current_balance=display_balance_val_cashout, custom_message_on_end=message_on_end)

            async def adapted_view_provider() -> MinesGameView:
                return MinesGameView(handler=self, game_state=game_state_after_cashout, player=interaction.user)

            await self.game_display_service.display_game_end(
                interaction=interaction, game_name=game_name_for_display, game_result=game_state_after_cashout,
                original_bet=bet_amount_for_display, view_provider=adapted_view_provider,
                game_specific_embed_builder=adapted_embed_builder, awarded_amount=awarded_amount
            )
            # Stop the specific view associated with this interaction using the passed 'view'
            if isinstance(view, MinesGameView) and view.game_instance_id == game_instance_id:
                view.stop()

        except (GameSettlementError, UserNotFoundError) as specific_error:
            logger.error('處理提現時發生已知遊戲異常: %s', specific_error, exc_info=True)
            from gacha.utils.cog_error_handler import handle_gacha_error
            await handle_gacha_error(interaction, specific_error, '處理尋寶礦區提現時')
            # Stop the specific view associated with this interaction on error using the passed 'view'
            if isinstance(view, MinesGameView) and view.game_instance_id == game_instance_id:
                view.stop()

        except Exception as e:
            logger.error('處理提現時發生未知異常: %s', e, exc_info=True)
            from gacha.utils.cog_error_handler import handle_gacha_error
            await handle_gacha_error(interaction, e, '處理尋寶礦區提現時')
            # Stop the specific view associated with this interaction on unknown error using the passed 'view'
            if isinstance(view, MinesGameView) and view.game_instance_id == game_instance_id:
                view.stop()

    async def handle_play_again(self, interaction: discord.Interaction, game_state: dict):
        """處理 '再來一局' 按鈕點擊 (來自 display_game_end_options)"""
        if not await interaction_utils.check_user_permission(interaction, self.user_id):
            return
            
        await interaction_utils.safe_defer(interaction) # Defer before intensive operations
        
        current_bet = game_state.get('bet_amount', MIN_BET)
        current_mine_count = game_state.get('mine_count', 3) # Default to 3 mines if not in old state
        
        try:
            # 檢查餘額是否足夠 - 這一檢查仍然有效
            # 假設 economy_service.get_balance 返回 int
            balance_data_play_again = await self.economy_service.get_balance(self.user_id)
            latest_balance = balance_data_play_again.get('balance', 0)
            if latest_balance < current_bet:
                await interaction_utils.safe_send_message(interaction, f'你的油幣不足以進行 {current_bet:,.0f} 油幣的下注！(當前餘額: {latest_balance:,.0f})', ephemeral=True)
                # Potentially re-enable buttons on the previous game end view if it's still active or has a message reference
                # For now, just returning.
                return
                
            # MinesService.create_game 現在返回 MinesGame 實例或拋出異常
            new_game_instance = await self.mines_service.create_game(user_id=self.user_id, bet_amount=current_bet, mine_count=current_mine_count)
            
            new_game_state_summary = new_game_instance.get_state_summary()
            
            # "新的" 字樣在再來一局情境下合理
            message = f'新的尋寶礦區遊戲已開始！您的賭注是 {current_bet:,.0f} 油幣，共有 {current_mine_count} 個地雷。祝您好運！' 
            
            # 創建新的遊戲視圖
            new_game_view = MinesGameView(handler=self, game_state=new_game_state_summary, player=interaction.user)
            self.game_view = new_game_view # Update the handler's current game view
            
            # 獲取新遊戲開始後的餘額以顯示 (注意: create_game 已扣除賭注)
            # 由於 create_game 內部的 _check_and_deduct_bet 應該已經更新了餘額，
            # 所以這裡的 get_balance 會獲取到下注後的餘額。
            balance_data_after_new_bet = await self.economy_service.get_balance(self.user_id)
            balance_after_new_bet = balance_data_after_new_bet.get('balance', 0)
            
            embed = build_mines_game_embed(game_state=new_game_state_summary, player=interaction.user, current_balance=balance_after_new_bet)
            
            # 因為原始互動是按鈕點擊，我們需要用 followup.send 來發送新遊戲界面
            # 舊的 display_game_end 訊息上的按鈕應該已經被 interaction_manager 禁用了
            new_message_sent = await interaction.followup.send(content=message, embed=embed, view=new_game_view, wait=True)
            if new_message_sent:
                new_game_view.interaction_message = new_message_sent
            else:
                logger.error("[MinesPlayAgain] Failed to send new game message for user %s after 'Play Again'", self.user_id)
                # Fallback or error message to user if followup.send fails
                await interaction.followup.send("創建新遊戲時訊息發送失敗，請稍後再試或聯繫管理員。", ephemeral=True)

        except (InsufficientFundsError, MinBetNotMetError, UserNotFoundError, GameSettlementError, ValueError) as specific_error:
            logger.error("處理 '再來一局' 時發生已知遊戲異常: %s", specific_error, exc_info=True)
            from gacha.utils.cog_error_handler import handle_gacha_error
            await handle_gacha_error(interaction, specific_error, '重新啟動尋寶礦區遊戲時')
            # Consider what to do with the UI if play again fails. 
            # The original game end message might still be there.
        except Exception as e:
            logger.error("處理 '再來一局' 時發生未知異常: %s", e, exc_info=True)
            from gacha.utils.cog_error_handler import handle_gacha_error
            await handle_gacha_error(interaction, e, '重新啟動尋寶礦區遊戲時')

    async def handle_adjust_and_replay(self, interaction: discord.Interaction, old_bet: int):
        """處理 '調整設定並重玩' 按鈕點擊"""
        if not await interaction_utils.check_user_permission(interaction, self.user_id, '這不是你的遊戲按鈕！'):
            return
        await interaction_utils.safe_defer(interaction, ephemeral=False, thinking=True)
        try:
            # 使用純異常模式
            balance_data = await self.economy_service.get_balance(self.user_id)
            current_balance = balance_data.get('balance', 0)
            
            embed = build_mode_selection_embed(current_bet=old_bet, current_balance=current_balance, player=interaction.user)
            from gacha.views.games.mines_view import MinesModeSelectView
            view = MinesModeSelectView(user_id=self.user_id, current_bet=old_bet, mines_service=self.mines_service, economy_service=self.economy_service, game_display_service=self.game_display_service)
            message = await interaction_utils.safe_send_message(interaction, embed=embed, view=view)
            if message:
                view.interaction_message = message
            else:
                logger.error('Failed to send new mode selection message for user %s in adjust_and_replay.', self.user_id)
                await interaction_utils.safe_send_message(interaction, '無法顯示模式選擇界面，請稍後再試。', ephemeral=True)
        except Exception as e:
            logger.error('處理調整設定並重玩時發生異常: %s', e, exc_info=True)
            from gacha.utils.cog_error_handler import handle_gacha_error
            await handle_gacha_error(interaction, e, '處理調整設定並重玩時')