# RPG 任務 12：初始數據生成腳本

本文檔詳細列出了為 RPG 系統創建初始配置數據 JSON 文件的腳本的任務。這些腳本將用於生成 `cards.json`, `active_skills.json`, `monsters.json` 等核心配置文件，確保它們符合 Pydantic 模型的結構，並包含合理的初始數據。

**參考設計文檔：**
*   `RPG_02_Configuration_Files.md` (所有 JSON 配置文件的結構和 Pydantic 模型)
*   `RPG_06_AI_and_Generation_Scripts.md` (關於使用 AI 輔助生成和腳本職責的說明)
*   `RPG_TASK_01_Project_Setup_and_Config_System.md` (Pydantic 模型的位置和已創建的空 JSON 文件)

**目標位置：** `scripts/generation/` 目錄下。

## 1. 腳本結構與通用功能

- [ ] **創建 `scripts/generation/generate_rpg_configs.py` (或多個特定腳本)。**
- [ ] **導入 Pydantic 模型：** 腳本需要導入在 `rpg_system/config/pydantic_models/` 中定義的所有相關 Pydantic 模型。
- [ ] **JSON 輸出功能：**
    - [ ] 實現一個輔助函數 `save_config_to_json(data: Dict[str, Any], model_class: Type[BaseModel], file_path: str)`:
        *   接收數據字典、對應的 Pydantic 主模型類 (e.g., `AllActiveSkillsConfig`) 以及輸出文件路徑。
        *   使用 `model_class.model_validate(data)` (Pydantic v2) 或 `model_class(**data)` (Pydantic v1) 來驗證數據結構。
        *   如果驗證成功，使用 `model_instance.model_dump_json(indent=2)` (Pydantic v2) 或 `model_instance.json(indent=2)` (Pydantic v1) 將數據序列化為格式化的 JSON 字符串。
        *   將 JSON 字符串寫入指定的 `file_path` (通常在 `rpg_system/config/data/`)。
        *   處理驗證失敗 (`pydantic.ValidationError`) 的情況，打印詳細錯誤並提示用戶修正數據源。
- [ ] **數據源：**
    -   腳本可以直接在 Python 代碼中定義初始數據結構 (字典列表)。
    -   (可選) 腳本可以讀取更易於編輯的中間格式 (如 CSV, YAML，或更簡單的 Python數據文件) 來生成 JSON。
    -   (可選，高級) 根據 `RPG_06`，可以集成 AI (如調用一個本地 LLM API 或粘貼預生成文本) 來輔助填充描述、名稱等創意內容，但核心結構和數值平衡仍需人工或腳本邏輯控制。

## 2. 各配置文件生成邏輯

為每個核心 JSON 配置文件實現數據定義和生成調用。
以下是一些示例，需要根據實際 Pydantic 模型和期望的初始數據進行填充。

- [ ] **`generate_effect_templates_config()`**
    - [ ] 定義一些通用的效果模板 (`EffectDefinition` 結構)。
        *   例如："造成100點物理傷害"，"恢復50點HP"，"施加[燃燒]狀態效果3回合"。
    - [ ] 數據結構: `Dict[str, EffectDefinition]` (e.g., `{"PHYSICAL_DAMAGE_100": effect_def_1, ...}`)
    - [ ] 調用 `save_config_to_json` 保存到 `effect_templates.json`。
- [ ] **`generate_status_effects_config()`**
    - [ ] 定義一些基礎的狀態效果 (`StatusEffectConfig` 結構)。
        *   例如："中毒" (每回合造成傷害，可疊加)，"治療術後恢復" (每回合恢復HP)，"眩暈" (無法行動)。
        *   確保 `effect_definitions_on_apply`, `per_tick`, `on_expire` 等字段引用了 `effect_templates.json` 中的鍵或直接定義效果。
    - [ ] 數據結構: `Dict[str, StatusEffectConfig]`
    - [ ] 調用 `save_config_to_json` 保存到 `status_effects.json`。
- [ ] **`generate_active_skills_config()`**
    - [ ] 定義一些初始主動技能 (`ActiveSkillConfig` 結構)。
        *   例如："普通攻擊"，"強力一擊"，"小治療術"，"火球術"。
        *   技能應包含不同等級的效果 (`effects_by_level`)。
        *   `effect_definitions` 可以引用 `effect_templates.json` 或直接定義。
        *   填充 `target_logic`。
    - [ ] 數據結構: `Dict[str, ActiveSkillConfig]`
    - [ ] 調用 `save_config_to_json` 保存到 `active_skills.json`。
- [ ] **`generate_passive_skills_config()`**
    - [ ] 定義一些通用被動技能 (`PassiveSkillConfig` 結構)。
        *   例如："攻擊強化I" (提升基礎攻擊力)，"堅韌I" (提升基礎HP)。
        *   `effects_by_level` 包含不同等級的 `PassiveEffectBlock`。
        *   `PassiveEffectBlock` 包含 `trigger_condition` 和 `effect_definitions`。
    - [ ] 數據結構: `Dict[str, PassiveSkillConfig]`
    - [ ] 調用 `save_config_to_json` 保存到 `passive_skills.json`。
- [ ] **`generate_innate_passive_skills_config()`**
    - [ ] 定義一些卡牌天賦被動技能 (`InnatePassiveSkillConfig` 結構)。
        *   例如：特定種族的特性，特定系列卡牌的專屬能力。
        *   `effects_by_star_level` 包含不同星級的 `PassiveEffectBlock`。
    - [ ] 數據結構: `Dict[str, InnatePassiveSkillConfig]`
    - [ ] 調用 `save_config_to_json` 保存到 `innate_passive_skills.json`。
- [ ] **`generate_cards_config()`**
    - [ ] 定義一些初始卡牌 (`CardConfig` 結構)。
        *   至少定義1-2張不同稀有度的卡牌作為模板。
        *   引用之前創建的 `innate_passive_skill_id`, `primary_attack_skill_id`。
        *   填充 `base_stats`, `growth_per_rpg_level`, `passive_skill_slots`。
        *   (可選) `star_level_effects_key`。
    - [ ] 數據結構: `Dict[str, CardConfig]`
    - [ ] 調用 `save_config_to_json` 保存到 `cards.json`。
- [ ] **`generate_star_level_effects_config()`**
    - [ ] 如果卡牌配置中使用了 `star_level_effects_key`，則定義對應的星級效果組 (`StarLevelEffectsConfig` 結構)。
        *   每個效果組包含多個星級的效果 (`StarLevelEffectDetail`)。
    - [ ] 數據結構: `Dict[str, StarLevelEffectsConfig]`
    - [ ] 調用 `save_config_to_json` 保存到 `star_level_effects.json`。
- [ ] **`generate_monsters_config()`**
    - [ ] 定義一些初始怪物 (`MonsterConfig` 結構)。
        *   至少定義幾種不同強度的怪物用於PVE樓層。
        *   填充其屬性、主動技能列表 (`active_skill_order`)、被動技能 (`equipped_passives`)。
    - [ ] 數據結構: `Dict[str, MonsterConfig]`
    - [ ] 調用 `save_config_to_json` 保存到 `monsters.json`。
- [ ] **`generate_reward_packages_config()`**
    - [ ] 定義一些獎勵包 (`RewardPackageConfig` 結構)。
        *   例如："新手樓層首次通關獎勵"，"每日任務獎勵"。
        *   包含 `RewardItem` 列表。
    - [ ] 數據結構: `Dict[str, RewardPackageConfig]`
    - [ ] 調用 `save_config_to_json` 保存到 `reward_packages.json`。
- [ ] **`generate_monster_groups_config()`**
    - [ ] 定義一些怪物遭遇組 (`MonsterGroupConfig` 結構)。
        *   每組包含一個或多個 `MonsterInGroup` (引用 `monsters.json` 中的怪物ID)。
    - [ ] 數據結構: `Dict[str, MonsterGroupConfig]`
    - [ ] 調用 `save_config_to_json` 保存到 `monster_groups.json`。
- [ ] **`generate_floors_config()`**
    - [ ] 定義一些PVE樓層 (`FloorConfig` 結構)。
        *   至少定義初始的幾個樓層。
        *   引用 `monster_groups.json` 中的鍵 (`possible_encounters`) 和 `reward_packages.json` 中的鍵。
    - [ ] 數據結構: `Dict[str, FloorConfig]` (鍵為樓層號字符串, e.g., "1", "2")
    - [ ] 調用 `save_config_to_json` 保存到 `floors.json`。

## 3. 腳本執行

- [ ] 在 `generate_rpg_configs.py` (或其他主腳本) 中提供一個 `main` 函數或類似的入口點。
- [ ] `if __name__ == "__main__":`
    - [ ] 依次調用上述各個 `generate_..._config()` 函數。
    - [ ] 打印執行的總結信息 (哪些文件被創建/更新)。
- [ ] **命令行參數 (可選)：**
    - [ ] 允許通過命令行參數指定輸出目錄。
    - [ ] 允許通過命令行參數選擇只生成特定的配置文件。
- [ ] **版本控制：** 生成的 JSON 文件應提交到版本控制中，作為項目的基礎數據。

## 4. 數據平衡與迭代

- [ ] **強調初始數據的平衡性可能較差，需要後續迭代和測試調整。**
- [ ] 生成腳本的目的是快速搭建一個可運行的基礎數據集，而不是一次性生成完美的最終數據。
- [ ] 設計一些簡單的、可擴展的數據模板，方便後續增減條目和調整數值。

## 5. 單元測試 (針對生成邏輯)

- [ ] (可選，但推薦) 為生成腳本中的複雜數據生成邏輯 (如果有) 編寫單元測試。
- [ ] 主要測試 Pydantic 模型驗證是否能在腳本生成數據後通過。
- [ ] 測試 `save_config_to_json` 輔助函數是否能正確處理成功和失敗的情況。

---
完成所有上述任務後，請勾選並更新 `RPG_IMPLEMENTATION_PLAN.md` 中的相應條目。 