gacha_core_settings:
  # gacha_settings.yaml
  # Gacha 核心玩法配置

  # 稀有度排序值 (數值越大，稀有度越高)
  rarity_sort_values:
    1: 1  # COMMON
    2: 2  # RARE
    3: 3  # SUPER_RARE
    4: 4  # SSR
    5: 5  # ULTRA_RARE
    6: 6  # LEGENDARY_RARE
    7: 7  # EXCLUSIVE

  # 星級相關
  max_star_level: 35
  stars_per_tier: 5
# 交易手續費設定
  trade_fee_percentage: 0.03
  min_trade_fee: 10

  # 按卡池和稀有度的統一售價
  pool_rarity_prices:
    main:
      1: 4
      2: 6
      3: 20
      4: 600
      5: 1500
      6: 12000
      7: 55000
    special:
      1: 7
      2: 50
      3: 400
      4: 13000
      5: 42000
    summer:
      1: 3
      2: 12
      3: 50
      4: 800
      5: 12000
      6: 40000
    vd:
      1: 3
      2: 12
      3: 70
      4: 1000
      5: 15000
      6: 45000
    special_maid:
      1: 4
      2: 80
      3: 500
      4: 17000
      5: 50000
    hololive:
      1: 4
      2: 12
      3: 80
      4: 800
      5: 7000
      6: 22000
      7: 60000
    ua: # Correctly placed under pool_rarity_prices
      1: 3
      2: 10
      3: 70
      4: 750
      5: 7000
      6: 20000
      7: 50000
    ptcg: # Pokemon Trading Card Game卡池
      1: 3
      2: 8
      3: 50
      4: 500
      5: 4000
      6: 15000
      7: 35000

  # 卡池類型名稱
  pool_type_names:
    main: '主卡池'
    special: '典藏卡池'
    summer: '泳裝卡池'
    vd: '情人節卡池'
    special_maid: '典藏女僕卡池'
    hololive: 'Hololive卡池' #
    ua: 'UNION ARENA卡池'
    ptcg: 'Pokemon TCG卡池'

  # 卡池類型前綴
  pool_type_prefixes:
    main: '普通'
    special: '典藏'
    summer: '泳裝'
    vd: '情人節'
    special_maid: '典藏女僕'
    hololive: 'Hololive'
    ua: 'UNION ARENA'
    ptcg: 'Pokemon TCG'

  # 卡池抽卡成本
  pool_costs:
    main: 50
    special: 140
    summer: 130
    vd: 150
    special_maid: 160
    hololive: 150
    ua: 120
    ptcg: 90
    all: 50 # 混合池成本

  # 各卡池稀有度及概率配置
  all_pool_rarity_configs:
    main:
      - [1, 68.74]
      - [2, 25.00]
      - [3, 4.50]
      - [4, 1.00]
      - [5, 0.70]
      - [6, 0.05]
      - [7, 0.01]
    special:
      - [1, 82.80]
      - [2, 15.00]
      - [3, 2.00]
      - [4, 0.15]
      - [5, 0.05]
    summer:
      - [1, 61.80]
      - [2, 25.00]
      - [3, 10.00]
      - [4, 3.00]
      - [5, 0.15]
      - [6, 0.05]
    vd:
      - [1, 61.80]
      - [2, 25.00]
      - [3, 10.00]
      - [4, 3.00]
      - [5, 0.15]
      - [6, 0.05]
    special_maid:
      - [1, 82.80]
      - [2, 15.00]
      - [3, 2.00]
      - [4, 0.15]
      - [5, 0.05]
    hololive:
      - [1, 64.44]
      - [2, 25.00]
      - [3, 8.00]
      - [4, 2.00]
      - [5, 0.50]
      - [6, 0.05]
      - [7, 0.01]
    ua: 
      - [1, 64.44]
      - [2, 25.00]
      - [3, 8.00]
      - [4, 2.00]
      - [5, 0.50]
      - [6, 0.05]
      - [7, 0.01]
    ptcg:
      - [1, 63.84]
      - [2, 25.00]
      - [3, 8.00]
      - [4, 2.50]
      - [5, 0.60]
      - [6, 0.05]
      - [7, 0.01]

  # 混合抽卡卡池概率配置
  mixed_pool_draw_config:
    - ["main", 99.00]
    - ["special", 1.00]
    - ["summer", 0.00]
    - ["vd", 0.00]
    - ["special_maid", 0.00]
    - ["hololive", 0.00] 
    - ["ua", 0.00]
    - ["ptcg", 0.00]

  # 卡池組合配置 (用於命令界面)
  pool_configurations:
    main:
      name: "主卡池"
      description: "所有常駐的基礎卡片。"
      pools: ["main"]
    all:
      name: "混合池 (50油)"
      description: "混合所有可用卡片的標準卡池。"
      pools: ["main", "special"]
    special:
      name: "典藏卡池 (140油)"
      description: "包含稀有和限定卡片的特殊卡池。"
      pools: ["special"]
    summer:
      name: "泳裝卡池 (130油)"
      description: "夏季主題限定卡池。"
      pools: ["summer"]
    vd:
      name: "情人節卡池 (150油)"
      description: "情人節主題限定卡池。"
      pools: ["vd"]
    special_maid:
      name: "典藏女僕卡池 (160油)"
      description: "特殊女僕主題限定卡池。"
      pools: ["special_maid"]
    hololive:
      name: "Hololive 卡池 (150油)"
      description: "Hololive 聯動主題限定卡池。"
      pools: ["hololive"]
    ua: 
      name: "UNION ARENA 卡池 (120油)"
      description: "UNION ARENA 主題限定卡池。"
      pools: ["ua"]
    ptcg:
      name: "Pokemon TCG 卡池 (90油)"
      description: "Pokemon Trading Card Game 主題限定卡池。"
      pools: ["ptcg"]


  # 各卡池可用稀有度列表
  pool_rarity_mapping:
    main: [1, 2, 3, 4, 5, 6, 7]
    special: [1, 2, 3, 4, 5]
    summer: [1, 2, 3, 4, 5, 6]
    vd: [1, 2, 3, 4, 5, 6]
    special_maid: [1, 2, 3, 4, 5]
    hololive: [1, 2, 3, 4, 5, 6, 7] 
    ua: [1, 2, 3, 4, 5, 6, 7]
    ptcg: [1, 2, 3, 4, 5, 6, 7]

  # 票券商店物品定義
  ticket_shop_items:
    # 主卡池 (main) 票券
    main_R7_specific:
      display_name: "主卡池 EX 指定券"
      description: "從主卡池中選擇一張EX稀有度的卡片。"
      cost_oil_tickets: 75000
      pool_type: 'main'
      rarity: 7
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 10
    main_R7_random:
      display_name: "主卡池 EX 隨機券"
      description: "從主卡池中隨機獲得一張EX稀有度的卡片。"
      cost_oil_tickets: 5000
      pool_type: 'main'
      rarity: 7
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 11
    main_R6_specific:
      display_name: "主卡池 LR 指定券"
      description: "從主卡池中選擇一張LR稀有度的卡片。"
      cost_oil_tickets: 15000
      pool_type: 'main'
      rarity: 6
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 20
    main_R6_random:
      display_name: "主卡池 LR 隨機券"
      description: "從主卡池中隨機獲得一張LR稀有度的卡片。"
      cost_oil_tickets: 1000
      pool_type: 'main'
      rarity: 6
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 21
    
    # 典藏卡池 (special) 票券
    special_R5_specific:
      display_name: "典藏卡池 UR 指定券"
      description: "從典藏卡池中選擇一張UR稀有度的卡片。"
      cost_oil_tickets: 42000
      pool_type: 'special'
      rarity: 5
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 30
    special_R5_random:
      display_name: "典藏卡池 UR 隨機券"
      description: "從典藏卡池中隨機獲得一張UR稀有度的卡片。"
      cost_oil_tickets: 2800
      pool_type: 'special'
      rarity: 5
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 31
    special_R4_specific:
      display_name: "典藏卡池 SSR 指定券"
      description: "從典藏卡池中選擇一張SSR稀有度的卡片。"
      cost_oil_tickets: 14010
      pool_type: 'special'
      rarity: 4
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 40
    special_R4_random:
      display_name: "典藏卡池 SSR 隨機券"
      description: "從典藏卡池中隨機獲得一張SSR稀有度的卡片。"
      cost_oil_tickets: 934
      pool_type: 'special'
      rarity: 4
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 41
    
    # 泳裝卡池 (summer) 票券
    summer_R6_specific:
      display_name: "泳裝卡池 LR 指定券"
      description: "從泳裝卡池中選擇一張LR稀有度的卡片。"
      cost_oil_tickets: 39000
      pool_type: 'summer'
      rarity: 6
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 50
    summer_R6_random:
      display_name: "泳裝卡池 LR 隨機券"
      description: "從泳裝卡池中隨機獲得一張LR稀有度的卡片。"
      cost_oil_tickets: 2600
      pool_type: 'summer'
      rarity: 6
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 51
    summer_R5_specific:
      display_name: "泳裝卡池 UR 指定券"
      description: "從泳裝卡池中選擇一張UR稀有度的卡片。"
      cost_oil_tickets: 13005
      pool_type: 'summer'
      rarity: 5
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 60
    summer_R5_random:
      display_name: "泳裝卡池 UR 隨機券"
      description: "從泳裝卡池中隨機獲得一張UR稀有度的卡片。"
      cost_oil_tickets: 867
      pool_type: 'summer'
      rarity: 5
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 61
    
    # 情人節卡池 (vd) 票券
    vd_R6_specific:
      display_name: "情人節卡池 LR 指定券"
      description: "從情人節卡池中選擇一張LR稀有度的卡片。"
      cost_oil_tickets: 45000
      pool_type: 'vd'
      rarity: 6
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 70
    vd_R6_random:
      display_name: "情人節卡池 LR 隨機券"
      description: "從情人節卡池中隨機獲得一張LR稀有度的卡片。"
      cost_oil_tickets: 3000
      pool_type: 'vd'
      rarity: 6
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 71
    vd_R5_specific:
      display_name: "情人節卡池 UR 指定券"
      description: "從情人節卡池中選擇一張UR稀有度的卡片。"
      cost_oil_tickets: 15000
      pool_type: 'vd'
      rarity: 5
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 80
    vd_R5_random:
      display_name: "情人節卡池 UR 隨機券"
      description: "從情人節卡池中隨機獲得一張UR稀有度的卡片。"
      cost_oil_tickets: 1000
      pool_type: 'vd'
      rarity: 5
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 81
    
    # 典藏女僕卡池 (special_maid) 票券
    special_maid_R5_specific:
      display_name: "典藏女僕卡池 UR 指定券"
      description: "從典藏女僕卡池中選擇一張UR稀有度的卡片。"
      cost_oil_tickets: 48000
      pool_type: 'special_maid'
      rarity: 5
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 90
    special_maid_R5_random:
      display_name: "典藏女僕卡池 UR 隨機券"
      description: "從典藏女僕卡池中隨機獲得一張UR稀有度的卡片。"
      cost_oil_tickets: 3200
      pool_type: 'special_maid'
      rarity: 5
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 91
    special_maid_R4_specific:
      display_name: "典藏女僕卡池 SSR 指定券"
      description: "從典藏女僕卡池中選擇一張SSR稀有度的卡片。"
      cost_oil_tickets: 16005
      pool_type: 'special_maid'
      rarity: 4
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 100
    special_maid_R4_random:
      display_name: "典藏女僕卡池 SSR 隨機券"
      description: "從典藏女僕卡池中隨機獲得一張SSR稀有度的卡片。"
      cost_oil_tickets: 1067
      pool_type: 'special_maid'
      rarity: 4
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 101
    
    # Hololive卡池 (hololive) 票券
    hololive_R7_specific:
      display_name: "Hololive卡池 EX 指定券"
      description: "從Hololive卡池中選擇一張EX稀有度的卡片。"
      cost_oil_tickets: 225000
      pool_type: 'hololive'
      rarity: 7
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 110
    hololive_R7_random:
      display_name: "Hololive卡池 EX 隨機券"
      description: "從Hololive卡池中隨機獲得一張EX稀有度的卡片。"
      cost_oil_tickets: 15000
      pool_type: 'hololive'
      rarity: 7
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 111
    hololive_R6_specific:
      display_name: "Hololive卡池 LR 指定券"
      description: "從Hololive卡池中選擇一張LR稀有度的卡片。"
      cost_oil_tickets: 45000
      pool_type: 'hololive'
      rarity: 6
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 120
    hololive_R6_random:
      display_name: "Hololive卡池 LR 隨機券"
      description: "從Hololive卡池中隨機獲得一張LR稀有度的卡片。"
      cost_oil_tickets: 3000
      pool_type: 'hololive'
      rarity: 6
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 121
    
    # UNION ARENA卡池 (ua) 票券
    ua_R7_specific:
      display_name: "UNION ARENA卡池 EX 指定券"
      description: "從UNION ARENA卡池中選擇一張EX稀有度的卡片。"
      cost_oil_tickets: 180000
      pool_type: 'ua'
      rarity: 7
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 130
    ua_R7_random:
      display_name: "UNION ARENA卡池 EX 隨機券"
      description: "從UNION ARENA卡池中隨機獲得一張EX稀有度的卡片。"
      cost_oil_tickets: 12000
      pool_type: 'ua'
      rarity: 7
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 131
    ua_R6_specific:
      display_name: "UNION ARENA卡池 LR 指定券"
      description: "從UNION ARENA卡池中選擇一張LR稀有度的卡片。"
      cost_oil_tickets: 36000
      pool_type: 'ua'
      rarity: 6
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 140
    ua_R6_random:
      display_name: "UNION ARENA卡池 LR 隨機券"
      description: "從UNION ARENA卡池中隨機獲得一張LR稀有度的卡片。"
      cost_oil_tickets: 2400
      pool_type: 'ua'
      rarity: 6
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 141
    
    # Pokemon TCG卡池 (ptcg) 票券
    ptcg_R7_specific:
      display_name: "Pokemon TCG卡池 EX 指定券"
      description: "從Pokemon TCG卡池中選擇一張EX稀有度的卡片。"
      cost_oil_tickets: 135000
      pool_type: 'ptcg'
      rarity: 7
      ticket_type: 'specific'
      emoji: "<:tz:1374853398998024284>"
      sort_order: 150
    ptcg_R7_random:
      display_name: "Pokemon TCG卡池 EX 隨機券"
      description: "從Pokemon TCG卡池中隨機獲得一張EX稀有度的卡片。"
      cost_oil_tickets: 9000
      pool_type: 'ptcg'
      rarity: 7
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 151
    ptcg_R6_specific:
      display_name: "Pokemon TCG卡池 LR 指定券"
      description: "從Pokemon TCG卡池中選擇一張LR稀有度的卡片。"
      cost_oil_tickets: 27000
      pool_type: 'ptcg'
      rarity: 6
      ticket_type: 'specific'
      emoji: "<:tx:1374853420019617933>"
      sort_order: 160
    ptcg_R6_random:
      display_name: "Pokemon TCG卡池 LR 隨機券"
      description: "從Pokemon TCG卡池中隨機獲得一張LR稀有度的卡片。"
      cost_oil_tickets: 1800
      pool_type: 'ptcg'
      rarity: 6
      ticket_type: 'random'
      emoji: "🎁"
      sort_order: 161

  # 排行榜每頁顯示數量
  leaderboard_items_per_page: 5

  # 排行榜顯示配置
  leaderboard_config:
    rarity:
      title: "🏆 稀有度數量排行榜"
      description: "按照稀有度收集數量排名的玩家"
      color: 16766720 # 0xFFD700 (Gold)
      category: "collection"
      default_for_category: true
    completion:
      title: "🏆 圖鑑完成率排行榜"
      description: "按照全圖鑑完成率排名的玩家"
      color: 255 # 0x0000FF (Blue)
      category: "collection"
    draws:
      title: "🏆 抽卡次數排行榜"
      description: "按照抽卡次數排名的玩家"
      color: 8388736 # 0x800080 (Purple)
      category: "general"
    oil:
      title: "🏆 油幣餘額排行榜"
      description: "按照油幣餘額排名的玩家"
      color: 32768 # 0x008000 (Green)
      category: "general"
      default_for_category: true
    collection_unique:
      title: "🏆 稀有度收集排行榜"
      description: "按照收集的不同卡片數量排名的玩家"
      color: 32896 # 0x008080 (Teal)
      category: "collection"
    portfolio_value:
      title: "📈 總資產價值排行榜"
      description: "按照玩家股票總市值排名的玩家"
      color: 3447003 # 0x3498DB (Blue)
      category: "stock_market"
      default_for_category: true
    stock_holding:
      title: "📊 特定股票持有排行榜"
      description: "按照特定股票持有數量排名的玩家"
      color: 3066993 # 0x2ECC71 (Green)
      category: "stock_market"
    trade_volume:
      title: "💰 股票交易總額排行榜"
      description: "按照股票市場交易總金額排名的玩家"
      color: 15844367 # 0xF1C40F (Yellow)
      category: "stock_market"
    trade_count:
      title: "⚖️ 股票交易次數排行榜"
      description: "按照股票市場交易次數排名的玩家"
      color: 15158332 # 0xE74C3C (Red)
      category: "stock_market"

  # --- Wish Service Configs ---
  # 這些值應與 gacha/app_config.py 中 GachaCoreSettings 內的默認值匹配
  # 如果在此處指定，則會覆蓋 Pydantic 模型中的默認值。
  wish_max_slots: 10 # Maximum number of wish slots a user can have.
  wish_max_power_level: 10 # Maximum wish power level a user can reach.
  default_wish_slots: 1 # Default number of wish slots for new users.
  default_wish_power_level: 1 # Default wish power level for new users.
  default_wish_multiplier: 3.0 # Default wish chance multiplier for power level 1.
  wish_slot_costs: # Costs to upgrade to the next wish slot (key is current_slots, value is cost for next).
    1: 10000
    2: 25000
    3: 50000
    4: 100000
    5: 250000
    6: 500000
    7: 1000000
    8: 2000000
    9: 5000000
  wish_power_costs: # Costs to upgrade to the next wish power level (key is current_level, value is cost for next).
    1: 20000
    2: 40000
    3: 80000
    4: 160000
    5: 300000
    6: 500000
    7: 800000
    8: 1200000
    9: 2000000
  wish_power_multipliers: # Wish chance multipliers for each power level.
    1: 3.0
    2: 4.0
    3: 5.0
    4: 6.0
    5: 8.0
    6: 10.0
    7: 12.0
    8: 15.0
    9: 18.0
    10: 20.0

  # --- Economy Service Configs ---
  # 這些值應與 gacha/app_config.py 中 GachaCoreSettings 內的默認值匹配
  economy_daily_reward: 5000 # Amount of oil for daily reward.
  economy_hourly_reward: 2000 # Amount of oil for hourly reward.
  economy_hourly_claim_key_prefix: "gacha:hourly_claim:" # Redis key prefix for hourly claims.

# 個人檔案服務相關設定
profile:
  image_local_base_path: "downloaded_gacha_master_cards" # 個人檔案卡片圖片本地基礎路徑
  # like_cooldown_seconds: 3600 # 按讚冷卻時間 (秒) - 已移至 ProfileService 內部管理
  # profile_cache_ttl: 604800 # 檔案圖片快取TTL (秒) - 已移至 ProfileService 內部管理
  # max_status_length: 150 # 個性簽名最大長度 - 已移至 ProfileService 或 ProfileCog 內部管理

# --- UI Settings (ui_settings.yaml) ---
# ui_settings: ... (This section should be in ui_settings.yaml, not here)
# Removed ui_settings from gacha_settings.yaml as it's loaded from a separate file.

# --- Other top-level settings (config.yaml) ---
# application: ...
# database: ...
# gacha_stock_integration: ...
# market_stats_updater: ...
# price_update_service: ...
# ai_assistant: ...
# Removed these from gacha_settings.yaml as they belong in config.yaml

gacha_notification_settings:
  enabled: true # 是否啟用抽卡通知
  webhook_url: "https://discord.com/api/webhooks/1377699345050374225/MTsvSpcSo5NgcCv5pq_hbzJR6FCK1qgFy5YMDU-xJqXCW82L3zi1JLXDOcU0uzW0k9eC" # Discord Webhook URL，用於發送通知
  # notification_channel_id: 1375058548874149898 # Discord 通知頻道ID (如果 webhook_url 未設定，則使用此項)
  # 設定哪些卡池的哪些稀有度卡片被抽出時需要通知
  # 格式: pool_type: [rarity1, rarity2, ...]
  # 如果某個 pool_type 未被列出，或列表為空，則該卡池的任何稀有度都不會觸發通知
  # 若要通知所有稀有度，可以將列表設置為包含該卡池所有可能的稀有度
  notify_rarities:
    main: [6, 7] # 主卡池通知 UR, LR, EX
    special: [4, 5]   # 典藏卡池通知 SSR, UR
    summer: [5, 6]    # 泳裝卡池通知 UR, LR
    vd: [5, 6]        # 情人節卡池通知 UR, LR
    special_maid: [4, 5] # 典藏女僕卡池通知 SSR, UR
    hololive: [6, 7]  # Hololive 卡池通知 LR, EX
    ua: [6, 7]        # UNION ARENA 卡池通知 LR, EX
    ptcg: [6, 7]      # Pokemon TCG 卡池通知 LR, EX
