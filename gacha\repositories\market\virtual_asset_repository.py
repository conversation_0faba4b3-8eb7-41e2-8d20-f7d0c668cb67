import asyncpg
from decimal import Decimal
from typing import List, Dict, Any, Optional
from utils.logger import logger

class VirtualAssetRepository:

    async def get_linked_assets_with_history(self, conn: asyncpg.Connection) -> List[asyncpg.Record]:
        """獲取所有設定了 linked_criteria_type (非 GLOBAL) 的虛擬資產及其最近一次的價格歷史。"""
        query = "\n            SELECT va.asset_id, va.current_price, va.influence_weight,\n                   va.linked_criteria_type, va.linked_criteria_value, va.linked_pool_context,\n                   (SELECT aph.price FROM asset_price_history aph\n                    WHERE aph.asset_id = va.asset_id ORDER BY aph.timestamp DESC OFFSET 1 LIMIT 1) AS previous_price\n            FROM virtual_assets va\n            WHERE va.linked_criteria_type IS NOT NULL AND va.linked_criteria_type != 'GLOBAL';\n        "
        try:
            return await conn.fetch(query)
        except Exception as e:
            logger.error('查詢連結資產時出錯: %s', e, exc_info=True)
            return []

    async def get_global_assets_with_history(self, conn: asyncpg.Connection) -> List[asyncpg.Record]:
        """獲取所有設定了 linked_criteria_type 為 GLOBAL 的虛擬資產及其最近一次的價格歷史。"""
        query = "\n            SELECT asset_id, current_price, influence_weight,\n                   (SELECT aph.price FROM asset_price_history aph\n                    WHERE aph.asset_id = va.asset_id ORDER BY aph.timestamp DESC OFFSET 1 LIMIT 1) AS previous_price\n            FROM virtual_assets va\n            WHERE va.linked_criteria_type = 'GLOBAL';\n        "
        try:
            return await conn.fetch(query)
        except Exception as e:
            logger.error('查詢全局資產時出錯: %s', e, exc_info=True)
            return []