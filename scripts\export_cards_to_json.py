#!/usr/bin/env python
"""
將數據庫中的卡片數據匯出至JSON文件

功能:
    1. 從數據庫中讀取所有卡片數據
    2. 根據卡池類型分別匯出到對應的JSON文件
    3. 支持指定特定卡池類型的匯出

使用方法:
    python scripts/export_cards_to_json.py [pool_type]
    
參數:
    pool_type: 可選參數，指定要匯出的卡池類型 (main/special/summer)。如不指定則匯出所有類型
"""

import asyncio
import json
import os
import sys
import logging
import argparse
from typing import Dict, List, Any, Optional

import asyncpg

# 設置項目根目錄到sys.path
script_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(script_dir)
sys.path.insert(0, root_dir)

# 導入必要的模組
from utils.logger import logger

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 配置數據庫連接參數
DB_CONFIG = {
    "user": "postgres",
    "password": "postgres",
    "database": "postgres",
    "host": "localhost"
}

# 輸出目錄
OUTPUT_DIR = "gacha/data"

# 卡池類型到文件名的映射
POOL_TYPE_FILENAMES = {
    "main": "card_data.json",
    "special": "card_data_special.json",
    "summer": "card_data_summer.json"
}

# 數據庫字段到JSON字段的映射
FIELD_MAPPINGS = {
    "main": {
        "card_id": "id",
        "name": "character_name",
        "series": "series",
        "rarity": "rarity",
        "image_url": "image_path",
        "description": "description"
    },
    "special": {
        "card_id": "id",
        "original_id": "uuid",
        "name": "cardName",
        "series": "seriesName",
        "rarity": "rarityName",
        "image_url": "mediaUrl",
        "description": "description"
    },
    "summer": {
        "card_id": "id",
        "name": "character_name",
        "series": "series",
        "rarity": "rarity",
        "image_url": "image_path",
        "description": "description"
    }
}

# 稀有度反向映射
RARITY_REVERSE_MAPPING = {
    "C-T1": "C",
    "C-T2": "R",
    "C-T3": "SR",
    "C-T4": "SSR",
    "C-T5": "UR",
    "T1": "C",
    "T2": "R",
    "T3": "SR",
    "T4": "SSR",
    "T5": "UR",
    "T6": "LR",
    "TS": "EX"
}

async def export_cards() -> None:
    """導出卡片數據到JSON文件"""
    try:
        # 連接數據庫
        conn = await asyncpg.connect(**DB_CONFIG)
        logger.info("數據庫連接成功")

        # 查詢所有卡片
        cards = await conn.fetch("""
            SELECT * FROM master_cards
            ORDER BY card_id
        """)
        
        # 轉換為字典列表
        card_list = []
        for card in cards:
            card_dict = dict(card)
            card_list.append(card_dict)
            
        # 確保輸出目錄存在
        os.makedirs(OUTPUT_DIR, exist_ok=True)
        
        # 寫入JSON文件
        output_file = os.path.join(OUTPUT_DIR, "cards.json")
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(card_list, f, ensure_ascii=False, indent=2)
            
        logger.info(f"成功導出 {len(card_list)} 張卡片到 {output_file}")
        
        # 關閉數據庫連接
        await conn.close()
        
    except Exception as e:
        logger.error(f"導出過程中發生錯誤: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(export_cards()) 