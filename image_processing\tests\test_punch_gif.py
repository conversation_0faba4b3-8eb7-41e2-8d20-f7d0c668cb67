"""
測試打拳GIF功能的腳本
"""
import asyncio
import os
import sys
from PIL import Image

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from image_processing.gif_modifier.punch_overlay import overlay_punch_gif

# 模擬Discord用戶對象
class MockUser:
    def __init__(self, id, avatar_path):
        self.id = id
        self.avatar_path = avatar_path
        
    @property
    def display_avatar(self):
        return self
        
    @property
    def url(self):
        return self.avatar_path

# 修改fetch_avatar函數，用於測試
async def mock_fetch_avatar(user):
    """模擬獲取用戶頭像的函數"""
    try:
        return Image.open(user.avatar_path).convert("RGBA")
    except Exception as e:
        print(f"無法打開頭像文件: {e}")
        return None

# 臨時替換fetch_avatar函數
import image_processing.gif_modifier.punch_overlay
original_fetch_avatar = image_processing.gif_modifier.punch_overlay.fetch_avatar
image_processing.gif_modifier.punch_overlay.fetch_avatar = mock_fetch_avatar

async def test_punch_gif():
    """測試打拳GIF功能"""
    print("開始測試打拳GIF功能...")
    
    # 測試參數
    gif_path = os.path.join("image_processing", "gifs", "打拳.gif")
    avatar_path = input("請輸入頭像圖片路徑: ")
    output_path = os.path.join("image_processing", "temp", "test_punch_output.gif")
    
    # 確認文件存在
    if not os.path.exists(gif_path):
        print(f"錯誤: GIF文件不存在 - {gif_path}")
        return False
    
    if not os.path.exists(avatar_path):
        print(f"錯誤: 頭像文件不存在 - {avatar_path}")
        return False
    
    try:
        # 創建模擬用戶
        mock_user = MockUser(123456, avatar_path)
        
        # 調用打拳GIF處理函數
        result_path = await overlay_punch_gif(gif_path, mock_user, output_path=output_path)
        
        print(f"GIF生成成功! 已保存到: {result_path}")
        
        # 嘗試打開生成的GIF文件
        try:
            if sys.platform == "win32":
                os.system(f'start {result_path}')
            elif sys.platform == "darwin":
                os.system(f'open {result_path}')
            else:
                os.system(f'xdg-open {result_path}')
        except Exception as e:
            print(f"無法自動打開文件: {e}")
            print(f"請手動打開文件: {result_path}")
            
        return True
        
    except Exception as e:
        print(f"測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 恢復原始函數
        image_processing.gif_modifier.punch_overlay.fetch_avatar = original_fetch_avatar

if __name__ == "__main__":
    # 運行測試
    asyncio.run(test_punch_gif()) 