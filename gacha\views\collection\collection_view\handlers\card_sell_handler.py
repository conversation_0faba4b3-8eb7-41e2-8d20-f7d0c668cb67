"""
處理卡片賣出功能的 Handler 類
"""
import discord
import traceback
from typing import TYPE_CHECKING, Callable, Any, Dict
from gacha.services.core.economy_service import EconomyService
from gacha.repositories.collection.user_collection_repository import UserCollectionRepository
from gacha.models.models import UserCard
from gacha.models.filters import CollectionFilters
from gacha.cogs.economy_cog import EconomyCog
from gacha.utils import interaction_utils
from gacha.views import utils as view_utils
from gacha.constants import RarityLevel
from gacha.exceptions import UserNotFoundError, StoredPriceError
from ..utils import create_sell_confirmation
from gacha.app_config import config_service
from gacha.views.collection.favorite_component import FavoriteComponent
from utils.operation_logger import OperationLogger
from utils.logger import logger
from .base_handler import BaseHandler
if TYPE_CHECKING:
    from ..card_view import CollectionView

class CardSellHandler(BaseHandler):
    """卡片賣出處理器

    採用統一的服務訪問模式，所有服務都通過 view.services 統一訪問，
    保持系統架構的一致性和可維護性。
    """

    def __init__(self, view: 'CollectionView'):
        super().__init__(view)
        self._warning_card_id: int | None = None
        self._warning_quantity: int = 0

    @property
    def economy_service(self) -> EconomyService:
        """統一的經濟服務訪問接口

        遵循系統的服務容器模式，通過 view.services 訪問服務
        """
        return self.view.services.economy

    @property
    def collection_repo(self) -> UserCollectionRepository:
        """統一的收藏庫訪問接口

        遵循系統的服務容器模式，通過 view.services 訪問服務
        """
        return self.view.services.collection_repo

    @property
    def user(self) -> discord.User:
        return self.view.user

    @property
    def user_id(self) -> int:
        return self.view.user.id

    @property
    def current_view_card(self) -> UserCard | None:
        """獲取當前卡片 - 使用統一接口"""
        return self.view.current_card

    async def _refresh_favorite_status(self, interaction: discord.Interaction, card: UserCard):
        """刷新卡片的最愛狀態"""
        await FavoriteComponent.check_favorite_status(interaction, self.user_id, card.card.card_id)
        user_card_from_db = await self.collection_repo.get_user_card(self.user_id, card.card.card_id)
        if user_card_from_db:
            card.is_favorite = user_card_from_db.is_favorite

    async def process_sell_card(self, interaction: discord.Interaction, quantity_to_sell: int, force_sell: bool=False):
        """處理賣卡的通用邏輯"""
        if not await self.view.interaction_check(interaction):
            return
        if not await self.check_cards_available(interaction, '當前頁面沒有卡片可賣'):
            return
        card = self.current_view_card
        if not card:
            await interaction_utils.safe_send_message(interaction, '錯誤：找不到當前卡片。', ephemeral=True)
            return
        card_id = card.card.card_id
        is_sell_all = quantity_to_sell == card.quantity
        await self._refresh_favorite_status(interaction, card)
        if card.is_favorite and (not force_sell):
            await self._show_sell_favorite_confirmation(interaction, card, quantity_to_sell, is_sell_all)
        else:
            await self._execute_sell_operation(interaction, card_id, quantity_to_sell, is_sell_all, force_sell=True)

    async def _show_sell_favorite_confirmation(self, interaction: discord.Interaction, card: UserCard, quantity_to_sell: int, is_sell_all: bool):
        """顯示賣出最愛卡片的確認視圖"""
        self._warning_card_id = card.card.card_id
        self._warning_quantity = quantity_to_sell
        card_name = card.card.name
        card_rarity_int = card.card.rarity
        card_rarity_level_enum = None
        if card_rarity_int is not None:
            try:
                card_rarity_level_enum = RarityLevel(card_rarity_int)
            except ValueError:
                pass
        full_friendly_name = view_utils.get_user_friendly_rarity_name(card_rarity_level_enum)
        if card_rarity_level_enum is not None and '(' in full_friendly_name and (')' in full_friendly_name):
            card_rarity_display = full_friendly_name.split(' ')[-1].strip('()')
        elif card_rarity_level_enum is not None:
            card_rarity_display = config_service.get_rarity_display_codes().get(card_rarity_level_enum.value)
        else:
            card_rarity_display = '?'

        async def confirm_sell_favorite_callback(button_interaction: discord.Interaction):
            if not await interaction_utils.check_user_permission(button_interaction, self.user_id, '只有原始命令發起者才能操作此按鈕。'):
                return
            current_card_for_confirmation = await self.collection_repo.get_user_card(self.user_id, self._warning_card_id)
            final_is_sell_all = False
            if current_card_for_confirmation:
                final_is_sell_all = self._warning_quantity == current_card_for_confirmation.quantity
            else:
                await interaction_utils.safe_send_message(button_interaction, '錯誤：找不到卡片資訊，可能已被賣出。', ephemeral=True)
                return
            await self._execute_sell_operation(button_interaction, self._warning_card_id, self._warning_quantity, final_is_sell_all, force_sell=True)
        await create_sell_confirmation(interaction=interaction, title='⚠️ 警告：即將賣出最愛卡片 ⚠️', description=f"這是一張最愛卡片！你確定要賣出{(' 所有 ' + str(quantity_to_sell) + ' 張' if quantity_to_sell > 1 else '它')}嗎？此操作無法復原。", field_name='卡片詳情', field_value=f'• 名稱: {card_name} ({card_rarity_display})\n• ID: {card.card.card_id}\n• 數量: {quantity_to_sell}張', confirm_callback=confirm_sell_favorite_callback)
        if quantity_to_sell > 1 and card.quantity > 1:
            await self._show_leave_one_option(interaction, card)

    async def _show_leave_one_option(self, interaction: discord.Interaction, card: UserCard):
        """顯示「只賣到剩最後一張」的選項"""
        leave_one_view = discord.ui.View(timeout=60)
        sell_leave_one_button = discord.ui.Button(style=discord.ButtonStyle.primary, label='只賣到剩最後一張', custom_id=f'sell_leave_one_{card.card.card_id}_{interaction.id}')

        async def sell_leave_one_callback(button_interaction: discord.Interaction):
            if not await interaction_utils.check_user_permission(button_interaction, self.user_id, '只有原始命令發起者才能操作此按鈕。'):
                return
            card_to_leave_one = await self.collection_repo.get_user_card(self.user_id, card.card.card_id)
            if not card_to_leave_one:
                await interaction_utils.safe_send_message(button_interaction, '無法找到卡片或卡片已變更，操作取消。', ephemeral=True)
                return
            adjusted_quantity = card_to_leave_one.quantity - 1
            if adjusted_quantity <= 0:
                await interaction_utils.safe_send_message(button_interaction, '沒有多餘的卡片可賣（已是最後一張或更少），已取消操作。', ephemeral=True)
                return
            await interaction_utils.safe_defer(button_interaction, ephemeral=True)
            await self._execute_sell_operation(button_interaction, card_to_leave_one.card.card_id, adjusted_quantity, False, force_sell=True, leave_one=True)
        sell_leave_one_button.callback = sell_leave_one_callback
        leave_one_view.add_item(sell_leave_one_button)
        option_embed = discord.Embed(title='額外選項', description='您也可以選擇只賣出部分卡片，保留最後一張：', color=discord.Color.blue())
        if interaction.response.is_done():
            await interaction.followup.send(embed=option_embed, view=leave_one_view, ephemeral=True)
        else:
            logger.warning('Original interaction for leave_one_option was not done. This might lead to issues.')
            try:
                if not interaction.response.is_done():
                    await interaction.response.defer(ephemeral=True)
                await interaction.followup.send(embed=option_embed, view=leave_one_view, ephemeral=True)
            except discord.HTTPException as e:
                logger.error('Error sending leave_one_option followup: %s', e)
                await interaction.followup.send('顯示額外選項時發生錯誤。', ephemeral=True)

    async def _execute_sell_operation(self, interaction: discord.Interaction, card_id: int, quantity: int, is_sell_all_flag: bool, force_sell: bool=False, leave_one: bool=False):
        """執行賣卡操作"""
        if not interaction.response.is_done():
            await interaction_utils.safe_defer(interaction, ephemeral=True)
        filters = CollectionFilters(card_id=card_id)
        operation_type_str: str = 'ONE'
        if is_sell_all_flag:
            operation_type_str = 'ALL'
        elif leave_one:
            operation_type_str = 'LEAVE_ONE'
        try:
            # 使用純異常模式，直接調用服務並處理可能的異常
            raw_sell_result = await self.economy_service.sell_cards_universal(user_id=self.user_id, filters=filters, operation_type=operation_type_str, force_sell=force_sell)
            
            # 處理成功情況
            op_display_name = ''
            sold_count = raw_sell_result.get('total_cards_sold', 0)
            if operation_type_str == 'ONE':
                op_display_name = f'賣出 {sold_count} 張' if sold_count == quantity else f'賣出 {sold_count} (請求 {quantity})'
            elif operation_type_str == 'ALL':
                op_display_name = '賣出全部'
            elif operation_type_str == 'LEAVE_ONE':
                op_display_name = '賣到剩一張'
            
            title = f'卡片 {op_display_name} 成功'
            message = raw_sell_result.get('message', '操作完成。')
            
            if sold_count == 0 and operation_type_str != 'LEAVE_ONE' and ('找不到' in message or '不足' in message or '沒有可賣' in message.lower()):
                title = f'卡片 {op_display_name} 提醒'
            elif sold_count == 0 and operation_type_str != 'LEAVE_ONE':
                title = f'卡片 {op_display_name} (售出0張)'
                
            final_embed = EconomyCog._create_bulk_success_embed(interaction.user, raw_sell_result, title)
            await interaction_utils.safe_send_message(interaction, embed=final_embed, view=None, ephemeral=True)
            
            # 更新視圖
            if hasattr(self.view, '_clear_page_cache'):
                self.view._clear_page_cache(f'卡片 {card_id} 售出')
            if hasattr(self.view, '_update_page') and callable(self.view._update_page):
                await self.view._update_page(self.view.current_page, interaction)
            else:
                logger.warning('CardSellHandler: self.view (type: %s) does not have a callable _update_page method for refresh.', type(self.view))
                
        except ValueError as e:
            # 處理業務邏輯錯誤（如沒有符合條件的卡片）
            error_embed = discord.Embed(title='卡片賣出提醒', description=str(e), color=discord.Color.gold())
            await interaction_utils.safe_send_message(interaction, embed=error_embed, ephemeral=True)
            
        except StoredPriceError as e:
            # 處理價格相關錯誤
            error_embed = discord.Embed(title='卡片賣出失敗', description=f"價格錯誤: {str(e)}", color=discord.Color.red())
            await interaction_utils.safe_send_message(interaction, embed=error_embed, ephemeral=True)
            
        except UserNotFoundError as e:
            # 處理用戶不存在錯誤
            error_embed = discord.Embed(title='卡片賣出失敗', description=f"用戶不存在: {str(e)}", color=discord.Color.red())
            await interaction_utils.safe_send_message(interaction, embed=error_embed, ephemeral=True)
            
        except Exception as e:
            # 處理其他未預期錯誤
            logger.error('Error in _execute_sell_operation for card_id %s, user %s: %s', card_id, self.user_id, e, exc_info=True)
            error_embed = discord.Embed(title='執行賣卡操作時發生嚴重錯誤', description=f'處理您的請求時發生了未預期的問題。請稍後再試或聯繫管理員。\n錯誤參考: OpSell-{interaction.id}', color=discord.Color.red())
            try:
                await interaction_utils.safe_send_message(interaction, embed=error_embed, ephemeral=True)
            except discord.HTTPException as http_err:
                logger.error("Failed to send error message during _execute_sell_operation's own exception: %s", http_err)
            except Exception as final_err:
                logger.error('Unexpected error when sending error message in _execute_sell_operation: %s', final_err)

    async def sell_one_card_callback(self, interaction: discord.Interaction):
        """賣出當前卡片按鈕回調"""
        await self.process_sell_card(interaction, 1)

    async def sell_all_current_card_callback(self, interaction: discord.Interaction):
        """賣出當前卡片所有數量的按鈕回調"""
        if not await self.view.interaction_check(interaction):
            return
        if not await self.check_cards_available(interaction, '當前頁面沒有卡片可賣'):
            return
        card = self.current_view_card
        if not card:
            await interaction_utils.safe_send_message(interaction, '錯誤：找不到當前卡片。', ephemeral=True)
            return
        quantity_to_sell = card.quantity
        await self.process_sell_card(interaction, quantity_to_sell)