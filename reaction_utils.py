"""
工具模塊 - 包含通用的工具函數和數據結構
"""
import json
import os
import logging

# 設定日誌
logger = logging.getLogger(__name__)

# 表情符號觸發計數
reaction_count = {}

def load_reaction_count():
    """載入表情符號計數數據"""
    global reaction_count
    
    try:
        if os.path.exists('reaction_count.json'):
            with open('reaction_count.json', 'r', encoding='utf-8') as f:
                reaction_count = json.load(f)
            logger.info(f"已載入表情符號計數數據，共 {len(reaction_count)} 條記錄")
        else:
            logger.info("未找到表情符號計數數據文件，將創建新記錄")
            reaction_count = {}
    except Exception as e:
        logger.error(f"載入表情符號計數數據時出錯: {str(e)}")
        reaction_count = {}

def save_reaction_count():
    """保存表情符號計數數據"""
    try:
        with open('reaction_count.json', 'w', encoding='utf-8') as f:
            json.dump(reaction_count, f, ensure_ascii=False, indent=4)
        logger.info(f"已保存表情符號計數數據，共 {len(reaction_count)} 條記錄")
    except Exception as e:
        logger.error(f"保存表情符號計數數據時出錯: {str(e)}")