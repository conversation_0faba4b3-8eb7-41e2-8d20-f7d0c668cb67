import json
import psycopg2
import os
import argparse
from dotenv import load_dotenv

# 載入 .env 檔案中的環境變數 (假設 .env 在專案根目錄)
dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
load_dotenv(dotenv_path=dotenv_path)

# 資料庫連接設定 (從環境變數讀取)
DB_HOST = os.getenv("PG_HOST", "127.0.0.1")
DB_PORT = os.getenv("PG_PORT", "5432")
DB_NAME = os.getenv("GACHA_DB_NAME") # 必須在 .env 中設定
DB_USER = os.getenv("PG_USER") # 必須在 .env 中設定
DB_PASSWORD = os.getenv("PG_PASSWORD") # 必須在 .env 中設定

# 稀有度名稱到整數的映射 (用於 mazoku 格式)
RARITY_MAP_MAZOKU = {
    "C": 1,
    "R": 2,
    "SR": 3,
    "SSR": 4,
    "UR": 5
}

# 情人節卡池稀有度字串 (來自 JSON) 到整數的映射
# 假設 T1 -> 1, T2 -> 2, ..., T6 -> 6
RARITY_MAP_VALENTINE = {
    "1": 1,  # T1
    "2": 2,  # T2
    "3": 3,  # T3
    "4": 4,  # T4
    "5": 5,  # T5
    "6": 6   # T6 - 根據您的價格表，情人節卡池有T6
}

# 不同 pool_type 的價格設定 (鍵為稀有度整數值)
PRICE_CONFIG = {
    "vd": { # 情人節卡池 (Valentine's Day)
        1: 12,
        2: 25,
        3: 50,
        4: 130,
        5: 850,
        6: 45000
    },
    "special_maid": { # 典藏女僕卡池
        1: 35,    # C
        2: 130,   # R
        3: 1300,  # SR (修正後)
        4: 20000, # SSR
        5: 40000  # UR
    },
    "hololive": { # Hololive 卡池
        7: 60000, # R7
        6: 22000, # R6
        5: 7000,  # R5
        4: 800,   # R4
        3: 80,    # R3
        2: 12,    # R2
        1: 4      # R1
    },
    "ua": { # Union Arena 卡池
        7: 50000, # R7
        6: 20000, # R6
        5: 7000,  # R5
        4: 750,   # R4
        3: 70,    # R3
        2: 10,    # R2
        1: 3      # R1
    },
    "ptcg": { # Pokemon Trading Card Game 卡池
        7: 35000, # R7
        6: 15000, # R6
        5: 4000,  # R5
        4: 500,   # R4
        3: 50,    # R3
        2: 8,     # R2
        1: 3      # R1
    }
}

# 不同 pool_type 的描述後綴
POOL_DESCRIPTION_SUFFIX = {
    "vd": "情人節版本",
    "special_maid": "典藏女僕"
    # "ua" 不需要特殊後綴，將使用預設描述
}

def get_db_connection():
    """建立並返回資料庫連接"""
    if not all([DB_NAME, DB_USER, DB_PASSWORD, DB_HOST, DB_PORT]): # 確保所有必要的變數都被檢查
        print("錯誤：資料庫連接資訊 (GACHA_DB_NAME, PG_USER, PG_PASSWORD, PG_HOST, PG_PORT) 未在 .env 檔案中完整設定。")
        print(f"請確認 .env 檔案位於: {dotenv_path} 且包含必要設定。")
        return None
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        return conn
    except psycopg2.Error as e:
        print(f"資料庫連接失敗: {e}")
        return None

def create_description(name, series, pool_type):
    """根據提供的格式生成描述"""
    suffix = POOL_DESCRIPTION_SUFFIX.get(pool_type, "")
    if suffix:
        return f"{name} from {series} ({suffix})"
    return f"{name} from {series}"

def import_cards_from_file(file_path, pool_type, file_format_type):
    """從指定檔案匯入卡片資料"""
    cards_to_insert = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"錯誤：找不到檔案 {file_path}")
        return
    except json.JSONDecodeError:
        print(f"錯誤：解析 JSON 檔案 {file_path} 失敗")
        return

    print(f"開始處理檔案: {file_path}，卡池類型: {pool_type}，格式: {file_format_type}")

    for card_data in data:
        original_id = None
        name = None
        series = None
        image_url = None
        rarity_int = None
        
        if file_format_type == "mazoku":
            original_id = card_data.get("uuid")
            name = card_data.get("cardName")
            series = card_data.get("seriesName")
            image_url = card_data.get("mediaUrl")
            rarity_name_str = card_data.get("rarityName")
            if rarity_name_str:
                rarity_int = RARITY_MAP_MAZOKU.get(rarity_name_str)
            if not all([original_id, name, series, image_url, rarity_name_str]):
                print(f"警告 (mazoku)：卡片資料不完整，跳過：{card_data.get('uuid') or '未知ID'}")
                continue
            if rarity_int is None:
                print(f"警告 (mazoku)：卡片 '{name}' 的稀有度 '{rarity_name_str}' 無法對應，跳過。")
                continue

        elif file_format_type == "valentine":
            original_id = card_data.get("id")
            name = card_data.get("name", card_data.get("character_name")) # 'name' 優先
            series = card_data.get("series")
            image_url = card_data.get("mediaURL")
            rarity_str_from_json = card_data.get("rarity") # 這是字串 "1", "2" 等
            if rarity_str_from_json:
                 rarity_int = RARITY_MAP_VALENTINE.get(rarity_str_from_json)
            if not all([original_id, name, series, image_url, rarity_str_from_json]):
                print(f"警告 (valentine)：卡片資料不完整，跳過：{card_data.get('id') or '未知ID'}")
                continue
            if rarity_int is None:
                print(f"警告 (valentine)：卡片 '{name}' 的稀有度字串 '{rarity_str_from_json}' 無法對應，跳過。")
                continue

        elif file_format_type == "hololive":
            original_id = card_data.get("uuid")
            name = card_data.get("cardName")
            series = card_data.get("seriesName")
            image_url = card_data.get("mediaUrl")
            rarity_int = card_data.get("rarityLevel") # 直接是整數 1-7
            description = original_id # 描述直接使用 original_id

            if not all([original_id, name, series, image_url, isinstance(rarity_int, int)]):
                print(f"警告 (hololive)：卡片資料不完整或稀有度格式錯誤，跳過：{card_data.get('uuid') or '未知ID'}")
                continue
            if not 1 <= rarity_int <= 7:
                 print(f"警告 (hololive)：卡片 '{name}' 的稀有度 '{rarity_int}' 不在有效範圍 (1-7)，跳過。")
                 continue

        elif file_format_type == "ua": # 新增 Union Arena 格式處理
            original_id = card_data.get("original_id")
            name = card_data.get("cardName")
            series = card_data.get("seriesName")
            image_url = card_data.get("mediaUrl")
            rarity_int = card_data.get("rarityLevel") # 直接是整數 1-7

            if not all([original_id, name, series, image_url, isinstance(rarity_int, int)]):
                print(f"警告 (ua)：卡片資料不完整或稀有度格式錯誤，跳過：{card_data.get('original_id') or '未知ID'}")
                continue
            if not 1 <= rarity_int <= 7:
                 print(f"警告 (ua)：卡片 '{name}' 的稀有度 '{rarity_int}' 不在有效範圍 (1-7)，跳過。")
                 continue

        elif file_format_type == "ptcg": # 新增 Pokemon TCG 格式處理
            original_id = card_data.get("original_id")
            name = card_data.get("cardName")
            series = card_data.get("seriesName")
            image_url = card_data.get("mediaUrl")
            rarity_int = card_data.get("rarityLevel") # 直接是整數 1-7

            if not all([original_id, name, series, image_url, isinstance(rarity_int, int)]):
                print(f"警告 (ptcg)：卡片資料不完整或稀有度格式錯誤，跳過：{card_data.get('original_id') or '未知ID'}")
                continue
            if not 1 <= rarity_int <= 7:
                 print(f"警告 (ptcg)：卡片 '{name}' 的稀有度 '{rarity_int}' 不在有效範圍 (1-7)，跳過。")
                 continue
        else:
            print(f"錯誤：未知的檔案格式類型 {file_format_type}")
            return

        sell_price = PRICE_CONFIG.get(pool_type, {}).get(rarity_int)
        if sell_price is None:
            print(f"警告：卡片 '{name}' (稀有度 {rarity_int}) 在池子 '{pool_type}' 中找不到價格設定，將使用預設售價 10。")
            sell_price = 10 # schema.sql 中的預設值

        description = create_description(name, series, pool_type)

        cards_to_insert.append({
            "original_id": original_id,
            "name": name,
            "series": series,
            "image_url": image_url,
            "description": description,
            "sell_price": sell_price,
            "pool_type": pool_type,
            "rarity": rarity_int,
            "is_active": True, # 預設為 True
            # price_config, current_market_sell_price, last_price_update_at 使用資料庫預設或NULL
        })
    
    insert_cards_to_db(cards_to_insert)

def insert_cards_to_db(cards):
    """將卡片資料插入到資料庫"""
    if not cards:
        print("沒有卡片需要插入。")
        return

    conn = get_db_connection()
    if not conn:
        return

    inserted_count = 0
    skipped_count = 0
    
    try:
        with conn.cursor() as cursor:
            # schema.sql 中 gacha_master_cards 的 original_id 有 UNIQUE 約束
            # [`schema.sql:638`](d:/DICKPK/schema.sql:638)
            # 因此，如果 original_id 已存在，插入會失敗或被 ON CONFLICT 處理
            insert_query = """
            INSERT INTO public.gacha_master_cards 
            (original_id, name, series, image_url, description, sell_price, pool_type, rarity, is_active, creation_date)
            VALUES (%(original_id)s, %(name)s, %(series)s, %(image_url)s, %(description)s, %(sell_price)s, %(pool_type)s, %(rarity)s, %(is_active)s, CURRENT_TIMESTAMP)
            ON CONFLICT (original_id) DO NOTHING;
            """
            
            for card in cards:
                try:
                    cursor.execute(insert_query, card)
                    if cursor.rowcount > 0:
                        inserted_count += 1
                    else:
                        skipped_count += 1
                        print(f"資訊：卡片 original_id '{card['original_id']}' ({card['name']}) 已存在或插入未影響任何行，跳過。")
                except psycopg2.Error as e:
                    conn.rollback() # 回滾當前事務中的失敗插入
                    print(f"資料庫錯誤，插入卡片 '{card.get('name')}' (ID: {card.get('original_id')}) 失敗: {e}")
                    # 決定是否繼續處理下一張卡片或中止

            conn.commit()
            print(f"成功插入 {inserted_count} 張卡片。")
            if skipped_count > 0:
                print(f"因 original_id 已存在或其他原因跳過 {skipped_count} 張卡片。")

    except psycopg2.Error as e:
        print(f"資料庫操作錯誤: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="匯入卡片資料到資料庫")
    parser.add_argument("file_format_type", choices=["mazoku", "valentine", "hololive", "ua", "ptcg"],
                        help="要匯入的檔案格式類型 ('mazoku', 'valentine', 'hololive', 'ua' 或 'ptcg')")
    parser.add_argument("file_path", type=str, help="JSON 檔案的路徑")
    parser.add_argument("pool_type", type=str, help="卡池類型 (例如: special_maid, vd, ptcg)")
    
    args = parser.parse_args()

    # 檢查 .env 是否已正確載入DB連接資訊
    if not all([DB_NAME, DB_USER, DB_PASSWORD, DB_HOST, DB_PORT]):
        print("腳本執行前：請確保 .env 檔案已正確設定資料庫連接資訊 (GACHA_DB_NAME, PG_USER, PG_PASSWORD, PG_HOST, PG_PORT)。")
    else:
        print(f"準備匯入 {args.file_format_type} 格式的檔案: {args.file_path} 到卡池 {args.pool_type}")
        import_cards_from_file(args.file_path, args.pool_type, args.file_format_type)
        print("\n匯入流程結束。")
        print("提醒：根據您的指示，可能需要手動執行『系統卡池更新調用』。")