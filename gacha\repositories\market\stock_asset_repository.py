import asyncpg
from typing import Optional, List, Dict, Any
from datetime import datetime

from database.base_repository import BaseRepository
from gacha.models.market_models import StockAsset, StockLifecycleStatus
from utils.logger import logger
from gacha.exceptions import DatabaseOperationError, RecordNotFoundError

# from gacha.exceptions import DataMappingError # Potential future use for
# Pydantic errors


class StockAssetRepository(BaseRepository[StockAsset]):
    def __init__(self, pool: asyncpg.Pool):
        super().__init__(pool)

    async def get_asset_by_id(
        self, asset_id: int, conn: Optional[asyncpg.Connection] = None
    ) -> Optional[StockAsset]:
        """
        根據 asset_id 從 virtual_assets 表查詢股票數據，並將結果映射到 StockAsset Pydantic 模型返回。
        
        Args:
            asset_id: 資產ID
            conn: 可選的資料庫連接
            
        Returns:
            Optional[StockAsset]: 找到的股票資產，如果不存在則返回 None
            
        Raises:
            DatabaseOperationError: 資料庫操作失敗時拋出
        """
        try:
            query = """
                SELECT
                    asset_id, asset_symbol, asset_name, description,
                    current_price, linked_criteria_type, linked_criteria_value,
                    linked_pool_context, base_volatility, volatility_factor,
                    influence_weight, total_shares, created_at, last_updated,
                    initial_anchor_price, current_anchor_price, anchor_price_updated_at,
                    lifecycle_status
                FROM virtual_assets
                WHERE asset_id = $1
            """
            record = await self._fetchrow(query, (asset_id,), connection=conn)
            return StockAsset(**record) if record else None
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"查詢資產 {asset_id} 失敗: {e}") from e

    async def update_lifecycle_status(
        self,
        asset_id: int,
        new_status: StockLifecycleStatus,
        conn: Optional[asyncpg.Connection] = None,
    ) -> bool:
        """
        更新指定 asset_id 的股票的 lifecycle_status 欄位和 last_updated 時間戳。
        
        Args:
            asset_id: 資產ID
            new_status: 新的生命週期狀態
            conn: 可選的資料庫連接
            
        Returns:
            bool: 成功更新返回 True，記錄不存在返回 False
            
        Raises:
            DatabaseOperationError: 資料庫操作失敗時拋出
        """
        try:
            query = """
                UPDATE virtual_assets
                SET lifecycle_status = $1, last_updated = $2
                WHERE asset_id = $3
            """
            status = await self._execute(
                query, (new_status.value, datetime.utcnow(), asset_id), connection=conn
            )
            return status.startswith("UPDATE 1")
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"更新資產 {asset_id} 生命週期狀態失敗: {e}") from e

    async def get_active_or_st_stocks(
        self, conn: Optional[asyncpg.Connection] = None
    ) -> List[StockAsset]:
        """
        查詢所有 lifecycle_status 為 ACTIVE 或 ST 的股票，並將結果映射到 StockAsset 模型列表返回。
        查詢應包含計算市值所需的 current_price 和 total_shares。
        
        Args:
            conn: 可選的資料庫連接
            
        Returns:
            List[StockAsset]: 活躍或ST狀態的股票列表
            
        Raises:
            DatabaseOperationError: 資料庫操作失敗時拋出
        """
        try:
            query = """
                SELECT
                    asset_id, asset_symbol, asset_name, description,
                    current_price, linked_criteria_type, linked_criteria_value,
                    linked_pool_context, base_volatility, volatility_factor,
                    influence_weight, total_shares, created_at, last_updated,
                    initial_anchor_price, current_anchor_price, anchor_price_updated_at,
                    lifecycle_status
                FROM virtual_assets
                WHERE lifecycle_status = ANY($1)
            """
            statuses = [
                StockLifecycleStatus.ACTIVE.value,
                StockLifecycleStatus.ST.value]
            records = await self._fetch(query, (statuses,), connection=conn)
            return [StockAsset(**record) for record in records]
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"查詢活躍股票失敗: {e}") from e

    async def insert_stock(
        self, stock_data: Dict[str, Any], conn: Optional[asyncpg.Connection] = None
    ) -> Optional[StockAsset]:
        """
        向 virtual_assets 表插入一條新的股票記錄。
        stock_data 是一個包含所有必要欄位的字典。
        使用 RETURNING * 或插入後再查詢，以返回完整的 StockAsset 對象。
        
        Args:
            stock_data: 包含股票數據的字典
            conn: 可選的資料庫連接
            
        Returns:
            Optional[StockAsset]: 插入的股票資產，如果插入失敗則返回 None
            
        Raises:
            DatabaseOperationError: 資料庫操作失敗時拋出
        """
        try:
            if "lifecycle_status" in stock_data and isinstance(
                stock_data["lifecycle_status"], StockLifecycleStatus
            ):
                stock_data["lifecycle_status"] = stock_data["lifecycle_status"].value

            if "initial_anchor_price" in stock_data:
                stock_data["current_anchor_price"] = stock_data["initial_anchor_price"]

            stock_data.setdefault("created_at", datetime.utcnow())
            stock_data.setdefault("last_updated", datetime.utcnow())
            if "current_anchor_price" in stock_data:
                stock_data.setdefault("anchor_price_updated_at", datetime.utcnow())

            columns = ", ".join(stock_data.keys())
            placeholders = ", ".join(
                [f"${i+1}" for i in range(len(stock_data))]
            )

            query = f"""
                INSERT INTO virtual_assets ({columns})
                VALUES ({placeholders})
                RETURNING asset_id, asset_symbol, asset_name, description,
                          current_price, linked_criteria_type, linked_criteria_value,
                          linked_pool_context, base_volatility, volatility_factor,
                          influence_weight, total_shares, created_at, last_updated,
                          initial_anchor_price, current_anchor_price, anchor_price_updated_at,
                          lifecycle_status
            """

            values = list(stock_data.values())
            record = await self._fetchrow(query, values, connection=conn)
            return StockAsset(**record) if record else None
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"插入股票數據失敗: {e}") from e

    async def get_active_companies_count(
        self, conn: Optional[asyncpg.Connection] = None
    ) -> int:
        """
        獲取狀態為 ACTIVE 的公司數量。
        
        Args:
            conn: 可選的資料庫連接
            
        Returns:
            int: 活躍公司數量
            
        Raises:
            DatabaseOperationError: 資料庫操作失敗時拋出
        """
        try:
            query = "SELECT COUNT(*) FROM virtual_assets WHERE lifecycle_status = $1"
            count = await self._fetchval(
                query, (StockLifecycleStatus.ACTIVE.value,), connection=conn
            )
            return count if count is not None else 0
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"查詢活躍公司數量失敗: {e}") from e
