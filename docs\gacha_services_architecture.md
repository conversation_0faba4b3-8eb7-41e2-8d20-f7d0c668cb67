# GachaService 與 DrawEngineService 架構與核心流程

## 高層次架構圖

```mermaid
graph TD
    User --> GachaService;
    GachaService --> UserService;
    GachaService --> UserCollectionRepository;
    GachaService --> DrawEngineService;
    GachaService --> RedisEventPublisher;
    GachaService --> "asyncpg.Pool (Database)";
    DrawEngineService --> MasterCardRepository;
    DrawEngineService --> WishService;
    DrawEngineService --> settings;
    UserService --> UserRepository;
    WishService --> UserWishRepository;
    UserCollectionRepository --> Database;
    MasterCardRepository --> Database;
    UserRepository --> Database;
    UserWishRepository --> Database;
```

## 核心流程說明 (N 連抽)

1.  **用戶發起抽卡請求:** 用戶通過某個接口（例如 Discord 命令）發起一次 N 連抽請求，指定卡池類型和數量 N。
2.  **[`GachaService`](gacha/services/core/gacha_service.py) 接收請求:** [`GachaService`](gacha/services/core/gacha_service.py) 的 `draw_multiple_from_pools` 方法被調用。
3.  **計算抽卡費用:** [`GachaService`](gacha/services/core/gacha_service.py) 根據卡池類型和數量 N 計算總費用。
4.  **餘額預檢查 (可選):** [`GachaService`](gacha/services/core/gacha_service.py) 嘗試進行非事務性的餘額預檢查。如果餘額不足，提前返回錯誤。
5.  **啟動數據庫事務:** [`GachaService`](gacha/services/core/gacha_service.py) 啟動一個數據庫事務，確保後續的油幣扣除和卡片添加操作的原子性。
6.  **獲取用戶並鎖定餘額:** 在事務內，[`GachaService`](gacha/services/core/gacha_service.py) 通過 `UserService` 獲取用戶信息，並鎖定用戶餘額以防止並發問題。再次檢查餘額是否足夠。
7.  **扣除油幣:** [`GachaService`](gacha/services/core/gacha_service.py) 通過 `UserService` 扣除用戶的油幣。
8.  **委託抽卡邏輯給 [`DrawEngineService`](gacha/services/core/draw_engine_service.py):** [`GachaService`](gacha/services/core/gacha_service.py) 調用 [`DrawEngineService`](gacha/services/core/draw_engine_service.py) 的 `determine_cards_to_draw` 方法，傳入用戶 ID、有效卡池列表和數量 N。
9.  **[`DrawEngineService`](gacha/services/core/draw_engine_service.py) 決定卡片:**
    *   對於每一次抽卡 (共 N 次):
        *   根據配置和權重選擇一個卡池類型。
        *   根據配置和權重選擇一個稀有度。
        *   應用許願邏輯 (通過 `WishService` 和 `MasterCardRepository` 協作)，嘗試抽取用戶許願的卡片。
        *   如果許願邏輯未選中，回退到從該稀有度/卡池中隨機選擇卡片 (通過 `MasterCardRepository`)。
    *   [`DrawEngineService`](gacha/services/core/draw_engine_service.py) 返回最終抽到的卡片列表 (包含卡片對象和抽到的卡池類型) 和卡片 ID 列表。
10. **添加卡片到用戶收藏:** [`GachaService`](gacha/services/core/gacha_service.py) 在事務內通過 `UserCollectionRepository` 將抽到的卡片批量添加到用戶收藏中，並檢查哪些是新獲得的卡片。
11. **提交事務:** 如果油幣扣除和卡片添加都成功，[`GachaService`](gacha/services/core/gacha_service.py) 提交數據庫事務。
12. **發布市場統計事件:** 事務成功後，[`GachaService`](gacha/services/core/gacha_service.py) 通過 `RedisEventPublisher` 發布市場統計更新事件 (例如總持有量和唯一持有者數量變化)。
13. **豐富卡片信息:** [`GachaService`](gacha/services/core/gacha_service.py) 獲取抽到卡片的額外信息，例如許願狀態 (通過 `WishService`)、收藏狀態 (通過 `UserCollectionRepository`) 和全服持有者數量 (通過 `UserCollectionRepository`)。
14. **格式化結果並返回:** [`GachaService`](gacha/services/core/gacha_service.py) 將抽到的卡片列表 (包含狀態信息)、新的用戶餘額和是否為多連抽等信息格式化後返回給調用者。