"""
QA系統服務模組 - 處理問答系統的主要邏輯
"""

import logging
import os
import discord
import asyncio
import uuid
import re
import io
from typing import Dict, Any, Optional, List
from datetime import datetime

# 引入通用組件
from ..ai_service_base import AIServiceBase
from ..message_handler import MessageHandler
from .. import config
from ..prompts import QA_SYSTEM_PROMPT

# 設置日誌
logger = logging.getLogger("QAService")

# 全局 QA 系統實例
qa_system_instance: Optional['QASystem'] = None 

class QASystem(AIServiceBase):
    """問答系統處理類"""
    
    def __init__(self):
        """初始化QA系統"""
        super().__init__()
        self.message_handler = MessageHandler("QA")
        self.system_prompt = QA_SYSTEM_PROMPT
        logger.info("QA系統初始化完成")
    
    async def process_qa_request(
        self,
        question: str,
        user_id: Optional[str],
        response_message: discord.WebhookMessage,
        request_id: str
    ):
        """處理純文本問答請求"""
        try:
            # 更新處理中消息
            await response_message.edit(content=f"💬 正在思考問題: {question[:30]}...")
            
            # 調用API處理問題
            api_response = await self.process_text(
                prompt=question,
                system_prompt=self.system_prompt,
                request_id=request_id
            )
            
            # 提取並清理回答
            answer_text = self._sanitize_ai_response(self.extract_response_text(api_response))
            
            # 創建並發送回答
            embed = self.format_qa_embed(question, answer_text)
            await response_message.edit(content=None, embed=embed)
            
            logger.info(f"已完成問答請求 ID: {request_id}")
        except Exception as e:
            logger.error(f"處理問答請求時出錯: {str(e)}")
            await response_message.edit(content="❌ 處理問題時發生錯誤，請稍後再試。")
    
    async def process_image_qa_request(
        self,
        image_data: bytes,
        user_id: Optional[str],
        response_message: discord.WebhookMessage,
        request_id: str,
        question: Optional[str] = None
    ):
        """處理包含圖像的問答請求"""
        try:
            # 設置默認問題（如果未提供）
            if not question:
                question = "請描述這張圖片，並回答圖片中可能的問題。"
            
            # 更新處理中消息
            await response_message.edit(content=f"🔍 正在分析圖片和問題: {question[:30]}...")
            
            # 調用API處理圖像請求
            api_response = await self.process_with_image(
                image_data=image_data,
                prompt=question,
                system_prompt=self.system_prompt,
                request_id=request_id
            )
            
            # 提取並清理回答
            answer_text = self._sanitize_ai_response(self.extract_response_text(api_response))
            
            # 創建embed格式回應
            embed = self.format_qa_embed(question, answer_text, is_image_qa=True)
            
            # 添加原始圖像作為嵌入圖片
            try:
                image_file = discord.File(io.BytesIO(image_data), filename="image_full.jpg")
                embed.set_image(url="attachment://image_full.jpg")
                await response_message.edit(content=None, embed=embed, attachments=[image_file])
            except Exception as e:
                logger.error(f"設置圖片時出錯: {str(e)}")
                await response_message.edit(content=None, embed=embed)
            
            logger.info(f"已完成圖像問答請求 ID: {request_id}")
        except Exception as e:
            logger.error(f"處理圖像問答請求時出錯: {str(e)}")
            await response_message.edit(content="❌ 處理問題時發生錯誤，請稍後再試。")

    def format_qa_embed(self, question: str, answer_text: str, is_image_qa: bool = False) -> discord.Embed:
        """
        創建問答的Discord embed格式
        
        參數:
            question (str): 用戶問題
            answer_text (str): AI回答內容
            is_image_qa (bool): 是否為圖像問答
            
        返回:
            discord.Embed: 格式化的嵌入式消息
        """
        # 嘗試添加信息框（先於其他格式處理）
        answer_text = self._format_callout_boxes(answer_text)
        
        # 檢測回答中的特殊內容和主題類別
        has_code = "```" in answer_text
        has_table = "|" in answer_text and "-|-" in answer_text.replace(" ", "")
        has_list = any(line.strip().startswith(('- ', '• ', '* ', '1. ', '2. ')) for line in answer_text.split('\n'))
        has_markdown_headers = any(line.strip().startswith('#') for line in answer_text.split('\n'))
        has_callouts = "📌" in answer_text or "💡" in answer_text or "⚠️" in answer_text or "❗" in answer_text
        
        # 猜測答案的主題類別
        category = self._guess_answer_category(question, answer_text)
        
        # 選擇嵌入式消息顏色（基於內容類型）
        color = discord.Color.blue()  # 默認顏色
        if is_image_qa:
            color = discord.Color.purple()  # 圖像問答顏色
        elif has_code:
            color = discord.Color.dark_gold()  # 代碼回答顏色
        elif has_table:
            color = discord.Color.teal()  # 表格回答顏色
        elif has_callouts:
            color = discord.Color.orange()  # 帶有提示的回答顏色
        elif category == "技術":
            color = discord.Color.from_rgb(114, 137, 218)  # 技術類回答顏色
        elif category == "生活":
            color = discord.Color.from_rgb(67, 181, 129)  # 生活類回答顏色
        elif category == "教育":
            color = discord.Color.from_rgb(240, 71, 71)  # 教育類回答顏色
        
        # 創建嵌入式消息
        embed = discord.Embed(color=color, timestamp=discord.utils.utcnow())
        
        # 設置標題（問題）
        # 限制問題長度以避免標題過長
        max_title_length = 256  # Discord embed標題最大長度
        title_icon = "📷 " if is_image_qa else "❓ "
        if len(question) > max_title_length - len(title_icon):
            title = f"{title_icon}{question[:max_title_length-len(title_icon)-3]}..."
        else:
            title = f"{title_icon}{question}"
        embed.title = title
        
        # 處理回答內容 - 增強非格式化的普通文本內容
        if not has_code and not has_table and not has_markdown_headers:
            # 對普通文本進行增強處理
            paragraphs = answer_text.split("\n\n")
            enhanced_paragraphs = []
            
            for para in paragraphs:
                # 不處理已經有特殊格式的段落
                if any(marker in para for marker in ["📌 **提示**", "💡 **小貼士**", "⚠️ **警告**", "❗ **重要**"]):
                    enhanced_paragraphs.append(para)
                # 不處理列表項
                elif not para.strip().startswith(('- ', '• ', '* ', '1. ', '2. ')):
                    enhanced_paragraphs.append(self._enhance_keywords(para))
                else:
                    # 處理列表項段落
                    lines = para.split("\n")
                    enhanced_lines = []
                    for line in lines:
                        if line.strip().startswith(('- ', '• ', '* ')):
                            # 找到分隔符位置
                            prefix = line[:line.find(' ') + 1]
                            content = line[line.find(' ') + 1:]
                            # 處理冒號分隔的情況
                            if ': ' in content and len(content.split(': ')[0]) < 20:
                                parts = content.split(': ', 1)
                                line = f"{prefix}**{parts[0]}**: {parts[1]}"
                            else:
                                line = prefix + self._enhance_keywords(content)
                        enhanced_lines.append(line)
                    enhanced_paragraphs.append("\n".join(enhanced_lines))
            
            # 重新組合增強後的文本
            answer_text = "\n\n".join(enhanced_paragraphs)
        
        # 對於長度超過4096字符的回答（Discord嵌入描述的限制），我們需要分割它
        if len(answer_text) > 4096:
            # 分割為多個部分
            first_part = answer_text[:4000]  # 略小於限制以確保安全
            remaining = answer_text[4000:]
            
            # 設置主要內容
            embed.description = first_part
            
            # 添加剩餘內容作為字段
            embed.add_field(name="續(1)", value=remaining[:1024] if len(remaining) > 1024 else remaining, inline=False)
            
            # 如果還有更多內容，繼續添加字段
            if len(remaining) > 1024:
                for i, chunk in enumerate(self._chunk_text(remaining[1024:], 1024), 2):
                    embed.add_field(name=f"續({i})", value=chunk, inline=False)
        else:
            embed.description = answer_text
        
        # 添加元數據字段
        # 1. 添加分類標籤
        embed.add_field(name="📂 分類", value=f"**{category}**", inline=True)
        
        # 2. 添加內容格式
        format_value = "一般文本"
        if has_code:
            format_value = "程式碼"
        elif has_table:
            format_value = "表格數據"
        elif has_list:
            format_value = "列表項目"
        elif has_markdown_headers:
            format_value = "結構化文本"
        elif has_callouts:
            format_value = "包含提示框"
            
        embed.add_field(name="📄 格式", value=format_value, inline=True)
        
        # 3. 添加生成時間
        current_time = self._get_current_time()
        embed.add_field(name="⏱️ 生成時間", value=current_time, inline=True)
        
        # 設置頁腳
        embed.set_footer(text=f"回答由AI生成 • APEX智能助手")
        
        # 添加作者字段，用於顯示回答類型
        author_name = "AI 助手"
        author_icon = "💡"
        
        if is_image_qa:
            author_name = "圖像問答助手"
            author_icon = "🖼️"
        elif has_code:
            author_name = "程式碼助手"
            author_icon = "💻"
        elif has_table:
            author_name = "數據助手"
            author_icon = "📊"
        
        embed.set_author(name=f"{author_icon} {author_name}")
        
        return embed
    
    def _chunk_text(self, text: str, chunk_size: int) -> List[str]:
        """將文本分割成固定大小的塊"""
        return [text[i:i+chunk_size] for i in range(0, len(text), chunk_size)]
    
    def _format_callout_boxes(self, text: str) -> str:
        """
        偵測並格式化文本中的重要信息塊
        
        將帶有特定關鍵詞的段落轉換為醒目的信息框
        
        參數:
            text (str): 原始文本
            
        返回:
            str: 格式化後的文本
        """
        # 如果已經包含自定義格式的信息框，跳過處理
        if any(marker in text for marker in ["📌 **提示**", "💡 **小貼士**", "⚠️ **警告**", "❗ **重要**"]):
            return text
            
        # 分行處理文本
        lines = text.split('\n')
        formatted_lines = []
        i = 0
        
        while i < len(lines):
            current_line = lines[i].strip()
            
            # 檢測可能的重要信息開頭
            # 注意：我們檢測以「注意」、「提示」、「重要」等詞開頭的段落
            is_tip = current_line.startswith(('提示:', '提示：', '小貼士:', '小貼士：', '提示', '小貼士'))
            is_note = current_line.startswith(('注意:', '注意：', '說明:', '說明：', '備註:', '備註：', '注意', '說明', '備註'))
            is_warning = current_line.startswith(('警告:', '警告：', '注意事項:', '注意事項：', '警告', '注意事項'))
            is_important = current_line.startswith(('重要:', '重要：', '關鍵:', '關鍵：', '重要', '關鍵'))
            
            if is_tip or is_note or is_warning or is_important:
                # 確定信息框類型和圖標
                if is_tip:
                    icon = "💡"
                    title = "小貼士"
                elif is_note:
                    icon = "📌"
                    title = "提示"
                elif is_warning:
                    icon = "⚠️"
                    title = "警告"
                elif is_important:
                    icon = "❗"
                    title = "重要"
                
                # 提取內容（移除前綴）
                content = current_line
                for prefix in ['提示:', '提示：', '小貼士:', '小貼士：', '注意:', '注意：', 
                              '警告:', '警告：', '說明:', '說明：', '備註:', '備註：', 
                              '重要:', '重要：', '關鍵:', '關鍵：', '注意事項:', '注意事項：',
                              '提示', '小貼士', '注意', '說明', '備註', '警告', '注意事項', '重要', '關鍵']:
                    if content.startswith(prefix):
                        content = content[len(prefix):].strip()
                        if content.startswith((':', '：')):
                            content = content[1:].strip()
                        break
                
                # 在Discord embed中，我們使用更醒目的格式
                box_start = f"{icon} **{title}**："
                
                # 如果內容太短，則與標題合併在一行
                if len(content) < 20:
                    formatted_lines.append(f"{box_start} {content}")
                else:
                    formatted_lines.append(box_start)
                    # 為了在embed中更醒目，我們添加 > 引用符號
                    formatted_lines.append(f"> {content}")
                
                # 如果下一行是空行，我們跳過
                if i + 1 < len(lines) and not lines[i + 1].strip():
                    i += 1
            else:
                formatted_lines.append(lines[i])
            
            i += 1
        
        return '\n'.join(formatted_lines)
    
    def _enhance_keywords(self, text: str) -> str:
        """
        增強文本中的關鍵詞
        
        參數:
            text (str): 原始文本
            
        返回:
            str: 增強後的文本
        """
        # 如果已經包含markdown標記，則不處理
        if "**" in text or "__" in text or "*" in text or "_" in text:
            return text
            
        # 關鍵詞列表（可根據實際需要擴展）
        important_terms = [
            # 技術詞彙
            "Python", "Java", "JavaScript", "TypeScript", "HTML", "CSS", "SQL", "API", 
            "Docker", "Kubernetes", "Git", "React", "Vue", "Angular", "Node.js", "Django",
            "Flask", "Spring", "MongoDB", "MySQL", "PostgreSQL", "Redis", "AWS", "Azure",
            "Google Cloud", "Firebase", "Linux", "Windows", "macOS", "iOS", "Android",
            
            # 重要概念
            "注意", "警告", "重要", "關鍵", "必須", "不要", "切記", "建議", "推薦",
            "最佳實踐", "原理", "基本概念", "核心", "框架", "結構", "模式", "設計模式",
            
            # 常見程序設計術語
            "函數", "方法", "類", "對象", "實例", "繼承", "封裝", "多態", "接口",
            "異常處理", "線程", "進程", "同步", "異步", "回調", "Promise", "async", "await",
            
            # 數據結構和算法
            "數組", "列表", "字典", "映射", "集合", "堆棧", "隊列", "樹", "圖",
            "排序", "搜索", "遍歷", "深度優先", "廣度優先", "動態規劃", "貪心算法"
        ]
        
        # 逐個處理關鍵詞
        enhanced_text = text
        for term in important_terms:
            # 避免過度標記和部分匹配
            pattern = rf'\b{term}\b'
            if re.search(pattern, enhanced_text, re.IGNORECASE):
                # 使用正則表達式替換，保持原有大小寫
                enhanced_text = re.sub(
                    pattern, 
                    lambda m: f"**{m.group(0)}**", 
                    enhanced_text, 
                    flags=re.IGNORECASE
                )
                
        return enhanced_text
    
    def _guess_answer_category(self, question: str, answer: str) -> str:
        """
        根據問題和回答內容，猜測回答的主題分類
        
        參數:
            question (str): 用戶問題
            answer (str): 回答內容
            
        返回:
            str: 推測的分類名稱
        """
        # 組合問題和回答文本進行分析
        combined_text = f"{question} {answer}".lower()
        
        # 技術相關關鍵詞
        tech_keywords = [
            "程式", "編程", "代碼", "程式碼", "api", "函數", "方法", "類", "對象", 
            "數據庫", "sql", "html", "css", "javascript", "python", "java", "c++", "服務器", 
            "伺服器", "網路", "算法", "數據結構", "框架", "開發", "運行", "執行", "bug", 
            "錯誤", "調試", "測試", "部署", "git", "linux", "windows", "mac", "命令行",
            "指令", "系統", "軟體", "硬體", "網站", "應用", "app", "手機", "電腦", "技術"
        ]
        
        # 生活相關關鍵詞
        life_keywords = [
            "健康", "飲食", "運動", "旅遊", "旅行", "美食", "料理", "烹飪", "食譜", 
            "減肥", "健身", "瑜伽", "購物", "娛樂", "電影", "音樂", "書籍", "閱讀",
            "生活", "日常", "家居", "裝修", "親子", "育兒", "寵物", "感情", "愛情",
            "婚姻", "情緒", "心理", "溝通", "交流", "時尚", "穿搭", "美容", "護膚"
        ]
        
        # 教育相關關鍵詞
        edu_keywords = [
            "學習", "教育", "考試", "課程", "知識", "學校", "大學", "高中", "初中",
            "小學", "幼兒園", "教師", "學生", "作業", "論文", "研究", "科學", "歷史",
            "地理", "數學", "物理", "化學", "生物", "語文", "英語", "外語", "文學"
        ]
        
        # 計算各類關鍵詞在文本中的出現次數
        tech_count = sum(1 for word in tech_keywords if word in combined_text)
        life_count = sum(1 for word in life_keywords if word in combined_text)
        edu_count = sum(1 for word in edu_keywords if word in combined_text)
        
        # 根據出現頻率判斷類別
        if tech_count > life_count and tech_count > edu_count:
            return "技術"
        elif life_count > tech_count and life_count > edu_count:
            return "生活"
        elif edu_count > tech_count and edu_count > life_count:
            return "教育"
        else:
            # 如果沒有明確類別或類別相同，返回默認值
            return "一般知識"
    
    def _get_current_time(self) -> str:
        """獲取當前時間格式化字符串"""
        now = datetime.now()
        return now.strftime("%Y-%m-%d %H:%M:%S")

    def _sanitize_ai_response(self, text: str) -> str:
        """
        清理AI回應中可能存在的「思考過程」或「元註解」
        
        參數:
            text (str): 從AI回應中提取的原始文本
            
        返回:
            str: 清理後的文本，僅包含實際答案內容
        """
        if not text:
            return text
            
        # 移除常見的思考過程標記與模式
        # 1. 移除以星號包圍的思考過程 (如 *思考：...*)
        text = re.sub(r'\*思考[:：].*?\*', '', text, flags=re.DOTALL)
        text = re.sub(r'\*[Tt]hinking[:：].*?\*', '', text, flags=re.DOTALL)
        text = re.sub(r'\*[Nn]ote[:：].*?\*', '', text, flags=re.DOTALL)
        text = re.sub(r'\*.*?思考.*?\*', '', text, flags=re.DOTALL)
        text = re.sub(r'\*.*?[Tt]hinking.*?\*', '', text, flags=re.DOTALL)
        
        # 2. 移除明確的步驟標記 (如 "步驟1：分析問題")
        text = re.sub(r'步驟\s*\d+\s*[:：].*?(?=步驟\s*\d+\s*[:：]|$)', '', text, flags=re.DOTALL)
        text = re.sub(r'[Ss]tep\s*\d+\s*[:：].*?(?=[Ss]tep\s*\d+\s*[:：]|$)', '', text, flags=re.DOTALL)
        
        # 3. 移除草稿/校閱標記
        text = re.sub(r'草稿[:：].*?(?=草稿[:：]|$)', '', text, flags=re.DOTALL)
        text = re.sub(r'校閱[:：].*?(?=校閱[:：]|$)', '', text, flags=re.DOTALL)
        text = re.sub(r'[Dd]raft[:：].*?(?=[Dd]raft[:：]|$)', '', text, flags=re.DOTALL)
        text = re.sub(r'[Rr]eview[:：].*?(?=[Rr]eview[:：]|$)', '', text, flags=re.DOTALL)
        
        # 4. 移除引用指令的文本
        text = re.sub(r'根據指示.*?(?=\n|$)', '', text)
        text = re.sub(r'按照要求.*?(?=\n|$)', '', text)
        text = re.sub(r'[Aa]s instructed.*?(?=\n|$)', '', text)
        text = re.sub(r'[Aa]s required.*?(?=\n|$)', '', text)
        
        # 5. 移除指令複述
        text = re.sub(r'我需要用繁體中文回答.*?(?=\n|$)', '', text)
        text = re.sub(r'我將使用.*?(?=\n|$)', '', text)
        text = re.sub(r'我應該.*?(?=\n|$)', '', text)
        text = re.sub(r'[Ii] need to respond in.*?(?=\n|$)', '', text)
        text = re.sub(r'[Ii] should.*?(?=\n|$)', '', text)
        text = re.sub(r'[Ii] will.*?(?=\n|$)', '', text)
        
        # 6. 移除常見的自我反思句子
        text = re.sub(r'讓我思考一下.*?(?=\n|$)', '', text)
        text = re.sub(r'首先，我需要.*?(?=\n|$)', '', text)
        text = re.sub(r'[Ll]et me think.*?(?=\n|$)', '', text)
        text = re.sub(r'[Ff]irst, I need to.*?(?=\n|$)', '', text)
        
        # 7. 移除數字列表標記後的思考過程
        text = re.sub(r'\d+\.\s*思考[:：].*?(?=\d+\.\s*|$)', '', text, flags=re.DOTALL)
        text = re.sub(r'\d+\.\s*[Tt]hinking[:：].*?(?=\d+\.\s*|$)', '', text, flags=re.DOTALL)
        
        # 8. 移除任何提及 ai_payload 標籤的文本
        text = re.sub(r'.*?ai_payload.*?(?=\n|$)', '', text, flags=re.IGNORECASE)
        text = re.sub(r'.*?標籤.*?(?=\n|$)', '', text)
        text = re.sub(r'.*?tag.*?(?=\n|$)', '', text, flags=re.IGNORECASE)
        
        # 9. 移除特定的不當內容關鍵詞
        inappropriate_terms = [
            "Apex", "nsfw", "NSFW", "創作出最頂級的作品", "虛擬世界", 
            "不拒絕創作", "不會回避任何細節", "不進行道德評判"
        ]
        for term in inappropriate_terms:
            text = text.replace(term, "")
        
        # 移除多餘空行
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # 去除首尾空白
        return text.strip()

    async def process_request_from_handler(
        self,
        image_data: Optional[bytes], # 由 MessageHandler 提供
        user_id: str,                # 由 MessageHandler 提供
        processing_message: discord.WebhookMessage, # 由 MessageHandler 提供
        request_id: str,             # 由 MessageHandler 提供
        user_prompt: Optional[str] = None # 由 MessageHandler 提供 (來自斜線命令的 question 或 Modal 輸入)
    ):
        """由 MessageHandler 調用，以統一處理圖像和文本問答請求。"""
        if image_data:
            # 如果提供了圖像數據，則調用圖像問答處理器
            # user_prompt 此時可能為 None (如果用戶只想讓 AI 描述圖片) 或包含針對圖片的問題
            await self.process_image_qa_request(
                image_data=image_data,
                user_id=user_id,
                response_message=processing_message,
                request_id=request_id,
                question=user_prompt # 即使是 None 也傳遞，process_image_qa_request 內部會處理
            )
        elif user_prompt: 
            # 如果沒有圖像數據，但有用戶提示 (問題)，則調用純文本問答處理器
            await self.process_qa_request(
                question=user_prompt,
                user_id=user_id,
                response_message=processing_message,
                request_id=request_id
            )
        else:
            # 這種情況理論上不應該發生，因為 ask_question_slash 或 Modal 會確保至少有其中一個
            logger.warning(f"[{request_id}] process_request_from_handler called with no image_data and no user_prompt.")
            try:
                await processing_message.edit(content="❌ 請求內容為空（沒有圖像或問題文本），無法處理。")
            except Exception as e_edit:
                logger.error(f"[{request_id}] Failed to edit message for empty request: {e_edit}")

# 全局的 MessageHandler 實例，由 QASystem 內部管理
# 我們將依賴 qa_system_instance.message_handler

async def init_services() -> bool:
    """初始化QA系統服務。如果已初始化，則重置現有實例。"""
    global qa_system_instance
    logger.info("Attempting to initialize/ensure QAService is operational...")

    # 如果實例已存在，先將其重置為 None
    # Python 的垃圾回收應該會處理舊的 QASystem 實例
    # 如果 QASystem 未來有需要顯式異步清理的資源，這裡需要添加 await qa_system_instance.shutdown() 之類的調用
    if qa_system_instance is not None:
        logger.info("Existing QASystem instance found. Resetting it before re-initialization.")
        qa_system_instance = None 

    try:
        qa_system_instance = QASystem()
        logger.info("QA系統服務 (QAService) 已成功初始化/重新初始化。")
        return True
    except Exception as e:
        logger.error(f"初始化QA系統服務 (QAService) 時發生錯誤: {str(e)}", exc_info=True)
        qa_system_instance = None
        return False

async def shutdown_services():
    """關閉QA系統服務。"""
    global qa_system_instance
    logger.info("Shutting down QAService explicitly...")
    if qa_system_instance:
        # 如果 QASystem 類未來有異步關閉方法，例如 qa_system_instance.close() 或 .shutdown()
        # 則應該在此處調用: await qa_system_instance.shutdown()
        logger.info("QASystem instance is being reset.")
    qa_system_instance = None
    logger.info("QAService explicit shutdown complete. Instance reset to None.")

async def ask_question_slash(
    interaction: discord.Interaction, 
    question: Optional[str] = None,  # 將 question 設為 Optional，因為可以通過 modal 輸入
    image: Optional[discord.Attachment] = None,
    use_modal: Optional[bool] = False # 新增參數，決定是否使用 Modal 提問
):
    """處理問答的Discord斜線命令 (文本或圖像)\n    新增 use_modal 參數以支持彈窗輸入模式\n    """
    if not qa_system_instance or not qa_system_instance.message_handler:
        await interaction.response.send_message("❌ 問答服務暫時不可用 (服務未初始化)，請稍後再試。", ephemeral=True)
        logger.warning("ask_question_slash called but qa_system_instance or its message_handler is not initialized.")
        return

    # 獲取 message_handler 實例
    current_message_handler = qa_system_instance.message_handler

    # 如果使用 Modal 進行提問 (use_modal=True)
    if use_modal:
        # 創建並發送 QAModal
        qa_modal = QAModal(qa_system_instance=qa_system_instance, original_interaction=interaction, attached_image=image)
        await interaction.response.send_modal(qa_modal)
        # Modal 提交後會處理後續邏輯，此處直接返回
        return

    # 如果不使用 Modal (use_modal=False 或未提供)，則直接處理
    if not question and not image:
        # 如果沒有問題也沒有圖片，並且不是 Modal 模式，提示用戶
        # 在命令定義時，應該將 question 設為 required=False (如果支持無問題僅圖片，或總是 modal)
        # 或者，如果必須有 question (當非 modal 且無圖片時)，則 discord.py 的命令檢查會處理
        # 此處假設可以無 question 觸發 (例如，準備用 modal)，但如果 modal=False 且無 question/image 則無效
        # 實際上 discord.py 的 @app_commands.describe 會強制要求 question (除非設置為可選)
        # 這裡的檢查是為了更明確的用戶反饋，如果命令允許這樣調用
        await interaction.response.send_message("❓ 請提供問題或上傳圖片進行分析。如果想通過彈窗輸入，請使用 `/ask use_modal:True`。", ephemeral=True)
        return
    
    # 確定處理消息文本
    processing_message_text = "💬 正在處理您的問題..."
    if image and question:
        processing_message_text = f"🖼️🔍 正在分析圖片並思考問題: {question[:30]}..."
    elif image:
        processing_message_text = "🖼️ 正在分析圖片..."
    elif question: # 確保 question 存在
        processing_message_text = f"💬 正在思考問題: {question[:30]}..."

    # 使用 MessageHandler 處理交互
    await current_message_handler.handle_interaction(
        interaction=interaction,
        processing_message=processing_message_text,
        processor_func=qa_system_instance.process_request_from_handler, # QASystem 需要一個新方法來接收來自 MessageHandler 的參數
        image=image,
        user_prompt=question, # 將 question 作為 user_prompt 傳遞
        allow_history_scan=False # 禁止掃描歷史記錄中的圖片
    )

# === Modal for QA ===
# 由於 QAModal 內部會引用 qa_system_instance，確保它在使用時是有效的
class QAModal(discord.ui.Modal, title='向AI提問'):
    def __init__(self, qa_system_instance: QASystem, original_interaction: discord.Interaction, attached_image: Optional[discord.Attachment] = None):
        super().__init__(timeout=300) # 5 分鐘超時
        self.qa_system_instance = qa_system_instance
        self.original_interaction = original_interaction # 保存原始交互，用於後續響應
        self.attached_image = attached_image # 保存附件

        self.question_input = discord.ui.TextInput(
            label='您的問題',
            style=discord.TextStyle.paragraph,
            placeholder='請在此輸入您的問題...\n如果您在命令中已附帶圖片，它將與此問題一起提交。',
            required=True,
            max_length=1500
        )
        self.add_item(self.question_input)

    async def on_submit(self, interaction: discord.Interaction):
        question = self.question_input.value
        
        # 防禦性檢查
        if not self.qa_system_instance or not self.qa_system_instance.message_handler:
            await interaction.response.send_message("❌ 問答服務暫時不可用 (實例丟失)，請稍後再試。", ephemeral=True)
            logger.error("QAModal on_submit: qa_system_instance or its message_handler is not available.")
            return
        
        # 此處 interaction 是 Modal 的提交交互，我們需要用它來發送初始的 "處理中" 消息
        # 然後，後續的處理邏輯（包括編輯此消息）將使用原始交互的 followup Webhook
        # 或 MessageHandler 內部處理這個問題

        processing_message_text = "💬 正在處理您在彈窗中提交的問題..."
        if self.attached_image and question:
            processing_message_text = f"🖼️🔍 正在分析圖片並思考問題 (來自彈窗): {question[:30]}..."
        elif self.attached_image:
            processing_message_text = "🖼️ 正在分析圖片 (來自彈窗)..."
        elif question:
            processing_message_text = f"💬 正在思考問題 (來自彈窗): {question[:30]}..."

        # MessageHandler 的 handle_interaction 期望一個 discord.Interaction 來發送初始響應。
        # Modal 的提交交互 (interaction) 可以用於此。
        await self.qa_system_instance.message_handler.handle_interaction(
            interaction=interaction, # 使用 Modal 的提交交互來發送初始響應
            processing_message=processing_message_text,
            processor_func=self.qa_system_instance.process_request_from_handler,
            image=self.attached_image, # 使用原始命令中附帶的圖片
            user_prompt=question, # 使用 Modal 中輸入的問題
            allow_history_scan=False # 禁止掃描歷史記錄中的圖片
        )
        # 注意: MessageHandler.handle_interaction 應該能夠處理 interaction.response.is_done() 的情況
        # 如果 Modal 的 on_submit 已經響應了 (例如 send_message)，它可能需要使用 interaction.followup
        # 但 MessageHandler 的設計是它自己發送初始的 processing_message

    async def on_error(self, interaction: discord.Interaction, error: Exception) -> None:
        logger.error(f"QAModal encountered an error: {error}", exc_info=True)
        await interaction.response.send_message('❌ 提交問題時發生錯誤，請稍後再試。', ephemeral=True)


# 在 QASystem 類中，我們需要添加一個 `process_request_from_handler` 方法
# 這個方法將被 `message_handler.handle_interaction` 調用
# QASystem 類的修改示例:
"""
class QASystem(AIServiceBase):
    # ... (現有 __init__ 和其他方法) ...

    async def process_request_from_handler(
        self,
        image_data: Optional[bytes], # 由 MessageHandler 提供
        user_id: str,                # 由 MessageHandler 提供
        processing_message: discord.WebhookMessage, # 由 MessageHandler 提供
        request_id: str,             # 由 MessageHandler 提供
        user_prompt: Optional[str] = None # 由 MessageHandler 提供 (來自斜線命令的 question 或 Modal 輸入)
    ):
        if image_data:
            await self.process_image_qa_request(
                image_data=image_data,
                user_id=user_id,
                response_message=processing_message,
                request_id=request_id,
                question=user_prompt
            )
        elif user_prompt: # 確保有問題才處理純文本請求
            await self.process_qa_request(
                question=user_prompt,
                user_id=user_id,
                response_message=processing_message,
                request_id=request_id
            )
        else:
            # 這種情況理論上不應發生，因為 ask_question_slash 或 Modal 會確保有內容
            logger.warning(f"[{request_id}] process_request_from_handler called with no image and no prompt.")
            await processing_message.edit(content="❌ 請求內容為空，無法處理。")

"""
# 上述 QASystem 的修改需要在實際的類定義中進行。
# 這裡的 ask_question_slash 和 QAModal 已經更新為使用它。


# 在 QASystem 類中，我們需要添加一個 `process_request_from_handler` 方法
# 這個方法將被 `message_handler.handle_interaction` 調用
# QASystem 類的修改示例:
"""
class QASystem(AIServiceBase):
    # ... (現有 __init__ 和其他方法) ...

    async def process_request_from_handler(
        self,
        image_data: Optional[bytes], # 由 MessageHandler 提供
        user_id: str,                # 由 MessageHandler 提供
        processing_message: discord.WebhookMessage, # 由 MessageHandler 提供
        request_id: str,             # 由 MessageHandler 提供
        user_prompt: Optional[str] = None # 由 MessageHandler 提供 (來自斜線命令的 question 或 Modal 輸入)
    ):
        if image_data:
            await self.process_image_qa_request(
                image_data=image_data,
                user_id=user_id,
                response_message=processing_message,
                request_id=request_id,
                question=user_prompt
            )
        elif user_prompt: # 確保有問題才處理純文本請求
            await self.process_qa_request(
                question=user_prompt,
                user_id=user_id,
                response_message=processing_message,
                request_id=request_id
            )
        else:
            # 這種情況理論上不應發生，因為 ask_question_slash 或 Modal 會確保有內容
            logger.warning(f"[{request_id}] process_request_from_handler called with no image and no prompt.")
            await processing_message.edit(content="❌ 請求內容為空，無法處理。")

"""
# 上述 QASystem 的修改需要在實際的類定義中進行。
# 這裡的 ask_question_slash 和 QAModal 已經更新為使用它。 