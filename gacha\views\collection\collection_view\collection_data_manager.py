"""
卡片集合數據管理模組
負責卡片數據的獲取、緩存和預加載邏輯
"""
import asyncio
import hashlib
import json
import time
from typing import Any, Dict, List, Optional, Set, Tuple
from utils.logger import logger
import asyncpg
from gacha.models.filters import CollectionFilters
from gacha.models.models import UserCard
from gacha.services.core.collection_service import CollectionService
from .gacha_services import GachaServices

class CollectionDataManager:
    """管理卡片集合數據的獲取和緩存"""

    def __init__(self, services: GachaServices, user_id: int):
        """初始化集合數據管理器

        參數:
            services: 統一的服務容器
            user_id: 用戶ID
        """
        self.services = services
        self.user_id = user_id
        self.cache: Dict[str, Any] = {}
        self.prefetch_tasks: Set[asyncio.Task] = set()
        self.prefetch_start_page: Optional[int] = None
        self.prefetch_end_page: Optional[int] = None
        self.last_sort_by: Optional[str] = None
        self.last_sort_order: Optional[str] = None
        self.last_filters_hash: Optional[str] = None
        self.last_custom_sort_mode: bool = False
        self.cached_total_cards: Optional[int] = None
        self.cached_unique_cards: Optional[int] = None
        self.cached_total_pages: Optional[int] = None

    @property
    def collection_service(self) -> CollectionService:
        """統一的收藏服務訪問接口

        遵循系統的服務容器模式，通過 services 訪問服務
        """
        return self.services.collection

    def _generate_cache_key(self, page: int, sort_by: str, sort_order: str, filters: Optional[CollectionFilters]) -> str:
        """生成緩存鍵

        參數:
            page: 頁碼
            sort_by: 排序字段
            sort_order: 排序順序
            filters: 過濾條件

        返回:
            str: 唯一標識當前查詢的緩存鍵
        """
        filters_str = str(filters) if filters else ''
        key_parts = [str(page), sort_by, sort_order, filters_str]
        combined = '_'.join(key_parts)
        return hashlib.md5(combined.encode()).hexdigest()

    async def get_page_data(self, page: int, sort_by: str, sort_order: str, filters: Optional[CollectionFilters]=None, custom_sort_mode: bool=False) -> Tuple[List[UserCard], int, int, int]:
        """獲取頁面數據，優先使用緩存，並優化統計查詢"""
        current_filters = filters if filters is not None else CollectionFilters()
        current_filters_hash = hashlib.md5(str(current_filters).encode()).hexdigest()
        sorting_changed = self.last_sort_by != sort_by or self.last_sort_order != sort_order or self.last_filters_hash != current_filters_hash or (self.last_custom_sort_mode != custom_sort_mode)
        if custom_sort_mode:
            sorting_changed = True
        if sorting_changed:
            self.clear_cache(f'排序或過濾條件變化: 舊={self.last_sort_by}-{self.last_sort_order}, 新={sort_by}-{sort_order}')
            self.last_sort_by = sort_by
            self.last_sort_order = sort_order
            self.last_filters_hash = current_filters_hash
            self.last_custom_sort_mode = custom_sort_mode
        cache_key = self._generate_cache_key(page, sort_by, sort_order, current_filters)
        if cache_key in self.cache:
            cached_page_data = self.cache[cache_key]
            self.cached_total_cards = cached_page_data[1]
            self.cached_unique_cards = cached_page_data[2]
            self.cached_total_pages = cached_page_data[3]
            logger.debug('[GACHA_DIAGNOSIS] CollectionDataManager.get_page_data: Cache hit for page %s. Returning cached data.', page)
            return cached_page_data
        logger.debug('[GACHA_DIAGNOSIS] CollectionDataManager.get_page_data: Cache miss for page %s, sort_by=%s, sort_order=%s, filters_hash=%s. Fetching fresh data.', page, sort_by, sort_order, current_filters_hash)
        paginated_data = await self.collection_service.get_user_cards_paginated(user_id=self.user_id, page=page, sort_by=sort_by, sort_order=sort_order, filters=current_filters)
        if 'error' in paginated_data and paginated_data['error']:
            logger.error('[GACHA] Error from collection_service.get_user_cards_paginated: %s', paginated_data['error'])
            return ([], 0, 0, 0)
        cards = paginated_data.get('cards', [])
        total_cards_stat = paginated_data.get('total_cards', 0)
        unique_cards_stat = paginated_data.get('unique_cards', 0)
        total_pages_from_service = paginated_data.get('total_pages', 0)
        self.cached_total_cards = total_cards_stat
        self.cached_unique_cards = unique_cards_stat
        self.cached_total_pages = total_pages_from_service
        page_data_tuple = (cards, total_cards_stat, unique_cards_stat, total_pages_from_service)
        self.cache[cache_key] = page_data_tuple
        logger.debug('[GACHA_DIAGNOSIS] CollectionDataManager.get_page_data: Fetched and cached data for page %s. Total pages: %s', page, total_pages_from_service)
        asyncio.create_task(self._prefetch_adjacent_pages(page, sort_by, sort_order, current_filters))
        return page_data_tuple

    async def _prefetch_adjacent_pages(self, current_page: int, sort_by: str, sort_order: str, filters: Optional[CollectionFilters]) -> None:
        """預加載相鄰頁面數據"""
        current_filters = filters if filters is not None else CollectionFilters()
        if self.last_custom_sort_mode:
            return
        prefetch_start = max(1, current_page - 1)
        prefetch_end = current_page + 2
        current_filters_hash = hashlib.md5(str(current_filters).encode()).hexdigest()
        if self.prefetch_start_page == prefetch_start and self.prefetch_end_page == prefetch_end and (self.last_sort_by == sort_by) and (self.last_sort_order == sort_order) and (self.last_filters_hash == current_filters_hash):
            return
        self.prefetch_start_page = prefetch_start
        self.prefetch_end_page = prefetch_end
        for task in self.prefetch_tasks:
            if not task.done():
                task.cancel()
        self.prefetch_tasks.clear()
        for page in range(prefetch_start, prefetch_end + 1):
            if self.cached_total_pages is not None and page > self.cached_total_pages:
                continue
            if page != current_page:
                cache_key = self._generate_cache_key(page, sort_by, sort_order, current_filters)
                if cache_key not in self.cache:
                    task = asyncio.create_task(self._prefetch_page(page, sort_by, sort_order, current_filters))
                    self.prefetch_tasks.add(task)
                    task.add_done_callback(self.prefetch_tasks.discard)

    async def _prefetch_page(self, page: int, sort_by: str, sort_order: str, filters: Optional[CollectionFilters]) -> None:
        """預加載指定頁面"""
        try:
            current_filters = filters if filters is not None else CollectionFilters()
            cache_key = self._generate_cache_key(page, sort_by, sort_order, current_filters)
            if cache_key not in self.cache:
                logger.debug('[GACHA][PREFETCH] Prefetching page %s data for user %s, sort: %s %s, filters_hash: %s.', page, self.user_id, sort_by, sort_order, hashlib.md5(str(current_filters).encode()).hexdigest())
                paginated_data = await self.collection_service.get_user_cards_paginated(user_id=self.user_id, page=page, sort_by=sort_by, sort_order=sort_order, filters=current_filters)
                if 'error' in paginated_data and paginated_data['error']:
                    logger.warning('[GACHA][PREFETCH] Error prefetching page %s for user %s: %s', page, self.user_id, paginated_data['error'])
                    return
                cards = paginated_data.get('cards', [])
                total_cards = paginated_data.get('total_cards', 0)
                unique_cards = paginated_data.get('unique_cards', 0)
                total_pages = paginated_data.get('total_pages', 0)
                self.cache[cache_key] = (cards, total_cards, unique_cards, total_pages)
                logger.debug('[GACHA][PREFETCH] Cached prefetched data for page %s, user %s. Fetched cards: %s', page, self.user_id, len(cards))
        except asyncio.CancelledError:
            logger.debug('[GACHA][PREFETCH] Prefetch task for page %s, user %s cancelled.', page, self.user_id)
        except Exception as e:
            logger.error('[GACHA][PREFETCH] Error prefetching page %s for user %s: %s', page, self.user_id, e, exc_info=True)

    def clear_cache(self, reason: str='') -> None:
        """清空緩存"""
        self.cache.clear()
        self.prefetch_start_page = None
        self.prefetch_end_page = None
        self.cached_total_cards = None
        self.cached_unique_cards = None
        self.cached_total_pages = None
        for task in self.prefetch_tasks:
            if not task.done():
                task.cancel()
        self.prefetch_tasks.clear()

    def invalidate_for_card(self, card_id: int) -> None:
        """使特定卡片相關的緩存失效"""
        self.clear_cache(f'卡片 {card_id} 數據變更')