import math
from typing import Dict, Any, List, Optional
from enum import Enum
from gacha.constants import RarityLevel
from gacha.models.shop_models import ShopItemDefinition, ExchangeSessionData
from gacha.models.models import Card
from gacha.exceptions import (
    NoCardsSelectedError, InvalidCardSelectionError, InvalidRarityError,
    ShopCardDetailsNotFoundError, ShopCardNotFoundError, TransactionError
)
from utils.logger import logger
from .base_ticket_service import BaseTicketService

class SpecificTicketService(BaseTicketService):
    """專門處理指定票券兌換服務"""

    async def process_specific_ticket_exchange(self, user_id: int, ticket_definition: ShopItemDefinition, selected_card_ids: List[int], session_id: str) -> Dict[str, Any]:
        """
        處理指定票券兌換流程，驗證卡片選擇並執行交易

        Args:
            user_id: 用戶ID
            ticket_definition: 票券定義
            selected_card_ids: 已選擇的卡片ID列表
            session_id: 會話ID

        Returns:
            Dict包含處理結果
            
        Raises:
            NoCardsSelectedError: 沒有選擇任何卡片
            InvalidCardSelectionError: 卡片選擇無效
            TransactionError: 交易執行失敗
        """
        expected_quantity = len(selected_card_ids)
        if expected_quantity == 0:
            raise NoCardsSelectedError()
            
        # 驗證卡片選擇
        await self._validate_selected_cards(card_ids=selected_card_ids, ticket_definition=ticket_definition)
        
        # 執行交易
        async with self.pool.acquire() as conn:
            transaction_result = await self._execute_exchange_transaction(
                conn=conn, 
                user_id=user_id, 
                session_id=session_id, 
                ticket_definition=ticket_definition, 
                quantity=expected_quantity, 
                cards_to_grant=selected_card_ids
            )
            
        # 交易成功，返回兌換的卡片信息
        granted_cards_info = transaction_result.get('granted_cards_info_for_embed', [])
        return {
            'exchanged_cards': granted_cards_info
        }

    async def _validate_selected_cards(self, card_ids: List[int], ticket_definition: ShopItemDefinition) -> None:
        """
        驗證所選卡片是否符合票券定義的要求

        Args:
            card_ids: 卡片ID列表
            ticket_definition: 票券定義

        Raises:
            ShopCardDetailsNotFoundError: 無法獲取卡片詳情
            InvalidCardSelectionError: 卡片選擇無效
        """
        if not ticket_definition.pool_type and ticket_definition.rarity is None:
            return  # 沒有限制條件，直接通過
            
        unique_card_ids = list(set(card_ids))
        cards_details = await self.master_card_repo.get_cards_details_by_ids(unique_card_ids)
        
        if not cards_details or len(cards_details) != len(unique_card_ids):
            missing_count = len(unique_card_ids) - len(cards_details) if cards_details else len(unique_card_ids)
            raise ShopCardDetailsNotFoundError(card_ids=unique_card_ids, missing_count=missing_count)
            
        card_details_map = {card.card_id: card for card in cards_details}
        invalid_cards = []
        
        for card_id in card_ids:
            if card_id not in card_details_map:
                continue
                
            card = card_details_map[card_id]
            if ticket_definition.pool_type and card.pool_type != ticket_definition.pool_type:
                invalid_cards.append({
                    'card_id': card.card_id, 
                    'reason': f'卡池類型不符 (要求: {ticket_definition.pool_type}, 實際: {card.pool_type})'
                })
                continue
                
            if ticket_definition.rarity is not None:
                card_rarity_int = card.rarity.value if hasattr(card.rarity, 'value') else card.rarity
                if card_rarity_int != ticket_definition.rarity:
                    invalid_cards.append({
                        'card_id': card.card_id, 
                        'reason': f'稀有度不符 (要求: {ticket_definition.rarity}, 實際: {card_rarity_int})'
                    })
                    
        if invalid_cards:
            raise InvalidCardSelectionError(invalid_cards=invalid_cards)

    async def get_specific_ticket_card_page(self, ticket_definition: ShopItemDefinition, page_number: int=1, items_per_page: int=1) -> Dict[str, Any]:
        """
        獲取指定兌換券可選卡片的分頁列表

        Args:
            ticket_definition: 票券定義，包含篩選條件
            page_number: 頁碼，從1開始
            items_per_page: 每頁項目數

        Returns:
            包含卡片和分頁信息的字典
            
        Raises:
            InvalidRarityError: 稀有度值無效
        """
        pool_type_filter = ticket_definition.pool_type
        rarity_filter_int = ticket_definition.rarity
        rarity_level_enum_filter: Optional[RarityLevel] = None
        
        if rarity_filter_int is not None:
            try:
                rarity_level_enum_filter = RarityLevel(rarity_filter_int)
            except ValueError as ve:
                logger.error('Invalid rarity value %s in ticket definition %s. Error: %s', rarity_filter_int, ticket_definition.id, ve, exc_info=True)
                raise InvalidRarityError(rarity=rarity_filter_int)
                
        try:
            cards_on_page, total_cards = await self.master_card_repo.get_paginated_cards_by_criteria(
                pool_type=pool_type_filter, 
                rarity=rarity_level_enum_filter, 
                search_query=None, 
                specific_card_id=None, 
                page=page_number, 
                per_page=items_per_page
            )
            
            total_pages = math.ceil(total_cards / items_per_page) if total_cards > 0 else 0
            current_page_for_response = max(1, min(page_number, total_pages if total_pages > 0 else 1))
            
            return {
                'cards': cards_on_page,
                'pagination': {
                    'current_page': current_page_for_response,
                    'total_pages': total_pages,
                    'total_items': total_cards
                }
            }
        except Exception as e:
            logger.error('Error getting specific ticket card page for ticket_id %s, page %s: %s', ticket_definition.id, page_number, e, exc_info=True)
            raise TransactionError(f'獲取卡片列表時發生錯誤: {str(e)}')

    async def find_target_page_for_specific_exchange(self, ticket_definition: ShopItemDefinition, search_query: str) -> Dict[str, Any]:
        """
        根據搜索查詢（ID或名稱）找到特定票券兌換會話的目標頁碼。

        Args:
            ticket_definition: 票券定義，包含篩選條件
            search_query: 搜索關鍵字（卡片ID或名稱）

        Returns:
            包含目標頁碼的字典
            
        Raises:
            InvalidRarityError: 稀有度值無效
            ShopCardNotFoundError: 找不到符合條件的卡片
        """
        pool_type_filter = ticket_definition.pool_type
        rarity_filter_int = ticket_definition.rarity
        rarity_level_enum_filter: Optional[RarityLevel] = None
        
        if rarity_filter_int is not None:
            try:
                rarity_level_enum_filter = RarityLevel(rarity_filter_int)
            except ValueError:
                raise InvalidRarityError(rarity=rarity_filter_int)
                
        target_card_id_to_find: Optional[int] = None
        search_query_stripped = search_query.strip()
        
        if search_query_stripped.isdigit():
            try:
                target_card_id_to_find = int(search_query_stripped)
                logger.info("SpecificTicketService: Search query '%s' is Card ID: %s", search_query_stripped, target_card_id_to_find)
            except ValueError:
                target_card_id_to_find = None
                logger.warning("SpecificTicketService: Search query '%s' isdigit but failed int conversion. Treating as name search.", search_query_stripped)
                
        if target_card_id_to_find is None:
            logger.info("SpecificTicketService: Searching for card name '%s'.", search_query_stripped)
            target_card_id_to_find = await self.master_card_repo.get_first_matching_card_id_by_name(
                name_query=search_query_stripped, 
                pool_type=pool_type_filter, 
                rarity=rarity_level_enum_filter
            )
            
            if target_card_id_to_find is None:
                raise ShopCardNotFoundError(search_query=search_query_stripped)
                
            logger.info("SpecificTicketService: Found card ID %s for name '%s'.", target_card_id_to_find, search_query_stripped)
            
        target_page = await self.master_card_repo.get_card_page_number(
            target_card_id=target_card_id_to_find, 
            pool_type=pool_type_filter, 
            rarity=rarity_level_enum_filter
        )
        
        if target_page is None:
            raise ShopCardNotFoundError(card_id=target_card_id_to_find)
            
        return {
            'target_page': target_page
        }