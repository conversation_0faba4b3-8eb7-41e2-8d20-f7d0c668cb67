"""
Gacha系統許願命令 - COG 版本
實現/wish命令，整合用戶查看、添加、移除許願卡片及擴充槽位、提升力度等功能
"""
import discord
from discord import app_commands
from discord.ext import commands
import re
from typing import Optional, List, Dict, Any, Union, Callable
from utils.logger import logger
from gacha.utils import interaction_utils
from gacha.utils.cog_error_handler import handle_gacha_error # 導入 handle_gacha_error
from gacha.services.core.wish_service import WishService
from gacha.services.core.gacha_service import GachaService
from gacha.services.core.economy_service import EconomyService
from gacha.services.core.user_service import UserService
from gacha.services.core.encyclopedia_service import EncyclopediaService
from gacha.repositories.card.master_card_repository import MasterCardRepository
from gacha.views import utils as view_utils
from gacha.constants import RarityLevel
from gacha.views.embeds.gacha.wish_embed_builder import WishEmbedBuilder
from gacha.views.ui_components.confirmation import ConfirmationFactory
from gacha.exceptions import UserNotFoundError, GachaSystemError, InsufficientBalanceError, CardNotFoundError, CardAlreadyInWishListError, WishListFullError, CardNotInWishListError

class WishView(discord.ui.View):
    """
    整合版許願系統視圖
    提供添加、移除許願卡片，擴充槽位和提升力度等功能
    """

    def __init__(self, user_id: int, wish_data: Dict[str, Any], wish_service: WishService, economy_service: EconomyService, cog_instance: commands.Cog, timeout=180):
        """初始化許願視圖

        參數:
            user_id: 用戶ID
            wish_data: 許願數據
            wish_service: 許願服務實例
            economy_service: 經濟服務實例
            cog_instance: Cog 的實例，用於訪問 bot 和其他服務
            timeout: 超時時間（秒）
        """
        super().__init__(timeout=timeout)
        self.user_id = user_id
        self.wish_data = wish_data
        self.wish_service = wish_service
        self.economy_service = economy_service
        self.cog_instance = cog_instance
        self.wish_slots = wish_data.get('wish_slots', 1)
        self.wish_power_level = wish_data.get('wish_power_level', 1)
        self.update_buttons()

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """
        檢查與此許願視圖互動的使用者是否為原始擁有者。
        """
        return await interaction_utils.check_user_permission(interaction=interaction, expected_user_id=self.user_id, error_message_on_fail='您無法操作他人的許願介面！')

    def update_buttons(self):
        """根據當前狀態更新按鈕"""
        current_slots = self.wish_data.get('wish_slots', 1)
        current_level = self.wish_data.get('wish_power_level', 1)
        used_slots = self.wish_data.get('used_slots', 0)
        slots_max = current_slots >= self.wish_service.MAX_WISH_SLOTS
        power_max = current_level >= self.wish_service.MAX_WISH_POWER_LEVEL
        expand_button = discord.utils.get(self.children, custom_id='expand')
        if expand_button:
            expand_button.disabled = slots_max
        powerup_button = discord.utils.get(self.children, custom_id='powerup')
        if powerup_button:
            powerup_button.disabled = power_max
        add_button = discord.utils.get(self.children, custom_id='add')
        if add_button:
            add_button.disabled = used_slots >= current_slots
        remove_button = discord.utils.get(self.children, custom_id='remove')
        if remove_button:
            remove_button.disabled = used_slots <= 0

    @discord.ui.button(label='添加許願', style=discord.ButtonStyle.primary, emoji='✨', custom_id='add', row=0)
    async def add_wish_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        select_modal = SelectWishMethodModal(title='選擇許願方式', wish_service=self.wish_service, cog_instance=self.cog_instance)
        await interaction.response.send_modal(select_modal)
        await select_modal.wait()
        if select_modal.submitted and select_modal.wish_added:
            self.wish_data = await self.wish_service.get_wish_list(self.user_id)
            self.update_buttons()
            builder = WishEmbedBuilder(user=interaction.user, wishes=self.wish_data.get('wishes', []), wish_slots=self.wish_data.get('wish_slots', 1), wish_power_level=self.wish_data.get('wish_power_level', 1), used_slots=self.wish_data.get('used_slots', 0), nickname=self.wish_data.get('user_nickname'), wish_service=self.wish_service)
            embed = builder.build_embed()
            await interaction_utils.safe_edit_message(interaction, embed=embed, view=self)

    @discord.ui.button(label='移除許願', style=discord.ButtonStyle.danger, emoji='🗑️', custom_id='remove', row=0)
    async def remove_wish_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        modal = WishCardIdModal(title='移除許願卡片', action='remove', wish_service=self.wish_service, cog_instance=self.cog_instance)
        await interaction.response.send_modal(modal)
        await modal.wait()
        if modal.submitted:
            self.wish_data = await self.wish_service.get_wish_list(self.user_id)
            self.update_buttons()
            builder = WishEmbedBuilder(user=interaction.user, wishes=self.wish_data.get('wishes', []), wish_slots=self.wish_data.get('wish_slots', 1), wish_power_level=self.wish_data.get('wish_power_level', 1), used_slots=self.wish_data.get('used_slots', 0), nickname=self.wish_data.get('user_nickname'), wish_service=self.wish_service)
            embed = builder.build_embed()
            await interaction_utils.safe_edit_message(interaction, embed=embed, view=self)

    @discord.ui.button(label='擴充槽位', style=discord.ButtonStyle.success, emoji='🔓', custom_id='expand', row=1)
    async def expand_slot_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        current_slots = self.wish_data.get('wish_slots', 1)
        cost = self.wish_service.calculate_next_slot_cost(current_slots)
        if current_slots >= self.wish_service.MAX_WISH_SLOTS:
            embed = discord.Embed(title='無法擴充許願槽位', description=f'你已達到許願槽位上限 ({self.wish_service.MAX_WISH_SLOTS}/{self.wish_service.MAX_WISH_SLOTS})', color=discord.Color.red())
            await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
            return

        async def expand_slot_confirmed(confirm_interaction: discord.Interaction, confirmed: bool):
            if not confirmed:
                await interaction_utils.safe_send_message(confirm_interaction, '已取消擴充槽位。', ephemeral=True)
                return
            try:
                result = await self.wish_service.expand_slot(self.user_id)
                self.wish_data = await self.wish_service.get_wish_list(self.user_id)
                self.wish_slots = self.wish_data.get('wish_slots', 1)
                self.update_buttons()
                builder = WishEmbedBuilder(user=confirm_interaction.user, wishes=self.wish_data.get('wishes', []), wish_slots=self.wish_data.get('wish_slots', 1), wish_power_level=self.wish_data.get('wish_power_level', 1), used_slots=self.wish_data.get('used_slots', 0), nickname=self.wish_data.get('user_nickname'), wish_service=self.wish_service)
                new_embed = builder.build_embed()
                success_embed = discord.Embed(title='擴充許願槽位成功', description=f"槽位已擴充\n當前許願槽位: {result.get('new_slots')}", color=discord.Color.green())
                await interaction_utils.safe_edit_message(confirm_interaction, embed=success_embed, view=None)
                original_interaction = interaction
                if original_interaction and original_interaction.message:
                    try:
                        main_builder = WishEmbedBuilder(user=original_interaction.user, wishes=self.wish_data.get('wishes', []), wish_slots=self.wish_slots, wish_power_level=self.wish_data.get('wish_power_level', 1), used_slots=self.wish_data.get('used_slots', 0), nickname=self.wish_data.get('user_nickname'), wish_service=self.wish_service)
                        main_embed = main_builder.build_embed()
                        await interaction_utils.safe_edit_message(original_interaction, embed=main_embed, view=self)
                    except Exception as e:
                        logger.error('Error updating original wish view message after slot expansion via safe_edit_message: %s', e)
            except UserNotFoundError as e:
                fail_embed = discord.Embed(title='擴充許願槽位失敗', description=str(e), color=discord.Color.red())
                await interaction_utils.safe_edit_message(confirm_interaction, embed=fail_embed, view=None)
            except InsufficientBalanceError as e:
                fail_embed = discord.Embed(title='擴充許願槽位失敗', description=f"油幣不足，無法擴充槽位。\n需要: {e.required} 油幣\n當前餘額: {e.current} 油幣", color=discord.Color.red())
                await interaction_utils.safe_edit_message(confirm_interaction, embed=fail_embed, view=None)
            except GachaSystemError as e:
                fail_embed = discord.Embed(title='擴充許願槽位失敗', description=str(e), color=discord.Color.red())
                await interaction_utils.safe_edit_message(confirm_interaction, embed=fail_embed, view=None)
            except Exception as e:
                logger.error('Error expanding wish slot: %s', e)
                fail_embed = discord.Embed(title='擴充許願槽位失敗', description=f"發生未知錯誤: {str(e)}", color=discord.Color.red())
                await interaction_utils.safe_edit_message(confirm_interaction, embed=fail_embed, view=None)
        confirm_text = f'你確定要花費 **{cost}** 油幣擴充許願槽位嗎？\n當前: {current_slots}/{self.wish_service.MAX_WISH_SLOTS}'
        await ConfirmationFactory.create_confirmation(interaction=interaction, title='確認擴充許願槽位', description=confirm_text, user_id=self.user_id, callback=expand_slot_confirmed, field_name='操作提示', field_value='點擊確認以花費油幣擴充許願槽位，或點擊取消放棄操作。', color=discord.Color.blue(), ephemeral=True, footer_text='擴充許願槽位將增加可同時許願的卡片數量')

    @discord.ui.button(label='提升力度', style=discord.ButtonStyle.success, emoji='💪', custom_id='powerup', row=1)
    async def power_up_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        current_level = self.wish_data.get('wish_power_level', 1)
        cost = self.wish_service.calculate_next_power_cost(current_level)
        if current_level >= self.wish_service.MAX_WISH_POWER_LEVEL:
            embed = discord.Embed(title='無法提升許願力度', description=f'你已達到許願力度上限 (Lv.{self.wish_service.MAX_WISH_POWER_LEVEL}/{self.wish_service.MAX_WISH_POWER_LEVEL})', color=discord.Color.red())
            await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
            return

        async def power_up_confirmed(confirm_interaction: discord.Interaction, confirmed: bool):
            if not confirmed:
                await interaction_utils.safe_send_message(confirm_interaction, '已取消提升力度。', ephemeral=True)
                return
            try:
                result = await self.wish_service.power_up(self.user_id)
                self.wish_data = await self.wish_service.get_wish_list(self.user_id)
                self.wish_power_level = self.wish_data.get('wish_power_level', 1)
                self.update_buttons()
                success_embed = discord.Embed(title='提升許願力度成功', description=f"力度已提升\n當前許願力度: {result.get('new_level')}\n權重倍率: {self.wish_service.get_wish_chance_multiplier(result.get('new_level', 1))}倍", color=discord.Color.green())
                await interaction_utils.safe_edit_message(confirm_interaction, embed=success_embed, view=None)
                original_interaction = interaction
                if original_interaction and original_interaction.message:
                    try:
                        main_builder = WishEmbedBuilder(user=original_interaction.user, wishes=self.wish_data.get('wishes', []), wish_slots=self.wish_slots, wish_power_level=self.wish_power_level, used_slots=self.wish_data.get('used_slots', 0), nickname=self.wish_data.get('user_nickname'), wish_service=self.wish_service)
                        main_embed = main_builder.build_embed()
                        await interaction_utils.safe_edit_message(original_interaction, embed=main_embed, view=self)
                    except Exception as e:
                        logger.error('Error updating original wish view message after power up via safe_edit_message: %s', e)
            except UserNotFoundError as e:
                fail_embed = discord.Embed(title='提升許願力度失敗', description=str(e), color=discord.Color.red())
                await interaction_utils.safe_edit_message(confirm_interaction, embed=fail_embed, view=None)
            except InsufficientBalanceError as e:
                fail_embed = discord.Embed(title='提升許願力度失敗', description=f"油幣不足，無法提升力度。\n需要: {e.required} 油幣\n當前餘額: {e.current} 油幣", color=discord.Color.red())
                await interaction_utils.safe_edit_message(confirm_interaction, embed=fail_embed, view=None)
            except GachaSystemError as e:
                fail_embed = discord.Embed(title='提升許願力度失敗', description=str(e), color=discord.Color.red())
                await interaction_utils.safe_edit_message(confirm_interaction, embed=fail_embed, view=None)
            except Exception as e:
                logger.error('Error powering up wish: %s', e)
                fail_embed = discord.Embed(title='提升許願力度失敗', description=f"發生未知錯誤: {str(e)}", color=discord.Color.red())
                await interaction_utils.safe_edit_message(confirm_interaction, embed=fail_embed, view=None)
        confirm_text = f'你確定要花費 **{cost}** 油幣提升許願力度嗎？\n當前: Lv.{current_level}/{self.wish_service.MAX_WISH_POWER_LEVEL}\n提升後權重將從 {self.wish_service.get_wish_chance_multiplier(current_level)}倍 → {self.wish_service.get_wish_chance_multiplier(current_level + 1)}倍'
        await ConfirmationFactory.create_confirmation(interaction=interaction, title='確認提升許願力度', description=confirm_text, user_id=self.user_id, callback=power_up_confirmed, field_name='操作提示', field_value='點擊確認以花費油幣提升許願力度，或點擊取消放棄操作。', color=discord.Color.blue(), ephemeral=True, footer_text='提升許願力度將增加許願卡片的抽取機率')

class SelectWishMethodModal(discord.ui.Modal):
    """選擇許願方式的模態框"""

    def __init__(self, title: str, wish_service: WishService, cog_instance: commands.Cog):
        super().__init__(title=title)
        self.wish_service = wish_service
        self.cog_instance = cog_instance
        self.submitted = False
        self.wish_added = False
        self.select_method = discord.ui.TextInput(label='輸入卡片ID', placeholder='請直接輸入要許願的卡片ID', min_length=1, max_length=15, required=True, style=discord.TextStyle.short)
        self.add_item(self.select_method)

    async def on_submit(self, interaction: discord.Interaction):
        try:
            card_id_input = self.select_method.value.strip()
            try:
                card_id = int(card_id_input)
                await self.wish_service.add_wish(interaction.user.id, card_id)
                embed = discord.Embed(title='添加許願成功', description='卡片已添加到許願列表', color=discord.Color.green())
                self.wish_added = True
                await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
                self.submitted = True
            except ValueError:
                await interaction_utils.safe_send_message(interaction, '請輸入有效的卡片ID（數字）。', ephemeral=True)
                self.submitted = True
            except CardNotFoundError as e:
                embed = discord.Embed(title='添加許願失敗', description=str(e), color=discord.Color.red())
                await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
                self.submitted = True
            except UserNotFoundError as e:
                embed = discord.Embed(title='添加許願失敗', description=str(e), color=discord.Color.red())
                await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
            except CardAlreadyInWishListError as e:
                embed = discord.Embed(title='添加許願失敗', description=str(e), color=discord.Color.red())
                await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
            except WishListFullError as e:
                embed = discord.Embed(title='添加許願失敗', description=str(e), color=discord.Color.red())
                await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
            except GachaSystemError as e:
                embed = discord.Embed(title='添加許願失敗', description=str(e), color=discord.Color.red())
                await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
                self.submitted = True
        except Exception as e:
            logger.error('[GACHA] 添加許願失敗: %s', str(e))
            await interaction_utils.safe_send_message(interaction, f'添加許願時發生錯誤: {str(e)}', ephemeral=True)
            self.submitted = True

class CardBrowseView(discord.ui.View):
    """卡片瀏覽視圖，用於瀏覽圖鑑並許願"""

    def __init__(self, user_id: int, current_page: int, total_pages: int, current_card_id: int, cog_instance: commands.Cog, master_card_repo: MasterCardRepository, timeout=180):
        super().__init__(timeout=timeout)
        self.user_id = user_id
        self.current_page = current_page
        self.total_pages = total_pages
        self.current_card_id = current_card_id
        self.cog_instance = cog_instance
        self.master_card_repo = master_card_repo
        self.wish_added = False

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """
        檢查與此卡片瀏覽視圖互動的使用者是否為原始擁有者。
        """
        return await interaction_utils.check_user_permission(interaction=interaction, expected_user_id=self.user_id, error_message_on_fail='您無法操作他人的卡片瀏覽介面！')

    @discord.ui.button(label='上一張', style=discord.ButtonStyle.secondary, emoji='⬅️', custom_id='prev_card', row=0)
    async def prev_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.current_page = max(1, self.current_page - 1)
        await self._update_card_view(interaction)

    @discord.ui.button(label='下一張', style=discord.ButtonStyle.secondary, emoji='➡️', custom_id='next_card', row=0)
    async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.current_page = min(self.total_pages, self.current_page + 1)
        await self._update_card_view(interaction)

    @discord.ui.button(label='許願此卡', style=discord.ButtonStyle.primary, emoji='✨', custom_id='wish_this_card', row=1)
    async def wish_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not hasattr(self.cog_instance, 'wish_service') or self.cog_instance.wish_service is None:
            logger.error('WishService not found in cog_instance for CardBrowseView.')
            await interaction_utils.safe_send_message(interaction, '內部錯誤，無法執行許願操作。', ephemeral=True)
            return
        wish_service: WishService = self.cog_instance.wish_service
        try:
            await wish_service.add_wish(self.user_id, self.current_card_id)
            embed = discord.Embed(title='添加許願成功', description='卡片已添加到許願列表', color=discord.Color.green())
            self.wish_added = True
            await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
        except CardNotFoundError as e:
            embed = discord.Embed(title='添加許願失敗', description=str(e), color=discord.Color.red())
            await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
        except UserNotFoundError as e:
            embed = discord.Embed(title='添加許願失敗', description=str(e), color=discord.Color.red())
            await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
        except CardAlreadyInWishListError as e:
            embed = discord.Embed(title='添加許願失敗', description=str(e), color=discord.Color.red())
            await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
        except WishListFullError as e:
            embed = discord.Embed(title='添加許願失敗', description=str(e), color=discord.Color.red())
            await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
        except GachaSystemError as e:
            embed = discord.Embed(title='添加許願失敗', description=str(e), color=discord.Color.red())
            await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
        except Exception as e:
            logger.error('[GACHA] 添加許願失敗: %s', str(e))
            embed = discord.Embed(title='添加許願失敗', description=f'發生未知錯誤: {str(e)}', color=discord.Color.red())
            await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)

    @discord.ui.button(label='關閉', style=discord.ButtonStyle.danger, emoji='❌', custom_id='close_browse', row=1)
    async def close_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        for child in self.children:
            child.disabled = True
        if interaction.message:
            await interaction_utils.safe_edit_message(interaction, view=self)
        else:
            await interaction.response.edit_message(view=self)
        self.stop()

    async def _update_card_view(self, interaction: discord.Interaction):
        try:
            if not hasattr(self.cog_instance, 'encyclopedia_service') or self.cog_instance.encyclopedia_service is None:
                logger.error('EncyclopediaService not found in cog_instance for CardBrowseView.')
                await interaction_utils.safe_send_message(interaction, '內部錯誤，無法載入卡片圖鑑。', ephemeral=True)
                return
            encyclopedia_service: EncyclopediaService = self.cog_instance.encyclopedia_service
            page_data = await encyclopedia_service.get_card_for_page(page=self.current_page)
            card_id = page_data.get('card_id')
            if not card_id:
                await interaction_utils.safe_send_message(interaction, '無法載入卡片圖鑑，請稍後再試。', ephemeral=True)
                return
            self.current_card_id = card_id
            card_object = await self.master_card_repo.get_card(card_id)
            if not card_object:
                await interaction_utils.safe_send_message(interaction, '無法獲取卡片信息，請稍後再試。', ephemeral=True)
                return
            rarity_level_enum = card_object.rarity
            rarity_display = view_utils.get_user_friendly_rarity_name(rarity_level_enum) if rarity_level_enum is not None else '未知'
            embed = discord.Embed(title=f"卡片圖鑑 - {card_object.name or '未知'}", description=f"ID: {card_id} | 系列: {card_object.series or '未知'} | 稀有度: {rarity_display}", color=discord.Color.blue())
            if card_object.image_url:
                embed.set_image(url=card_object.image_url)
            embed.set_footer(text=f'頁碼: {self.current_page}/{self.total_pages}')
            if interaction.message:
                await interaction_utils.safe_edit_message(interaction, embed=embed, view=self)
            else:
                await interaction.response.edit_message(embed=embed, view=self)
        except Exception as e:
            logger.error('[GACHA] 更新卡片視圖失敗: %s', str(e), exc_info=True)
            await interaction_utils.safe_send_message(interaction, f'更新卡片視圖時發生錯誤: {str(e)}', ephemeral=True)

class WishCardIdModal(discord.ui.Modal):
    """許願卡片ID輸入模態框"""

    def __init__(self, title: str, action: str, wish_service: WishService, cog_instance: commands.Cog):
        super().__init__(title=title)
        self.action = action
        self.wish_service = wish_service
        self.cog_instance = cog_instance
        self.submitted = False
        self.card_id_input = discord.ui.TextInput(label='卡片ID', placeholder='請輸入卡片ID（數字）', min_length=1, max_length=10, required=True, style=discord.TextStyle.short)
        self.add_item(self.card_id_input)

    async def on_submit(self, interaction: discord.Interaction):
        try:
            try:
                card_id = int(self.card_id_input.value.strip())
            except ValueError:
                await interaction_utils.safe_send_message(interaction, '請輸入有效的卡片ID（數字）。', ephemeral=True)
                self.submitted = True
                return
            
            action_text = ''
            try:
                if self.action == 'add':
                    await self.wish_service.add_wish(interaction.user.id, card_id)
                    action_text = '添加許願'
                    embed = discord.Embed(title=f'{action_text}成功', description='卡片已添加到許願列表', color=discord.Color.green())
                elif self.action == 'remove':
                    await self.wish_service.remove_wish(interaction.user.id, card_id)
                    action_text = '移除許願'
                    embed = discord.Embed(title=f'{action_text}成功', description='卡片已從許願列表中移除', color=discord.Color.green())
                else:
                    await interaction_utils.safe_send_message(interaction, '未知的操作類型。', ephemeral=True)
                    self.submitted = True
                    return
                await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
            except CardNotFoundError as e:
                embed = discord.Embed(title=f'{action_text}失敗', description=str(e), color=discord.Color.red())
                await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
            except UserNotFoundError as e:
                embed = discord.Embed(title=f'{action_text}失敗', description=str(e), color=discord.Color.red())
                await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
            except CardAlreadyInWishListError as e:
                embed = discord.Embed(title=f'{action_text}失敗', description=str(e), color=discord.Color.red())
                await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
            except CardNotInWishListError as e:
                embed = discord.Embed(title=f'{action_text}失敗', description=str(e), color=discord.Color.red())
                await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
            except WishListFullError as e:
                embed = discord.Embed(title=f'{action_text}失敗', description=str(e), color=discord.Color.red())
                await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
            except GachaSystemError as e:
                embed = discord.Embed(title=f'{action_text}失敗', description=str(e), color=discord.Color.red())
                await interaction_utils.safe_send_message(interaction, embed=embed, ephemeral=True)
            
            self.submitted = True
        except Exception as e:
            logger.error('[GACHA] 處理許願模態框提交失敗: %s', str(e))
            await interaction_utils.safe_send_message(interaction, f'處理請求時發生錯誤: {str(e)}', ephemeral=True)
            self.submitted = True

class WishCog(commands.Cog):

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.wish_service: WishService = getattr(bot, 'wish_service', None)
        self.economy_service: EconomyService = getattr(bot, 'economy_service', None)
        self.gacha_service: GachaService = getattr(bot, 'gacha_service', None)
        self.encyclopedia_service: EncyclopediaService = getattr(bot, 'encyclopedia_service', None)
        self.master_card_repo: MasterCardRepository = getattr(bot, 'master_card_repo', None)
        if not self.wish_service:
            logger.critical('WishService not found on bot object! WishCog may not function correctly.')
        if not self.economy_service:
            logger.critical('EconomyService not found on bot object! WishCog may not function correctly.')
        if not self.encyclopedia_service:
            logger.warning('EncyclopediaService not found on bot object! CardBrowseView in WishCog might not function.')
        if not self.master_card_repo:
            logger.critical('MasterCardRepository not found on bot object! CardBrowseView in WishCog will not function.')

    @app_commands.command(name='wish', description='查看你的許願列表')
    async def wish_command(self, interaction: discord.Interaction):
        """顯示用戶的許願列表"""
        try:
            user_id = interaction.user.id
            if not self.wish_service or not self.economy_service:
                logger.error('WishService or EconomyService not properly initialized in WishCog for wish_command.')
                await interaction_utils.safe_send_message(interaction, '機器人內部錯誤，許願相關服務未正確初始化。', ephemeral=True)
                return
                
            wish_data = await self.wish_service.get_wish_list(user_id)
            nickname = interaction.user.display_name
            builder = WishEmbedBuilder(user=interaction.user, wishes=wish_data.get('wishes', []), wish_slots=wish_data.get('wish_slots', 1), wish_power_level=wish_data.get('wish_power_level', 1), used_slots=wish_data.get('used_slots', 0), wish_service=self.wish_service, nickname=nickname)
            embed = builder.build_embed()
            view = WishView(user_id=user_id, wish_data=wish_data, wish_service=self.wish_service, economy_service=self.economy_service, cog_instance=self)
            await interaction_utils.safe_send_message(interaction, embed=embed, view=view, ephemeral=True)
        except UserNotFoundError as e:
            await interaction_utils.safe_send_message(interaction, str(e), ephemeral=True)
        except GachaSystemError as e:
            await interaction_utils.safe_send_message(interaction, f"獲取許願列表失敗: {str(e)}", ephemeral=True)
        except Exception as e:
            await handle_gacha_error(interaction, e, '處理 /wish 命令時')

async def setup(bot: commands.Bot):
    await bot.add_cog(WishCog(bot))
    logger.info('WishCog 已成功加載。')