from typing import Dict, Any, List, Optional
from enum import Enum
from gacha.constants import Ra<PERSON>Level
from gacha.models.shop_models import ShopItemDefinition
from gacha.models.models import Card
from gacha.exceptions import (
    InvalidRarityError, NoCardsDeterminedError, TransactionError, InsufficientOilTicketsError
)
from utils.logger import logger
from .base_ticket_service import BaseTicketService

class RandomTicketService(BaseTicketService):
    """專門處理隨機票券兌換服務"""

    async def process_random_ticket_exchange(self, user_id: int, ticket_definition: ShopItemDefinition, quantity: int, session_id: str) -> Dict[str, Any]:
        """
        處理隨機票券兌換流程，包括抽卡、交易執行

        Args:
            user_id: 用戶ID
            ticket_definition: 票券定義
            quantity: 兌換數量
            session_id: 會話ID

        Returns:
            Dict包含處理結果
            
        Raises:
            InsufficientOilTicketsError: 油票餘額不足
            InvalidRarityError: 稀有度值無效
            NoCardsDeterminedError: 未能確定抽取的卡片
            TransactionError: 交易執行失敗
        """
        # 內聯餘額檢查邏輯 (符合重構原則)
        total_oil_ticket_cost = ticket_definition.cost_oil_tickets * quantity
        current_balance_decimal = await self.user_service.get_oil_ticket_balance(user_id)
        current_balance_tickets = self.get_available_oil_tickets(current_balance_decimal)
        
        if current_balance_tickets < total_oil_ticket_cost:
            logger.warning(
                'User %s: Insufficient balance for random ticket exchange (session %s). Current: %s, Required: %s. Ticket ID: %s, Quantity: %s',
                user_id, session_id, current_balance_tickets, total_oil_ticket_cost, ticket_definition.id, quantity
            )
            raise InsufficientOilTicketsError(required_amount=total_oil_ticket_cost, current_balance=current_balance_tickets)

        pool_type = ticket_definition.pool_type
        rarity_int = ticket_definition.rarity
        
        if pool_type is None:
            logger.error("Ticket definition for random exchange (ID: %s, session: %s) is missing 'pool_type'.", ticket_definition.id, session_id)
            raise TransactionError("兌換券定義不完整，缺少 'pool_type'。")
            
        if rarity_int is None:
            logger.error("Ticket definition for random exchange (ID: %s, session: %s) is missing 'rarity'.", ticket_definition.id, session_id)
            raise TransactionError("兌換券定義不完整，缺少 'rarity'。")
            
        try:
            rarity_enum = RarityLevel(rarity_int)
        except ValueError:
            logger.error('Invalid rarity value %s in ticket definition %s for session %s.', rarity_int, ticket_definition.id, session_id)
            raise InvalidRarityError(rarity=rarity_int)
            
        cards_to_grant = await self._draw_random_cards(rarity=rarity_enum, pool_type=pool_type, quantity=quantity, session_id=session_id)
        
        if not cards_to_grant:
            logger.warning('Session %s: No cards were drawn for rarity %s from pool_type %s.', session_id, rarity_enum, pool_type)
            raise NoCardsDeterminedError(f"成功兌換 {ticket_definition.display_name}，但本次未抽中任何卡片。")
            
        async with self.pool.acquire() as conn:
            transaction_result = await self._execute_exchange_transaction(
                conn=conn, 
                user_id=user_id, 
                session_id=session_id, 
                ticket_definition=ticket_definition, 
                quantity=quantity, 
                cards_to_grant=cards_to_grant
            )
            
        # 交易成功，返回抽到的卡片信息
        return {
            'drawn_cards': cards_to_grant,
            'granted_cards_info_for_embed': transaction_result.get('granted_cards_info_for_embed', [])
        }

    async def _draw_random_cards(self, rarity: RarityLevel, pool_type: str, quantity: int, session_id: str) -> List[Card]:
        """
        從指定稀有度和卡池中抽取卡片

        Args:
            rarity: 稀有度枚舉
            pool_type: 卡池類型
            quantity: 抽取數量
            session_id: 會話ID，用於日誌記錄

        Returns:
            抽到的卡片對象列表
            
        Raises:
            NoCardsDeterminedError: 未能確定抽取的卡片
        """
        try:
            cards = await self.master_card_repo.get_card_objects_by_rarity(rarity=rarity, pool_types=[pool_type], limit=quantity)
            logger.info('Session %s: Drew %s card objects. IDs: %s', session_id, len(cards), [c.card_id for c in cards] if cards else 'None')
            
            if not cards or len(cards) < quantity:
                logger.warning('Session %s: Expected to draw %s cards, but got %s. Rarity: %s, Pool: %s. This might indicate insufficient cards in the pool.', 
                              session_id, quantity, len(cards) if cards else 0, rarity, pool_type)
                
            return cards
        except Exception as e:
            logger.error('Error drawing cards for session %s: %s', session_id, e, exc_info=True)
            raise NoCardsDeterminedError(f"抽取卡片時發生錯誤: {str(e)}")