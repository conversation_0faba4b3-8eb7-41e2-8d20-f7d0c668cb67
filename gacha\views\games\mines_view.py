# -*- coding: utf-8 -*-
"""
Gacha 系統尋寶礦區模式選擇視圖
"""

# Constants MIN_BET and MAX_BET moved to gacha/views/modals.py
import discord
import logging
from typing import Optional, TYPE_CHECKING
import discord  # Add missing import? No, it's at line 2
from gacha.services.games.mines_service import MinesService
from gacha.services.core.economy_service import EconomyService
from gacha.services.ui.game_display_service import (
    GameDisplayService,
)  # Import GameDisplayService
from gacha.utils import interaction_utils  # 導入 interaction_utils

# Import Handler and related components
from gacha.views.collection.collection_view.interaction_manager import (
    InteractionManager,
)
from gacha.utils.mines_constants import MIN_BET, MAX_BET

if TYPE_CHECKING:
    from gacha.views.handlers.mines_callback_handler import MinesCallbackHandler


class MinesModeSelectView(discord.ui.View):
    """尋寶礦區模式選擇的 View"""

    def __init__(
        self,
        user_id: int,
        current_bet: int,
        mines_service: MinesService,
        economy_service: EconomyService,
        game_display_service: GameDisplayService,
        timeout=180,
    ):  # Add game_display_service
        super().__init__(timeout=timeout)
        self.user_id = user_id
        self.current_bet = current_bet  # This will be updated by the handler/modal
        self.interaction_message: Optional[discord.InteractionMessage] = (
            None  # Use Optional
        )

        # Store the injected service
        self.mines_service = mines_service
        self.economy_service = economy_service
        self.game_display_service = game_display_service

        # Initialize InteractionManager and Handler
        self.interaction_manager = InteractionManager()
        from gacha.views.handlers.mines_callback_handler import (
            MinesCallbackHandler,
        )  # 延遲導入, MODIFIED: Corrected import path

        self.handler = MinesCallbackHandler(
            interaction_manager=self.interaction_manager,
            user_id=self.user_id,
            mines_service=self.mines_service,  # Use stored instance
            economy_service=self.economy_service,  # Use stored instance
            game_display_service=self.game_display_service,  # Pass the injected service
            mode_view=self,  # Pass self reference to handler
        )

        # Add mode buttons
        easy_btn = discord.ui.Button(
            label="🟢 簡單 (3 雷)",
            style=discord.ButtonStyle.success,
            custom_id="mines_mode_easy_3",
            row=0,
        )
        easy_btn.callback = lambda i: self.handler.handle_mode_selection(i, 3)
        self.add_item(easy_btn)

        medium_btn = discord.ui.Button(
            label="🟡 中等 (7 雷)",
            style=discord.ButtonStyle.primary,
            custom_id="mines_mode_medium_7",
            row=0,
        )
        medium_btn.callback = lambda i: self.handler.handle_mode_selection(
            i, 7)
        self.add_item(medium_btn)

        hard_btn = discord.ui.Button(
            label="🔴 困難 (12 雷)",
            style=discord.ButtonStyle.danger,
            custom_id="mines_mode_hard_12",
            row=0,
        )
        hard_btn.callback = lambda i: self.handler.handle_mode_selection(i, 12)
        self.add_item(hard_btn)

        # Add the "Custom Bet" button that triggers the modal via handler
        custom_bet_button = discord.ui.Button(
            label="⚙️ 自訂金額",
            style=discord.ButtonStyle.secondary,
            custom_id="mines_custom_bet_modal",
            row=1,
        )
        custom_bet_button.callback = (
            self.handler.handle_custom_bet_modal
        )  # Assign handler's modal callback
        self.add_item(custom_bet_button)

    async def interaction_check(
            self, interaction: discord.Interaction) -> bool:
        """
        檢查與此模式選擇視圖互動的使用者是否為原始玩家。
        """
        # 假設 user_id 在 __init__ 中已儲存為 self.user_id
        return await interaction_utils.check_user_permission(
            interaction=interaction,
            expected_user_id=self.user_id,
            error_message_on_fail="這不是您的遊戲選擇介面，無法操作！",
        )

    async def on_timeout(self):
        if self.interaction_message:
            try:
                await self.interaction_message.edit(
                    content="模式選擇已超時。", view=None
                )
            except discord.NotFound:
                logging.warning(
                    f"Failed to edit timed out mines mode selection message for user {self.user_id} (message not found)."
                )
            except discord.Forbidden:
                logging.error(
                    f"Failed to edit timed out mines mode selection message for user {self.user_id} (forbidden)."
                )
            except Exception as e:
                logging.error(
                    f"Error editing timed out mines mode selection message for user {self.user_id}: {e}"
                )
        self.stop()
