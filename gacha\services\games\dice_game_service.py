import asyncpg
import random
from typing import Dict, Any, Optional
import discord
import functools
from database.postgresql.async_manager import AsyncPgManager
from gacha.services.core.economy_service import EconomyService
from gacha.services.games.base_game_service import BaseGameService
from gacha.services.ui.game_display_service import GameDisplayService
from gacha.exceptions import MinBetNotMetError, InvalidGameActionError, GameSettlementError
from utils.logger import logger


class DiceGame:
    """骰子大小遊戲 (保持同步)"""

    def __init__(self, user_id: int, bet: int, choice: str):
        """初始化遊戲"""
        self.user_id = user_id
        self.bet = bet
        self.choice = choice
        self.dice_value = None
        self.result = None
        self.payout = 0
        self.game_over = False
        self._roll_dice()

    def _roll_dice(self):
        """擲骰子並判斷結果"""
        self.dice_value = random.randint(1, 6)
        self.game_over = True
        is_big = self.dice_value >= 4
        
        if (is_big and self.choice == 'big') or (not is_big and self.choice == 'small'):
            self.result = 'win'
            self.payout = self.bet * 2
        else:
            self.result = 'lose'
            self.payout = 0

    def get_game_state(self) -> Dict[str, Any]:
        """獲取當前遊戲狀態"""
        return {
            'game_over': self.game_over,
            'result': self.result,
            'payout': self.payout,
            'bet': self.bet,
            'choice': self.choice,
            'dice_value': self.dice_value,
            'is_big': self.dice_value >= 4 if self.dice_value else None
        }


class DiceGameService(BaseGameService):
    """骰子遊戲服務類 (Asyncpg 版本)，處理遊戲邏輯和結算 (繼承自 BaseGameService)
    使用純異常模式進行錯誤處理
    """
    MIN_BET = 10

    def __init__(self, economy_service: EconomyService, game_display_service: GameDisplayService, pool: Optional[asyncpg.Pool] = None):
        """初始化遊戲服務 (Asyncpg 版本)"""
        super().__init__(economy_service=economy_service, pool=pool)
        self._game_display_service = game_display_service

    async def start_dice_game(self, interaction: discord.Interaction, user_id: int, bet: int, choice: str, game_id: str) -> None:
        """開始一局骰子大小遊戲，使用純異常模式"""
        
        # 檢查選擇是否有效
        if choice not in ['big', 'small']:
            raise InvalidGameActionError(action=choice, game_type='骰子大小')
            
        # 檢查最低下注要求
        if bet < self.MIN_BET:
            raise MinBetNotMetError(bet_placed=bet, min_bet=self.MIN_BET)
        
        # 檢查並扣除下注金額
        new_balance_after_bet = await self._check_and_deduct_bet(user_id=user_id, bet=bet, game_name='骰子大小', min_bet=self.MIN_BET)
        
        try:
            game = DiceGame(user_id, bet, choice)
            game_state = game.get_game_state()
            
            if game_state.get('game_over', False):
                # 結算遊戲
                settlement_result = await self._settle_winnings(user_id, game_state.get('payout', 0), game_state.get('bet', 0), '骰子大小')
                
                final_balance = settlement_result.get('new_balance', new_balance_after_bet)
                user = interaction.user
                
                # 顯示遊戲結果
                from gacha.views.embeds.games.dice_game_embed_builder import DiceGameEmbedBuilder
                from gacha.views.games.dice_game_view import DiceGameView

                def embed_builder_func(state, bet_amount):
                    return DiceGameEmbedBuilder(user=user, game_state=state, new_balance=final_balance).build_result_embed()

                async def async_view_provider():
                    view = DiceGameView(
                        user=user, 
                        dice_game_service=self, 
                        economy_service=self.economy_service, 
                        game_display_service=self._game_display_service, 
                        original_bet=bet, 
                        game_state=game_state
                    )
                    await view.init_buttons()
                    return view
                
                await self._game_display_service.display_game_end(
                    interaction=interaction, 
                    game_name='Dice', 
                    game_result=game_state, 
                    original_bet=bet, 
                    view_provider=async_view_provider, 
                    game_specific_embed_builder=embed_builder_func, 
                    send_new_message=True
                )
            else:
                # 這不應該發生，因為骰子遊戲總是立即結束
                logger.error('Dice game for user %s, game ID %s finished but game_over is False. This is an unexpected state.', user_id, game_id)
                raise GameSettlementError(game_id=game_id, reason='遊戲狀態異常')
                
        except Exception as e:
            logger.error('[GACHA_DICE] 開始遊戲失敗 (Unexpected Exception): %s', str(e), exc_info=True)
            # 回滾下注
            await self.rollback_bet(user_id=user_id, bet_amount=bet, game_name='骰子大小', reason=f'遊戲執行失敗: {e}')
            raise GameSettlementError(game_id=game_id, reason=f'遊戲執行失敗: {e}') 