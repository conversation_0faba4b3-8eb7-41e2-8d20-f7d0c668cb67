import discord
from typing import Dict, Any, List, Optional
from gacha.utils.mines_constants import TOTAL_TILES, TILE_HIDDEN, TILE_MINE, TILE_SAFE, TILE_SPECIAL_COIN, TILE_SPECIAL_STAR

def build_mines_game_embed(game_state: Dict[str, Any], player: discord.User | discord.Member, current_balance: Optional[int]=None, custom_message_on_end: Optional[str]=None) -> discord.Embed:
    """根據遊戲狀態構建尋寶礦區遊戲的 Embed"""
    from gacha.services.games.mines_service import TOTAL_TILES
    bet_amount = game_state.get('bet_amount', 0)
    mine_count = game_state.get('mine_count', 0)
    current_multiplier = game_state.get('current_multiplier', 1.0)
    found_safe_tiles_count = game_state.get('found_safe_tiles_count', 0)
    game_over = game_state.get('game_over', False)
    last_result = game_state.get('last_result')
    revealed_tiles = game_state.get('revealed_tiles', [])
    board_display = game_state.get('board_display')
    total_safe_tiles = TOTAL_TILES - mine_count
    potential_winnings = bet_amount * current_multiplier
    if game_over:
        if last_result == 'LOSS':
            color = discord.Color.red()
            title = '💥 踩到地雷了！'
        elif last_result == 'CASH_OUT' or last_result == 'WIN':
            color = discord.Color.green()
            title = '💰 成功提現！'
        else:
            color = discord.Color.orange()
            title = '⛏️ 尋寶礦區 - 遊戲結束'
    else:
        color = discord.Color.blue()
        title = '⛏️ 尋寶礦區 - 進行中'
    embed = discord.Embed(title=title, color=color)
    embed.set_author(name=f'{player.display_name} 的遊戲', icon_url=player.display_avatar.url if player.display_avatar else None)
    embed.set_thumbnail(url='https://cdn.dev.conquest.bot/thumbnails/explore_empty.png')
    mode_map = {3: '簡單 (3 雷)', 7: '中等 (7 雷)', 12: '困難 (12 雷)'}
    mode_str = mode_map.get(mine_count, f'{mine_count} 雷')
    embed.add_field(name='模式', value=mode_str, inline=True)
    embed.add_field(name='賭注', value=f'{bet_amount:,} 油幣', inline=True)
    embed.add_field(name='目前乘數', value=f'{current_multiplier:.3f}x', inline=True)
    if game_over:
        if custom_message_on_end:
            embed.description = custom_message_on_end
        elif last_result == 'LOSS':
            embed.description = f'您損失了 **{bet_amount:,}** 油幣。'
        elif last_result == 'CASH_OUT' or last_result == 'WIN':
            final_winnings = game_state.get('final_winnings', potential_winnings)
            embed.description = f'您贏得了 **{final_winnings:,.0f}** 油幣！({current_multiplier:.3f}x)'
        if current_balance is not None:
            balance_message = f'\n💰 **目前餘額**: {current_balance:,.0f} 油幣'
            if embed.description:
                embed.description += balance_message
            else:
                embed.description = balance_message.lstrip()
    elif current_balance is not None:
        balance_message = f'💰 **目前餘額**: {current_balance:,.0f} 油幣'
        if embed.description:
            embed.description += f'\n{balance_message}'
        else:
            embed.description = balance_message
    if not game_over:
        embed.add_field(name='潛在獎金', value=f'{potential_winnings:,.0f} 油幣', inline=True)
        embed.add_field(name='進度', value=f'{found_safe_tiles_count} / {total_safe_tiles}', inline=True)
        embed.add_field(name='\u200b', value='\u200b', inline=True)
    return embed