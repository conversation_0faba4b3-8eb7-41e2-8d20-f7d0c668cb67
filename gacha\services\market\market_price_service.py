from __future__ import annotations
import asyncio
import asyncpg
from utils.logger import logger
import time
from typing import Dict, Any, Optional, List, Set
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime, timezone
import json

class MarketPriceService:

    def __init__(self, pool: asyncpg.Pool, master_card_repo: Any, redis_manager: Optional[Any]=None):
        if pool is None:
            raise ValueError('MarketPriceService requires an asyncpg connection pool.')
        if master_card_repo is None:
            raise ValueError('MarketPriceService requires a MasterCardRepository instance.')
        self.pool = pool
        self.master_card_repo = master_card_repo
        self.redis_manager = redis_manager
        self.CACHE_PREFIX = 'card_market_price:'
        self.CACHE_TTL_SECONDS = 60

    async def _calculate_price_components_for_single_card(self, card_id: int, base_sell_price: Decimal, price_config: Dict[str, Any], pool_type: Optional[str], rarity: Optional[int], market_stats: Dict[str, Decimal], category_stock_influences: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Helper function to calculate price components for a single card,
        assuming all necessary data has been pre-fetched.
        This function does NOT perform any I/O (DB or Cache).
        Returns None if essential data (like base_sell_price) is missing or invalid.
        """
        logger.debug('[CALC_PRICE_SINGLE] Calculating for card_id: %s, base_sell_price: %s, price_config: %s, pool_type: %s, rarity: %s, market_stats: %s, category_stock_influences: %s', card_id, base_sell_price, price_config is not None, pool_type, rarity, market_stats is not None, len(category_stock_influences) if category_stock_influences else 0)
        if base_sell_price is None or base_sell_price <= 0:
            logger.warning('[CALC_PRICE_SINGLE] Returning None for card_id: %s due to missing/invalid base_sell_price: %s', card_id, base_sell_price)
            return None
        min_modifier = Decimal(price_config.get('min_modifier', 0.1))
        max_modifier = Decimal(price_config.get('max_modifier', 10.0))
        stock_influence_weight = Decimal(price_config.get('stock_influence_weight', 1.0))
        supply_demand_modifier = market_stats.get('supply_demand_modifier', Decimal(1.0))
        previous_sd_modifier = market_stats.get('previous_sd_modifier', supply_demand_modifier)
        trend_symbol = '─'
        if supply_demand_modifier > previous_sd_modifier:
            trend_symbol = '↑'
        elif supply_demand_modifier < previous_sd_modifier:
            trend_symbol = '↓'
        total_stock_influence_offset = Decimal(0.0)
        for influence_record in category_stock_influences:
            category_offset = Decimal(influence_record['base_stock_modifier']) - Decimal(1.0)
            expiry_time = influence_record['news_effect_expiry']
            if expiry_time:
                current_time_aware_utc = datetime.now(timezone.utc)
                if expiry_time.tzinfo is None:
                    expiry_time_aware_utc = expiry_time.replace(tzinfo=timezone.utc)
                else:
                    expiry_time_aware_utc = expiry_time.astimezone(timezone.utc)
                if expiry_time_aware_utc > current_time_aware_utc:
                    category_offset += Decimal(influence_record['temporary_news_modifier']) - Decimal(1.0)
            total_stock_influence_offset += category_offset
        final_stock_influence = Decimal(1.0) + total_stock_influence_offset
        logger.debug('[CALC_PRICE_SINGLE] card_id: %s, total_stock_influence_offset: %s, final_stock_influence: %s', card_id, total_stock_influence_offset, final_stock_influence)
        weighted_stock_influence = Decimal(1.0) + (final_stock_influence - Decimal(1.0)) * stock_influence_weight
        final_modifier = supply_demand_modifier * weighted_stock_influence
        clamped_modifier = max(min_modifier, min(max_modifier, final_modifier))
        logger.debug('[CALC_PRICE_SINGLE] card_id: %s, weighted_stock_influence: %s, final_modifier: %s, clamped_modifier: %s', card_id, weighted_stock_influence, final_modifier, clamped_modifier)
        actual_sell_price = (base_sell_price * clamped_modifier).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
        actual_sell_price = max(Decimal('1.0'), actual_sell_price)
        logger.debug('[CALC_PRICE_SINGLE] card_id: %s, calculated actual_sell_price: %s', card_id, actual_sell_price)
        return {'actual_price': actual_sell_price, 'trend_symbol': trend_symbol, 'base_sell_price': base_sell_price, 'supply_demand_modifier': supply_demand_modifier, 'previous_sd_modifier': previous_sd_modifier, 'final_stock_influence': final_stock_influence, 'stock_influence_weight': stock_influence_weight, 'weighted_stock_influence': weighted_stock_influence, 'final_modifier_raw': final_modifier, 'clamped_modifier': clamped_modifier, 'min_modifier_config': min_modifier, 'max_modifier_config': max_modifier}

    async def _read_prices_from_cache(self, card_ids: List[int]) -> tuple[Dict[int, Optional[Dict[str, Any]]], List[int]]:
        """Reads prices from cache for the given card_ids."""
        results: Dict[int, Optional[Dict[str, Any]]] = {}
        card_ids_to_fetch_from_db: List[int] = []
        cache_hits = 0
        if not (self.redis_manager and hasattr(self.redis_manager, 'mget') and hasattr(self.redis_manager, 'setex')):
            logger.debug('[BATCH_PRICE_CACHE_READ] Redis manager not available or mget/setex not supported. Skipping cache read.')
            return (results, list(card_ids))
        cache_keys_to_fetch: List[str] = [f'{self.CACHE_PREFIX}{card_id}_details' for card_id in card_ids]
        try:
            cached_data_json_list = await self.redis_manager.mget(cache_keys_to_fetch)
            for i, card_id in enumerate(card_ids):
                cached_json = cached_data_json_list[i]
                if cached_json:
                    try:
                        cached_data = json.loads(cached_json)
                        for key, value in cached_data.items():
                            if isinstance(value, str):
                                try:
                                    if '.' in value or value.isdigit() or (value.startswith('-') and value[1:].isdigit()):
                                        cached_data[key] = Decimal(value)
                                except Exception:
                                    pass
                        if 'trend_symbol' in cached_data and (not isinstance(cached_data['trend_symbol'], str)):
                            cached_data['trend_symbol'] = str(cached_data.get('trend_symbol', ''))
                        results[card_id] = cached_data
                        cache_hits += 1
                    except json.JSONDecodeError:
                        logger.warning('[BATCH_PRICE_CACHE_READ] Failed to decode cached JSON for card_id %s.', card_id)
                        card_ids_to_fetch_from_db.append(card_id)
                    except Exception as e_parse:
                        logger.warning('[BATCH_PRICE_CACHE_READ] Error parsing cached data for card_id %s: %s.', card_id, e_parse)
                        card_ids_to_fetch_from_db.append(card_id)
                else:
                    card_ids_to_fetch_from_db.append(card_id)
        except Exception as e_redis_read:
            logger.warning('[BATCH_PRICE_CACHE_READ] Redis cache batch read error: %s. Adding all to DB fetch list.', e_redis_read)
            card_ids_to_fetch_from_db = list(card_ids)
            results.clear()
        logger.debug('[BATCH_PRICE_CACHE_READ] Cache hits: %s/%s. IDs to fetch from DB: %s', cache_hits, len(card_ids), len(card_ids_to_fetch_from_db))
        return (results, sorted(list(set(card_ids_to_fetch_from_db))))

    async def _fetch_pricing_data_from_db(self, card_ids_to_fetch: List[int], conn: asyncpg.Connection) -> tuple[Dict[int, Dict[str, Any]], Dict[int, Dict[str, Decimal]], Dict[str, Dict[str, Any]], Dict[int, List[str]], List[int]]:
        """Fetches all necessary pricing data from the database for the given card_ids."""
        card_master_map: Dict[int, Dict[str, Any]] = {}
        market_stats_map: Dict[int, Dict[str, Decimal]] = {}
        category_influence_data_map: Dict[str, Dict[str, Any]] = {}
        card_to_category_keys_map: Dict[int, List[str]] = {}
        valid_card_ids_from_db: List[int] = []
        if not card_ids_to_fetch:
            return (card_master_map, market_stats_map, category_influence_data_map, card_to_category_keys_map, valid_card_ids_from_db)
        card_master_map = await self.master_card_repo.get_cards_details_for_pricing_batch(card_ids_to_fetch, connection=conn)
        valid_card_ids_from_db = [cid for cid in card_ids_to_fetch if cid in card_master_map]
        if not valid_card_ids_from_db:
            logger.warning('[BATCH_PRICE_DB_FETCH] No valid master data found for any of the requested DB IDs: %s', card_ids_to_fetch)
            return (card_master_map, market_stats_map, category_influence_data_map, card_to_category_keys_map, [])
        market_stats_query = 'SELECT card_id, supply_demand_modifier, previous_sd_modifier FROM gacha_card_market_stats WHERE card_id = ANY($1::int[])'
        market_stats_rows = await conn.fetch(market_stats_query, valid_card_ids_from_db)
        for row in market_stats_rows:
            s_d_mod = Decimal(row['supply_demand_modifier']) if row['supply_demand_modifier'] is not None else Decimal(1.0)
            prev_s_d_mod = Decimal(row['previous_sd_modifier']) if row['previous_sd_modifier'] is not None else s_d_mod
            market_stats_map[row['card_id']] = {'supply_demand_modifier': s_d_mod, 'previous_sd_modifier': prev_s_d_mod}
        all_category_keys_set: Set[str] = set()
        for card_id_for_cat_map in valid_card_ids_from_db:
            master_info = card_master_map.get(card_id_for_cat_map)
            if not master_info:
                continue
            keys_for_this_card: List[str] = []
            pool_type, rarity = (master_info.get('pool_type'), master_info.get('rarity'))
            if pool_type:
                keys_for_this_card.append(f'POOL_TYPE:{pool_type}')
                all_category_keys_set.add(keys_for_this_card[-1])
            if pool_type and rarity is not None:
                keys_for_this_card.append(f'RARITY_IN_POOL:{pool_type}:{rarity}')
                all_category_keys_set.add(keys_for_this_card[-1])
            card_to_category_keys_map[card_id_for_cat_map] = keys_for_this_card
        if all_category_keys_set:
            stock_influences_query = 'SELECT category_key, base_stock_modifier, temporary_news_modifier, news_effect_expiry FROM gacha_category_stock_influence WHERE category_key = ANY($1::varchar[])'
            all_influences_raw = await conn.fetch(stock_influences_query, list(all_category_keys_set))
            for influence_row in all_influences_raw:
                category_influence_data_map[influence_row['category_key']] = dict(influence_row)
        logger.debug('[BATCH_PRICE_DB_FETCH] Fetched data for %s cards from DB.', len(valid_card_ids_from_db))
        return (card_master_map, market_stats_map, category_influence_data_map, card_to_category_keys_map, valid_card_ids_from_db)

    async def _calculate_prices_and_prepare_cache(self, card_ids_to_calculate: List[int], card_master_map: Dict[int, Dict[str, Any]], market_stats_map: Dict[int, Dict[str, Decimal]], category_influence_data_map: Dict[str, Dict[str, Any]], card_to_category_keys_map: Dict[int, List[str]]) -> tuple[Dict[int, Optional[Dict[str, Any]]], Dict[str, str]]:
        """Calculates prices for the given cards and prepares entries for caching."""
        calculated_results: Dict[int, Optional[Dict[str, Any]]] = {}
        new_cache_entries: Dict[str, str] = {}
        for card_id_calc in card_ids_to_calculate:
            master_info = card_master_map.get(card_id_calc)
            if not master_info:
                calculated_results[card_id_calc] = None
                continue
            raw_sell_price = master_info.get('sell_price')
            if raw_sell_price is None:
                calculated_results[card_id_calc] = None
                continue
            try:
                base_sell_price = Decimal(raw_sell_price)
            except Exception:
                calculated_results[card_id_calc] = None
                continue
            price_config = master_info.get('price_config') or {}
            pool_type, rarity = (master_info.get('pool_type'), master_info.get('rarity'))
            current_market_stats = market_stats_map.get(card_id_calc, {'supply_demand_modifier': Decimal(1.0), 'previous_sd_modifier': Decimal(1.0)})
            card_specific_category_keys = card_to_category_keys_map.get(card_id_calc, [])
            card_specific_stock_influences: List[Dict[str, Any]] = [category_influence_data_map[cat_key] for cat_key in card_specific_category_keys if cat_key in category_influence_data_map]
            try:
                price_details = await self._calculate_price_components_for_single_card(card_id_calc, base_sell_price, price_config, pool_type, rarity, current_market_stats, card_specific_stock_influences)
                calculated_results[card_id_calc] = price_details
                if price_details:
                    cache_key_write = f'{self.CACHE_PREFIX}{card_id_calc}_details'
                    serializable_details = {k: str(v) if isinstance(v, Decimal) else v for k, v in price_details.items()}
                    new_cache_entries[cache_key_write] = json.dumps(serializable_details)
            except Exception as e_calc_batch:
                logger.error('[BATCH_PRICE_CALC] Exception during _calculate_price_components_for_single_card for card_id %s: %s', card_id_calc, e_calc_batch, exc_info=True)
                calculated_results[card_id_calc] = None
        logger.debug('[BATCH_PRICE_CALC] Calculated prices for %s cards. New cache entries: %s', len(card_ids_to_calculate), len(new_cache_entries))
        return (calculated_results, new_cache_entries)

    async def _write_prices_to_cache(self, new_cache_entries: Dict[str, str]):
        """Writes new price entries to the cache."""
        if not (self.redis_manager and hasattr(self.redis_manager, 'setex') and new_cache_entries):
            logger.debug('[BATCH_PRICE_CACHE_WRITE] Redis manager not available or no new entries to cache.')
            return
        for cache_key, data_json in new_cache_entries.items():
            try:
                await self.redis_manager.setex(cache_key, self.CACHE_TTL_SECONDS, data_json)
            except Exception as e_cache_write:
                logger.warning('[BATCH_PRICE_CACHE_WRITE] Redis cache write error for key %s: %s', cache_key, e_cache_write)
        logger.debug('[BATCH_PRICE_CACHE_WRITE] Attempted to write %s entries to cache.', len(new_cache_entries))

    async def get_actual_card_sell_prices_batch(self, card_ids: List[int], connection: Optional[asyncpg.Connection]=None) -> Dict[int, Optional[Dict[str, Any]]]:
        """
        Calculates actual dynamic market sell prices for a batch of card_ids.
        Returns a dictionary mapping card_id to its price details.
        """
        if not card_ids:
            return {}
        batch_price_start_time = time.monotonic()
        all_results, card_ids_to_fetch_from_db = await self._read_prices_from_cache(card_ids)
        if not card_ids_to_fetch_from_db:
            logger.info('[BATCH_PRICE] All %s card prices found in cache. Duration: %sms', len(card_ids), (time.monotonic() - batch_price_start_time) * 1000)
            return all_results
        active_conn: asyncpg.Connection
        release_conn = False
        if connection:
            active_conn = connection
        else:
            active_conn = await self.pool.acquire()
            release_conn = True
        try:
            card_master_map, market_stats_map, category_influence_data_map, card_to_category_keys_map, valid_card_ids_from_db = await self._fetch_pricing_data_from_db(card_ids_to_fetch_from_db, active_conn)
            for db_card_id in card_ids_to_fetch_from_db:
                if db_card_id not in valid_card_ids_from_db:
                    all_results[db_card_id] = None
            if not valid_card_ids_from_db:
                logger.info('[BATCH_PRICE] No valid card data from DB for IDs: %s. Duration: %sms', card_ids_to_fetch_from_db, (time.monotonic() - batch_price_start_time) * 1000)
                if release_conn and active_conn:
                    await self.pool.release(active_conn)
                return all_results
            calculated_db_results, new_cache_entries = await self._calculate_prices_and_prepare_cache(valid_card_ids_from_db, card_master_map, market_stats_map, category_influence_data_map, card_to_category_keys_map)
            all_results.update(calculated_db_results)
            await self._write_prices_to_cache(new_cache_entries)
        except Exception as e:
            logger.error('[BATCH_PRICE] Error during DB fetch or calculation phase: %s', e, exc_info=True)
            for failed_db_card_id in card_ids_to_fetch_from_db:
                if all_results.get(failed_db_card_id) is None:
                    all_results[failed_db_card_id] = None
        finally:
            if release_conn and active_conn:
                await self.pool.release(active_conn)
        total_duration = (time.monotonic() - batch_price_start_time) * 1000
        logger.info('[BATCH_PRICE] Processed %s card IDs. Total duration: %sms. Fetched from DB: %s', len(card_ids), total_duration, len(card_ids_to_fetch_from_db))
        return all_results

    async def calculate_and_get_market_prices_for_ids(self, card_ids: List[int], connection: Optional[asyncpg.Connection]=None) -> Dict[int, Decimal]:
        """
        Calculates and returns the actual market sell prices (as Decimal) for a batch of card_ids.
        This method is intended for the background price update mechanism.
        It calls get_actual_card_sell_prices_batch and extracts the 'actual_price'.
        """
        if not card_ids:
            return {}
        logger.debug('[CALC_MARKET_PRICES_FOR_IDS] Request for card_ids: %s', card_ids)
        price_details_batch: Dict[int, Optional[Dict[str, Any]]] = await self.get_actual_card_sell_prices_batch(card_ids, connection=connection)
        market_prices: Dict[int, Decimal] = {}
        for card_id, details in price_details_batch.items():
            if details and 'actual_price' in details and isinstance(details['actual_price'], Decimal):
                market_prices[card_id] = details['actual_price']
            else:
                logger.warning('[CALC_MARKET_PRICES_FOR_IDS] Could not determine market price for card_id: %s. Details: %s', card_id, details)
        return market_prices
    pass