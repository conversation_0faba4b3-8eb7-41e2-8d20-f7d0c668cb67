"""
Gacha系統21點遊戲回調處理器
"""
from typing import TYPE_CHECKING, Optional, Callable, Coroutine, Any, Dict
import discord
from utils.logger import logger
from gacha.utils import interaction_utils
from gacha.services.games.blackjack_service import BlackjackGameService
from gacha.services.core.economy_service import EconomyService
from gacha.services.ui.game_display_service import GameDisplayService
from gacha.views.embeds.games.blackjack_embed_builder import BlackjackEmbedBuilder
from .base_game_handler import BaseGameCallbackHandler
if TYPE_CHECKING:
    from gacha.views.collection.collection_view.interaction_manager import InteractionManager
    from gacha.views.games.blackjack_view import BlackjackView

class BlackjackCallbackHandler(BaseGameCallbackHandler):
    """處理21點遊戲視圖回調的類"""

    def __init__(self, blackjack_service: BlackjackGameService, economy_service: EconomyService, game_display_service: GameDisplayService, interaction_manager: 'InteractionManager', user_id: int, view: 'BlackjackView', game_id: Optional[str]=None):
        """初始化回調處理器

        參數:
            blackjack_service: BlackjackGameService 實例
            economy_service: EconomyService 實例
            game_display_service: GameDisplayService 實例
            interaction_manager: InteractionManager 實例
            user_id: 觸發遊戲的原始用戶 ID
            view: 關聯的 BlackjackView 實例
            game_id: 遊戲ID (可選, 與特定View實例關聯)
        """
        super().__init__(interaction_manager, user_id)
        self._blackjack_service = blackjack_service
        self._economy_service = economy_service
        self._game_display_service = game_display_service
        self.view = view # Store the view instance
        self._game_id = game_id

    def _format_error_embed(self, interaction: discord.Interaction, error_result: Dict[str, Any]) -> discord.Embed:
        """根據服務返回的錯誤結果格式化錯誤 Embed。"""
        error_code = error_result.get('error_code')
        error_details = error_result.get('error_details', {})
        default_message = error_result.get('message', '發生未知錯誤。')
        title = '遊戲操作錯誤'
        description = default_message
        if error_code == 'GAME_NOT_FOUND':
            title = '找不到遊戲'
            description = f"無法找到對應的遊戲 (ID: {error_details.get('game_id', '未知')})。\n可能遊戲已結束或過期。"
        elif error_code == 'UNKNOWN_PLAYER_ACTION':
            title = '未知操作'
            description = f"收到了未知的玩家操作: `{error_details.get('action', '未知')}`"
        elif error_code == 'SETTLEMENT_MISSING_USER_ID' or error_code == 'SETTLEMENT_GAME_NOT_FOUND_OR_NOT_OVER' or error_code == 'SETTLEMENT_GAME_NOT_OVER':
            title = '結算失敗'
            description = f'遊戲結算時發生問題。\n原因: {default_message}'
        elif error_code == 'AWARD_SETTLEMENT_FAILED':
            title = '獎勵發放失敗'
            description = f"在為你派發獎勵時遇到問題。\n原因: {error_details.get('reason', default_message)}"
            if error_details.get('balance_error'):
                description += f" (餘額查詢也失敗: {error_details.get('balance_error')})"
        elif error_code == 'MIN_BET_NOT_MET':
            title = '下注金額太低'
            description = f"你的下注金額 {error_details.get('bet_placed', '?')} 油幣過低。\n"
            description += f"此遊戲的最低下注為 {error_details.get('min_bet', '?')} 油幣。"
        elif error_code == 'INSUFFICIENT_FUNDS':
            title = '油幣不足'
            description = f"你沒有足夠的油幣來下注 {error_details.get('required_bet', '?')} 油幣。\n"
            description += f"你目前的餘額為 {error_details.get('current_balance', '?')} 油幣。"
        elif error_code == 'BLACKJACK_START_FAILED' or error_code == 'UNEXPECTED_BET_ERROR':
            title = '遊戲啟動失敗'
            description = f"無法啟動新的21點遊戲。\n原因: {error_details.get('reason', default_message)}"
        elif default_message:
            description = default_message
        embed = discord.Embed(title=title, description=description, color=discord.Color.red())
        if error_code:
            embed.set_footer(text=f'錯誤代碼: {error_code}')
        return embed

    async def _create_new_game_view(self, user, game_state, original_bet):
        """創建新的遊戲視圖實例

        參數:
            user: Discord用戶對象
            game_state: 遊戲狀態字典
            original_bet: 原始下注金額

        返回:
            新建的BlackjackView實例
        """
        from gacha.views.games.blackjack_view import BlackjackView
        new_view = BlackjackView(user=user, game_state=game_state, blackjack_service=self._blackjack_service, economy_service=self._economy_service, game_display_service=self._game_display_service, original_bet=original_bet)
        await new_view.init_buttons()
        return new_view

    async def handle_action_callback(self, interaction: discord.Interaction, action: str):
        """處理玩家動作（要牌或停牌）的通用回調
        這個方法取代了 hit_callback 和 stand_callback
        使用純異常模式，讓所有錯誤向上傳播給 Cog 層處理
        """
        if not await interaction_utils.check_user_permission(interaction, self.user_id):
            return
        await interaction_utils.safe_defer(interaction)
        
        # 確保有 game_id
        if not self._game_id:
            raise ValueError(f"處理遊戲動作時缺少 game_id (action: {action})")
            
        # 使用純異常模式，直接調用服務方法
        updated_game_state = await self._blackjack_service.player_action(interaction, self._game_id, action)
        
        # 獲取用戶餘額
        user = interaction.user
        user_id = self.user_id
        balance_info = await self._economy_service.get_balance(user_id)
        current_balance = balance_info.get('balance', 0)
            
        # 獲取原始下注額
        original_bet_for_view = updated_game_state.get('original_bet', updated_game_state.get('bet', 0))
        if not original_bet_for_view and self._game_id in self._blackjack_service.active_games:
            original_bet_for_view = self._blackjack_service.active_games[self._game_id].bet
            
        # 創建新視圖
        new_view = await self._create_new_game_view(user, updated_game_state, original_bet_for_view)
        
        # 構建 Embed
        embed_builder = BlackjackEmbedBuilder(updated_game_state, user, current_balance)
        updated_embed = embed_builder.build_embed()
        
        # 更新訊息
        await interaction_utils.safe_edit_message(interaction, embed=updated_embed, view=new_view)

    async def handle_replay_callback(self, interaction: discord.Interaction):
        """處理 Blackjack 重玩按鈕的回調
        使用純異常模式，讓所有錯誤向上傳播給 Cog 層處理
        """
        if not await interaction_utils.check_user_permission(interaction, self.user_id):
            return
        await interaction_utils.safe_defer(interaction, ephemeral=True, thinking=False)
        
        # 解析自定義 ID
        custom_id = interaction.data.get('custom_id', '')
        
        parts = custom_id.split(':')
        if len(parts) != 4 or parts[0] != 'blackjack' or parts[1] != 'replay':
            raise ValueError(f'重玩請求的格式不正確: {custom_id}')
            
        action_type = parts[2]
        try:
            original_bet_from_id = int(parts[3])
        except ValueError:
            raise ValueError(f'重玩請求中的賭注金額格式不正確: {parts[3]}')
            
        # 計算新下注金額
        min_bet = self._blackjack_service.MIN_BET
        new_bet = original_bet_from_id
        if action_type == 'double':
            new_bet = original_bet_from_id * 2
        elif action_type == 'half':
            new_bet = max(min_bet, original_bet_from_id // 2)
        elif action_type == 'min':
            new_bet = min_bet
        elif action_type != 'same':
            raise ValueError(f'未知的重玩操作類型: {action_type}')
            
        # 檢查餘額
        latest_balance_info = await self._economy_service.get_balance(interaction.user.id)
        latest_balance = latest_balance_info.get('balance', 0)
        if latest_balance < new_bet:
            from gacha.exceptions import InsufficientFundsError
            raise InsufficientFundsError(required_amount=new_bet, current_balance=latest_balance)
        
        # 停止原始結果訊息的 View 響應，防止多次重玩，無需 API 調用
        if self.view:
            self.view.stop()
            logger.debug('Stopped blackjack replay view (self.view) for interaction %s to prevent further clicks.', interaction.id)
        else:
            logger.warning('Callback handler does not have a reference to its view for interaction %s.', interaction.id)

        # 創建新遊戲
        new_game_id = str(interaction.id)
        
        # 使用純異常模式啟動新遊戲
        game_state = await self._blackjack_service.start_new_game(interaction, interaction.user.id, new_bet, new_game_id)
        
        # 創建新視圖和 Embed
        new_balance_after_bet = game_state.get('new_balance')
        new_view = await self._create_new_game_view(interaction.user, game_state, new_bet)
        await new_view.init_buttons()
        embed_builder = BlackjackEmbedBuilder(game_state, interaction.user, new_balance_after_bet)
        new_embed = embed_builder.build_embed()
        
        # 發送新訊息
        message = await interaction.followup.send(embed=new_embed, view=new_view, ephemeral=False)
        if message is None:
            raise RuntimeError('創建新遊戲時發生嚴重錯誤，無法顯示遊戲界面')
            
        new_view.message = message