**V. 核心運行邏輯詳解**

1.  服務器啟動： `ConfigLoader` 加載所有JSON到內存。

2.  卡牌屬性計算 (`AttributeCalculator` - 獨立服務/模塊):
    *   **職責：** 計算一個卡牌實例在特定狀態下（例如，戰鬥開始前，或戰鬥中某個時刻的基礎值）的最終RPG屬性。此計算器是獨立的服務或模塊，會被 `Combatant` 實例在初始化時（通過其 `calculate_final_stats` 方法）調用。
    *   **輸入參數 (示例)：**
        *   `card_id` (字符串/整數)
        *   `user_card_instance_data` (對象，來自 `gacha_user_collections` 的記錄，至少包含 `rpg_level`, `star_level` (0-35培養星級))
        *   `global_skill_levels` (字典，`skill_id` -> `skill_level`，來自 `gacha_user_learned_global_skills`)
        *   `all_config_data` (包含所有已加載的JSON配置數據的對象)
    *   **計算步驟 (高層次)：**
        1.  **獲取基礎配置：** 從 `all_config_data['cards.json']` 獲取指定 `card_id` 的基礎配置 (包括 `base_stats`, `growth_per_rpg_level`, `innate_passive_skill_id`, `star_level_effects_key`)。
        2.  **計算RPG等級帶來的屬性：**
            *   `current_rpg_level = user_card_instance_data.rpg_level`
            *   `final_stat = base_stat + (growth_per_level * (current_rpg_level - 1))` (對每個屬性進行)
        3.  **應用 `star_level_effects.json` 帶來的屬性加成：**
            *   `current_star_level = user_card_instance_data.star_level` (0-35範圍)
            *   `star_effects_key = card_config.star_level_effects_key`
            *   如果 `star_effects_key` 存在，則從 `all_config_data['star_level_effects.json'][star_effects_key]` 中查找不大於 `current_star_level` 的最大培養星級檔位定義的 `additional_stats_flat` 和 `additional_stats_percent`，並應用到當前屬性上。
        4.  **應用天賦被動技能 (`innate_passive_skill_id`) 中靜態的、非戰鬥觸發的屬性修改 (如果有的話)：**
            *   `innate_skill_id = card_config.innate_passive_skill_id`
            *   `innate_skill_config = all_config_data['innate_passive_skills.json'][innate_skill_id]`
            *   根據 `current_star_level` (0-35) 確定適用的 `effects_by_star_level` 中的效果塊。
            *   檢查效果塊中是否有在戰鬥外即生效的、直接修改屬性的靜態效果 (這類效果不常見，多數被動是在戰鬥中觸發，但設計上需考慮可能性)。如果存在，則應用。
        5.  **應用已裝備的通用被動技能 (`equipped_common_passives`) 中靜態的、非戰鬥觸發的屬性修改 (如果有的話)：**
            *   遍歷 `user_card_instance_data.equipped_common_passives` 中記錄的 `skill_id`。
            *   對於每個裝備的通用被動技能 `skill_id`，從 `gacha_user_learned_global_skills` 中獲取玩家對該 `skill_id` 的全局熟練度等級 (`current_skill_level`)。
            *   從 `all_config_data['passive_skills.json']` 獲取技能配置。
            *   根據 `current_skill_level` 確定適用的 `effects_by_level` 中的效果塊。
            *   檢查並應用戰鬥外即生效的靜態屬性修改。
        6.  **返回最終計算的屬性集合。**
    *   **注意：** 此計算器主要負責戰鬥開始前或角色面板展示時的屬性。戰鬥中的動態Buff/Debuff導致的屬性變化由 `Combatant` 實例和 `EffectApplier` 處理。

3.  **全局技能熟練度管理 (`PlayerGlobalSkillProficiencyService`)**
    *   **職責：** 
        *   管理玩家在 `gacha_user_learned_global_skills` 表中各技能的全局熟練度等級 (`skill_level`) 和經驗 (`skill_xp`)。
        *   處理通過"獻祭"裝備了同名技能的卡牌來提升特定 `skill_id` 全局熟練度經驗的邏輯。
        *   提供查詢玩家當前所有全局技能熟練度信息的接口。
    *   **提升方式（主要）：卡牌獻祭**
        *   玩家選擇一個希望提升全局熟練度的 `skill_id`。
        *   玩家選擇一批自己擁有且裝備了該 `skill_id` 的卡牌實例 (`gacha_user_collections` 中的記錄) 進行獻祭。
            *   **批量獻祭選項：**
                *   `include_favorites` (布爾值): 是否允許獻祭被標記為 `is_favorited = true` 的卡牌。
                *   `sacrifice_all_but_one` (布爾值): 若為 `true`，即使選擇了某個技能的多張卡牌（包括最愛），系統也會至少保留一張裝備了該技能的卡牌不被獻祭（如果玩家擁有多於一張）。如果玩家只有一張裝備該技能的卡牌，則根據 `include_favorites` 決定是否能獻祭。
        *   對於每張成功被選定用於獻祭的卡牌：
            *   系統從 `active_skills.json` 或 `passive_skills.json` 中查找該 `skill_id` 對應的 `xp_gain_on_sacrifice` 配置值。
            *   將此經驗值增加到玩家該 `skill_id` 的 `gacha_user_learned_global_skills.skill_xp` 上。
            *   獻祭後的卡牌實例從 `gacha_user_collections` 表中刪除。
            *   服務會檢查 `skill_xp` 是否達到或超過了升到下一全局熟練度等級所需的經驗（基於技能的 `xp_to_next_level_config` 或全局等級經驗表計算），如果滿足則自動提升 `skill_level` 並扣除相應經驗。
        *   **其他可能的提升方式 (次要/補充)：** 消耗特定經驗道具、完成特定任務獎勵等，這些方式會直接增加目標 `skill_id` 的 `skill_xp`。
    *   **玩家指令接口 (示例)：**
        *   `view_global_skill_proficiencies(user_id)`: 返回該用戶所有已記錄的全局技能及其等級和經驗。
        *   `sacrifice_cards_for_skill_xp(user_id, target_skill_id, card_instance_ids_to_sacrifice: List[str], include_favorites: bool, sacrifice_all_but_one: bool)`: 執行批量獻祭操作。

4.  **卡牌技能配置與獲取 (`PlayerCardSkillManagementService`)**
    *   **職責：** 處理卡牌技能的裝備、替換和轉移。**不直接處理全局技能熟練度的提升。**
    *   **技能獲取/替換途徑：**
        *   **A. 商店購買 (一次性替換)：**
            *   玩家從商店選擇一個技能 (`skill_id`) 進行購買。
            *   購買成功後，玩家選擇自己擁有的一張卡牌 (`target_card_instance_id`) 和該卡牌上的一個技能槽位 (主動或被動，`target_slot_index`)。
            *   系統將商店購買的 `skill_id` 設置到目標卡牌的目標槽位上，直接覆蓋該槽位原有的 `skill_id` (舊技能消失)。
            *   商店購買的技能在裝備時，其效果相當於該 `skill_id` 在全局熟練度等級1時的效果，但在戰鬥中實際生效的等級依然由玩家對該 `skill_id` 的全局熟練度等級決定。
        *   **B. 卡牌間技能轉移：**
            *   玩家選擇一張源卡牌 (`source_card_instance_id`) 和其上的一個已裝備通用技能的槽位 (`source_slot_index`)。
            *   玩家選擇一張目標卡牌 (`target_card_instance_id`) 和其上的一個同類型技能槽位 (`target_slot_index` - 主動技能只能轉到主動槽，被動只能轉到被動槽)。
            *   系統將源卡牌槽位中的 `skill_id` 複製到目標卡牌的目標槽位上，直接覆蓋目標槽位原有的 `skill_id`。
            *   轉移完成後，源卡牌 (`source_card_instance_data`) 從玩家的 `gacha_user_collections` 表中刪除。
            *   天賦被動技能 (`innate_passive_skill_id`) 不可通過此方式轉移。
    *   **主動技能順序設定：** 玩家可以調整卡牌 `equipped_active_skill_ids` 數組中各 `skill_id` 的順序，以決定戰鬥中的釋放優先級。

5.  戰鬥準備 (`BattleCoordinatorService`):
    *   根據玩家選擇的隊伍和要挑戰的樓層，創建玩家方和怪物方的 `Combatant` 對象列表。
    *   為每個 `Combatant` 調用 `AttributeCalculator` 計算初始戰鬥屬性。
    *   初始化 `Combatant` 的技能實例 (`SkillInstance` 列表):
        *   對於每個裝備的技能 `skill_id` (來自卡牌的 `equipped_active_skill_ids` 和 `equipped_common_passives`)：
            *   查閱 `gacha_user_learned_global_skills` 獲取玩家對該 `skill_id` 的全局熟練度等級 (`current_skill_level`)。
            *   創建 `SkillInstance` 時傳入此 `current_skill_level`。
        *   天賦被動技能 (`innate_passive_skill_id`) 的等級由卡牌的 `star_level` 決定。
        *   普攻技能 (`primary_attack_skill_id`) 通常也關聯一個全局熟練度等級，或者有其固定的設定。
    *   創建 `Battle` 對象。

6.  PVE戰鬥流程 (`Battle` 對象 + `EffectApplier` + `DamageHandler` + `StatusEffectHandler` + `TargetSelector`) (全自動):
    *   **`Battle.start()`:** 決定先手順序 (基於 `spd`)，觸發戰鬥開始型被動/天賦。
    *   **回合循環 (`Battle.next_turn()`):**
        *   **回合開始階段:**
            *   所有主動技能冷卻-1。
            *   `StatusEffectHandler` 處理Buff/Debuff的tick (回合開始時)。
            *   觸發回合開始型被動/天賦。
        *   **行動階段 (按順序為每個單位執行):**
            1.  `acting_combatant = battle.get_acting_combatant()`。
            2.  `action_to_take = acting_combatant.get_available_action()`: 根據主動技能順序、MP、冷卻，決定使用哪個 `skill_id` (或普攻)。
            3.  `skill_definition = acting_combatant.get_skill_instance(skill_id).get_definition_from_config(config_data)`: 獲取該技能當前等級的效果定義 (`effect_definitions` 列表)。
            4.  `targets = TargetSelector.select_targets(acting_combatant, all_combatants_on_field, skill_definition.target_type, skill_definition.target_logic_details)`。
            5.  **`EffectApplier.apply_skill_effects(skill_definition.effect_definitions, acting_combatant, targets, battle_context)`:**
                *   遍歷該技能的所有 `effect_definition`。
                *   對於每個 `effect_def`:
                    *   **如果 `effect_def.effect_type == "DAMAGE"`:**
                        *   調用 `DamageHandler.calculate_final_damage(effect_def, acting_combatant, target, skill_level_data)` 對每個目標計算最終傷害。
                            *   `DamageHandler` 內部：
                                *   計算基礎傷害。
                                *   遍歷 `effect_def.modifiers`，對每個 `modifier_def`，調用 `DamageHandler._apply_modifier()`。
                                *   `_apply_modifier` 內部使用字典分發 `modifier_def.modifier_type` 到對應的私有處理函數 (如 `_process_stat_scaling_modifier`, `_process_missing_hp_modifier` 等)，這些函數根據 `modifier_def` 中的參數（如 `source`, `stat_name`, `scaling_type`, `scaling_factor`)和戰鬥實時數據計算修正。
                                *   處理暴擊。
                                *   （可選）處理目標最終防禦減傷。
                        *   目標 `take_damage()`。
                    *   **如果 `effect_def.effect_type == "HEAL"`:** (類似處理)
                    *   **如果 `effect_def.effect_type == "APPLY_STATUS_EFFECT"`:**
                        *   調用 `StatusEffectHandler.apply_status(effect_def, acting_combatant, target)`。
                    *   **其他 `effect_type`...**
                *   記錄詳細戰鬥日誌 (`BattleLogEntry`)。
            6.  更新施法者MP，設置技能冷卻。
        *   **被動觸發時機 (由 `EffectApplier` 或特定事件監聽器處理):** 如攻擊時、受擊時、造成傷害後等，檢查相關單位的天賦/通用被動，若觸發則執行其 `effect_definitions`。
        *   **回合結束階段:**
            *   `StatusEffectHandler` 處理Buff/Debuff的tick (回合結束時)。
            *   觸發回合結束型被動/天賦。
            *   檢查戰鬥是否結束 (`Battle.check_win_condition()`)。
    *   **戰鬥結束 (`BattleCoordinatorService`):** 更新進度，發放獎勵，返回戰鬥結果和日誌給表現層。

7.  卡牌RPG等級提升 (`PlayerCardManagementService`): 戰鬥勝利後，參戰卡牌獲得經驗。
8.  卡牌升星 (`PlayerCardManagementService`): 消耗資源提升 `star_level`。天賦被動隨星級自動強化，`star_level_effects.json` 中的額外效果在對應星級觸發。

**普攻與技能執行 (`ActionHandler` 或類似概念)**

*   **普攻的傷害類型：**
    *   卡牌的普攻技能由 `cards.json` 中的 `primary_attack_skill_id` 指定，該ID指向 `active_skills.json` 中的一個技能條目。
    *   普攻的傷害類型 (PHYSICAL, MAGICAL, TRUE_DAMAGE) 直接由其在 `active_skills.json` 中對應的技能定義的 `effect_definitions` (通常是第一個效果) 中的 `damage_type` 字段決定。
    *   普攻技能生效時，其等級同樣參考玩家對該 `primary_attack_skill_id` 的全局熟練度等級 (如果普攻技能ID存在於 `gacha_user_learned_global_skills` 中的話，否則可能按1級或特定配置處理)。
*   **技能效果處理 (`EffectApplier`