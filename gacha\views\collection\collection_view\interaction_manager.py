"""
交互響應管理器模組
處理Discord交互響應並避免重複響應問題
"""
from typing import Any, Callable, Optional, Set
import discord
from utils.logger import logger

class InteractionManager:
    """管理交互響應的類，確保每個交互只被響應一次"""

    def __init__(self):
        """初始化交互管理器"""
        self.responded_interactions: Set[int] = set()

    def is_responded(self, interaction_id: int) -> bool:
        """檢查交互是否已被響應

        參數:
            interaction_id: 交互ID

        返回:
            bool: 如果交互已被響應則返回True，否則返回False
        """
        return interaction_id in self.responded_interactions

    def mark_responded(self, interaction_id: int) -> None:
        """標記交互已被響應

        參數:
            interaction_id: 要標記的交互ID
        """
        self.responded_interactions.add(interaction_id)

    async def try_respond(self, interaction: discord.Interaction, response_func: Callable, *args: Any, **kwargs: Any) -> bool:
        """嘗試響應交互，如果已響應則跳過

        參數:
            interaction: Discord交互對象
            response_func: 用於響應交互的異步函數
            *args: 傳遞給response_func的位置參數
            **kwargs: 傳遞給response_func的關鍵字參數

        返回:
            bool: 如果響應成功則返回True，否則返回False
        """
        if self.is_responded(interaction.id):
            logger.warning('交互 %s 已被響應，跳過重複處理', interaction.id)
            return False
        try:
            await response_func(*args, **kwargs)
            self.mark_responded(interaction.id)
            return True
        except Exception as e:
            logger.error('交互響應失敗: %s', e)
            return False

    async def try_defer(self, interaction: discord.Interaction, ephemeral: bool=False) -> bool:
        """嘗試延遲交互響應

        參數:
            interaction: Discord交互對象
            ephemeral: 是否只對用戶可見

        返回:
            bool: 如果操作成功則返回True，否則返回False
        """
        if self.is_responded(interaction.id):
            logger.warning('交互 %s 已被響應，無法延遲', interaction.id)
            return False
        if interaction.response.is_done():
            logger.warning('交互 %s 已完成，無法延遲', interaction.id)
            return False
        try:
            await interaction.response.defer(ephemeral=ephemeral)
            self.mark_responded(interaction.id)
            return True
        except Exception as e:
            logger.error('延遲交互失敗: %s', e)
            return False

    async def try_respond_or_followup(self, interaction: discord.Interaction, *args: Any, **kwargs: Any) -> bool:
        """嘗試直接響應交互，如果失敗則使用 followup.send

        參數:
            interaction: Discord交互對象
            *args: 傳遞給 send_message 或 followup.send 的位置參數
            **kwargs: 傳遞給 send_message 或 followup.send 的關鍵字參數

        返回:
            bool: 如果響應成功（無論哪種方式）則返回True，否則返回False
        """
        if self.is_responded(interaction.id):
            logger.warning('交互 %s 已被標記為已響應，嘗試 followup', interaction.id)
            try:
                await interaction.followup.send(*args, **kwargs)
                return True
            except discord.HTTPException as e:
                logger.error('Followup 響應失敗 (交互 %s): %s', interaction.id, e)
                return False
            except Exception as e:
                logger.error('Followup 響應時發生意外錯誤 (交互 %s): %s', interaction.id, e)
                return False
        try:
            await interaction.response.send_message(*args, **kwargs)
            self.mark_responded(interaction.id)
            return True
        except discord.InteractionResponded:
            logger.warning('交互 %s 已被響應，回退到 followup', interaction.id)
            try:
                await interaction.followup.send(*args, **kwargs)
                self.mark_responded(interaction.id)
                return True
            except discord.HTTPException as e:
                logger.error('Followup 響應失敗 (交互 %s): %s', interaction.id, e)
                return False
            except Exception as e:
                logger.error('Followup 響應時發生意外錯誤 (交互 %s): %s', interaction.id, e)
                return False
        except discord.HTTPException as e:
            logger.error('直接響應失敗 (交互 %s): %s', interaction.id, e)
            return False
        except Exception as e:
            logger.error('直接響應時發生意外錯誤 (交互 %s): %s', interaction.id, e)
            return False