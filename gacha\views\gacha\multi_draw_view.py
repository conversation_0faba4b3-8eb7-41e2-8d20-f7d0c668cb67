"""
Gacha系統十連抽視圖
創建並格式化用於展示十連抽結果的Discord Embed和視圖
"""
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
import discord
from utils.logger import logger
from gacha.models.models import Card, CardWithStatus
from gacha.services.core.wish_service import WishService
from .base_draw_view import BaseDrawView
from gacha.views.embeds.gacha.draw_embed_builder import DrawEmbedBuilder
from gacha.views.embeds.gacha.multi_draw_embed_builder import MultiDrawEmbedBuilder
from gacha.views.collection.favorite_component import FavoriteStateUpdatable
from .multi_draw_button_manager import MultiDrawButtonManager

@dataclass
class ProcessedCardInfo:
    card: Card
    is_new_card: bool
    star_level: int
    is_wish: bool
    is_favorite: bool
    pool_type: str
    rarity_code: int
    owner_count: int
    original_result: Union[Dict[str, Any], CardWithStatus]

class MultiDrawView(BaseDrawView, FavoriteStateUpdatable):
    """十連抽結果視圖，帶有翻頁按鈕。
    按鈕管理已重構為使用 MultiDrawButtonManager。
    """

    def __init__(self, user: discord.User, cards_results: List[Union[Dict[str, Any], CardWithStatus]], owner_counts: Dict[int, int], balance: int, timeout: int=180, on_multi_draw_callback=None, pool_types=None, pool_config_key=None):
        """初始化十連抽視圖
        重構說明：按鈕現在在初始化時創建一次，翻頁時僅更新狀態。
        """
        super().__init__(user=user, balance=balance, timeout=timeout, on_draw_callback=on_multi_draw_callback, pool_types=pool_types, pool_config_key=pool_config_key)
        self.cards_results_raw = cards_results
        self.owner_counts = owner_counts
        self.current_index = 0
        self.total_cards = len(self.cards_results_raw)
        self._processed_cards_view_data: List[ProcessedCardInfo] = []
        for raw_result in self.cards_results_raw:
            card, is_new, star, is_wish_flag, is_fav, pool, rarity = BaseDrawView.extract_card_info(raw_result)
            owner_count = self.owner_counts.get(card.card_id, 0)
            self._processed_cards_view_data.append(ProcessedCardInfo(card=card, is_new_card=is_new, star_level=star, is_wish=is_wish_flag, is_favorite=is_fav, pool_type=pool, rarity_code=rarity, owner_count=owner_count, original_result=raw_result))
        
        summary_builder = MultiDrawEmbedBuilder(user=self.user, cards_results=self.cards_results_raw, balance=self.balance, nickname=self.user.display_name)
        self.summary_embed_cache: discord.Embed = summary_builder.build_embed()
        self._summary_field_count: int = len(self.summary_embed_cache.fields)

        # 按鈕實例將由 ButtonManager 創建並附加到 self 上
        self.continue_button: Optional[discord.ui.Button] = None
        self.favorite_button: Optional[discord.ui.Button] = None
        self.prev_button: Optional[discord.ui.Button] = None
        self.next_button: Optional[discord.ui.Button] = None

        self.button_manager = MultiDrawButtonManager(self)
        self.button_manager.create_all_buttons()
        self.button_manager.add_buttons_to_view()
        self.button_manager.refresh_buttons_state() # 設置初始狀態

    @property
    def current_card(self) -> Optional[ProcessedCardInfo]:
        """返回當前正在顯示的 ProcessedCardInfo 實例，以兼容 FavoriteComponent 的期望結構。"""
        if not self._processed_cards_view_data or self.current_index < 0 or self.current_index >= self.total_cards:
            return None
        return self._processed_cards_view_data[self.current_index]

    async def _continue_multi_draw_callback(self, interaction: discord.Interaction):
        """繼續十連抽按鈕處理"""
        await self._process_draw_callback(interaction, is_multi_draw=True)

    async def _previous_button_callback(self, interaction: discord.Interaction):
        """上一張卡片按鈕處理"""
        if not await self.interaction_check(interaction):
            return
        self.current_index = (self.current_index - 1 + self.total_cards) % self.total_cards
        self.button_manager.refresh_buttons_state()
        
        await interaction.response.defer()
        
        current_processed_info = self._processed_cards_view_data[self.current_index]
        summary_embed_copy = self.summary_embed_cache.copy()
        builder = MultiDrawEmbedBuilder(user=self.user, cards_results=self.cards_results_raw, balance=self.balance, nickname=self.user.display_name)
        enriched_embed = builder.enrich_embed_with_page_details(
            base_embed=summary_embed_copy, 
            card=current_processed_info.card, 
            is_new_card=current_processed_info.is_new_card, 
            is_wish=current_processed_info.is_wish, 
            is_favorite=current_processed_info.is_favorite, 
            star_level=current_processed_info.star_level, 
            pool_type=current_processed_info.pool_type, 
            owner_count=current_processed_info.owner_count, 
            current_index=self.current_index, 
            total_cards=self.total_cards, 
            summary_field_count=self._summary_field_count
        )
        await interaction.followup.edit_message(message_id=interaction.message.id, embed=enriched_embed, view=self)

    async def _next_button_callback(self, interaction: discord.Interaction):
        """下一張卡片按鈕處理"""
        if not await self.interaction_check(interaction):
            return
        self.current_index = (self.current_index + 1) % self.total_cards
        self.button_manager.refresh_buttons_state()

        await interaction.response.defer()

        current_processed_info = self._processed_cards_view_data[self.current_index]
        summary_embed_copy = self.summary_embed_cache.copy()
        builder = MultiDrawEmbedBuilder(user=self.user, cards_results=self.cards_results_raw, balance=self.balance, nickname=self.user.display_name)
        enriched_embed = builder.enrich_embed_with_page_details(
            base_embed=summary_embed_copy, 
            card=current_processed_info.card, 
            is_new_card=current_processed_info.is_new_card, 
            is_wish=current_processed_info.is_wish, 
            is_favorite=current_processed_info.is_favorite, 
            star_level=current_processed_info.star_level, 
            pool_type=current_processed_info.pool_type, 
            owner_count=current_processed_info.owner_count, 
            current_index=self.current_index, 
            total_cards=self.total_cards, 
            summary_field_count=self._summary_field_count
        )
        await interaction.followup.edit_message(message_id=interaction.message.id, embed=enriched_embed, view=self)

    async def get_current_page_embed(self, interaction: Optional[discord.Interaction]=None) -> discord.Embed:
        """獲取當前卡片的嵌入

        參數:
            interaction: 可選的 Discord interaction 物件。

        返回:
            discord.Embed: 格式化後的Discord Embed對象
        """
        current_processed_info = self._processed_cards_view_data[self.current_index]
        card = current_processed_info.card
        is_new_card = current_processed_info.is_new_card
        star_level = current_processed_info.star_level
        is_wish = current_processed_info.is_wish
        is_favorite = current_processed_info.is_favorite
        pool_type = current_processed_info.pool_type
        owner_count = current_processed_info.owner_count
        
        current_interaction = interaction if interaction is not None else getattr(self, 'interaction', None)
        
        builder = DrawEmbedBuilder(
            user=self.user, 
            card=card, 
            balance=self.balance, 
            is_new_card=is_new_card, 
            pool_type=pool_type, 
            is_wish=is_wish, 
            star_level=star_level, 
            is_favorite=is_favorite,
            owner_count=owner_count, 
            interaction=current_interaction
        )
        embed = builder.build_embed()
        original_footer_text = embed.footer.text if embed.footer and embed.footer.text else ""
        embed.set_footer(text=f'{original_footer_text} • 第 {self.current_index + 1}/{self.total_cards} 張')
        return embed

    async def get_combined_embed(self, interaction: Optional[discord.Interaction]=None) -> discord.Embed:
        """獲取合併總覽和卡片詳細信息的嵌入"""
        if not self._processed_cards_view_data:
            logger.error('get_combined_embed called with no card results.')
            return discord.Embed(title='錯誤', description='沒有卡片結果可顯示。', color=discord.Color.red())
        
        current_processed_info = self._processed_cards_view_data[self.current_index]
        summary_embed_copy = self.summary_embed_cache.copy()
        builder = MultiDrawEmbedBuilder(user=self.user, cards_results=self.cards_results_raw, balance=self.balance, nickname=self.user.display_name)
        
        return builder.enrich_embed_with_page_details(
            base_embed=summary_embed_copy, 
            card=current_processed_info.card, 
            is_new_card=current_processed_info.is_new_card, 
            is_wish=current_processed_info.is_wish, 
            is_favorite=current_processed_info.is_favorite,
            star_level=current_processed_info.star_level, 
            pool_type=current_processed_info.pool_type, 
            owner_count=current_processed_info.owner_count, 
            current_index=self.current_index, 
            total_cards=self.total_cards, 
            summary_field_count=self._summary_field_count
        )

    def update_favorite_state(self, card_id: int, is_favorite: bool) -> bool:
        """實現FavoriteStateUpdatable介面，更新視圖內部（_processed_cards_view_data）的最愛狀態
        並重建總覽嵌入的緩存以反映變化。
        """
        try:
            updated = False
            for idx, p_info in enumerate(self._processed_cards_view_data):
                if p_info.card.card_id == card_id:
                    p_info.is_favorite = is_favorite
                    # 更新原始數據中的狀態（如果適用）
                    if isinstance(p_info.original_result, CardWithStatus) and hasattr(p_info.original_result, 'status'):
                        p_info.original_result.status.is_favorite = is_favorite
                    elif isinstance(p_info.original_result, dict):
                        # 處理字典形式的原始結果，確保is_favorite鍵存在或card對象存在
                        if 'is_favorite' in p_info.original_result:
                            p_info.original_result['is_favorite'] = is_favorite
                        elif 'card' in p_info.original_result and isinstance(p_info.original_result['card'], dict):
                             p_info.original_result['card']['is_favorite'] = is_favorite # 假設card也是個dict
                    updated = True
                    break # 找到並更新後即可退出循環
            
            if updated:
                # 重建 summary_embed_cache 以反映 cards_results_raw 中可能的 is_favorite 狀態變化
                # 假設 MultiDrawEmbedBuilder 在構建時會從 cards_results_raw 讀取 is_favorite
                summary_builder = MultiDrawEmbedBuilder(user=self.user, cards_results=self.cards_results_raw, balance=self.balance, nickname=self.user.display_name)
                self.summary_embed_cache = summary_builder.build_embed()
                self._summary_field_count = len(self.summary_embed_cache.fields) # 同步字段計數
                return True
            return False
        except Exception as e:
            logger.error('更新MultiDrawView最愛狀態失敗: %s', e, exc_info=True)
            return False

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """檢查互動是否來自視圖擁有者。"""
        if interaction.user.id != self.user.id:
            try:
                await interaction.response.send_message("你無法操作不屬於你的抽卡結果視圖。", ephemeral=True)
            except discord.InteractionResponded:
                await interaction.followup.send("你無法操作不屬於你的抽卡結果視圖。", ephemeral=True)
            except Exception as e:
                logger.warning(f"Error sending interaction check message: {e}")
            return False
        return True