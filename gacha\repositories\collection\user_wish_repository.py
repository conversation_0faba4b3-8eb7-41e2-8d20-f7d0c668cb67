import asyncpg
from typing import Dict, List, Optional, Tuple, Any, Set
from database.base_repository import BaseRepository
from utils.logger import logger
from gacha.constants import RarityLevel
from gacha.models.models import Card
from gacha.exceptions import DatabaseOperationError, RecordNotFoundError

class UserWishRepository(BaseRepository):
    """用戶許願存儲庫 (Asyncpg 版本)，管理 gacha_user_wishes 表"""

    def __init__(self, pool: asyncpg.Pool):
        """初始化用戶許願存儲庫 (Asyncpg 版本)

        參數:
            pool: asyncpg 連接池實例
        """
        super().__init__(pool)
        self.table_name = 'gacha_user_wishes'

    async def get_user_wishes(self, user_id: int, connection: Optional[asyncpg.Connection]=None) -> List[Dict[str, Any]]:
        """(Async) 獲取用戶的所有許願卡片"""
        query = f'SELECT * FROM {self.table_name} WHERE user_id = $1 ORDER BY slot_index'
        results = await self._fetch(query, (user_id,), connection=connection)
        return [dict(row) for row in results] if results else []

    async def get_user_wishes_by_rarity(self, user_id: int, rarity: RarityLevel, connection: Optional[asyncpg.Connection]=None) -> List[Dict[str, Any]]:
        """(Async) 獲取用戶特定稀有度的許願卡片 (使用 RarityLevel Enum)"""
        query = f'\n            SELECT uw.* FROM {self.table_name} uw\n            JOIN gacha_master_cards mc ON uw.card_id = mc.card_id\n            WHERE uw.user_id = $1 AND mc.rarity = $2\n            ORDER BY uw.slot_index\n        '
        results = await self._fetch(query, (user_id, rarity.value), connection=connection)
        return [dict(row) for row in results] if results else []

    async def get_user_wish_ids_by_rarity(self, user_id: int, rarity: RarityLevel, connection: Optional[asyncpg.Connection]=None) -> List[int]:
        """(Async) 獲取用戶特定稀有度的許願卡片ID列表 (使用 RarityLevel Enum)

        參數:
            user_id: 用戶ID
            rarity: 卡片稀有度 (RarityLevel Enum)

        返回:
            List[int]: 符合條件的卡片ID列表
        """
        query = f'\n            SELECT uw.card_id FROM {self.table_name} uw\n            JOIN gacha_master_cards mc ON uw.card_id = mc.card_id\n            WHERE uw.user_id = $1 AND mc.rarity = $2\n        '
        results = await self._fetch(query, (user_id, rarity.value), connection=connection)
        return [row['card_id'] for row in results] if results else []

    async def get_all_user_wishes_with_rarity_grouped(self, user_id: int, connection: Optional[asyncpg.Connection]=None) -> Dict[RarityLevel, List[int]]:
        """(Async) 獲取用戶所有許願卡片的 ID，按稀有度分組。"""
        query = f'\n            SELECT mc.rarity, uw.card_id\n            FROM {self.table_name} uw\n            JOIN gacha_master_cards mc ON uw.card_id = mc.card_id\n            WHERE uw.user_id = $1\n        '
        results = await self._fetch(query, (user_id,), connection=connection)
        wishes_by_rarity: Dict[RarityLevel, List[int]] = {}
        if results:
            for row in results:
                try:
                    rarity_level = RarityLevel(row['rarity'])
                    card_id = row['card_id']
                    if rarity_level not in wishes_by_rarity:
                        wishes_by_rarity[rarity_level] = []
                    wishes_by_rarity[rarity_level].append(card_id)
                except ValueError:
                    logger.warning("[UserWishRepository] Invalid rarity value '%s' for card_id %s wished by user %s. Skipping.", row['rarity'], row['card_id'], user_id)
                    continue
        return wishes_by_rarity

    async def add_user_wish(self, user_id: int, card_id: int, slot_index: int, connection: Optional[asyncpg.Connection]=None) -> int:
        """(Async) 向用戶的許願列表添加一張卡片。成功返回記錄ID，失敗則拋出異常。"""
        query = f'\n            INSERT INTO {self.table_name} (user_id, card_id, slot_index)\n            VALUES ($1, $2, $3)\n            RETURNING id\n        '
        result = await self._fetchval(query, (user_id, card_id, slot_index), connection=connection)
        if result is None:
            raise DatabaseOperationError(f"添加許願記錄失敗: user_id={user_id}, card_id={card_id}, slot_index={slot_index}")
        return result

    async def remove_user_wish(self, user_id: int, card_id: int, connection: Optional[asyncpg.Connection]=None) -> int:
        """(Async) 從用戶的許願列表中移除一張卡片。成功返回影響的行數 (應為1)，失敗則拋出異常。"""
        query = f'DELETE FROM {self.table_name} WHERE user_id = $1 AND card_id = $2'
        status = await self._execute(query, (user_id, card_id), connection=connection)
        if status and status.startswith('DELETE '):
            try:
                return int(status.split(' ')[1])
            except (IndexError, ValueError):
                logger.error('[UserWishRepository] Unexpected status from _execute for remove_user_wish: %s', status)
                return 0
        return 0

    async def get_user_wishes_with_details(self, user_id: int, connection: Optional[asyncpg.Connection]=None) -> List[Dict[str, Any]]:
        """(Async) 獲取用戶的所有許願卡片及其詳細信息 (JOIN gacha_master_cards)。"""
        query = f'\n            SELECT\n                uw.user_id,\n                uw.card_id,\n                uw.slot_index,\n                uw.created_at,\n                mc.name AS card_name,\n                mc.series AS card_series,\n                mc.rarity AS card_rarity,\n                mc.image_url AS card_image_url,\n                mc.pool_type AS card_pool_type\n                -- Add other fields from gacha_master_cards as needed\n            FROM {self.table_name} uw\n            JOIN gacha_master_cards mc ON uw.card_id = mc.card_id\n            WHERE uw.user_id = $1\n            ORDER BY uw.slot_index\n        '
        results = await self._fetch(query, (user_id,), connection=connection)
        detailed_wishes = []
        if results:
            for row in results:
                card_info = {'card_id': row['card_id'], 'name': row['card_name'], 'series': row['card_series'], 'rarity': row['card_rarity'], 'image_url': row['card_image_url'], 'pool_type': row['card_pool_type']}
                detailed_wishes.append({'user_id': row['user_id'], 'card_id': row['card_id'], 'slot_index': row['slot_index'], 'created_at': row['created_at'], 'card_info': card_info})
        return detailed_wishes

    async def get_wished_card_ids_for_user(self, user_id: int, card_ids: List[int], connection: Optional[asyncpg.Connection]=None) -> Set[int]:
        """(Async) 檢查提供的卡片ID列表中，哪些是該用戶許願了的。返回許願了的卡片ID集合。"""
        if not card_ids:
            return set()
        placeholders = ', '.join([f'${i + 2}' for i in range(len(card_ids))])
        query = f'\n            SELECT card_id\n            FROM {self.table_name}\n            WHERE user_id = $1 AND card_id IN ({placeholders})\n        '
        params = [user_id] + card_ids
        results = await self._fetch(query, params, connection=connection)
        return {row['card_id'] for row in results} if results else set()

    async def get_user_wish_power_level(self, user_id: int, connection: Optional[asyncpg.Connection]=None) -> int:
        """(Async) 獲取用戶的許願強度。

        參數:
            user_id: 用戶ID
            connection: 可選的數據庫連接

        返回:
            int: 用戶的許願強度，默認為1
        """
        logger.debug('[UserWishRepository] 獲取用戶 %s 的許願強度，暫時返回默認值1', user_id)
        return 1