import asyncpg
from typing import List, Dict, Any, Optional
from database.base_repository import BaseRepository
from utils.logger import logger
from gacha.exceptions import DatabaseOperationError, RecordNotFoundError

class PlayerPortfolioRepository(BaseRepository):
    """玩家資產組合存儲庫，管理 player_portfolios 表"""

    def __init__(self, pool: asyncpg.Pool):
        super().__init__(pool)
        self.table_name = 'player_portfolios'

    async def get_holders_of_asset(self, asset_id: int, conn: Optional[asyncpg.Connection]=None) -> List[Dict[str, Any]]:
        """
        查詢 player_portfolios 表，返回所有持有指定 asset_id 的玩家記錄列表。
        每條記錄至少應包含 user_id 和 quantity。
        
        Args:
            asset_id: 資產ID
            conn: 可選的資料庫連接
            
        Returns:
            List[Dict[str, Any]]: 持有該資產的玩家記錄列表
            
        Raises:
            DatabaseOperationError: 資料庫操作失敗時拋出
        """
        try:
            query = f'SELECT user_id, quantity FROM {self.table_name} WHERE asset_id = $1'
            records = await self._fetch(query, (asset_id,), connection=conn)
            return [dict(record) for record in records]
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"查詢資產 {asset_id} 持有者失敗: {e}") from e

    async def remove_asset_holding(self, user_id: int, asset_id: int, conn: Optional[asyncpg.Connection]=None) -> bool:
        """
        從 player_portfolios 表中刪除指定 user_id 和 asset_id 的持股記錄。
        
        Args:
            user_id: 用戶ID
            asset_id: 資產ID
            conn: 可選的資料庫連接
            
        Returns:
            bool: 成功刪除返回 True，記錄不存在返回 False
            
        Raises:
            DatabaseOperationError: 資料庫操作失敗時拋出
        """
        try:
            query = f'DELETE FROM {self.table_name} WHERE user_id = $1 AND asset_id = $2'
            status = await self._execute(query, (user_id, asset_id), connection=conn)
            
            if status.startswith('DELETE'):
                try:
                    num_deleted = int(status.split(' ')[-1])
                    if num_deleted > 0:
                        logger.info('Successfully removed asset %s for user %s. (%s)', asset_id, user_id, status)
                        return True
                    else:
                        logger.warning('No asset %s found for user %s to remove. (%s)', asset_id, user_id, status)
                        return False
                except (IndexError, ValueError) as e:
                    logger.warning('Unexpected status format from DELETE operation for asset %s, user %s: %s. Error: %s', asset_id, user_id, status, e)
                    return False
            else:
                logger.warning('Unexpected status from DELETE operation for asset %s, user %s: %s', asset_id, user_id, status)
                return False
                
        except asyncpg.PostgresError as e:
            raise DatabaseOperationError(f"刪除用戶 {user_id} 的資產 {asset_id} 失敗: {e}") from e