"""
改進的按鈕管理器，減少重複代碼並提供更清晰的邏輯
"""

from typing import TYPE_CHECKING
import discord
from .button_factory import ButtonFactory
from .utils import get_card_state, CardStateInfo
from gacha.views.collection.favorite_component import FavoriteComponent

if TYPE_CHECKING:
    from .card_view import CollectionView


class ImprovedButtonManager:
    """改進的按鈕管理器 - 直接調用 Handler 方法的最佳實踐版本"""

    def __init__(self, view: "CollectionView"):
        self.view = view
        # 假設 self.view.favorite_button_main 等按鈕已經在 CollectionView 中被創建
        # 並在 CollectionView 的 _create_all_buttons 方法中賦值給 self.view 的屬性
        self._button_layouts = {
            # (show_function_mode, in_sort_mode): [buttons_for_this_mode]
            (False, False): [  # 主模式
                self.view.favorite_button_main,
                self.view.sell_one_button_main,
                self.view.sell_all_button_main,
                self.view.function_button,
            ],
            (True, False): [   # 功能模式 (非排序)
                # Row 1: Batch, Back
                self.view.batch_favorite_button,
                self.view.batch_unfavorite_button,
                self.view.back_to_main_button,
                # Row 2: Enhance, Sort, Desc
                self.view.enhance_button,
                self.view.sort_button,
                self.view.description_button,
            ],
            (True, True): [    # 功能模式 (排序)
                # Row 2: Move (as per original structure, assuming these are on their own rows or grouped)
                self.view.move_top_button,
                self.view.move_up_button,
                self.view.move_down_button,
                self.view.move_bottom_button,
                # Row 3: Position, Back
                self.view.move_position_button,
                self.view.back_to_function_button,
            ],
        }

    def get_card_state(self) -> CardStateInfo:
        """獲取當前卡片狀態，使用統一的狀態管理工具"""
        return get_card_state(self.view)

    async def add_buttons(self):
        """添加按鈕到視圖，根據當前視圖狀態添加不同的按鈕"""
        current_mode_key = (
            self.view.state.show_function_mode,
            self.view.state.in_sort_mode,
        )
        
        buttons_to_add = self._button_layouts.get(current_mode_key, [])
        
        for button in buttons_to_add:
            # 確保按鈕存在，避免 AttributeError
            if button is not None:
                self.view.add_item(button)
            else:
                # 可以選擇記錄一個警告，如果某個預期的按鈕沒有被正確初始化
                from utils.logger import logger # 延遲導入以避免循環
                logger.warning(f"Button in layout for mode {current_mode_key} was None.")

    def _refresh_main_mode_buttons_state(self, state: CardStateInfo):
        """刷新主模式按鈕的狀態"""
        # 假設 _update_main_favorite_button_state 處理 favorite_button_main
        self._update_main_favorite_button_state(state)
        
        if hasattr(self.view, 'sell_one_button_main') and self.view.sell_one_button_main:
            self.view.sell_one_button_main.disabled = not state.is_available
        if hasattr(self.view, 'sell_all_button_main') and self.view.sell_all_button_main:
            self.view.sell_all_button_main.disabled = not state.is_available
        if hasattr(self.view, 'function_button') and self.view.function_button:
            self.view.function_button.disabled = not state.is_available

    def _refresh_function_mode_buttons_state(self, state: CardStateInfo):
        """刷新功能模式（非排序）按鈕的狀態"""
        if hasattr(self.view, 'batch_favorite_button') and self.view.batch_favorite_button:
            self.view.batch_favorite_button.disabled = not state.is_available
        if hasattr(self.view, 'batch_unfavorite_button') and self.view.batch_unfavorite_button:
            self.view.batch_unfavorite_button.disabled = not state.is_available
        
        if hasattr(self.view, 'enhance_button') and self.view.enhance_button:
            self.view.enhance_button.disabled = not (state.is_available and state.has_duplicates)
        
        if hasattr(self.view, 'sort_button') and self.view.sort_button:
            ButtonFactory.update_sort_mode_button_state(
                self.view.sort_button,
                state.is_available,
                state.is_favorite
            )
        
        if hasattr(self.view, 'description_button') and self.view.description_button:
            # 根據原始碼，此處有星級檢查， CardStateInfo 應包含 star_level
            self.view.description_button.disabled = not (state.is_available and state.star_level >= 10)
        # back_to_main_button 通常總是啟用

    def _refresh_sort_mode_buttons_state(self, state: CardStateInfo):
        """刷新排序模式按鈕的狀態"""
        is_enabled = state.is_available
        if hasattr(self.view, 'move_top_button') and self.view.move_top_button:
            self.view.move_top_button.disabled = not is_enabled
        if hasattr(self.view, 'move_up_button') and self.view.move_up_button:
            self.view.move_up_button.disabled = not is_enabled
        if hasattr(self.view, 'move_down_button') and self.view.move_down_button:
            self.view.move_down_button.disabled = not is_enabled
        if hasattr(self.view, 'move_bottom_button') and self.view.move_bottom_button:
            self.view.move_bottom_button.disabled = not is_enabled
        if hasattr(self.view, 'move_position_button') and self.view.move_position_button:
            self.view.move_position_button.disabled = not is_enabled
        # back_to_function_button 通常總是啟用

    async def refresh_functional_buttons(self):
        """根據當前卡片狀態刷新 *已存在* 的功能按鈕實例的屬性"""
        state = self.get_card_state()

        if not self.view.state.show_function_mode:
            self._refresh_main_mode_buttons_state(state)
        elif self.view.state.in_sort_mode: # Implies show_function_mode is True
            self._refresh_sort_mode_buttons_state(state)
        else: # Implies show_function_mode is True and not in_sort_mode
            self._refresh_function_mode_buttons_state(state)

    def _update_main_favorite_button_state(self, state: CardStateInfo):
        """更新主模式下的最愛按鈕狀態 - 統一調用 FavoriteComponent"""
        button = self.view.favorite_button_main
        if state.is_available and self.view.favorite_handler:
             # --- 調用 FavoriteComponent 的方法來統一更新樣式 ---
             FavoriteComponent.update_button_state(button, state.is_favorite)
             button.disabled = False # 確保按鈕是啟用的 (如果 is_available)
             # FavoriteComponent.update_button_state 會處理 label, style, emoji
        else:
             # 如果卡片不可用，則禁用按鈕並設置為預設狀態
             FavoriteComponent.update_button_state(button, False) # 設置為未收藏的樣式
             button.disabled = True

    # --- 舊的 _add_* 方法不再需要，移除或註釋掉 ---
    # def _add_main_mode_buttons(self): ...
    # async def _add_function_mode_buttons(self): ...
    # def _add_sort_mode_buttons(self): ...
    # def _add_favorite_button(self, state: CardStateInfo, row: int): ...
    # def _add_sell_buttons(self, state: CardStateInfo, row: int): ...
    # def _add_batch_buttons(self, state: CardStateInfo, row: int): ...
    # def _add_enhance_button(self, state: CardStateInfo, row: int): ...
    # def _add_sort_button(self, state: CardStateInfo, row: int): ...
    # def _add_description_button(self, state: CardStateInfo, row: int): ...
    # async def _add_wish_button(self, state: CardStateInfo, row: int): ...
