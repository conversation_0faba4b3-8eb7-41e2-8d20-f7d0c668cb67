"""
打拳GIF疊加模塊
將用戶頭像疊加到打拳GIF上，頭像會隨著拳頭動作產生位移和縮放效果。
"""
import os
import discord
import aiohttp
from PIL import Image, ImageSequence
import io
import math
from .config import get_gif_config

async def fetch_avatar(user):
    """獲取用戶頭像"""
    avatar_url = user.display_avatar.url
    async with aiohttp.ClientSession() as session:
        async with session.get(str(avatar_url)) as resp:
            if resp.status == 200:
                avatar_data = await resp.read()
                return Image.open(io.BytesIO(avatar_data)).convert("RGBA")
    return None

def determine_punch_direction(frame_index, num_frames):
    """
    判斷當前幀的拳頭方向
    
    返回:
        int: 負值表示左拳，正值表示右拳，絕對值表示強度
    """
    # 使用8幀循環模式
    cycle_pos = frame_index % 8
    
    # 根據用戶觀察到的拳頭動作調整位移模式
    # 右拳: 2-3幀出拳, 4-5幀收拳
    # 左拳: 6-7幀出拳, 0-1幀收拳
    displacements = [
        -3,   # 位置0: 左拳收回中
        0,    # 位置1: 恢復到中間
        3,    # 位置2: 右拳開始出拳
        8,    # 位置3: 右拳完全伸出
        5,    # 位置4: 右拳開始收回
        0,    # 位置5: 恢復到中間
        -3,   # 位置6: 左拳開始出拳
        -8    # 位置7: 左拳完全伸出
    ]
    
    # 返回當前幀的位移量
    return displacements[cycle_pos]

def determine_punch_scale(frame_index, num_frames):
    """
    判斷當前幀的拳頭力度及對應的縮放效果
    
    返回:
        float: 拳頭出力時的縮放係數
    """
    # 使用8幀循環模式，與位移保持一致
    cycle_pos = frame_index % 8
    
    # 縮放比例 - 根據拳頭出力調整
    max_scale = 1.1  # 最大放大到1.1倍
    min_scale = 1.0  # 正常大小
    
    # 根據拳頭動作定義縮放模式
    # 右拳出力(2-3)和左拳出力(6-7)時放大
    # 其他時間保持正常大小
    scales = [
        1.0,      # 位置0: 左拳收回中，正常大小
        1.0,      # 位置1: 恢復到中間
        1.05,     # 位置2: 右拳開始出拳，稍微放大
        1.1,      # 位置3: 右拳完全伸出，最大放大
        1.05,     # 位置4: 右拳開始收回，稍微放大
        1.0,      # 位置5: 恢復到中間
        1.05,     # 位置6: 左拳開始出拳，稍微放大
        1.1       # 位置7: 左拳完全伸出，最大放大
    ]
    
    # 返回當前幀的縮放係數
    return scales[cycle_pos]

async def overlay_punch_gif(gif_path, user, output_path=None, fixed_size=(0, 0)):
    """
    將用戶頭像疊加到打拳GIF上，頭像會隨著拳頭動作產生位移和縮放效果
    
    參數:
        gif_path (str): GIF文件路徑
        user (discord.User): Discord用戶對象
        output_path (str, 可選): 輸出文件路徑，默認為臨時文件
        fixed_size (tuple, 可選): 設置固定輸出尺寸，例如(350, 350)，默認(0, 0)表示保持原尺寸
        
    返回:
        str: 生成的GIF文件路徑
    """
    # 獲取GIF配置
    gif_config = get_gif_config(gif_path)
    
    # 獲取用戶頭像
    avatar = await fetch_avatar(user)
    if avatar is None:
        raise ValueError("無法獲取用戶頭像")
    
    # 調整頭像大小
    avatar_size = gif_config["avatar_size"]
    avatar = avatar.resize(avatar_size, Image.LANCZOS)
    
    try:
        # 使用PIL打開GIF
        original_gif = Image.open(gif_path)
        
        # 取得原始GIF的信息
        original_duration = original_gif.info.get('duration', 100)
        original_loop = original_gif.info.get('loop', 0)
        
        # 獲取GIF尺寸
        gif_width, gif_height = original_gif.size
        
        # 獲取GIF幀數
        num_frames = 0
        for frame in ImageSequence.Iterator(original_gif):
            num_frames += 1
        
        # 創建輸出文件路徑
        if output_path is None:
            output_dir = os.path.join("image_processing", "temp")
            os.makedirs(output_dir, exist_ok=True)
            gif_name = os.path.basename(gif_path)
            output_path = os.path.join(output_dir, f"{gif_name.split('.')[0]}_{user.id}.gif")
        
        # 處理GIF的每一幀並疊加頭像
        frames = []
        for i, frame in enumerate(ImageSequence.Iterator(original_gif)):
            # 創建新的空白幀
            new_frame = Image.new("RGBA", (gif_width, gif_height), (0, 0, 0, 0))
            
            # 獲取拳頭方向和縮放效果
            punch_direction = determine_punch_direction(i, num_frames)
            punch_scale = determine_punch_scale(i, num_frames)
            
            # 計算當前幀頭像需要的尺寸
            scaled_size = (
                int(avatar_size[0] * punch_scale),
                int(avatar_size[1] * punch_scale)
            )
            
            # 調整頭像大小
            scaled_avatar = avatar.resize(scaled_size, Image.LANCZOS)
            
            # 計算頭像的位置 - 考慮縮放後的尺寸，保持居中對齊
            avatar_x = (gif_width - scaled_size[0]) // 2 + punch_direction
            avatar_y = (gif_height - scaled_size[1] - gif_config["offset_y"]) // 2
            
            # 放置頭像
            new_frame.paste(scaled_avatar, (avatar_x, avatar_y), scaled_avatar)
            
            # 取得轉換為RGBA的GIF幀
            frame_rgba = frame.convert("RGBA")
            
            # 再放置GIF幀（在頭像上方），保留GIF的透明度
            new_frame.alpha_composite(frame_rgba)
            
            # 添加到幀列表
            frames.append(new_frame)
        
        # 如果需要調整為固定尺寸
        if fixed_size[0] > 0 and fixed_size[1] > 0:
            resized_frames = []
            for frame in frames:
                # 使用高質量的LANCZOS算法調整大小
                resized_frame = frame.resize(fixed_size, Image.LANCZOS)
                resized_frames.append(resized_frame)
            frames = resized_frames
        
        # 處理透明通道
        transparent_color = (0, 0, 0)  # 黑色作為透明色
        converted_frames = []
        
        for frame in frames:
            # 創建一個RGB圖像，用特定的顏色填充
            background = Image.new("RGB", frame.size, transparent_color)
            # 合成頭像和GIF，透明部分將顯示為transparent_color
            background.paste(frame, (0, 0), frame)
            # 轉換為P模式，指定顏色範圍
            paletted = background.quantize(colors=255, method=2)
            
            # 獲取alpha通道作為遮罩
            alpha = frame.getchannel('A')
            # 創建透明區域的遮罩
            mask = Image.eval(alpha, lambda a: 0 if a > 0 else 255)
            # 設置透明區域
            paletted.paste(255, mask)
            
            # 設置透明索引
            paletted.info['transparency'] = 255
            converted_frames.append(paletted)
        
        # 保存為GIF
        output_buffer = io.BytesIO()
        converted_frames[0].save(
            output_buffer,
            format="GIF",
            save_all=True,
            append_images=converted_frames[1:],
            duration=original_duration,
            loop=original_loop,
            disposal=2,  # 確保每一幀都完全替換前一幀
            transparency=255
        )
        
        # 寫入文件
        with open(output_path, 'wb') as f:
            f.write(output_buffer.getvalue())
        
        # 強制清除可能的緩存文件
        try:
            import glob
            for cache_file in glob.glob(os.path.join(output_dir, f"{gif_name.split('.')[0]}_*.gif")):
                if cache_file != output_path:
                    try:
                        os.remove(cache_file)
                    except:
                        pass
        except:
            pass
        
        return output_path
    
    except Exception as e:
        # 處理錯誤
        import traceback
        error_details = traceback.format_exc()
        raise ValueError(f"打拳GIF處理失敗: {str(e)}\n{error_details}") 