"""
Gacha系統十連抽Embed構建器
"""
from typing import Any, Dict, List, Optional, Union
from decimal import Decimal
import discord
from utils.logger import logger
from gacha.models.models import Card, CardWithStatus
from gacha.views.utils import format_oil
from gacha.views.embeds.base_embed_builder import BaseEmbedBuilder
from gacha.views import utils as view_utils
from gacha.app_config import config_service
from gacha.constants import RarityLevel

class MultiDrawEmbedBuilder(BaseEmbedBuilder):
    """構建十連抽結果嵌入消息的類"""

    def __init__(self, user: discord.User, cards_results: List[Union[Dict[str, Any], CardWithStatus]], balance: int, nickname: Optional[str]=None):
        """初始化十連抽Embed構建器

        參數:
            user: Discord用戶對象
            cards_results: 十連抽結果列表 (已按稀有度排序) - 可以是CardWithStatus對象列表或舊格式的字典列表
            balance: 用戶剩餘油幣
            nickname: 用戶暱稱，如果有的話優先使用
        """
        multi_draw_data = {'user': user, 'cards_results': cards_results, 'balance': balance, 'nickname': nickname}
        super().__init__(data=multi_draw_data)
        self.user = user
        self.cards_results = cards_results
        self.balance = balance
        self.nickname = nickname

    def build_embed(self) -> discord.Embed:
        """創建十連抽總覽的Embed

        返回:
            格式化後的Discord Embed對象
        """
        display_name = self.nickname or self.user.display_name
        representative_card_rarity_int: Optional[int] = None
        representative_card_pool_type: Optional[str] = None
        final_embed_color_obj: discord.Color = self.DEFAULT_EMBED_COLOR
        if self.cards_results:
            first_result = self.cards_results[0]
            card_for_color: Optional[Card] = None
            pool_type_for_color: Optional[str] = None
            if hasattr(first_result, 'card') and isinstance(first_result.card, Card):
                card_for_color = first_result.card
                pool_type_for_color = first_result.pool_type
            elif isinstance(first_result, dict) and 'card' in first_result and isinstance(first_result['card'], Card):
                card_for_color = first_result['card']
                pool_type_for_color = first_result.get('pool_type') or card_for_color.pool_type
            if card_for_color:
                representative_card_rarity_int = card_for_color.rarity
                if representative_card_rarity_int is not None:
                    rarity_enum_for_color = None
                    try:
                        rarity_enum_for_color = RarityLevel(representative_card_rarity_int)
                    except ValueError:
                        logger.warning('[MultiDrawEmbedBuilder] 無法將代表性稀有度 %s 解析為 RarityLevel。', representative_card_rarity_int)
                    if rarity_enum_for_color:
                        actual_color = self._get_rarity_color(rarity_level=rarity_enum_for_color, pool_type=pool_type_for_color)
                        final_embed_color_obj = actual_color
            else:
                logger.warning('[MultiDrawEmbedBuilder] Could not extract card for color from first_result.')
        else:
            logger.warning('[MultiDrawEmbedBuilder] cards_results is empty, cannot determine color.')
        embed = self._create_base_embed(title='', description='抽取了 10 張卡片', color=final_embed_color_obj)
        self._set_draw_author(embed, user=self.user, nickname=self.nickname, is_multi_draw=True)
        pool_results = {}
        for result in self.cards_results:
            if hasattr(result, 'pool_type'):
                pool_type = result.pool_type
            else:
                pool_type = result.get('pool_type') or result['card'].pool_type
            pool_results.setdefault(pool_type, []).append(result)
        DISCORD_FIELD_LIMIT = 1000
        for pool_type, results_list in pool_results.items():
            if not results_list:
                continue
            pool_name = config_service.get_pool_type_names().get(pool_type, pool_type.capitalize())
            generated_fields = self._build_fields_for_pool_results(results_list, pool_name)
            for name, value in generated_fields:
                self._add_field(embed, name=name, value=value, inline=False)
        if representative_card_rarity_int is not None:
            rep_rarity_enum = None
            if representative_card_rarity_int is not None:
                try:
                    rep_rarity_enum = RarityLevel(representative_card_rarity_int)
                except ValueError:
                    pass
            thumbnail_url = view_utils.get_rarity_image(rep_rarity_enum)
        embed.set_footer(text='使用翻頁按鈕查看卡片詳情')
        return embed

    def _generate_card_list_content(self, results: List[Union[Dict[str, Any], CardWithStatus]], shorten_series: bool=False) -> List[str]:
        """生成卡片列表的純文字內容行列表。

        參數:
            results: 卡片結果列表。
            shorten_series: 是否縮短系列名稱。

        返回:
            List[str]: 每張卡片格式化後的字串列表。
        """
        return [self._format_card_info(result, shorten_series=shorten_series) for result in results]

    def _format_rarity_count_line(self, rarity_level: Optional[RarityLevel], count: int) -> str:
        """格式化稀有度統計行。"""
        emoji = view_utils.get_rarity_display_code(rarity_level)
        friendly_code = ''
        if rarity_level:
            if rarity_level == RarityLevel.SSR:
                friendly_code = 'SSR'
            else:
                friendly_name_full = view_utils.get_user_friendly_rarity_name(rarity_level)
                parts = friendly_name_full.split(' ')
                if len(parts) > 0 and parts[-1].startswith('(') and parts[-1].endswith(')'):
                    friendly_code = parts[-1].strip('()')
                else:
                    friendly_code = str(rarity_level.value)
        else:
            friendly_code = '未知'
        return f'{emoji} **{friendly_code}**: x{count}'

    def _build_fields_for_pool_results(self, results: List[Union[Dict[str, Any], CardWithStatus]], pool_name: str) -> List[tuple[str, str]]:
        """為單個卡池的結果列表生成 embed 欄位。"""
        fields_to_add = []
        DISCORD_FIELD_LIMIT = 1000
        formatted_lines_full = self._generate_card_list_content(results, shorten_series=False)
        field_content_full = '\n'.join(formatted_lines_full)
        if len(field_content_full) <= DISCORD_FIELD_LIMIT:
            fields_to_add.append((f'【{pool_name}】', field_content_full))
            return fields_to_add
        if len(results) <= 8:
            formatted_lines_shortened = self._generate_card_list_content(results, shorten_series=True)
            field_content_shortened = '\n'.join(formatted_lines_shortened)
            if len(field_content_shortened) <= DISCORD_FIELD_LIMIT:
                fields_to_add.append((f'【{pool_name}】', field_content_shortened))
                return fields_to_add
            else:
                mid_point = len(formatted_lines_shortened) // 2
                content1_lines = formatted_lines_shortened[:mid_point]
                content2_lines = formatted_lines_shortened[mid_point:]
                content1 = '\n'.join(content1_lines)
                content2 = '\n'.join(content2_lines)
                fields_to_add.append((f'【{pool_name}】', content1))
                fields_to_add.append((f'【{pool_name}】(續)', content2))
                return fields_to_add
        high_rarity_results = []
        low_rarity_results = []
        rarity_counts: Dict[int, int] = {}
        for res_item in results:
            card_obj = res_item.card if hasattr(res_item, 'card') else res_item['card']
            rarity_code = card_obj.rarity
            rarity_counts[rarity_code] = rarity_counts.get(rarity_code, 0) + 1
            if rarity_code >= RarityLevel.SSR.value:
                high_rarity_results.append(res_item)
            else:
                low_rarity_results.append(res_item)
        content_parts = []
        if high_rarity_results:
            content_parts.extend(self._generate_card_list_content(high_rarity_results, shorten_series=True))
        if low_rarity_results:
            low_rarity_lines_detailed = self._generate_card_list_content(low_rarity_results, shorten_series=True)
            temp_combined_content = '\n'.join(content_parts + low_rarity_lines_detailed)
            if len(temp_combined_content) <= DISCORD_FIELD_LIMIT:
                content_parts.extend(low_rarity_lines_detailed)
            else:
                for r_code in sorted(rarity_counts.keys()):
                    if r_code < RarityLevel.SSR.value and rarity_counts[r_code] > 0:
                        try:
                            r_lvl_enum = RarityLevel(r_code)
                            content_parts.append(self._format_rarity_count_line(r_lvl_enum, rarity_counts[r_code]))
                        except ValueError:
                            logger.warning('策略4：無法解析稀有度代碼 %s 為 RarityLevel', r_code)
        field_content_grouped = '\n'.join(content_parts)
        if len(field_content_grouped) <= DISCORD_FIELD_LIMIT:
            fields_to_add.append((f'【{pool_name}】', field_content_grouped))
            return fields_to_add
        t5_plus_results = []
        t4_results_for_counting = []
        other_rarity_counts_s5: Dict[int, int] = {}
        for res_item_final in results:
            card_final = res_item_final.card if hasattr(res_item_final, 'card') else res_item_final['card']
            rarity_code_final_inner = card_final.rarity
            if rarity_code_final_inner >= RarityLevel.ULTRA_RARE.value:
                t5_plus_results.append(res_item_final)
            elif rarity_code_final_inner == RarityLevel.SSR.value:
                t4_results_for_counting.append(res_item_final)
            else:
                other_rarity_counts_s5[rarity_code_final_inner] = other_rarity_counts_s5.get(rarity_code_final_inner, 0) + 1
        final_parts_for_strategy5 = []
        if t5_plus_results:
            final_parts_for_strategy5.extend(self._generate_card_list_content(t5_plus_results, shorten_series=True))
        if t4_results_for_counting:
            try:
                r4_enum = RarityLevel.SSR
                final_parts_for_strategy5.append(self._format_rarity_count_line(r4_enum, len(t4_results_for_counting)))
            except ValueError:
                logger.error('策略5：無法獲取 RarityLevel FOR T4')
        for r_code_final, count in sorted(other_rarity_counts_s5.items()):
            try:
                r_lvl_enum_final = RarityLevel(r_code_final)
                final_parts_for_strategy5.append(self._format_rarity_count_line(r_lvl_enum_final, count))
            except ValueError:
                logger.warning('策略5：無法解析稀有度代碼 %s 為 RarityLevel', r_code_final)
        final_content_ultimate = '\n'.join(final_parts_for_strategy5)
        fields_to_add.append((f'【{pool_name}】', final_content_ultimate))
        return fields_to_add

    def _format_card_info(self, result: Union[Dict[str, Any], CardWithStatus], shorten_series: bool=False) -> str:
        """格式化單張卡片信息 (私有方法)
        現在委託給 CardFormattingService.format_multi_draw_card_summary_line。

        參數:
            result: 卡片結果字典或CardWithStatus對象
            shorten_series: 是否縮短系列名稱

        返回:
            str: 格式化後的卡片信息
        """
        if hasattr(result, 'status') and isinstance(result, CardWithStatus):
            card = result.card
            is_new_card = result.status.is_new_card
            is_wish = result.status.is_wish
            is_favorite = result.status.is_favorite
        elif isinstance(result, dict):
            card_data = result.get('card')
            if not isinstance(card_data, Card):
                logger.error('_format_card_info: Result dictionary does not contain a valid Card object: %s', result)
                return '錯誤: 無法解析卡片資訊'
            card = card_data
            is_new_card = result.get('is_new_card', False)
            is_wish = result.get('is_wish', False)
            is_favorite = result.get('is_favorite', False)
        else:
            logger.error('_format_card_info: Invalid result type: %s. Expected CardWithStatus or dict.', type(result))
            return '錯誤: 無效的卡片資料格式'
        card_rarity_enum: Optional[RarityLevel] = None
        try:
            if isinstance(card.rarity, int):
                card_rarity_enum = RarityLevel(card.rarity)
            elif isinstance(card.rarity, RarityLevel):
                card_rarity_enum = card.rarity
        except ValueError:
            logger.warning('_format_card_info: Could not parse rarity %s for card %s to RarityLevel.', card.rarity, card.name)
        return self.card_formatter.format_multi_draw_card_summary_line(card_name=card.name, card_series=card.series, is_favorite=is_favorite, is_new_card=is_new_card, is_wish=is_wish, rarity_enum=card_rarity_enum, shorten_series=shorten_series)

    def _build_and_enrich_multi_draw_page_embed(self, embed_to_enrich: discord.Embed, card: Card, is_new_card: bool, is_wish: bool, is_favorite: bool, star_level: int, pool_type: str, owner_count: int, current_index: int, total_cards: int, original_summary_field_count: int) -> discord.Embed:
        """
        統一處理多抽卡片頁面 Embed 的構建與內容填充。
        此方法旨在合併 build_combined_embed 和 enrich_embed_with_page_details 的核心邏輯。
        現在它將使用 BaseEmbedBuilder 的方法來添加獨立的卡片資訊和市場/餘額欄位，
        並且會根據當前顯示的卡片更新 Embed 顏色。

        參數:
            embed_to_enrich: 作為基礎的 Embed 物件 (應為副本或新創建的)。
            card: 當前要顯示的卡片物件。
            is_new_card: 是否為新卡。
            is_wish: 是否為許願卡。
            is_favorite: 是否為最愛卡。
            star_level: 卡片星級。
            pool_type: 當前卡片的卡池類型。
            owner_count: 卡片擁有者數量。
            current_index: 當前卡片在多抽結果中的索引。
            total_cards: 多抽結果的總卡片數量。
            original_summary_field_count: 原始 embed_to_enrich 中代表總覽部分的欄位數量。
        """
        current_card_rarity_enum: Optional[RarityLevel] = None
        try:
            if isinstance(card.rarity, int):
                current_card_rarity_enum = RarityLevel(card.rarity)
            elif isinstance(card.rarity, RarityLevel):
                current_card_rarity_enum = card.rarity
        except ValueError:
            logger.warning('MultiDraw (_build_and_enrich): 無法將卡片稀有度 %s (卡片: %s) 解析為 RarityLevel。', card.rarity, card.name)
        current_card_color = self._get_rarity_color(rarity_level=current_card_rarity_enum, pool_type=pool_type)
        embed_to_enrich.color = current_card_color
        num_fields_to_remove = len(embed_to_enrich.fields) - original_summary_field_count
        for _ in range(num_fields_to_remove):
            if len(embed_to_enrich.fields) > original_summary_field_count:
                embed_to_enrich.remove_field(len(embed_to_enrich.fields) - 1)
        display_name_for_title = self.nickname or self.user.display_name
        custom_field_name = self.card_formatter.format_multi_draw_page_card_event_title(display_name=display_name_for_title, is_wish=is_wish, pool_type=pool_type, is_new_card=is_new_card)
        self._add_card_primary_details_field_or_description(embed=embed_to_enrich, card=card, is_favorite=is_favorite, is_new_card=is_new_card, is_wish=is_wish, star_level=star_level, owner_count=owner_count, as_description=False, field_name=custom_field_name, inline=False)
        self._add_market_price_and_balance_field(embed=embed_to_enrich, current_market_sell_price=card.current_market_sell_price, card_rarity_for_fallback=current_card_rarity_enum, pool_type_for_fallback=pool_type, balance=self.balance, field_name='', inline=False)
        self._set_card_visuals(embed=embed_to_enrich, card_image_url=card.image_url, card_rarity_enum=current_card_rarity_enum, pool_type=pool_type)
        self._set_draw_specific_footer(embed=embed_to_enrich, card_for_id=card, is_wish=is_wish, multi_draw_page_info=f'第 {current_index + 1}/{total_cards} 張', include_bot_signature=True)
        return embed_to_enrich

    def build_combined_embed(self, current_card_with_status: CardWithStatus, owner_count: int, current_index: int, total_cards: int) -> discord.Embed:
        """創建合併十連抽總覽和當前卡片詳細信息的Embed
        現在通過調用 _build_and_enrich_multi_draw_page_embed 實現。
        """
        summary_embed = self.build_embed()
        original_summary_field_count = len(summary_embed.fields)
        card = current_card_with_status.card
        is_new_card = current_card_with_status.status.is_new_card
        is_wish = current_card_with_status.status.is_wish
        is_favorite = current_card_with_status.status.is_favorite
        star_level = current_card_with_status.status.star_level
        pool_type = current_card_with_status.pool_type
        return self._build_and_enrich_multi_draw_page_embed(embed_to_enrich=summary_embed, card=card, is_new_card=is_new_card, is_wish=is_wish, is_favorite=is_favorite, star_level=star_level, pool_type=pool_type, owner_count=owner_count, current_index=current_index, total_cards=total_cards, original_summary_field_count=original_summary_field_count)

    def enrich_embed_with_page_details(self, base_embed: discord.Embed, card: Card, is_new_card: bool, is_wish: bool, is_favorite: bool, star_level: int, pool_type: str, owner_count: int, current_index: int, total_cards: int, summary_field_count: int) -> discord.Embed:
        """
        Enriches a base embed (summary) with details of the current card page.
        Now delegates to _build_and_enrich_multi_draw_page_embed.
        The caller (MultiDrawView) is responsible for passing base_embed.copy().
        """
        return self._build_and_enrich_multi_draw_page_embed(embed_to_enrich=base_embed, card=card, is_new_card=is_new_card, is_wish=is_wish, is_favorite=is_favorite, star_level=star_level, pool_type=pool_type, owner_count=owner_count, current_index=current_index, total_cards=total_cards, original_summary_field_count=summary_field_count)