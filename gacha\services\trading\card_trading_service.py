import asyncpg
import logging
import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, Tuple, Union
from gacha.app_config import config_service
from gacha.models.trade_models import CardTradeHistoryModel, TradeType, TradeStatus
from gacha.repositories.trading.card_trade_history_repository import CardTradeHistoryRepository
from gacha.repositories.card.master_card_repository import MasterCardRepository
from gacha.services.core.collection_service import CollectionService
from gacha.services.core.user_service import UserService
from gacha.exceptions import (
    UserNotFoundError, 
    CardNotFoundError, 
    InsufficientBalanceError,
    DatabaseOperationError,
    GachaSystemError
)

logger = logging.getLogger(__name__)


class TradingError(GachaSystemError):
    """交易相關錯誤的基礎類別"""
    pass


class InvalidTradeParametersError(TradingError):
    """無效的交易參數錯誤"""
    pass


class InsufficientCardQuantityError(TradingError):
    """卡片數量不足錯誤"""
    
    def __init__(self, card_name: str, required: int, current: int):
        super().__init__(f"卡片「{card_name}」數量不足：需要 {required} 張，目前 {current} 張")
        self.card_name = card_name
        self.required = required
        self.current = current


class TradeNotFoundError(TradingError):
    """交易不存在錯誤"""
    
    def __init__(self, trade_id: str):
        super().__init__(f"交易 {trade_id} 不存在或已過期")
        self.trade_id = trade_id


class UnauthorizedTradeAccessError(TradingError):
    """未授權的交易存取錯誤"""
    pass


class CardTradingService:
    """
    Service layer for handling P2P card trading logic.
    使用純異常模式進行錯誤處理
    """

    def __init__(self, pool: asyncpg.Pool, collection_service: CollectionService, user_service: UserService, trade_history_repo: CardTradeHistoryRepository, master_card_repo: MasterCardRepository):
        """
        Initializes the CardTradingService.

        Args:
            pool: The asyncpg connection pool.
            collection_service: Service for managing user card collections.
            user_service: Service for managing user data (like oil balance).
            trade_history_repo: Repository for trade history.
            master_card_repo: Repository for master card data.
        """
        self.pool = pool
        self.collection_service = collection_service
        self.user_service = user_service
        self.trade_history_repo = trade_history_repo
        self.master_card_repo = master_card_repo
        self.pending_trades: Dict[str, Dict[str, Any]] = {}
        self.trade_fee_percentage = Decimal(str(config_service.get_config('gacha_core_settings.trade_fee_percentage')))
        self.min_trade_fee = Decimal(str(config_service.get_config('gacha_core_settings.min_trade_fee')))

    def _parse_card_id(self, card_id_input: Union[int, str, None], id_description: str) -> Optional[int]:
        """
        Parses a card ID input (str or int) into an integer.
        Raises InvalidTradeParametersError if parsing fails.
        Returns None if input is None.
        """
        if card_id_input is None:
            return None
            
        try:
            return int(card_id_input)
        except ValueError:
            logger.warning("Invalid %s format '%s'. Must be an integer.", id_description, card_id_input)
            raise InvalidTradeParametersError(f"{id_description} '{card_id_input}' 格式不正確，必須是數字。")

    async def initiate_trade(self, initiator_id: int, receiver_id: int, offered_card_id: Union[int, str], offer_quantity: int, requested_card_id: Optional[Union[int, str]]=None, requested_quantity: Optional[int]=None, price: Optional[int]=None) -> Dict[str, Any]:
        """
        Handles the initiation of a P2P trade request.
        Validates the request and prepares trade details for confirmation.

        Returns:
            Trade details dictionary for confirmation.
            
        Raises:
            InvalidTradeParametersError: 當交易參數無效時
            CardNotFoundError: 當卡片不存在時
            InsufficientCardQuantityError: 當卡片數量不足時
            DatabaseOperationError: 當資料庫操作失敗時
        """
        # 解析卡片ID
        current_offered_card_id = self._parse_card_id(offered_card_id, '提供的卡片 ID')
        if current_offered_card_id is None:
            raise InvalidTradeParametersError('提供的卡片 ID 不能為空')
            
        current_requested_card_id = self._parse_card_id(requested_card_id, '要求的卡片 ID')
        
        logger.info('Initiating trade (parsed IDs): initiator=%s, receiver=%s, offered_card=%sx%s, requested_card=%sx%s, price=%s', 
                   initiator_id, receiver_id, current_offered_card_id, offer_quantity, current_requested_card_id, requested_quantity, price)
        
        # 驗證交易參數
        if initiator_id == receiver_id:
            raise InvalidTradeParametersError('你不能與自己交易')
            
        if offer_quantity <= 0:
            raise InvalidTradeParametersError('提供的卡片數量必須大於0')
            
        if price is not None and price <= 0:
            raise InvalidTradeParametersError('價格必須大於0')
            
        if current_requested_card_id is not None and price is not None:
            raise InvalidTradeParametersError('不能同時要求卡片和油幣')
            
        if current_requested_card_id is not None:
            if requested_quantity is None or requested_quantity <= 0:
                requested_quantity = 1

        try:
            # 驗證提供的卡片存在
            offered_card = await self.master_card_repo.get_card(current_offered_card_id)
            if not offered_card:
                raise CardNotFoundError(f'提供的卡片 (ID: {current_offered_card_id}) 不存在')
                
            # 驗證要求的卡片存在（如果有指定）
            requested_card = None
            if current_requested_card_id:
                requested_card = await self.master_card_repo.get_card(current_requested_card_id)
                if not requested_card:
                    raise CardNotFoundError(f'要求的卡片 (ID: {current_requested_card_id}) 不存在')
                    
            # 檢查發起者是否有足夠的卡片
            initiator_card_quantity = await self.collection_service.get_user_card_quantity(
                user_id=initiator_id, 
                master_card_id=current_offered_card_id
            )
            if initiator_card_quantity < offer_quantity:
                raise InsufficientCardQuantityError(
                    card_name=offered_card.name,
                    required=offer_quantity,
                    current=initiator_card_quantity
                )
                
        except (CardNotFoundError, InsufficientCardQuantityError):
            raise
        except Exception as e:
            logger.error('Error during trade initiation validation for initiator %s: %s', initiator_id, e, exc_info=True)
            raise DatabaseOperationError(f'驗證交易資訊時發生錯誤：{e}') from e

        # 確定交易類型
        trade_type_value = TradeType.GIFT_CARD.value
        if price is not None:
            trade_type_value = TradeType.CARD_FOR_OIL.value
        elif current_requested_card_id is not None:
            trade_type_value = TradeType.CARD_FOR_CARD.value

        # 創建交易詳情
        trade_id = str(uuid.uuid4())
        trade_details = {
            'trade_id': trade_id,
            'initiator_id': initiator_id,
            'receiver_id': receiver_id,
            'offered_card_id': current_offered_card_id,
            'offered_card_name': offered_card.name,
            'offered_quantity': offer_quantity,
            'requested_card_id': current_requested_card_id,
            'requested_card_name': requested_card.name if current_requested_card_id and requested_card else None,
            'requested_quantity': requested_quantity,
            'price': price,
            'trade_type': trade_type_value,
            'status': TradeStatus.PENDING.value
        }
        
        self.pending_trades[trade_id] = trade_details
        logger.info('Trade %s initiated and stored. Details: %s', trade_id, trade_details)
        return trade_details

    async def get_trade_details(self, trade_id: str) -> Dict[str, Any]:
        """
        Retrieves pending trade details by trade_id.
        
        Raises:
            TradeNotFoundError: 當交易不存在時
        """
        trade_details = self.pending_trades.get(trade_id)
        if not trade_details:
            raise TradeNotFoundError(trade_id)
        return trade_details

    async def _remove_pending_trade(self, trade_id: str):
        """Removes a trade from pending_trades if it exists."""
        if trade_id in self.pending_trades:
            del self.pending_trades[trade_id]
            logger.info('Removed trade %s from pending trades.', trade_id)
        else:
            logger.warning('Attempted to remove non-existent trade_id %s from pending_trades.', trade_id)

    async def accept_trade(self, trade_details: Dict[str, Any], accepting_user_id: int) -> None:
        """
        Handles the confirmation of a P2P trade by the receiver.
        - Retrieves trade details.
        - Performs final checks.
        - Executes the trade (atomically update collections and balances).
        - Calculates and applies fees.
        - Records the trade in history.
        - Removes trade from pending list.
        
        Raises:
            InvalidTradeParametersError: 當交易參數無效時
            UnauthorizedTradeAccessError: 當用戶無權限接受交易時
            InsufficientCardQuantityError: 當卡片數量不足時
            InsufficientBalanceError: 當油幣餘額不足時
            DatabaseOperationError: 當資料庫操作失敗時
        """
        trade_id = trade_details.get('trade_id')
        if not trade_id:
            raise InvalidTradeParametersError('交易資訊不完整 (缺少 trade_id)')
            
        logger.info('Attempting to accept trade %s: details=%s, accepting_user_id=%s', trade_id, trade_details, accepting_user_id)
        
        if not trade_details or not isinstance(trade_details, dict):
            raise InvalidTradeParametersError('無效的交易資訊')
            
        receiver_id = trade_details.get('receiver_id')
        if accepting_user_id != receiver_id:
            raise UnauthorizedTradeAccessError('您不是此交易的指定接收方，無法接受')
            
        # 提取交易詳情
        initiator_id = trade_details.get('initiator_id')
        offered_card_id = trade_details.get('offered_card_id')
        offered_quantity = trade_details.get('offered_quantity')
        requested_card_id = trade_details.get('requested_card_id')
        requested_quantity = trade_details.get('requested_quantity')
        price = trade_details.get('price')
        trade_type_str = trade_details.get('trade_type')
        
        if None in [initiator_id, offered_card_id, offered_quantity, trade_type_str]:
            raise InvalidTradeParametersError('交易資訊不完整')
            
        try:
            trade_type = TradeType(trade_type_str)
        except ValueError:
            raise InvalidTradeParametersError(f'無效的交易類型: {trade_type_str}')

        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    # 檢查發起者是否仍有足夠的卡片
                    current_initiator_offered_cards = await self.collection_service.get_user_card_quantity(
                        user_id=initiator_id, 
                        master_card_id=offered_card_id, 
                        connection=conn
                    )
                    if current_initiator_offered_cards < offered_quantity:
                        raise InsufficientCardQuantityError(
                            card_name=trade_details.get('offered_card_name', str(offered_card_id)),
                            required=offered_quantity,
                            current=current_initiator_offered_cards
                        )
                    
                    # 根據交易類型進行不同的檢查
                    if trade_type == TradeType.CARD_FOR_CARD:
                        if not requested_card_id or not requested_quantity:
                            raise InvalidTradeParametersError('接受卡換卡交易時，要求的卡片資訊不完整')
                            
                        current_receiver_requested_cards = await self.collection_service.get_user_card_quantity(
                            user_id=accepting_user_id, 
                            master_card_id=requested_card_id, 
                            connection=conn
                        )
                        if current_receiver_requested_cards < requested_quantity:
                            raise InsufficientCardQuantityError(
                                card_name=trade_details.get('requested_card_name', str(requested_card_id)),
                                required=requested_quantity,
                                current=current_receiver_requested_cards
                            )
                            
                    elif trade_type == TradeType.CARD_FOR_OIL:
                        if price is None or price <= 0:
                            raise InvalidTradeParametersError('接受油幣交易時，價格資訊不正確')
                            
                        receiver_balance = await self.user_service.get_user_balance(user_id=accepting_user_id)
                        if receiver_balance < price:
                            raise InsufficientBalanceError(
                                message=f'您的油幣不足以完成此交易',
                                required=price,
                                current=receiver_balance
                            )
                    
                    # 執行卡片轉移
                    await self.collection_service.transfer_card_quantity(
                        sender_id=initiator_id, 
                        receiver_id=accepting_user_id, 
                        master_card_id=offered_card_id, 
                        quantity=offered_quantity, 
                        connection=conn
                    )
                    logger.info('Transferred %s of card %s from %s to %s', offered_quantity, offered_card_id, initiator_id, accepting_user_id)
                    
                    final_initiator_gets_oil = 0
                    fee_paid = Decimal('0')
                    
                    if trade_type == TradeType.CARD_FOR_CARD:
                        # 卡換卡：轉移要求的卡片
                        await self.collection_service.transfer_card_quantity(
                            sender_id=accepting_user_id, 
                            receiver_id=initiator_id, 
                            master_card_id=requested_card_id, 
                            quantity=requested_quantity, 
                            connection=conn
                        )
                        logger.info('Transferred %s of card %s from %s to %s', requested_quantity, requested_card_id, accepting_user_id, initiator_id)
                        
                    elif trade_type == TradeType.CARD_FOR_OIL:
                        # 卡換油：處理油幣轉移和手續費
                        decimal_price = Decimal(str(price))
                        fee_paid = max(self.min_trade_fee, decimal_price * self.trade_fee_percentage)
                        fee_paid = fee_paid.quantize(Decimal('1'))
                        net_amount_to_initiator = decimal_price - fee_paid
                        final_initiator_gets_oil = int(net_amount_to_initiator)
                        
                        await self.user_service.award_balance(user_id=accepting_user_id, amount=-price, connection=conn)
                        logger.info('Deducted %s oil from receiver %s.', price, accepting_user_id)
                        
                        await self.user_service.award_balance(user_id=initiator_id, amount=final_initiator_gets_oil, connection=conn)
                        logger.info('Awarded %s oil to initiator %s (Price: %s, Fee: %s).', final_initiator_gets_oil, initiator_id, price, fee_paid)
                    
                    # 記錄交易歷史
                    history_entry = CardTradeHistoryModel(
                        initiator_user_id=initiator_id,
                        receiver_user_id=accepting_user_id,
                        offered_master_card_id=offered_card_id,
                        offered_quantity=offered_quantity,
                        requested_master_card_id=requested_card_id if trade_type == TradeType.CARD_FOR_CARD else None,
                        requested_quantity=requested_quantity if trade_type == TradeType.CARD_FOR_CARD else None,
                        price_amount=price if trade_type == TradeType.CARD_FOR_OIL else None,
                        fee_charged=int(fee_paid) if trade_type == TradeType.CARD_FOR_OIL else None,
                        trade_type=trade_type,
                        completed_at=datetime.now()
                    )
                    await self.trade_history_repo.add_trade_history(history_entry, connection=conn)
                    logger.info('Trade history recorded for trade between %s and %s.', initiator_id, accepting_user_id)
                    
            logger.info('Trade %s between %s and %s completed successfully.', trade_id, initiator_id, accepting_user_id)
            await self._remove_pending_trade(trade_id)
            
        except (InvalidTradeParametersError, UnauthorizedTradeAccessError, InsufficientCardQuantityError, InsufficientBalanceError):
            raise
        except Exception as e:
            logger.error('Error during trade %s acceptance: %s. Details: %s', trade_id, e, trade_details, exc_info=True)
            raise DatabaseOperationError(f'接受交易時發生錯誤：{e}') from e

    async def cancel_trade(self, trade_details: Dict[str, Any], canceling_user_id: int, reason: str='交易被取消') -> str:
        """
        Handles the cancellation/rejection of a P2P trade.
        Removes trade from pending list.
        
        Returns:
            Cancellation reason message
            
        Raises:
            InvalidTradeParametersError: 當交易參數無效時
            UnauthorizedTradeAccessError: 當用戶無權限取消交易時
        """
        trade_id = trade_details.get('trade_id')
        if not trade_id:
            raise InvalidTradeParametersError('交易資訊不完整 (缺少 trade_id)')
            
        logger.info("Attempting to cancel trade %s: details=%s, canceling_user_id=%s, reason='%s'", 
                   trade_id, trade_details, canceling_user_id, reason)
        
        if not trade_details or not isinstance(trade_details, dict):
            raise InvalidTradeParametersError('無效的交易資訊')
            
        initiator_id = trade_details.get('initiator_id')
        receiver_id = trade_details.get('receiver_id')
        
        if initiator_id is None or receiver_id is None:
            raise InvalidTradeParametersError('交易資訊不完整')
            
        if canceling_user_id != initiator_id and canceling_user_id != receiver_id:
            raise UnauthorizedTradeAccessError('您不是此交易的參與者，無法取消')
            
        logger.info('Trade %s between initiator %s and receiver %s was cancelled by user %s. Reason: %s', 
                   trade_id, initiator_id, receiver_id, canceling_user_id, reason)
        await self._remove_pending_trade(trade_id)
        return reason