# -*- coding: utf-8 -*-
"""
Gacha 系統遊戲通用回調處理器基底類別
"""

from typing import TYPE_CHECKING, Any, Optional
import discord

from utils.logger import logger
from gacha.utils import interaction_utils  # 新增導入

if TYPE_CHECKING:
    # 避免循環導入，僅用於類型提示
    from ..base_view import BaseView  # 假設有一個通用的 BaseView 或需要指定具體 View
    from gacha.services.games.base_game_service import (
        BaseGameService,
    )  # 假設有基礎遊戲服務
    from gacha.views.collection.collection_view import (
        InteractionManager,
    )  # 沿用 Blackjack 的管理器


class BaseGameCallbackHandler:
    """處理遊戲視圖回調的基底類別"""

    def __init__(
            self,
            interaction_manager: "InteractionManager",
            user_id: int):
        """
        初始化基底回調處理器

        參數:
            interaction_manager: InteractionManager 實例，用於管理互動
            user_id: 觸發遊戲的原始用戶 ID
        """
        self.interaction_manager = interaction_manager
        self.user_id = user_id
        # 注意：子類可能需要 game_service 或其他依賴，可以在子類的 __init__ 中添加
