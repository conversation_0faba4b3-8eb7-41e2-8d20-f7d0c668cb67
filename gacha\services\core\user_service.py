import asyncpg
from typing import Dict, Optional, Any, List
from decimal import Decimal
from utils.logger import logger
from database.postgresql.async_manager import AsyncPgManager
from gacha.models.models import GachaUser
from gacha.repositories.user.user_repository import UserRepository
from gacha.exceptions import UserNotFoundError, DatabaseOperationError

class UserService:
    """用戶服務 (Asyncpg 版本)，提供獲取和管理用戶資訊的統一介面"""

    def __init__(self, pool: asyncpg.Pool):
        """初始化用戶服務 (Asyncpg 版本)"""
        if pool is None:
            err_msg = 'UserService 初始化失敗：必須提供 asyncpg 連接池。'
            logger.error(err_msg)
            raise ValueError(err_msg)
        self.pool = pool
        self.user_repo = UserRepository(self.pool)

    async def get_user(self, user_id: int, create_if_missing: bool=False, nickname: Optional[str]=None, connection: Optional[asyncpg.Connection]=None) -> GachaUser:
        """
        (Async) 獲取用戶資訊，可選是否自動創建
        
        Args:
            user_id: 用戶ID
            create_if_missing: 如果用戶不存在是否創建
            nickname: 用戶暱稱（僅在創建時使用）
            connection: 可選的資料庫連接
            
        Returns:
            GachaUser: 用戶對象
            
        Raises:
            UserNotFoundError: 如果用戶不存在且不創建
            DatabaseOperationError: 如果資料庫操作失敗
        """
        try:
            user = await self.user_repo.get_user_optional(user_id, connection=connection)
            if not user and create_if_missing:
                user = await self.create_user(user_id, nickname, connection=connection)
            elif not user:
                raise UserNotFoundError(f"找不到用戶 {user_id}", user_id=user_id)
            return user
        except UserNotFoundError:
            # 直接向上傳播這些已知異常
            raise
        except Exception as e:
            logger.error('[GACHA] 獲取用戶資訊失敗: %s', str(e), exc_info=True)
            raise DatabaseOperationError(f"獲取用戶資訊失敗: {str(e)}", original_exception=e)

    async def create_user(self, user_id: int, nickname: Optional[str]=None, connection: Optional[asyncpg.Connection]=None) -> GachaUser:
        """(Async) 創建新用戶"""
        return await self.user_repo.create_user(user_id, nickname, connection=connection)

    async def get_balance(self, user_id: int, nickname: Optional[str]=None) -> Dict[str, int]:
        """
        (Async) 獲取用戶油幣餘額和相關資訊（自動創建用戶）
        
        Args:
            user_id: 用戶ID
            nickname: 用戶暱稱（僅在創建時使用）
            
        Returns:
            Dict[str, int]: 包含餘額和抽卡次數的字典
            
        Raises:
            UserNotFoundError: 如果用戶不存在且無法創建
            DatabaseOperationError: 如果資料庫操作失敗
        """
        # 獲取用戶 - 如果不存在會拋出異常
        user = await self.get_user(user_id, create_if_missing=True, nickname=nickname)
        
        # 返回餘額信息
        return {
            'balance': user.oil_balance,
            'total_draws': user.total_draws
        }

    async def get_user_balance(self, user_id: int) -> int:
        """
        (Async) 獲取用戶油幣餘額，主要用於 GachaService 的預檢查。
        如果用戶不存在，則拋出 UserNotFoundError。不會自動創建用戶。
        """
        try:
            # get_user 會在找不到用戶且 create_if_missing=False 時拋出 UserNotFoundError
            user = await self.get_user(user_id, create_if_missing=False)
            return user.oil_balance
        except UserNotFoundError:
            raise
        except Exception as e:
            logger.error('[USER_SERVICE] 獲取用戶 (ID: %s) 餘額進行預檢查失敗: %s', user_id, str(e), exc_info=True)
            # 對於其他未知錯誤，包裝成 DatabaseOperationError
            raise DatabaseOperationError(f"獲取用戶 {user_id} 餘額失敗: {str(e)}", original_exception=e)

    async def get_nickname(self, user_id: int) -> Optional[str]:
        """(Async) 獲取用戶暱稱（不自動創建用戶）"""
        try:
            # get_user 會在找不到用戶且 create_if_missing=False 時拋出 UserNotFoundError
            user = await self.get_user(user_id, create_if_missing=False) 
            return user.nickname
        except UserNotFoundError:
            raise # 直接重新拋出 UserNotFoundError
        except Exception as e:
            logger.error('[GACHA] 獲取用戶暱稱失敗: %s', str(e), exc_info=True)
            # 對於其他未知錯誤，包裝成 DatabaseOperationError
            raise DatabaseOperationError(f"獲取用戶 {user_id} 暱稱失敗: {str(e)}", original_exception=e)


    async def ensure_nickname_updated(self, user_id: int, nickname: str) -> bool:
        """(Async) 確保用戶暱稱已更新"""
        try:
            updated = await self.user_repo.update_nickname_if_changed(user_id, nickname)
            return updated
        except Exception as e:
            logger.error('Ensure nickname update failed: user=%s, error=%s', user_id, e, exc_info=True)
            return False

    async def update_balance(self, user_id: int, new_balance: int) -> bool:
        """(Async) 更新用戶油幣餘額"""
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    success = await self.user_repo.update_balance(user_id, new_balance, connection=conn)
                    return new_balance if success else None
        except Exception as e:
            logger.error('更新餘額失敗: user=%s, error=%s', user_id, e, exc_info=True)
            return False

    async def update_daily_claim(self, user_id: int) -> bool:
        """(Async) 更新用戶每日獎勵領取時間"""
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    await self.user_repo.update_daily_claim(user_id, connection=conn)
                    return True
        except Exception as e:
            logger.error('更新每日領取失敗: user=%s, error=%s', user_id, e, exc_info=True)
            return False

    async def award_balance(self, user_id: int, amount: int, connection: Optional[asyncpg.Connection]=None) -> int:
        """(Async) 增加或減少用戶餘額 (返回新餘額)"""
        conn_to_use = connection or self.pool
        try:
            if not connection:
                async with conn_to_use.acquire() as conn_internal:
                    async with conn_internal.transaction():
                        return await self._award_balance_logic(user_id, amount, conn_internal)
            else:
                return await self._award_balance_logic(user_id, amount, conn_to_use)
        except Exception as e:
            logger.error('[GACHA] 餘額操作失敗: %s', str(e), exc_info=True)
            raise

    async def _award_balance_logic(self, user_id: int, amount: int, connection: asyncpg.Connection) -> int:
        """Internal logic for awarding balance within a transaction"""
        from gacha.exceptions import UserNotFoundError, InsufficientFundsError
        
        try:
            user = await self.user_repo.get_user_for_update(user_id, connection=connection)
        except UserNotFoundError:
            # 如果用戶不存在，嘗試創建
            user = await self.user_repo.create_user(user_id, connection=connection)
        
        if amount < 0 and user.oil_balance + amount < 0:
            raise InsufficientFundsError(
                required_amount=abs(amount), 
                current_balance=user.oil_balance
            )
        
        new_balance = user.oil_balance + amount
        # update_balance 現在會拋出異常而不是返回布林值
        await self.user_repo.update_balance(user_id, new_balance, connection=connection)
        return new_balance

    async def _prepare_user_for_draw(self, user_id: int, connection: Optional[asyncpg.Connection]=None) -> Optional[GachaUser]:
        """(Async) 為抽卡操作準備用戶，統一共用邏輯"""
        try:
            # get_user 會在找不到時拋出 UserNotFoundError，所以不需要額外檢查 user 是否為 None
            # create_if_missing=True 會處理用戶不存在的情況
            user = await self.get_user(user_id, create_if_missing=True, connection=connection)
            return user
        except UserNotFoundError:
            # 雖然 get_user 設置了 create_if_missing=True，理論上不應發生此錯誤
            # 但如果 create_user 失敗或有其他邏輯導致，還是要處理
            logger.error('[GACHA] 準備用戶抽卡時，即使嘗試創建用戶，用戶 %s 仍未找到', user_id, exc_info=True)
            raise # 重新拋出 UserNotFoundError 或讓上層轉換
        except DatabaseOperationError as e:
            logger.error('[GACHA] 準備用戶抽卡時資料庫操作失敗 for user %s: %s', user_id, str(e), exc_info=True)
            raise # 重新拋出已知的資料庫錯誤
        except Exception as e: # 捕獲其他未預期的錯誤
            logger.error('[GACHA] 準備用戶抽卡時發生未預期錯誤 for user %s: %s', user_id, str(e), exc_info=True)
            raise DatabaseOperationError(f"準備用戶 {user_id} 抽卡失敗: {str(e)}", original_exception=e)

    async def get_oil_ticket_balance(self, user_id: int, connection: Optional[asyncpg.Connection]=None) -> Decimal:
        """獲取用戶的油票餘額 (使用 Decimal)
        如果用戶不存在，repository 層應返回 Decimal(0) 或引發錯誤，服務層傳遞此結果。
        """
        try:
            balance = await self.user_repo.get_oil_ticket_balance(user_id, connection=connection)
            return balance
        except Exception as e:
            logger.error('[USER_SERVICE] 獲取用戶 (ID: %s) 油票餘額失敗: %s', user_id, e, exc_info=True)
            raise

    async def deduct_oil_tickets(self, user_id: int, amount: int, connection: Optional[asyncpg.Connection]=None) -> None:
        """扣除用戶的油票。
        
        Args:
            user_id: 用戶ID
            amount: 要扣除的油票數量（必須為正整數）
            connection: 可選的資料庫連接
            
        Raises:
            ValueError: 如果 amount 不是正整數
            UserNotFoundError: 如果找不到用戶
            InsufficientBalanceError: 如果餘額不足
        """
        if not isinstance(amount, int) or amount <= 0:
            logger.warning('[USER_SERVICE] 嘗試扣除非正數油票 (amount: %s) for user ID: %s', amount, user_id)
            raise ValueError(f"扣除的油票數量必須為正整數，收到: {amount}")
        try:
            # 直接讓 repository 層拋出異常
            await self.user_repo.deduct_oil_tickets(user_id, amount, connection=connection)
        except Exception as e:
            logger.error('[USER_SERVICE] 扣除用戶 (ID: %s) 油票 %s 失敗: %s', user_id, amount, e, exc_info=True)
            # 向上傳播異常
            raise